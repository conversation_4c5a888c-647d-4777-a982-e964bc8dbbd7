{"permissions": {"allow": ["Bash(rm:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(cp:*)", "Bash(bun lint:*)", "Bash(bunx tsc:*)", "Bash(find:*)", "<PERSON><PERSON>(mv:*)", "Bash(npx tsc:*)", "Bash(grep:*)", "Bash(rg:*)", "WebFetch(domain:api-dev.movuca.app)", "Bash(git rm:*)", "Bash(npx eslint:*)", "Bash(bun format:*)", "Bash(gh pr view:*)", "mcp__sequential-thinking__sequentialthinking", "mcp__gemini-cli__ask-gemini", "mcp__figma__get_figma_data", "Bash(bun run eslint:*)", "WebFetch(domain:shopify.github.io)"], "deny": []}}