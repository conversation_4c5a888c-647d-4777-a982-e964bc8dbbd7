import { ExpoConfig } from 'expo/config';
import 'ts-node/register';

const appName = 'Movuca';
const slug = 'movuca';
const productionConfig = {
  version: process.env.MY_CUSTOM_PROJECT_VERSION || '1.0.0',
};
const developmentConfig = {
  version: '1.0.0',
};

const config = process.env.NODE_ENV === 'production' ? productionConfig : developmentConfig;

export default ({ config }: { config: ExpoConfig }) => {
  const plugins = [
    ...(config.plugins || []),
    './plugins/withHardwareAccelerated.ts',
    './plugins/withGoogleMapsApiKey.ts',
    './plugins/withMapboxAccessToken.ts',
    './plugins/withPatchPodspec.ts',
    [
      'expo-build-properties',
      {
        android: {
          extraMavenRepos: ['../../node_modules/@notifee/react-native/android/libs'],
          signingConfig: {
            alias: process.env.ANDROID_KEY_ALIAS,
            keystorePassword: process.env.ANDROID_KEYSTORE_PASSWORD,
            keyPassword: process.env.ANDROID_KEY_PASSWORD,
            storeFile: process.env.ANDROID_KEYSTORE_PATH,
          },
        },
        ios: {
          deploymentTarget: '16.5',
        },
      },
    ],
    [
      '@rnmapbox/maps',
      {
        RNMapboxMapsDownloadToken: process.env.MAPBOX_SECRET_API_KEY,
        RNMapboxMapsVersion: '11.13.2',
      },
    ],
  ];

  return {
    ...config,
    name: appName,
    slug,
    plugins,
    android: {
      ...config.android,
      config: {
        ...config.android?.config,
        googleMaps: {
          apiKey: process.env.EXPO_PUBLIC_GOOGLE_MAPS_API_KEY,
        },
      },
    },
    // All values in extra will be passed to your app.
    extra: {
      ...config.extra,
      fact: 'kittens are cool',
    },
  };
};
