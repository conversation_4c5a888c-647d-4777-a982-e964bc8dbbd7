# React 19 Concurrent Features Migration Guide

## Migrating from useDebounce to useDeferredValue

This guide explains how we've migrated from traditional debouncing to React 19's concurrent features in the Movuca mobile app.

### What Changed?

We've replaced manual debouncing with React 19's built-in concurrent features:
- `useDebounce` → `useDeferredValue`
- Manual loading states → `useTransition`

### Key Benefits

1. **Better Performance**: React's scheduler optimizes when updates happen
2. **Smoother UI**: Input remains responsive during heavy computations
3. **Less Code**: No need for custom debounce implementations
4. **Better Integration**: Works seamlessly with React's rendering priorities

## Examples

### Before (Traditional Debouncing)

```typescript
import { useDebounce } from '@/src/shared/hooks/useDebounce';

const SearchComponent = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const debouncedQuery = useDebounce(searchQuery, 500);
  
  const { data, isLoading } = useQuery({
    queryKey: ['search', debouncedQuery],
    queryFn: () => searchAPI(debouncedQuery),
    enabled: debouncedQuery.length > 2,
  });
  
  return (
    <input
      value={searchQuery}
      onChange={(e) => setSearchQuery(e.target.value)}
    />
  );
};
```

### After (React 19 Concurrent Features)

```typescript
import { useDeferredValue, useTransition } from 'react';

const SearchComponent = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isPending, startTransition] = useTransition();
  const deferredQuery = useDeferredValue(searchQuery);
  
  const { data, isLoading } = useQuery({
    queryKey: ['search', deferredQuery],
    queryFn: () => searchAPI(deferredQuery),
    enabled: deferredQuery.length > 2,
  });
  
  const handleSearch = (text: string) => {
    startTransition(() => {
      setSearchQuery(text);
    });
  };
  
  return (
    <input
      value={searchQuery}
      onChange={(e) => handleSearch(e.target.value)}
    />
  );
};
```

## Updated Hooks

### useEventDraft

- **Purpose**: Auto-save event drafts with deferred updates
- **Change**: Replaced 3-second debounce with `useDeferredValue`
- **Benefit**: Saves happen during idle time, not on fixed intervals

### useLocationSearch

- **Purpose**: Search locations with responsive UI
- **Changes**:
  - Uses `useDeferredValue` for query deferral
  - Uses `useTransition` for loading states
  - Combined loading state: `isLoading || isPending`
- **Benefits**: 
  - Input stays responsive during searches
  - Natural debouncing based on user behavior
  - Better loading state management

### useFoursquareLocationSearch (New)

A new hook that fully leverages React 19 features:

```typescript
const {
  searchQuery,        // Current input value
  deferredQuery,      // Deferred value for API calls
  updateSearch,       // Update function with transition
  places,             // Search results
  isSearching,        // Combined loading state
} = useFoursquareLocationSearch();
```

## When to Use Each Approach

### Use `useDeferredValue` when:
- You need UI optimization (deferred rendering)
- Search inputs where slight delays are acceptable
- Heavy computations that shouldn't block user input
- You want React to handle timing based on device performance

### Use traditional debouncing when:
- You need exact timing control (e.g., exactly 500ms delay)
- Specific business requirements for delays
- Integration with non-React systems that expect fixed delays

## Best Practices

1. **Combine with useTransition**: For better loading state management
   ```typescript
   const [isPending, startTransition] = useTransition();
   const deferredValue = useDeferredValue(inputValue);
   ```

2. **Show Loading States**: Combine query and transition states
   ```typescript
   const isLoading = query.isLoading || isPending;
   ```

3. **Keep Input Responsive**: Wrap state updates in transitions
   ```typescript
   const handleChange = (value: string) => {
     startTransition(() => {
       setValue(value);
     });
   };
   ```

4. **Provide Feedback**: Show users that something is happening
   ```typescript
   {isPending && <Spinner />}
   {deferredValue !== inputValue && <span>Searching...</span>}
   ```

## Performance Considerations

- `useDeferredValue` doesn't guarantee a fixed delay
- Updates happen when React determines the browser is idle
- Better for user experience as it adapts to device performance
- Reduces unnecessary API calls during rapid typing

## Migration Checklist

- [x] Remove `useDebounce` imports
- [x] Replace with `useDeferredValue`
- [x] Add `useTransition` for loading states
- [x] Update loading state logic
- [x] Test on low-end devices
- [x] Verify API call frequency

## Resources

- [React 19 Docs - useDeferredValue](https://react.dev/reference/react/useDeferredValue)
- [React 19 Docs - useTransition](https://react.dev/reference/react/useTransition)
- [Concurrent Features Overview](https://react.dev/blog/2022/03/29/react-v18#concurrent-features)
