# NATIVE NICE TO HAVE INTEGRATIONS

## Face ID

### Android

### iOS

## Translation API
https://github.com/huextrat/react-native-ios-translate-sheet
https://github.com/huextrat/react-native-ios-translate-tasks
Android -> https://github.com/JasonCareter/fast-mlkit-translate-text

## Speech to Text

## Text to Speech

## Voice Recognition

## Gemini NANO & Apple Inteligence

Excelente! Encontrei várias bibliotecas que facilitam as integrações on-device. Vou organizar por categoria:

## **🍎 Apple Intelligence (iOS 18+)**

**react-native-image-playground** (Mai<PERSON> recente)
- **GitHub:** `react-native-image-playground`
- **Funcionalidade:** Integra Image Playground do Apple Intelligence 
- **Suporte:** iOS 18.2+ com Apple Intelligence
- **Instalação:** `npm install react-native-image-playground`

```bash
npm install react-native-image-playground
```

## **🤖 Gemini Nano + ML Kit (Android)**

**react-native-mlkit** (Wrapper completo)
- **GitHub:** `a7medev/react-native-ml-kit`
- **Funcionalidades:** Text recognition, face detection, image labeling
- **Suporte:** Android on-device

```bash
npm install @react-native-ml-kit/text-recognition
npm install @react-native-ml-kit/face-detection
npm install @react-native-ml-kit/image-labeling
```

**Infinite Red MLKit**
- **Docs:** docs.infinite.red/react-native-mlkit
- **Expo/React Native:** Suporte completo
- **On-device:** 100% on-device processing

**Outras opções ML Kit:**
- `react-native-mlkit-ocr` - OCR específico
- `@react-native-firebase/ml` - Via Firebase (deprecated para novos projetos)

## **🚀 LLMs On-Device Universais**

**react-native-ai** (Callstack - Recomendado)
- **GitHub:** `callstackincubator/ai`
- **Engine:** MLC LLM + Vercel AI SDK
- **Compatibilidade:** iOS & Android
- **Modelos:** Llama, Phi, Qwen, etc.

```bash
npm install react-native-ai
```

**Configuração:**
```json
// mlc-config.json
{
  "iphone": [
    {
      "model": "Llama-2-7b-chat-hf-q4f16_1",
      "model_id": "Llama-2-7b-chat-hf-q4f16_1"
    }
  ],
  "android": [
    {
      "model": "Llama-2-7b-chat-hf-q4f16_1", 
      "model_id": "Llama-2-7b-chat-hf-q4f16_1"
    }
  ]
}
```

**llama.rn** (Binding direto)
- **GitHub:** `mybigday/llama.rn`
- **Engine:** llama.cpp binding
- **Modelos:** GGUF files (Hugging Face)
- **Multimodal:** Vision + Audio support

```bash
npm install llama.rn
```

**Uso básico:**
```javascript
import { initLlama } from 'llama.rn';

const context = await initLlama({
  model: 'file://path/to/model.gguf',
  n_ctx: 2048,
  n_gpu_layers: 1 // Metal no iOS
});

const response = await context.completion({
  messages: [
    { role: 'user', content: 'Hello!' }
  ]
});
```

## **🎯 Engine Base: MLC LLM**

**MLC LLM** (Engine Universal)
- **GitHub:** `mlc-ai/mlc-llm`
- **Missão:** Deploy de LLMs em qualquer plataforma
- **Suporte:** iOS, Android, Web, Desktop
- **API:** OpenAI-compatible

## **📋 Resumo das Melhores Opções**

| Funcionalidade | Biblioteca Recomendada | Plataforma |
|---|---|---|
| **Apple Intelligence** | `react-native-image-playground` | iOS 18.2+ |
| **ML Kit/Gemini Nano** | `@react-native-ml-kit/*` | Android |
| **LLMs Universais** | `react-native-ai` | iOS + Android |
| **LLMs Diretos** | `llama.rn` | iOS + Android |
| **OCR Específico** | `react-native-mlkit-ocr` | iOS + Android |

## **🛠️ Setup Recomendado**

**1. Para começar rapidamente:**
```bash
# ML Kit para Android
npm install @react-native-ml-kit/text-recognition

# Apple Intelligence para iOS
npm install react-native-image-playground  

# LLMs on-device universais
npm install react-native-ai
```

**2. Para máximo controle:**
```bash
# Engine direto para LLMs
npm install llama.rn

# ML Kit completo
npm install @react-native-ml-kit/text-recognition
npm install @react-native-ml-kit/face-detection
npm install @react-native-ml-kit/image-labeling
```

## **⚡ Implementação Unificada**

```typescript
// services/OnDeviceAI.ts
import { Platform } from 'react-native';
import { getModels, streamText } from 'react-native-ai';
import TextRecognition from '@react-native-ml-kit/text-recognition';

class OnDeviceAIService {
  async generateText(prompt: string) {
    // LLM on-device
    const models = await getModels();
    return await streamText({
      model: models[0],
      prompt
    });
  }
  
  async recognizeText(imageUri: string) {
    // ML Kit OCR
    return await TextRecognition.recognize(imageUri);
  }
  
  async useAppleIntelligence(imageParams: any) {
    if (Platform.OS === 'ios') {
      // Usar react-native-image-playground
      const { ImagePlayground } = require('react-native-image-playground');
      return await ImagePlayground.generate(imageParams);
    }
    throw new Error('Apple Intelligence apenas no iOS');
  }
}
```

As bibliotecas estão bem maduras! O `react-native-ai` da Callstack é especialmente interessante por combinar MLC LLM com Vercel AI SDK. 

Qual funcionalidade específica você quer implementar primeiro no Movuca?