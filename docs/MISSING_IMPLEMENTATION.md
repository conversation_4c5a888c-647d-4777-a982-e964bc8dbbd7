# Movuca App - Missing Implementation Checklist

## 📋 Overview

This document tracks what's been implemented and what's still missing in the Movuca mobile app. The app focuses exclusively on nightlife venues (bars, clubs, lounges) and party discovery.

## ✅ What's Been Implemented

### Core Infrastructure
- ✅ Foursquare integration with nightlife filtering
- ✅ React Native 0.79 with React 19 setup
- ✅ Theme system with Shopify Restyle
- ✅ Authentication flows
- ✅ State management (Zustand + TanStack Query)
- ✅ Navigation structure

### Discovery Feature (~60% Complete)
- ✅ `discovery.types.ts` - Type definitions
- ✅ `discoveryStore.ts` - State management
- ✅ `DiscoveryCard.tsx` - Card component
- ✅ `RadarMap.tsx` - Map component
- ✅ `useNearbySearch.ts` - Search hook
- ✅ `RadarScreen.tsx` - Main screen
- ✅ `SearchBar.tsx` - Global search with autocomplete ✨ NEW
- ✅ `FilterSheet.tsx` - Bottom sheet filters ✨ NEW
- ✅ `CategoryPills.tsx` - Quick category filters ✨ NEW
- ✅ `useSearch.ts` - Search logic with React 19 ✨ NEW
- ✅ `useRadar.ts` - Map functionality ✨ NEW
- ✅ `useFilters.ts` - Filter persistence ✨ NEW
- ✅ `useRecommendations.ts` - AI suggestions ✨ NEW
- ✅ `SearchScreen.tsx` - Full search page ✨ NEW
- ✅ `FiltersScreen.tsx` - Advanced filters ✨ NEW
- ✅ `useLocation.ts` - Location management ✨ NEW

## ❌ What's Missing

### 1. Discovery Feature Completion (HIGH PRIORITY)

#### Components (`src/features/discovery/components/`)
```typescript
// SearchBar.tsx - Global search with autocomplete
interface SearchBarProps {
  value: string;
  onChangeText: (text: string) => void;
  onFocus?: () => void;
  placeholder?: string;
  showFilters?: boolean;
  onFilterPress?: () => void;
}

// FilterSheet.tsx - Bottom sheet with filters
interface FilterSheetProps {
  visible: boolean;
  onClose: () => void;
  filters: FilterState;
  onApply: (filters: FilterState) => void;
}

// CategoryPills.tsx - Quick category filters
interface CategoryPillsProps {
  categories: Category[];
  selected: string[];
  onSelect: (categoryId: string) => void;
  scrollable?: boolean;
}
```

#### Hooks (`src/features/discovery/hooks/`)
```typescript
// useRadar.ts - Radar map functionality
export const useRadar = () => {
  // Map interactions
  // Marker clustering
  // Location tracking
  // Region changes
};

// useFilters.ts - Filter state management
export const useFilters = () => {
  // Filter persistence
  // Quick filters
  // Advanced filters
  // Reset functionality
};

// useRecommendations.ts - AI recommendations
export const useRecommendations = (preferences: UserPreferences) => {
  // Personalized suggestions
  // Trending venues
  // Similar places
  // Time-based recommendations
};
```

#### Screens (`src/features/discovery/screens/`)
```typescript
// SearchScreen.tsx - Full search experience
- Search bar with results
- Recent searches
- Popular searches
- Category browsing

// FiltersScreen.tsx - Advanced filtering
- Distance radius
- Price range
- Rating minimum
- Open now
- Features (live music, outdoor, etc.)
- Party time filters (happy hour, late night)
```

### 2. Events Feature (MEDIUM PRIORITY)

Complete feature structure for user-created events:

```
src/features/events/
├── components/
│   ├── EventCard.tsx          # Preview card
│   ├── EventDetails.tsx       # Full details view
│   ├── AttendeesList.tsx      # Who's going
│   ├── EventActions.tsx       # RSVP, share, save
│   ├── EventComments.tsx      # Discussion
│   └── CreateEventForm.tsx    # Multi-step form
├── hooks/
│   ├── useEvents.ts           # List queries
│   ├── useEventDetails.ts     # Single event
│   ├── useEventActions.ts     # Mutations
│   └── useEventCreation.ts    # Create/edit
├── screens/
│   ├── EventsListScreen.tsx   # Browse events
│   ├── EventDetailsScreen.tsx # Single event
│   ├── CreateEventScreen.tsx  # Create flow
│   └── MyEventsScreen.tsx     # User's events
├── services/
│   └── eventService.ts        # API calls
└── types/
    └── event.types.ts         # Type definitions
```

Key Features:
- Private event registration (from previous implementation)
- RSVP management
- Guest lists
- Event updates
- Photo uploads
- Comments/discussion

### 3. Venues Feature (MEDIUM PRIORITY)

Integration with Foursquare + user features:

```
src/features/venues/
├── components/
│   ├── VenueCard.tsx          # Preview with key info
│   ├── VenueHeader.tsx        # Hero, name, rating
│   ├── VenueInfo.tsx          # Hours, contact, features
│   ├── VenuePhotos.tsx        # Gallery from Foursquare
│   ├── VenueTips.tsx          # User reviews
│   ├── CheckInButton.tsx      # Check-in action
│   └── VenueFeatures.tsx      # Party features icons
├── hooks/
│   ├── useVenues.ts           # List queries
│   ├── useVenueDetails.ts     # Full details
│   ├── useCheckIn.ts          # Check-in logic
│   └── useFoursquareVenue.ts  # Direct integration
├── screens/
│   ├── VenuesListScreen.tsx   # Browse venues
│   ├── VenueDetailsScreen.tsx # Single venue
│   ├── VenueMapScreen.tsx     # Map view
│   └── PopularScreen.tsx      # Trending venues
├── services/
│   └── venueService.ts        # API + Foursquare
└── types/
    └── venue.types.ts         # Type definitions
```

Key Features:
- Real-time Foursquare data
- Party-specific filters (serves alcohol, live music, late night)
- Check-ins and badges
- User tips and photos
- Popular times
- Happy hour indicators

### 4. Tickets Feature (LOW PRIORITY)

Professional ticketed events:

```
src/features/tickets/
├── components/
│   ├── TicketCard.tsx         # Event preview
│   ├── TicketPurchase.tsx     # Buy flow
│   ├── TicketQRCode.tsx       # Digital ticket
│   ├── SeatPicker.tsx         # Venue layout
│   ├── PaymentForm.tsx        # Secure payment
│   └── AffiliateCode.tsx      # Promo codes
├── hooks/
│   ├── useTicketedEvents.ts   # List queries
│   ├── useTicketPurchase.ts   # Purchase flow
│   ├── useMyTickets.ts        # User's tickets
│   └── useAffiliates.ts       # Affiliate codes
├── screens/
│   ├── TicketedEventsScreen.tsx  # Browse
│   ├── TicketDetailsScreen.tsx    # Event info
│   ├── PurchaseScreen.tsx         # Buy tickets
│   ├── MyTicketsScreen.tsx        # Purchased
│   └── AffiliateScreen.tsx        # Find codes
├── services/
│   └── ticketService.ts       # API integration
└── types/
    └── ticket.types.ts        # Type definitions
```

Key Features:
- Affiliate system (from Figma designs)
- QR code tickets
- Transfer mechanism
- Refund handling
- Early bird pricing

### 5. Shared Components (AS NEEDED)

```
src/features/shared/components/
├── MapView.tsx           # Reusable map wrapper
├── ContentCard.tsx       # Base card component
├── ShareSheet.tsx        # Share functionality
├── SaveButton.tsx        # Bookmark action
├── PriceTag.tsx          # Price display
├── TimeInfo.tsx          # Event/venue times
├── DistanceBadge.tsx     # Distance display
└── RatingStars.tsx       # Rating component
```

### 6. Services Layer

```
src/features/shared/services/
├── locationService.ts    # Location utilities
├── notificationService.ts # Push notifications
├── analyticsService.ts   # Event tracking
└── shareService.ts       # Deep links
```

## 🎯 Implementation Priority

### Phase 1: Complete Discovery (1-2 weeks)
1. SearchBar component with Foursquare autocomplete
2. FilterSheet with party-specific filters
3. CategoryPills for quick filtering
4. Search and Filter screens
5. Missing hooks (useRadar, useFilters, useRecommendations)

### Phase 2: Events Feature (2-3 weeks)
1. Event data models and services
2. Event creation flow (reuse private registration work)
3. Event listing and details
4. RSVP and attendance management
5. Comments and updates

### Phase 3: Venues Feature (2 weeks)
1. Venue service with Foursquare integration
2. Venue details with party features
3. Check-in system
4. User reviews and tips
5. Popular times and live updates

### Phase 4: Tickets Feature (2-3 weeks)
1. Ticketed events data model
2. Purchase flow with payment
3. QR code generation
4. Affiliate system (from Figma)
5. My tickets management

### Phase 5: Polish & Launch (1 week)
1. Performance optimization
2. Error handling
3. Analytics integration
4. Deep linking
5. Push notifications

## 🔧 Technical Considerations

### API Integration
- Foursquare v3 category IDs need updating
- Backend API endpoints need implementation
- Payment gateway integration for tickets
- Real-time updates via WebSocket

### Performance
- Implement map clustering for many markers
- Lazy load images from Foursquare
- Cache venue data locally
- Optimize list rendering with FlashList

### Testing
- Unit tests for all hooks
- Integration tests for services
- E2E tests for critical flows
- Performance testing on devices

## 📱 Navigation Updates

```typescript
// Update navigation structure
const MainTabs = () => (
  <Tab.Navigator>
    <Tab.Screen name="Radar" component={DiscoveryStack} />
    <Tab.Screen name="Events" component={EventsStack} />
    <Tab.Screen name="Create" component={CreateModal} />
    <Tab.Screen name="Venues" component={VenuesStack} />
    <Tab.Screen name="Profile" component={ProfileStack} />
  </Tab.Navigator>
);

// Each feature needs its stack
const EventsStack = () => (
  <Stack.Navigator>
    <Stack.Screen name="EventsList" component={EventsListScreen} />
    <Stack.Screen name="EventDetails" component={EventDetailsScreen} />
    <Stack.Screen name="CreateEvent" component={CreateEventScreen} />
  </Stack.Navigator>
);
```

## 🎨 UI Components Needed

### From Figma Design System
- Event cards with attendance avatars
- Venue cards with live indicators
- Ticket cards with pricing
- Filter pills with counts
- Map markers by type
- Feature badges (🍺 Drinks, 🎵 Live Music)
- Time-based indicators (Happy Hour, Late Night)

## 📊 State Management

### Additional Zustand Stores
```typescript
// eventStore.ts
interface EventStore {
  events: Event[];
  myEvents: Event[];
  filters: EventFilters;
  // ...
}

// venueStore.ts  
interface VenueStore {
  venues: Venue[];
  checkedInVenues: string[];
  savedVenues: string[];
  // ...
}

// ticketStore.ts
interface TicketStore {
  upcomingTickets: Ticket[];
  pastTickets: Ticket[];
  affiliateCodes: AffiliateCode[];
  // ...
}
```

## 🚀 Next Steps

1. **Immediate**: Complete Discovery feature components
2. **This Week**: Set up Events feature structure
3. **Next Week**: Implement core Events functionality
4. **Following**: Venues with full Foursquare integration
5. **Final**: Tickets with affiliate system

Remember: Focus on nightlife venues only - bars, clubs, lounges, breweries. No museums, aquariums, or family venues!

## 📦 Required Dependencies

The following packages need to be installed for the new Discovery features:

```bash
bun add @gorhom/bottom-sheet @react-native-community/slider react-native-maps
```

Note: React Native Maps requires additional native setup for iOS and Android. Follow the [official installation guide](https://github.com/react-native-maps/react-native-maps/blob/master/docs/installation.md).
