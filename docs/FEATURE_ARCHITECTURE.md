# Movuca App Feature Architecture

## Overview

Movuca is a location-based discovery app for finding events, venues, and parties nearby. The architecture is designed for easy discovery, interactive experiences, and valuable location-based information.

## Core Features

### 1. Discovery (Main Hub)
- **Radar View**: Real-time map showing everything happening nearby
- **Search**: Find specific events, venues, or parties
- **Filters**: Category, distance, time, price, ratings
- **Recommendations**: AI-powered suggestions based on preferences

### 2. Events
- **User-created events**: Birthday parties, meetups, gatherings
- **Event listing**: Browse all events with filters
- **Event details**: Full info, attendees, comments, photos
- **Event management**: Edit, cancel, invite friends

### 3. Venues
- **Foursquare integration**: Real venue data
- **Venue profiles**: Hours, photos, ratings, features
- **Check-ins**: Track visits, earn badges
- **Reviews & Tips**: User-generated content

### 4. Ticketed Events
- **Professional events**: Concerts, shows, festivals
- **Ticket purchase**: Secure payment flow
- **QR codes**: Digital ticket management
- **Transfer & Refunds**: Flexible ticket handling

## Feature Structure

```
src/features/
├── discovery/
│   ├── components/
│   │   ├── RadarMap.tsx           # Interactive map with markers
│   │   ├── SearchBar.tsx          # Global search with filters
│   │   ├── FilterSheet.tsx        # Bottom sheet with filter options
│   │   ├── CategoryPills.tsx      # Quick category filters
│   │   └── DiscoveryCard.tsx      # Unified card for all content types
│   ├── hooks/
│   │   ├── useRadar.ts           # Radar functionality
│   │   ├── useNearbySearch.ts    # Location-based search
│   │   ├── useFilters.ts         # Filter state management
│   │   └── useRecommendations.ts # AI recommendations
│   ├── screens/
│   │   ├── RadarScreen.tsx       # Main discovery screen
│   │   ├── SearchScreen.tsx      # Search with results
│   │   └── FiltersScreen.tsx     # Advanced filters
│   └── types/
│       └── discovery.types.ts
│
├── events/
│   ├── components/
│   │   ├── EventCard.tsx         # Event preview card
│   │   ├── EventDetails.tsx      # Full event info
│   │   ├── AttendeesList.tsx     # Who's going
│   │   ├── EventActions.tsx      # RSVP, share, save
│   │   └── EventComments.tsx     # Discussion thread
│   ├── hooks/
│   │   ├── useEvents.ts          # Event queries
│   │   ├── useEventDetails.ts    # Single event data
│   │   ├── useEventActions.ts    # RSVP, like, share
│   │   └── useEventCreation.ts   # Create/edit events
│   ├── screens/
│   │   ├── EventsListScreen.tsx  # Browse all events
│   │   ├── EventDetailsScreen.tsx # Single event view
│   │   ├── CreateEventScreen.tsx  # Multi-step creation
│   │   └── MyEventsScreen.tsx    # User's events
│   └── services/
│       └── eventService.ts
│
├── venues/
│   ├── components/
│   │   ├── VenueCard.tsx         # Venue preview
│   │   ├── VenueHeader.tsx       # Hero image, ratings
│   │   ├── VenueInfo.tsx         # Hours, contact, features
│   │   ├── VenuePhotos.tsx       # Photo gallery
│   │   ├── VenueTips.tsx         # User tips & reviews
│   │   └── CheckInButton.tsx     # Check-in action
│   ├── hooks/
│   │   ├── useVenues.ts          # Venue queries
│   │   ├── useVenueDetails.ts    # Single venue data
│   │   ├── useCheckIn.ts         # Check-in functionality
│   │   └── useFoursquareVenue.ts # Foursquare integration
│   ├── screens/
│   │   ├── VenuesListScreen.tsx  # Browse venues
│   │   ├── VenueDetailsScreen.tsx # Single venue view
│   │   ├── VenueMapScreen.tsx    # Venues on map
│   │   └── AddVenueScreen.tsx    # Submit new venue
│   └── services/
│       └── venueService.ts
│
├── tickets/
│   ├── components/
│   │   ├── TicketCard.tsx        # Ticket event preview
│   │   ├── TicketPurchase.tsx    # Buy ticket flow
│   │   ├── TicketQRCode.tsx      # Digital ticket
│   │   ├── SeatPicker.tsx        # Venue seating
│   │   └── PaymentForm.tsx       # Secure payment
│   ├── hooks/
│   │   ├── useTicketedEvents.ts  # Ticketed event queries
│   │   ├── useTicketPurchase.ts  # Purchase flow
│   │   ├── useMyTickets.ts       # User's tickets
│   │   └── useTicketTransfer.ts  # Transfer tickets
│   ├── screens/
│   │   ├── TicketedEventsScreen.tsx # Browse ticketed events
│   │   ├── TicketDetailsScreen.tsx   # Event with ticket info
│   │   ├── PurchaseScreen.tsx        # Buy tickets
│   │   ├── MyTicketsScreen.tsx       # Purchased tickets
│   │   └── TicketViewScreen.tsx      # Single ticket view
│   └── services/
│       └── ticketService.ts
│
└── shared/
    ├── components/
    │   ├── LocationPicker.tsx    # Foursquare location search
    │   ├── MapView.tsx           # Reusable map component
    │   ├── ContentCard.tsx       # Base card component
    │   ├── ShareSheet.tsx        # Share functionality
    │   ├── SaveButton.tsx        # Save/bookmark
    │   └── PriceTag.tsx          # Price display
    ├── hooks/
    │   ├── useLocation.ts        # User location
    │   ├── useMap.ts            # Map interactions
    │   ├── useShare.ts          # Sharing functionality
    │   └── useSaved.ts          # Saved items
    └── types/
        └── shared.types.ts
```

## Key Integrations

### 1. Foursquare Places API
- Venue data and photos
- Location search and autocomplete
- Categories and features
- Real-time availability

### 2. Location Services
- User's current location
- Geofencing for notifications
- Distance calculations
- Route planning

### 3. Real-time Updates
- Live attendance counts
- Event status changes
- New comments/activity
- Price updates

## User Flows

### Discovery Flow
1. Open app → See radar with nearby items
2. Filter by category or search
3. Tap marker/card for details
4. Take action (RSVP, buy ticket, check-in)

### Event Creation Flow
1. Tap create button
2. Choose type (event/ticketed)
3. Fill details with location picker
4. Set privacy and capacity
5. Publish and share

### Venue Interaction Flow
1. Discover venue (search/radar/list)
2. View details and photos
3. Check-in when arriving
4. Leave tips for others

## Technical Patterns

### State Management
```typescript
// Zustand store for discovery filters
interface DiscoveryStore {
  filters: FilterState;
  searchQuery: string;
  selectedCategories: string[];
  mapRegion: Region;
  updateFilters: (filters: Partial<FilterState>) => void;
  clearFilters: () => void;
}

// TanStack Query for data fetching
const useNearbyContent = (location: Location, filters: FilterState) => {
  return useQuery({
    queryKey: ['nearby', location, filters],
    queryFn: () => discoveryService.getNearby(location, filters),
    staleTime: 30 * 1000, // 30 seconds
  });
};
```

### React 19 Patterns
```typescript
// Deferred search updates
const SearchBar = () => {
  const [query, setQuery] = useState('');
  const deferredQuery = useDeferredValue(query);
  
  const { data } = useSearch(deferredQuery);
  
  return <TextInput value={query} onChangeText={setQuery} />;
};
```

### Location Integration
```typescript
// Foursquare venue picker
const LocationPicker = ({ onSelect }) => {
  const { suggestions, search } = useFoursquareLocationSearch();
  
  return (
    <View>
      <SearchBar onChangeText={search} />
      <FlatList
        data={suggestions}
        renderItem={({ item }) => (
          <VenueOption venue={item} onPress={() => onSelect(item)} />
        )}
      />
    </View>
  );
};
```

## Navigation Structure

```typescript
// Bottom Tabs
<Tab.Navigator>
  <Tab.Screen name="Radar" component={RadarStack} />
  <Tab.Screen name="Search" component={SearchStack} />
  <Tab.Screen name="Create" component={CreateModal} />
  <Tab.Screen name="Tickets" component={TicketsStack} />
  <Tab.Screen name="Profile" component={ProfileStack} />
</Tab.Navigator>

// Each tab has its own stack
const RadarStack = () => (
  <Stack.Navigator>
    <Stack.Screen name="RadarHome" component={RadarScreen} />
    <Stack.Screen name="EventDetails" component={EventDetailsScreen} />
    <Stack.Screen name="VenueDetails" component={VenueDetailsScreen} />
    <Stack.Screen name="Filters" component={FiltersScreen} />
  </Stack.Navigator>
);
```

## Performance Optimizations

1. **Lazy Loading**: Load venue photos only when needed
2. **Map Clustering**: Group nearby markers at zoom levels
3. **Infinite Scroll**: Paginate lists with React Query
4. **Image Caching**: Cache Foursquare photos locally
5. **Deferred Updates**: Use React 19 for search/filters

## Accessibility

- Screen reader support for all interactive elements
- High contrast mode support
- Minimum touch targets of 44x44
- Keyboard navigation for forms
- Alt text for all images

## Future Enhancements

1. **Social Features**
   - Friend activity feed
   - Group planning
   - Social check-ins

2. **Gamification**
   - Badges for activities
   - Leaderboards
   - Rewards program

3. **AI Features**
   - Personalized recommendations
   - Smart notifications
   - Predictive search

4. **Business Tools**
   - Venue analytics
   - Promotion tools
   - Event insights
