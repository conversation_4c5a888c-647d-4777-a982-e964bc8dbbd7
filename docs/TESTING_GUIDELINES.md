Here's a structured documentation for testing React Native components using React Native Testing Library (RNTL) with your tech stack:

---

# React Native Testing Guide
**Tech Stack**: React Native 0.79, React 19, Expo, Reanimated

## 1. Core Testing Patterns

### Basic Component Test
```javascript
test('renders text content', () => {
  render(<Text>Hello World</Text>);
  expect(screen.getByText('Hello World')).toBeTruthy();
});
```

### Interaction Testing
```javascript
test('button press triggers action', () => {
  const mockFn = jest.fn();
  render(<Button title="Press me" onPress={mockFn} />);
  
  fireEvent.press(screen.getByText('Press me'));
  expect(mockFn).toHaveBeenCalledTimes(1);
});
```

## 2. State Management Testing
```javascript
test('state updates correctly', () => {
  render(<Counter />);
  
  fireEvent.press(screen.getByText('Increment'));
  expect(screen.getByText('Count: 1')).toBeTruthy();
});
```

## 3. Asynchronous Operations
```javascript
test('loads async data', async () => {
  const mockFetch = jest.fn().mockResolvedValue('Data loaded');
  render(<AsyncLoader fetchData={mockFetch} />);

  fireEvent.press(screen.getByText('Load Data'));
  expect(screen.getByText('Loading...')).toBeTruthy();

  await waitFor(() => {
    expect(screen.getByText('Data: Data loaded')).toBeTruthy();
    expect(screen.queryByText('Loading...')).toBeNull();
  });
});
```

## 4. Reanimated Animation Testing
```javascript
test('animation updates values', () => {
  jest.useFakeTimers();
  
  render(<FadeInBox />);
  const box = screen.getByTestId('animated-box');
  
  // Initial state
  expect(box.props.style.opacity).toBe(0);
  
  fireEvent.press(screen.getByText('Start Animation'));
  jest.advanceTimersByTime(500);
  
  // After animation
  expect(box.props.style.opacity).toBe(1);
  jest.useRealTimers();
});
```

## 5. Expo Module Mocking
```javascript
// __mocks__/expo-location.js
export default {
  getCurrentPositionAsync: jest.fn().mockResolvedValue({
    coords: { latitude: 51.5074, longitude: -0.1278 }
  })
};

test('gets device location', async () => {
  render(<LocationComponent />);
  
  fireEvent.press(screen.getByText('Get Location'));
  
  await waitFor(() => {
    expect(screen.getByText('Latitude: 51.5074')).toBeTruthy();
  });
});
```

## 6. Advanced Testing Scenarios

### Navigation Testing
```javascript
test('navigates to details screen', () => {
  const mockNavigate = jest.fn();
  render(<ProductCard navigation={{ navigate: mockNavigate }} />);
  
  fireEvent.press(screen.getByText('View Details'));
  expect(mockNavigate).toHaveBeenCalledWith('ProductDetails');
});
```

### Context Providers
```javascript
test('consumes theme context', () => {
  render(
    <ThemeProvider value="dark">
      <ThemedComponent />
    </ThemeProvider>
  );
  
  expect(screen.getByText('Current theme: dark')).toBeTruthy();
});
```

### Form Testing
```javascript
test('form validation', () => {
  render(<LoginForm />);
  
  fireEvent.changeText(screen.getByPlaceholderText('Email'), 'invalid-email');
  fireEvent.press(screen.getByText('Submit'));
  
  expect(screen.getByText('Invalid email format')).toBeTruthy();
});
```

## 7. Best Practices

1. **Test ID Strategy**:
```jsx
<View testID="login-screen">
  <TextInput testID="email-input" />
</View>
```

2. **Accessibility Testing**:
```javascript
expect(screen.getByA11yLabel('Close button')).toBeTruthy();
expect(screen.getByRole('button')).toBeTruthy();
```

3. **Snapshot Testing**:
```javascript
test('matches snapshot', () => {
  const tree = render(<Component />).toJSON();
  expect(tree).toMatchSnapshot();
});
```

4. **Performance Testing**:
```javascript
test('renders within 100ms', () => {
  const start = performance.now();
  render(<ComplexComponent />);
  const duration = performance.now() - start;
  expect(duration).toBeLessThan(100);
});
```

## 8. Testing Reanimated Components

Special configuration for Reanimated:
```javascript
// jest-setup.js
import 'react-native-reanimated/lib/reanimated2/jestUtils';

beforeEach(() => {
  require('react-native-reanimated/lib/reanimated2/jestUtils').setUpTests();
});

// In package.json
"jest": {
  "setupFiles": ["<rootDir>/jest-setup.js"]
}
```

## 9. Continuous Integration

Sample `.github/workflows/test.yml`:
```yaml
name: Tests
on: [push]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - run: npm ci
      - run: npm test -- --coverage
      - uses: codecov/codecov-action@v3
```

---

## Key Considerations for Your Stack

1. **React 19 Compatibility**:
   - Use `renderHook` for custom hooks testing
   - Test new features like Actions and Optimistic UI

2. **Expo Specifics**:
   - Mock Expo modules (Camera, Notifications, etc.)
   - Test in-browser with Expo Go simulator

3. **Reanimated Tips**:
   - Use `withReanimatedTimer` helper
   - Test animation callbacks
   ```javascript
   test('calls onAnimationEnd', () => {
     const onEnd = jest.fn();
     render(<AnimatedComponent onAnimationEnd={onEnd} />);
     
     fireEvent(screen.getByTestId('animated-view'), 'onAnimationEnd');
     expect(onEnd).toHaveBeenCalled();
   });
   ```

4. **Native Modules**:
   - Mock native modules using `jest.mock`
   - Test error handling for native failures

This documentation provides a comprehensive starting point for testing React Native applications with your specific stack. Remember to:
- Test user flows, not implementation details
- Combine unit, integration, and end-to-end tests
- Run tests in CI/CD pipeline
- Measure coverage but focus on meaningful tests