# Design Guidelines for Movuca App

This document outlines the design guidelines and best practices for the Movuca app's UI development using the Restyle theming system.

## Core Principles

1. **Consistency**: Use theme tokens (colors, spacing, typography) consistently across the app
2. **Responsiveness**: Design for different screen sizes using the responsive utilities
3. **Accessibility**: Ensure sufficient contrast, touch targets, and text sizes
4. **Performance**: Optimize UI components for smooth performance

## Theme Usage Guidelines

### Using Theme Components

- Always use themed components (`Box`, `Text`, etc.) instead of React Native primitives (`View`, `Text`, etc.)
- Use `Card` components for content containers instead of creating custom card-like styles
- Use `Row` and `Column` for layouts instead of manually creating flex containers

```tsx
// ✅ Good
<Column spacing="md_16">
  <Text variant="h_24SemiBold_section">Title</Text>
  <Text variant="b_16Regular_input">Content</Text>
</Column>

// ❌ Avoid
<View style={{ flexDirection: 'column', gap: 16 }}>
  <Text style={{ fontSize: 24, fontWeight: 'bold' }}>Title</Text>
  <Text style={{ fontSize: 16 }}>Content</Text>
</View>
```

### Spacing and Layout

- Always use theme spacing values (`xs_8`, `md_16`, etc.) instead of hard-coded numbers
- Use responsive spacing that adapts to screen size
- Maintain a consistent content padding across screens (typically `md_16` or `lg_24`)
- Use the responsive utilities to adapt layouts for different screen sizes

```tsx
// ✅ Good
<Box padding="md_16" marginTop="lg_24">
  <Text variant="h_24SemiBold_section">Title</Text>
</Box>

// ❌ Avoid
<View style={{ padding: 16, marginTop: 24 }}>
  <Text style={{ fontSize: 28, fontWeight: 'bold' }}>Title</Text>
</View>
```

### Typography

- Use text variants consistently across the app:
  - `heading1-6`: For titles and headers
  - `subtitle1-2`: For emphasis and subheadings
  - `body1-2`: For regular text
  - `caption`: For small, secondary text
  - `button`: For button text
  - `overline`: For labels and categories

- Follow semantic usage of typography:
  - Use only one `heading1` per screen (main title)
  - Use `heading2-6` for section headers
  - Use `body1` for primary content text
  - Use `body2` for secondary content
  - Use `caption` for helper text and timestamps

```tsx
// ✅ Good
<Text variant="H_40Bold_title">Section Title</Text>
<Text variant="b_16Regular_input">Main content text that explains the feature.</Text>
<Text variant="l_12SemiBold_button" color="textTertiary">Last updated: 2 days ago</Text>

// ❌ Avoid
<Text style={{ fontSize: 24, fontWeight: 'bold' }}>Section Title</Text>
<Text style={{ fontSize: 16 }}>Main content text that explains the feature.</Text>
<Text style={{ fontSize: 12, color: '#777' }}>Last updated: 2 days ago</Text>
```

### Colors

- Always use theme colors instead of hard-coded hex values
- Follow semantic usage of colors:
  - `primary`, `secondary`: For accent colors, buttons, and highlighting
  - `background`, `card`, `surface`: For containers and screens
  - `text`, `textSecondary`, `textTertiary`: For text and icons
  - `error`, `success`, `warning`: For status and feedback

```tsx
// ✅ Good
<Box backgroundColor="card">
  <Text color="textSecondary">Status: <Text color="success">Completed</Text></Text>
</Box>

// ❌ Avoid
<View style={{ backgroundColor: '#f5f5f5' }}>
  <Text style={{ color: '#666' }}>Status: <Text style={{ color: '#2e7d32' }}>Completed</Text></Text>
</View>
```

### Responsive Design

- Use the `useResponsive` hook to adapt layouts for different screen sizes
- Design for mobile-first, then adapt for larger screens
- Avoid fixed dimensions that don't scale with different screen sizes

```tsx
// ✅ Good
const { isPhone, select } = useResponsive();

<Box 
  flexDirection={isPhone ? 'column' : 'row'}
  width={select({ phone: '100%', tablet: '80%', desktop: '60%' })}
>
  <Card flex={1} />
  <Card flex={1} marginLeft={isPhone ? 0 : 'md_16'} marginTop={isPhone ? 'md_16' : 0} />
</Box>

// ❌ Avoid
<View style={{ flexDirection: 'column', width: '100%' }}>
  <Card style={{ flex: 1 }} />
  <Card style={{ flex: 1, marginTop: 16 }} />
</View>
```

## Component Best Practices

### Cards

- Use the `Card` component for content containers
- Choose the appropriate variant:
  - `defaults`: For basic containers
  - `elevated`: For prominent containers with shadow
  - `outlined`: For containers with a border
- Maintain consistent padding inside cards (typically `md_16` or `lg_24`)

### Buttons

- Use the `Button` component for all clickable actions
- Choose the appropriate variant based on importance:
  - `primary`: For main actions (e.g., "Submit", "Save")
  - `secondary`: For secondary actions (e.g., "Next", "Continue")
  - `outline`: For less important actions (e.g., "See more")
  - `ghost`: For subtle actions (e.g., "Cancel", "Skip")
  - `danger`: For destructive actions (e.g., "Delete", "Remove")
- Choose the appropriate size based on context:
  - `small`: For compact interfaces or secondary actions
  - `medium` (default): For most actions
  - `large`: For prominent actions

### Inputs

- Use the `TextInput` component for all text input fields
- Always include a label for clarity
- Provide helper text for additional context when needed
- Show error messages when validation fails
- Use the appropriate keyboard type for different input types

### Navigation

- Use a consistent header style across screens
- Ensure navigation elements (back buttons, tabs) are easily reachable
- Use theme colors for navigation elements to maintain consistency

## Accessibility Guidelines

- Ensure sufficient color contrast (4.5:1 for normal text, 3:1 for large text)
- Use semantic components and proper labeling
- Make touch targets at least 44×44 points
- Support dynamic text sizes
- Test with screen readers

## Theme Customization

When extending the theme system:

1. Add new values to the theme file first
2. Create re-usable components that use theme values
3. Document the new theme values or components in code comments
4. Follow the existing naming conventions:
   - Colors: Semantic names (e.g., `primary`, `secondary`, `success`)
   - Spacing: Descriptor_value format (e.g., `xs_8`, `md_16`, `lg_24`)
   - Typography: Clear variant names (e.g., `heading1`, `body1`, `caption`)

## Performance Considerations

- Use `React.memo()` for pure components that render frequently
- Use `useMemo` and `useCallback` for expensive calculations and functions
- Avoid deeply nested layouts and excessive use of shadows
- Use `FlatList` or `SectionList` for long lists instead of `ScrollableContainer`
- Use image optimization techniques for faster loading

---

By following these guidelines, we can maintain a consistent, accessible, and performant UI throughout the Movuca app.
