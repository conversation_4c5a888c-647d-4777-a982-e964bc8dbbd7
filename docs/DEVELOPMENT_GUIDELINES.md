# Scalable Component Development Guide

## Table of Contents
1. [Architecture Overview](#architecture-overview)
2. [Component Structure Template](#component-structure-template)
3. [TypeScript Patterns](#typescript-patterns)
4. [Theming & Variants](#theming--variants)
5. [Animation Patterns](#animation-patterns)
6. [React 19 Concurrent Features](#react-19-concurrent-features)
7. [Accessibility Guidelines](#accessibility-guidelines)
8. [Responsive Design](#responsive-design)
9. [Testing Considerations](#testing-considerations)
10. [Documentation Standards](#documentation-standards)

---

## Architecture Overview

### Core Principles
- **Single Responsibility**: Each component should have one clear purpose
- **Composition over Inheritance**: Build complex components by combining simpler ones
- **Prop Drilling Prevention**: Use context for shared state, props for direct communication
- **Type Safety**: Leverage TypeScript for better developer experience and fewer runtime errors

### Technology Stack
- **@shopify/restyle**: For consistent theming and variant systems
- **react-native-reanimated**: For performant animations
- **TypeScript**: For type safety and better developer experience
- **Forward Refs**: For better component composition

---

## Component Structure Template

```tsx
import React, { forwardRef, ComponentProps } from 'react';
import { View, AccessibilityProps } from 'react-native';
import { 
  useRestyle, 
  composeRestyleFunctions, 
  createVariant, 
  VariantProps, 
  BoxProps 
} from '@shopify/restyle';
import Animated, { useSharedValue, useAnimatedStyle } from 'react-native-reanimated';

import { Theme, TokensThemeColors } from '../theme/theme';
import Box from '../theme/Box';
import Text from '../theme/Text';

// 1. Define base restyle component with variants
const BaseComponent = createRestyleComponent<
  VariantProps<Theme, 'componentVariants'> & BoxProps<Theme>,
  Theme
>([createVariant({ themeKey: 'componentVariants' })], Animated.View);

// 2. Define component-specific props
type ComponentProps = {
  // Core functionality props
  title: string;
  onPress?: () => void;
  
  // Visual customization props
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  
  // State props
  loading?: boolean;
  disabled?: boolean;
  
  // Customization props
  variant?: ComponentProps<typeof BaseComponent>['variant'];
  componentProps?: Partial<ComponentProps<typeof BaseComponent>>;
  textProps?: Partial<ComponentProps<typeof Text>>;
} & AccessibilityProps;

// 3. Main component with proper typing
const Component = forwardRef<View, ComponentProps>((props, ref) => {
  const {
    title,
    onPress,
    leftIcon,
    rightIcon,
    loading = false,
    disabled = false,
    variant = 'default',
    componentProps,
    textProps,
    ...accessibilityProps
  } = props;

  // 4. Animation setup
  const scale = useSharedValue(1);
  
  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  // 5. State-dependent variant logic
  const getVariant = () => {
    if (disabled) return 'disabled';
    if (loading) return 'loading';
    return variant;
  };

  // 6. Event handlers
  const handlePress = () => {
    if (!disabled && !loading && onPress) {
      onPress();
    }
  };

  return (
    <BaseComponent
      ref={ref}
      variant={getVariant()}
      style={animatedStyle}
      onPress={handlePress}
      accessibilityRole="button"
      accessibilityState={{
        disabled: disabled || loading,
        busy: loading,
      }}
      {...accessibilityProps}
      {...componentProps}
    >
      {/* Component content */}
      {leftIcon && <Box marginRight="xs_8">{leftIcon}</Box>}
      
      <Text variant="componentText" {...textProps}>
        {title}
      </Text>
      
      {rightIcon && <Box marginLeft="xs_8">{rightIcon}</Box>}
    </BaseComponent>
  );
});

Component.displayName = 'Component';

export default Component;
```

---

## TypeScript Patterns

### 1. Prop Interface Design
```tsx
// ✅ Good: Explicit, extensible, well-documented
type ButtonProps = {
  /** Button text content */
  title: string;
  /** Called when button is pressed */
  onPress?: () => void;
  /** Visual style variant */
  variant?: 'primary' | 'secondary' | 'danger';
  /** Loading state indicator */
  loading?: boolean;
} & AccessibilityProps;

// ❌ Avoid: Unclear, hard to extend
type ButtonProps = {
  text: any;
  click: Function;
  style: any;
};
```

### 2. Conditional Props
```tsx
// For mutually exclusive props
type ConditionalProps = 
  | { mode: 'edit'; onSave: () => void; onCancel?: () => void }
  | { mode: 'view'; onEdit: () => void };

// For dependent props
type DependentProps = {
  dismissible?: boolean;
} & (
  | { dismissible: true; onDismiss: () => void }
  | { dismissible?: false; onDismiss?: never }
);
```

### 3. Generic Components
```tsx
interface ListProps<T> {
  data: T[];
  renderItem: (item: T, index: number) => React.ReactNode;
  keyExtractor: (item: T) => string;
}

function List<T>({ data, renderItem, keyExtractor }: ListProps<T>) {
  // Implementation
}
```

---

## Theming & Variants

### 1. Define Variants in Theme
```tsx
// theme/variants.ts
export const buttonVariants = {
  primary: {
    backgroundColor: 'primary',
    borderRadius: 'medium',
    paddingVertical: 'md_16',
    paddingHorizontal: 'lg_24',
  },
  secondary: {
    backgroundColor: 'secondary',
    borderRadius: 'medium',
    paddingVertical: 'md_16',
    paddingHorizontal: 'lg_24',
  },
  outline: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: 'primary',
    borderRadius: 'medium',
    paddingVertical: 'md_16',
    paddingHorizontal: 'lg_24',
  },
};
```

### 2. Use Variants in Components
```tsx
// Create restyle component with variants
const BaseButton = createRestyleComponent<
  VariantProps<Theme, 'buttonVariants'> & BoxProps<Theme>,
  Theme
>([createVariant({ themeKey: 'buttonVariants' })], Pressable);

// Use in component
<BaseButton variant="primary" {...restProps}>
  {/* Content */}
</BaseButton>
```

### 3. Responsive Variants
```tsx
// Support different variants per screen size
variant={{
  phone: 'small',
  tablet: 'medium',
  desktop: 'large'
}}
```

---

## Animation Patterns

### 1. Consistent Animation Configuration
```tsx
// constants/animation.ts
export const ButtonVariantAnimations = {
  primary: {
    scale: {
      default: 1,
      pressed: 0.95,
    },
    config: {
      damping: 20,
      stiffness: 300,
    },
  },
  secondary: {
    scale: {
      default: 1,
      pressed: 0.98,
    },
    config: {
      damping: 15,
      stiffness: 200,
    },
  },
};
```

### 2. Reusable Animation Hooks
```tsx
// hooks/useButtonAnimation.ts
export const useButtonAnimation = (variant: string) => {
  const scale = useSharedValue(1);
  
  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));
  
  const handlePress = (pressed: boolean) => {
    const animation = ButtonVariantAnimations[variant];
    scale.value = withSpring(
      pressed ? animation.scale.pressed : animation.scale.default,
      animation.config
    );
  };
  
  return { animatedStyle, handlePress };
};
```

### 3. Layout Animations
```tsx
// For entering/exiting animations
const enteringAnimation = new Keyframe({
  0: { opacity: 0, transform: [{ scale: 0.8 }] },
  100: { opacity: 1, transform: [{ scale: 1 }] },
}).duration(200);

const exitingAnimation = new Keyframe({
  0: { opacity: 1, transform: [{ scale: 1 }] },
  100: { opacity: 0, transform: [{ scale: 0.8 }] },
}).duration(150);
```

---

## React 19 Concurrent Features

### 1. Use useDeferredValue Instead of Manual Debouncing
```tsx
// ✅ Good: React 19 concurrent features
import { useState, useDeferredValue, useTransition } from 'react';

const SearchComponent = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isPending, startTransition] = useTransition();
  const deferredQuery = useDeferredValue(searchQuery);
  
  const handleSearch = (text: string) => {
    startTransition(() => {
      setSearchQuery(text);
    });
  };
  
  // Use deferredQuery for API calls
  const { data } = useQuery({
    queryKey: ['search', deferredQuery],
    queryFn: () => searchAPI(deferredQuery),
    enabled: deferredQuery.length > 2,
  });
  
  return (
    <>
      <TextInput
        value={searchQuery}
        onChangeText={handleSearch}
        placeholder="Search..."
      />
      {isPending && <ActivityIndicator />}
    </>
  );
};

// ❌ Avoid: Manual debouncing
import { useDebounce } from 'custom-hook';
const debouncedValue = useDebounce(value, 500);
```

### 2. Combine Loading States
```tsx
// Combine query loading with transition pending state
const isLoading = query.isLoading || isPending;

// Show different UI states
if (isPending) {
  return <Text>Updating...</Text>;
}
if (query.isLoading) {
  return <Text>Loading results...</Text>;
}
```

### 3. Location Search Pattern
```tsx
// See full implementation in docs/REACT_19_CONCURRENT_FEATURES.md
const useLocationSearch = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isPending, startTransition] = useTransition();
  const deferredQuery = useDeferredValue(searchQuery);
  
  const query = useQuery({
    queryKey: ['locations', deferredQuery],
    queryFn: () => foursquare.searchPlaces({ query: deferredQuery }),
    enabled: deferredQuery.length > 2,
  });
  
  return {
    searchQuery,
    deferredQuery,
    locations: query.data?.results || [],
    isLoading: query.isLoading || isPending,
  };
};
```

### 4. Form Auto-save Pattern
```tsx
const useAutoSave = (data: any) => {
  const deferredData = useDeferredValue(data);
  
  useEffect(() => {
    // Save happens during idle time
    saveToStorage(deferredData);
  }, [deferredData]);
};
```

---

## Accessibility Guidelines

### 1. Required Properties
```tsx
// Always include these for interactive components
<Pressable
  accessibilityRole="button"
  accessibilityLabel="Close dialog"
  accessibilityHint="Double tap to dismiss this dialog"
  accessibilityState={{
    disabled: isDisabled,
    selected: isSelected,
    busy: isLoading,
  }}
>
```

### 2. Dynamic Labels
```tsx
// Generate meaningful labels based on state
const getAccessibilityLabel = () => {
  if (loading) return `${title}, loading`;
  if (disabled) return `${title}, disabled`;
  return title;
};

// For dismissible elements
accessibilityLabel={dismissible ? `Remove ${label}` : label}
```

### 3. Focus Management
```tsx
// Proper focus handling for custom inputs
const inputRef = useRef<TextInput>(null);

const focus = () => {
  inputRef.current?.focus();
};

<Pressable onPress={focus} accessibilityRole="none">
  {/* Label or wrapper */}
</Pressable>
```

---

## Responsive Design

### 1. useResponsive Hook Pattern
```tsx
// Custom hook for responsive design
const { isTablet, isDesktop, select } = useResponsive();

// Usage in components
const fontSize = select({
  phone: 14,
  tablet: 16,
  desktop: 18,
});

const columns = select({
  phone: 1,
  tablet: 2,
  desktop: 3,
});
```

### 2. Conditional Rendering
```tsx
// Render different layouts based on screen size
if (gridMode && (isTablet || isDesktop)) {
  return <GridLayout>{children}</GridLayout>;
}

return <ListLayout>{children}</ListLayout>;
```

### 3. Responsive Spacing
```tsx
// Use theme spacing tokens that adapt
<Box 
  padding={select({
    phone: 'sm_12',
    tablet: 'md_16',
    desktop: 'lg_24'
  })}
>
```

---

## Testing Considerations

### 1. Component Test Structure
```tsx
// Component.test.tsx
describe('Component', () => {
  it('renders with required props', () => {
    render(<Component title="Test" />);
    expect(screen.getByText('Test')).toBeDefined();
  });

  it('handles press events', () => {
    const onPress = jest.fn();
    render(<Component title="Test" onPress={onPress} />);
    
    fireEvent.press(screen.getByText('Test'));
    expect(onPress).toHaveBeenCalledTimes(1);
  });

  it('respects disabled state', () => {
    const onPress = jest.fn();
    render(<Component title="Test" onPress={onPress} disabled />);
    
    fireEvent.press(screen.getByText('Test'));
    expect(onPress).not.toHaveBeenCalled();
  });

  it('has proper accessibility attributes', () => {
    render(<Component title="Test" />);
    const component = screen.getByRole('button');
    
    expect(component).toHaveProp('accessibilityRole', 'button');
  });
});
```

### 2. Theme Testing
```tsx
// Test with different theme variants
it('applies correct variant styles', () => {
  const { rerender } = render(<Component variant="primary" />);
  // Assert primary styles
  
  rerender(<Component variant="secondary" />);
  // Assert secondary styles
});
```

---

## Documentation Standards

### 1. Component Documentation Template
```tsx
/**
 * Component Name
 * 
 * Brief description of what the component does and when to use it.
 * 
 * @example
 * ```tsx
 * <Component
 *   title="Example"
 *   onPress={() => console.log('pressed')}
 *   variant="primary"
 * />
 * ```
 */
```

### 2. Props Documentation
```tsx
type ComponentProps = {
  /** 
   * The text content displayed in the component
   * @example "Click me"
   */
  title: string;
  
  /** 
   * Callback fired when component is pressed
   * @example () => navigation.navigate('NextScreen')
   */
  onPress?: () => void;
  
  /**
   * Visual style variant
   * @default 'primary'
   */
  variant?: 'primary' | 'secondary' | 'danger';
};
```

### 3. README Structure
```markdown
# Component Name

## Overview
Brief description and use cases.

## Installation
Any additional dependencies or setup required.

## Usage
Basic usage examples with code snippets.

## Props
Detailed prop documentation with types and examples.

## Variants
Available style variants with visual examples.

## Accessibility
Accessibility features and considerations.

## Examples
Common use cases and advanced examples.

## Troubleshooting
Common issues and solutions.
```

---

## Best Practices Summary

### ✅ Do's
- Use TypeScript for all components
- Follow forward ref pattern for reusable components
- Implement consistent animation patterns
- Include accessibility props on all interactive elements
- Use theme variants for consistent styling
- Write comprehensive tests
- Document props and usage examples
- Use responsive design patterns
- Handle loading and error states
- Follow naming conventions

### ❌ Don'ts
- Don't use `any` types
- Don't hardcode colors or spacing values
- Don't ignore accessibility requirements
- Don't create components without proper error boundaries
- Don't skip prop validation
- Don't forget to handle edge cases
- Don't create overly complex component APIs
- Don't ignore responsive design
- Don't skip testing critical user interactions
- Don't forget to update documentation

---

## Quick Checklist for New Components

- [ ] TypeScript interfaces defined
- [ ] Forward ref implemented
- [ ] Theme variants configured
- [ ] Animations implemented (if needed)
- [ ] Accessibility attributes added
- [ ] Responsive design considered
- [ ] Error states handled
- [ ] Loading states implemented
- [ ] Tests written
- [ ] Documentation updated
- [ ] Examples provided
- [ ] Storybook story created (if applicable)

---

This guide provides a comprehensive foundation for building scalable, maintainable components. Always consider the specific needs of your project and adapt these patterns accordingly.