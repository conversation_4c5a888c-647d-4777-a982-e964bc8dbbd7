import React, { useContext, useEffect, useRef, useState } from 'react';

import { Animated, Button, NativeModules, Text, TextInput, View } from 'react-native';

import { act, fireEvent, render, screen, waitFor } from '@testing-library/react-native';
import Reanimated, { useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated';

jest.mock('react-native-reanimated', () => {
  const actualReanimated = jest.requireActual('react-native-reanimated/mock');

  const mockWithTiming = jest.fn((value, config) => value);

  return {
    ...actualReanimated,
    useSharedValue: (initialValue: any) => ({
      value: initialValue,
    }),
    useAnimatedStyle: (styleFunction: () => any) => {
      return styleFunction();
    },
    withTiming: mockWithTiming,
  };
});

const BasicButton = ({ onPress, title }: { onPress: () => void; title: string }) => {
  return <Button title={title} onPress={onPress} />;
};

describe('BasicButton', () => {
  it('renders with correct title', () => {
    render(<BasicButton title="Click Me" onPress={() => {}} />);
    expect(screen.getByText('Click Me')).toBeTruthy();
  });

  it('calls onPress when clicked', () => {
    const mockOnPress = jest.fn();
    render(<BasicButton title="Click Me" onPress={mockOnPress} />);
    fireEvent.press(screen.getByText('Click Me'));
    expect(mockOnPress).toHaveBeenCalledTimes(1);
  });
});

const Counter = () => {
  const [count, setCount] = useState(0);

  return (
    <View>
      <Text>Count: {count}</Text>
      <Button title="Increment" onPress={() => setCount(count + 1)} />
    </View>
  );
};

describe('Counter', () => {
  it('renders initial count of 0', () => {
    render(<Counter />);
    expect(screen.getByText('Count: 0')).toBeTruthy();
  });

  it('increments count when button is pressed', () => {
    render(<Counter />);
    fireEvent.press(screen.getByText('Increment'));
    expect(screen.getByText('Count: 1')).toBeTruthy();
  });
});

const AsyncLoader = ({ fetchData }: { fetchData: () => Promise<string> }) => {
  const [data, setData] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const handlePress = async () => {
    setLoading(true);
    const result = await fetchData();
    setData(result);
    setLoading(false);
  };

  return (
    <View>
      <Button title="Load Data" onPress={handlePress} disabled={loading} />
      {loading && <Text>Loading...</Text>}
      {data && <Text>Data: {data}</Text>}
    </View>
  );
};

describe('AsyncLoader', () => {
  it('shows loading state and then data after fetch', async () => {
    const mockFetchData = jest.fn().mockResolvedValue('Test Data');
    render(<AsyncLoader fetchData={mockFetchData} />);

    fireEvent.press(screen.getByText('Load Data'));
    expect(screen.getByText('Loading...')).toBeTruthy();

    await waitFor(() => {
      expect(screen.getByText('Data: Test Data')).toBeTruthy();
      expect(screen.queryByText('Loading...')).toBeNull();
    });
  });
});

const LoginForm = () => {
  const [email, setEmail] = useState('');
  const [error, setError] = useState('');

  const validate = () => {
    if (!email.includes('@')) {
      setError('Invalid email');
      return false;
    }
    return true;
  };

  return (
    <View>
      <TextInput testID="email-input" value={email} onChangeText={setEmail} placeholder="Email" />
      {error ? <Text testID="error-text">{error}</Text> : null}
      <Button testID="submit-button" title="Submit" onPress={validate} />
    </View>
  );
};

describe('LoginForm', () => {
  it('shows error for invalid email', () => {
    render(<LoginForm />);

    fireEvent.changeText(screen.getByTestId('email-input'), 'invalid-email');
    fireEvent.press(screen.getByTestId('submit-button'));

    expect(screen.getByTestId('error-text')).toHaveTextContent('Invalid email');
  });

  it('accepts valid email', () => {
    render(<LoginForm />);

    fireEvent.changeText(screen.getByTestId('email-input'), '<EMAIL>');
    fireEvent.press(screen.getByTestId('submit-button'));

    expect(screen.queryByTestId('error-text')).toBeNull();
  });
});

const TimerComponent = () => {
  const [seconds, setSeconds] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setSeconds(s => s + 1);
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  return <Text>Seconds: {seconds}</Text>;
};

describe('TimerComponent', () => {
  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('updates every second', async () => {
    render(<TimerComponent />);

    expect(screen.getByText('Seconds: 0')).toBeTruthy();

    // Use act to wrap timer updates
    await act(async () => {
      jest.advanceTimersByTime(2000);
    });

    expect(screen.getByText('Seconds: 2')).toBeTruthy();
  });
});

const ThemeContext = React.createContext('light');

const ThemedButton = () => {
  const theme = useContext(ThemeContext);
  return <Button title={`Current theme: ${theme}`} />;
};

describe('ThemedButton', () => {
  it('uses default theme', () => {
    render(
      <ThemeContext.Provider value="dark">
        <ThemedButton />
      </ThemeContext.Provider>
    );

    expect(screen.getByText(/Current theme: dark/)).toBeTruthy();
  });
});

/*************  ✨ Windsurf Command ⭐  *************/
/**
 * A component that demonstrates how to use the use hook
 * to get the theme from the ThemeContext.
 *
 * @param {Object} props - Component props
 * @param {Function} props.onPress - Function to call when the button is pressed
 * @param {Function} props.onPressPromise - Function to call with a promise
 * that resolves after 1 second.
 * @returns {ReactElement} A React component
 */
/*******  3e0adf76-261d-4a4c-8a48-8a353ac9ecb8  *******/ const LocationButton = () => {
  const [location, setLocation] = useState<string | null>(null);

  const getLocation = async () => {
    try {
      // Pretend this is a native module
      const loc = await NativeModules.Location.getCurrentLocation();
      setLocation(loc);
    } catch {
      setLocation('Error');
    }
  };

  return (
    <View>
      <Button title="Get Location" onPress={getLocation} />
      {location && <Text>{location}</Text>}
    </View>
  );
};

describe('LocationButton', () => {
  it('handles location fetch', async () => {
    NativeModules.Location = {
      getCurrentLocation: jest.fn().mockResolvedValue('New York'),
    };

    render(<LocationButton />);
    fireEvent.press(screen.getByText('Get Location'));

    await waitFor(() => {
      expect(screen.getByText('New York')).toBeTruthy();
    });
  });
});

const AnimatedBox = () => {
  const opacity = useRef(new Animated.Value(0)).current;

  const fadeIn = () => {
    Animated.timing(opacity, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();
  };

  return (
    <View>
      <Animated.View testID="box" style={{ opacity }} />
      <Button title="Fade In" onPress={fadeIn} />
    </View>
  );
};

describe('AnimatedBox', () => {
  beforeEach(() => {
    jest.useFakeTimers();
    // Mock the Animated API to work with fake timers
    jest.spyOn(Animated, 'timing').mockImplementation((value, config) => {
      return {
        start: (callback: any) => {
          // Simulate the animation by directly setting the value
          value.setValue(config.toValue as number & { x: number; y: number });
          if (callback) callback({ finished: true });
        },
        stop: () => {},
        reset: () => {},
      };
    });
  });

  afterEach(() => {
    jest.useRealTimers();
    jest.restoreAllMocks();
  });

  it('triggers fade in animation', async () => {
    render(<AnimatedBox />);

    const fadeButton = screen.getByText('Fade In');

    // Spy on the animation start
    const animatedTimingSpy = jest.spyOn(Animated, 'timing');

    fireEvent.press(fadeButton);

    // Verify animation was called with correct parameters
    expect(animatedTimingSpy).toHaveBeenCalledWith(
      expect.any(Object), // the animated value
      expect.objectContaining({
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      })
    );
  });

  it('calls animation start when button is pressed', () => {
    render(<AnimatedBox />);

    const startMock = jest.fn();
    jest.spyOn(Animated, 'timing').mockReturnValue({
      start: startMock,
      stop: () => {},
      reset: () => {},
    });

    fireEvent.press(screen.getByText('Fade In'));

    expect(startMock).toHaveBeenCalled();
  });
});

const ReanimatedBox = () => {
  const opacity = useSharedValue(0);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity: opacity.value,
    };
  });

  const fadeIn = () => {
    opacity.value = withTiming(1, { duration: 500 });
  };

  return (
    <View>
      <Animated.View
        testID="reanimated-box"
        style={[{ width: 100, height: 100, backgroundColor: 'blue' }, animatedStyle]}
      />
      <Button title="Fade In" onPress={fadeIn} />
    </View>
  );
};

describe('ReanimatedBox', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the animated box', () => {
    render(<ReanimatedBox />);

    expect(screen.getByTestId('reanimated-box')).toBeTruthy();
    expect(screen.getByText('Fade In')).toBeTruthy();
  });

  it('calls withTiming when fade in is pressed', () => {
    render(<ReanimatedBox />);

    fireEvent.press(screen.getByText('Fade In'));

    expect(withTiming).toHaveBeenCalledWith(1, { duration: 500 });
  });

  it('updates shared value when animation is triggered', () => {
    let sharedValueRef: any;

    // Mock useSharedValue to capture the ref
    jest
      .spyOn(require('react-native-reanimated'), 'useSharedValue')
      .mockImplementation(initialValue => {
        const ref = { value: initialValue };
        sharedValueRef = ref;
        return ref;
      });

    render(<ReanimatedBox />);

    expect(sharedValueRef.value).toBe(0);

    fireEvent.press(screen.getByText('Fade In'));

    // In the mock, withTiming just returns the target value
    expect(sharedValueRef.value).toBe(1);
  });
});

const MyComponent = ({
  onPress,
  onPressPromise,
}: {
  onPress: () => void;
  onPressPromise: (promise: Promise<void>) => void;
}) => {
  const promise = new Promise(resolve => {
    setTimeout(() => {
      resolve(true);
    }, 1000);
  });
  return (
    <View>
      <Button title="Press me" onPress={onPress} />
      <Text>My Component</Text>

      <Button title="Promise" onPress={() => onPressPromise(promise.then(() => {}))} />
    </View>
  );
};

const Child = ({ message }: { message: string }) => {
  return <Text>{message}</Text>;
};

const Parent = () => {
  return (
    <View>
      <Child message="Hello from Parent" />
    </View>
  );
};

describe('Parent', () => {
  it('correctly passes message to Child', () => {
    render(<Parent />);
    expect(screen.getByText('Hello from Parent')).toBeTruthy();
  });
});

describe('MyComponent', () => {
  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('renders correctly', async () => {
    const onPress = jest.fn();
    const onPressPromise = jest.fn(promise => promise);

    render(<MyComponent onPress={onPress} onPressPromise={onPressPromise} />);
    expect(screen.getByText('My Component')).toBeTruthy();

    fireEvent.press(screen.getByText('Press me'));
    expect(onPress).toHaveBeenCalledTimes(1);

    fireEvent.press(screen.getByText('Promise'));
    expect(onPressPromise).toHaveBeenCalledTimes(1);

    await act(async () => {
      jest.advanceTimersByTime(1000);
    });
  });
});
