# CODEOWNERS Guidelines

This document outlines how we use the `.github/CODEOWNERS` file to automate pull request reviews and ensure that the right people are always involved in changes to our codebase.

## What is the CODEOWNERS file?

The `CODEOWNERS` file is a GitHub feature that allows us to define which individuals or teams are responsible for the code in our repository. When a pull request modifies any file that has a designated owner, that owner is automatically requested for review.

## How it Works

The file is located at `.github/CODEOWNERS`. Each line in the file follows a simple pattern:

```
# <comment>
<file-pattern> <@github-username-or-team> [<@optional-owner>...]
```

- The `file-pattern` is a glob pattern that matches files in the repository.
- The `@github-username-or-team` is the GitHub handle for an individual user or a team (e.g., `@movuca/mobile-devs`).

## How to Improve Our CODEOWNERS File

Our `CODEOWNERS` file is designed to be a living document that evolves with our team and our codebase. Here are some ways we can improve it over time:

### 1. Create and Use GitHub Teams

Instead of assigning code to individuals, it's better to create GitHub teams for different areas of the app. This makes the file more resilient to changes in team structure.

**Example:**

```
# Instead of this:
src/features/auth/ @henrique @jane-doe

# Do this:
src/features/auth/ @movuca/auth-feature-team
```

### 2. Increase Granularity

As our features become more complex, we can assign owners to more specific subdirectories. This ensures that reviews are more focused and efficient.

**Example:**

```
# Good
src/features/explore/ @movuca/explore-team

# Better
src/features/explore/components/ @movuca/frontend-team
src/features/explore/hooks/      @movuca/data-team
src/features/explore/services/  @movuca/backend-team
```

### 3. Assign Ownership of Critical Files

We should ensure that our most critical files, such as `package.json`, `eas.json`, and our CI workflows, have designated owners. This prevents accidental breaking changes.

### 4. Regular Reviews

We should periodically review the `CODEOWNERS` file to ensure it's still accurate and reflects our current team structure and codebase.
