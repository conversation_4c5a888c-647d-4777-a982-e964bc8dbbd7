# Theme Constants Reference Guide

This document provides a comprehensive reference for all theme constants and variant names in the Movuca mobile app to prevent typos and ensure consistency.

## 🎨 Colors

### **Primary Colors**
```typescript
primary              // Main brand color
secondary            // Secondary brand color  
background           // Main background
surface              // Surface elements
card                 // Card backgrounds
```

### **Text Colors**
```typescript
text                 // Primary text (use instead of textPrimary)
textSecondary        // Secondary text
textTertiary         // Muted/tertiary text
textInverted         // White text on dark backgrounds
```

### **Status Colors**
```typescript
success              // Green success color
error                // Red error color  
warning              // Yellow warning color
```

### **Extended Colors**
```typescript
surfaceContainer     // Container surfaces
border               // Border color
disabled             // Disabled state color
transparent          // Transparent
```

## 📏 Spacing

### **Spacing Tokens** (use underscore format)
```typescript
none_0: 0           xxxs_2: 2         xxs_4: 4          xs_8: 8
sm_12: 12           md_16: 16          ml_20: 20         lg_24: 24
xl_32: 32           xxl_40: 40        xxxl_48: 48       huge_64: 64
giant_80: 80        massive_128: 128
```

### **Most Common Usage**
```typescript
xs_8                // Small gaps, margins
md_16                // Standard padding
lg_24                // Large spacing, screen padding  
xl_32               // Section spacing
```

### **⚠️ Common Mistakes**
```typescript
// ❌ Wrong
paddingHorizontal="lg_24"    // lg_ doesn't exist
marginBottom="md_16"         // md_ doesn't exist

// ✅ Correct  
paddingHorizontal="lg_24"     // Use l_ for large
marginBottom="md_16"          // Use m_ for medium
```

## 🔤 Typography

### **Heading Variants**
```typescript
h_48Bold_splash       // Hero/splash screens
h_40Bold_title        // Main page titles
h_32Bold_title        // Section titles
h_28Bold_section      // Subsection headers
h_24Bold_section      // Card/component titles
h_20Bold_subsection   // Small headers
h_16SemiBold_button   // Button text
```

### **Body Text Variants**
```typescript
b_18Regular_subtitle  // Subtitles
b_16Regular_input     // Input placeholder/content
b_16SemiBold_button   // Button labels
b_14Regular_body      // Main body text
b_14Regular_content   // Content text
```

### **Label & Caption Variants**
```typescript
l_12SemiBold_button   // Small button text
l_12Regular_label     // Form labels
c_14Regular_caption   // Captions, metadata
c_12Regular_label     // Small labels, hints
```

### **⚠️ Typography Guidelines**
- Always use semantic variants (h_, b_, l_, c_)
- Include size, weight, and context in variant name
- Use `text` color instead of `textPrimary`

## 🧩 Component Variants

### **Button Variants**
```typescript
primary              // Main call-to-action
secondary            // Secondary actions
outline              // Outlined style
ghost                // Minimal/text style
danger               // Destructive actions
success              // Positive actions
```

### **Button Sizes**
```typescript
small                // Compact buttons
medium               // Default size (can omit)
large                // Prominent buttons
```

### **Card Variants**
```typescript
defaults             // Basic card (can omit)
elevated             // Card with shadow
outlined             // Card with border
```

### **Alert Variants**
```typescript
defaults             // Default info style (can omit)
success              // Success message
warning              // Warning message
error                // Error message
```

### **Tag Variants**
```typescript
// Solid variants
defaults, success, error, warning, info

// Outline variants  
outline, outlineSuccess, outlineError, outlineWarning, outlineInfo

// Ghost variants
ghost, ghostSuccess, ghostError, ghostWarning, ghostInfo
```

## 📐 Border Radius

```typescript
none_0: 0           // No radius
xs_4: 4             // Small radius
s_8: 8              // Standard radius
sm_12: 12            // Medium radius
l_16: 16            // Large radius
xl_20: 20           // Extra large radius
xxlg_24: 24          // Extra extra large
xxxl_32: 32         // Maximum radius
circle_9999: 9999   // Perfect circle
```

## 📱 Size References

### **Avatar Sizes**
```typescript
xs: 24px            s: 32px             m: 40px
l: 48px             xl: 56px            2xl: 64px
3xl: 102px
```

### **Icon Sizes**
```typescript
16: small           20: medium          24: large
28: extra large     32: display         40: hero
```

## 🎯 Usage Examples

### **Correct Component Usage**
```typescript
// ✅ Good - using correct constants
<Box 
  backgroundColor="background"
  padding="md_16"
  borderRadius="sm_12"
>
  <Text variant="h_24Bold_section" color="text">
    Section Title
  </Text>
  <Text variant="b_14Regular_body" color="textSecondary">
    Content text here
  </Text>
  <Button variant="primary" size="medium">
    Action
  </Button>
</Box>

// ❌ Bad - incorrect constants
<Box 
  backgroundColor="cardBackground"    // Wrong color name
  padding="md_16"                     // Wrong spacing format
  borderRadius="medium"               // Wrong radius format
>
  <Text variant="h_24Bold" color="text">  // Wrong variant & color
    Section Title
  </Text>
</Box>
```

### **Screen Layout Pattern**
```typescript
<Box flex={1} backgroundColor="background">
  <StatusBar style={theme.name === 'dark' ? 'light' : 'dark'} />
  
  {/* Header */}
  <Box 
    paddingHorizontal="lg_24" 
    paddingBottom="md_16"
    style={{ paddingTop: insets.top + 16 }}
  >
    <Text variant="h_28Bold_section" color="text">
      Screen Title
    </Text>
  </Box>
  
  {/* Content */}
  <ScrollView contentContainerStyle={{ padding: 24 }}>
    <Card variant="outlined" padding="md_16">
      <Text variant="b_16SemiBold_button" color="text">
        Card Title  
      </Text>
    </Card>
  </ScrollView>
</Box>
```

## 🚨 Common Mistakes to Avoid

### **Spacing Mistakes**
```typescript
// ❌ Wrong formats
"lg_24", "md_16", "xlarge_32"

// ✅ Correct formats  
"lg_24", "md_16", "xl_32"
```

### **Color Mistakes**
```typescript
// ❌ Wrong color names
"textPrimary", "primaryText", "mainText"

// ✅ Correct color names
"text", "textSecondary", "textTertiary"
```

### **Typography Mistakes**
```typescript
// ❌ Wrong variant patterns
"heading1", "body", "h24Bold"

// ✅ Correct variant patterns
"h_24Bold_section", "b_14Regular_body", "l_12Regular_label"
```

## 🔍 Quick Reference Lookup

When in doubt, remember these patterns:

1. **Spacing**: `{size}_{pixels}` (xs_8, md_16, lg_24)
2. **Typography**: `{type}_{size}{weight}_{context}` (h_24Bold_section)
3. **Colors**: Simple semantic names (text, background, primary)
4. **Variants**: Descriptive names (primary, outlined, success)

## 📋 Development Checklist

Before committing code, verify:

- [ ] All spacing uses underscore format (md_16, not md_16)
- [ ] Text color uses "text" not "textPrimary" 
- [ ] Typography variants include size, weight, and context
- [ ] Component variants use semantic names
- [ ] Border radius uses underscore format (sm_12, not medium)
- [ ] No hardcoded pixel values or hex colors

This reference ensures consistent theming across the entire Movuca mobile application.