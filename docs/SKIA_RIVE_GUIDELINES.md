# React Native Skia & Rive Development Guidelines

## Table of Contents
1. [Architecture Overview](#architecture-overview)
2. [React Native Skia Guidelines](#react-native-skia-guidelines)
3. [React Native Rive Guidelines](#react-native-rive-guidelines)
4. [Performance Optimization](#performance-optimization)
5. [Integration with Theme System](#integration-with-theme-system)
6. [Testing Strategies](#testing-strategies)
7. [Best Practices](#best-practices)

---

## Architecture Overview

### Three-Tier Animation System

```
┌─────────────────────────────────┐
│     React Native Reanimated     │  ← UI Animations, Gestures
├─────────────────────────────────┤
│     React Native Skia v2.0      │  ← Custom Graphics, Effects
├─────────────────────────────────┤
│     React Native Rive           │  ← Complex Vector Animations
└─────────────────────────────────┘
```

### Decision Matrix

| Use Case | Technology | Performance | Complexity |
|----------|------------|-------------|------------|
| UI Transitions | Reanimated | ⭐⭐⭐⭐ | ⭐⭐ |
| Gesture Animations | Reanimated | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| Custom Graphics | Skia | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| Data Visualizations | Skia | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| Character Animations | Rive | ⭐⭐⭐⭐ | ⭐⭐ |
| Interactive Graphics | Rive | ⭐⭐⭐⭐ | ⭐⭐ |

---

## React Native Skia Guidelines

### Installation & Setup

Skia is already installed in the project:
```json
"@shopify/react-native-skia": "v2.0.0-next.4"
```

### Basic Component Structure

```tsx
import React from 'react';
import { Canvas, Circle, Group, Paint } from '@shopify/react-native-skia';
import { useTheme } from '@/src/core/theme';

interface SkiaComponentProps {
  size: number;
  animated?: boolean;
}

export const SkiaComponent: React.FC<SkiaComponentProps> = ({ 
  size, 
  animated = false 
}) => {
  const theme = useTheme();
  
  return (
    <Canvas style={{ width: size, height: size }}>
      <Group>
        <Circle
          cx={size / 2}
          cy={size / 2}
          r={size / 4}
          color={theme.colors.primary}
        />
      </Group>
    </Canvas>
  );
};
```

### Advanced Skia Patterns

#### 1. Custom Drawings with Paths

```tsx
import React from 'react';
import { 
  Canvas, 
  Path, 
  Skia, 
  useSharedValueEffect,
  useValue 
} from '@shopify/react-native-skia';
import { useSharedValue, withTiming } from 'react-native-reanimated';
import { useTheme } from '@/src/core/theme';

export const AnimatedPath: React.FC<{ progress: number }> = ({ progress }) => {
  const theme = useTheme();
  const animatedProgress = useSharedValue(0);
  const skiaProgress = useValue(0);

  // Sync Reanimated with Skia
  useSharedValueEffect(() => {
    skiaProgress.current = animatedProgress.value;
  }, animatedProgress);

  React.useEffect(() => {
    animatedProgress.value = withTiming(progress, { duration: 1000 });
  }, [progress, animatedProgress]);

  const path = Skia.Path.Make();
  path.moveTo(0, 100);
  path.quadTo(50, 0, 100, 100);
  path.lineTo(200, 100);

  return (
    <Canvas style={{ width: 200, height: 100 }}>
      <Path
        path={path}
        color={theme.colors.primary}
        style="stroke"
        strokeWidth={3}
        start={0}
        end={skiaProgress}
      />
    </Canvas>
  );
};
```

#### 2. Gradients and Effects

```tsx
import React from 'react';
import {
  Canvas,
  Circle,
  LinearGradient,
  vec,
  Blur,
  Shadow,
} from '@shopify/react-native-skia';
import { useTheme } from '@/src/core/theme';

export const GradientCircle: React.FC<{ size: number }> = ({ size }) => {
  const theme = useTheme();
  const center = size / 2;
  const radius = size / 3;

  return (
    <Canvas style={{ width: size, height: size }}>
      <Circle cx={center} cy={center} r={radius}>
        <LinearGradient
          start={vec(0, 0)}
          end={vec(size, size)}
          colors={[theme.colors.primary, theme.colors.secondary]}
        />
        <Blur blur={2} />
        <Shadow dx={2} dy={2} blur={4} color="rgba(0,0,0,0.3)" />
      </Circle>
    </Canvas>
  );
};
```

#### 3. Data Visualization

```tsx
import React from 'react';
import {
  Canvas,
  Path,
  Skia,
  Text,
  useFont,
} from '@shopify/react-native-skia';
import { useTheme } from '@/src/core/theme';

interface DataPoint {
  x: number;
  y: number;
  label: string;
}

interface ChartProps {
  data: DataPoint[];
  width: number;
  height: number;
}

export const SkiaChart: React.FC<ChartProps> = ({ data, width, height }) => {
  const theme = useTheme();
  const font = useFont(require('@/assets/fonts/Urbanist-Regular.ttf'), 12);

  const createPath = (points: DataPoint[]) => {
    const path = Skia.Path.Make();
    if (points.length === 0) return path;

    const scaleX = width / (points.length - 1);
    const maxY = Math.max(...points.map(p => p.y));
    const scaleY = height / maxY;

    path.moveTo(0, height - points[0].y * scaleY);
    
    points.forEach((point, index) => {
      if (index > 0) {
        path.lineTo(index * scaleX, height - point.y * scaleY);
      }
    });

    return path;
  };

  const chartPath = createPath(data);

  if (!font) {
    return null;
  }

  return (
    <Canvas style={{ width, height }}>
      {/* Chart line */}
      <Path
        path={chartPath}
        color={theme.colors.primary}
        style="stroke"
        strokeWidth={2}
      />
      
      {/* Data points */}
      {data.map((point, index) => {
        const x = (index * width) / (data.length - 1);
        const y = height - (point.y * height) / Math.max(...data.map(p => p.y));
        
        return (
          <React.Fragment key={index}>
            <Circle
              cx={x}
              cy={y}
              r={4}
              color={theme.colors.primary}
            />
            <Text
              x={x - 10}
              y={y - 10}
              text={point.label}
              font={font}
              color={theme.colors.textSecondary}
            />
          </React.Fragment>
        );
      })}
    </Canvas>
  );
};
```

#### 4. Interactive Skia Components

```tsx
import React from 'react';
import {
  Canvas,
  Circle,
  useTouchHandler,
  useValue,
} from '@shopify/react-native-skia';
import { useTheme } from '@/src/core/theme';

export const InteractiveCircle: React.FC<{ size: number }> = ({ size }) => {
  const theme = useTheme();
  const touchX = useValue(size / 2);
  const touchY = useValue(size / 2);
  const isPressed = useValue(false);

  const touchHandler = useTouchHandler({
    onStart: (event) => {
      touchX.current = event.x;
      touchY.current = event.y;
      isPressed.current = true;
    },
    onActive: (event) => {
      touchX.current = event.x;
      touchY.current = event.y;
    },
    onEnd: () => {
      isPressed.current = false;
    },
  });

  return (
    <Canvas style={{ width: size, height: size }} onTouch={touchHandler}>
      <Circle
        cx={touchX}
        cy={touchY}
        r={isPressed.current ? 30 : 20}
        color={isPressed.current ? theme.colors.primary : theme.colors.secondary}
      />
    </Canvas>
  );
};
```

### Skia Performance Optimization

#### 1. Use Derived Values
```tsx
import { useDerivedValue } from 'react-native-reanimated';
import { useValue } from '@shopify/react-native-skia';

// ✅ Efficient
const skiaValue = useValue(0);
const animatedValue = useSharedValue(0);

useDerivedValue(() => {
  skiaValue.current = animatedValue.value;
});

// ❌ Inefficient - causes re-renders
const [localValue, setLocalValue] = useState(0);
```

#### 2. Minimize Canvas Re-creation
```tsx
// ✅ Stable canvas size
const CANVAS_SIZE = { width: 200, height: 200 };

export const OptimizedSkiaComponent = () => {
  return (
    <Canvas style={CANVAS_SIZE}>
      {/* Content */}
    </Canvas>
  );
};

// ❌ Recreated on every render
export const UnoptimizedComponent = ({ width, height }) => {
  return (
    <Canvas style={{ width, height }}>
      {/* Content */}
    </Canvas>
  );
};
```

---

## React Native Rive Guidelines

### Installation (When Adding)

```bash
bun add react-native-rive
```

For iOS, add to `ios/Podfile`:
```ruby
pod 'RiveRuntime', '~> 5.0'
```

### Basic Rive Component

```tsx
import React from 'react';
import { View } from 'react-native';
import RiveComponent from 'react-native-rive';
import { useTheme } from '@/src/core/theme';

interface RiveAnimationProps {
  animationName: string;
  width: number;
  height: number;
  autoplay?: boolean;
}

export const RiveAnimation: React.FC<RiveAnimationProps> = ({
  animationName,
  width,
  height,
  autoplay = true,
}) => {
  const theme = useTheme();

  return (
    <View style={{ width, height }}>
      <RiveComponent
        style={{ width, height }}
        source={require(`@/assets/animations/${animationName}.riv`)}
        autoplay={autoplay}
        // Theme-aware color overrides
        textRuns={[
          {
            name: 'primary-color',
            text: theme.colors.primary,
          },
        ]}
      />
    </View>
  );
};
```

### Advanced Rive Patterns

#### 1. Interactive Rive with State Machines

```tsx
import React, { useRef, useEffect } from 'react';
import RiveComponent from 'react-native-rive';
import { useTheme } from '@/src/core/theme';

interface InteractiveRiveProps {
  width: number;
  height: number;
  onStateChange?: (state: string) => void;
}

export const InteractiveRive: React.FC<InteractiveRiveProps> = ({
  width,
  height,
  onStateChange,
}) => {
  const theme = useTheme();
  const riveRef = useRef<RiveComponent>(null);

  const handleRiveEvent = (event: any) => {
    console.log('Rive Event:', event);
    onStateChange?.(event.name);
  };

  const triggerInput = (inputName: string, value: any) => {
    riveRef.current?.setInputState(inputName, value);
  };

  useEffect(() => {
    // Set theme-based inputs
    triggerInput('theme', theme.name);
  }, [theme.name]);

  return (
    <RiveComponent
      ref={riveRef}
      style={{ width, height }}
      source={require('@/assets/animations/interactive-character.riv')}
      stateMachineName="Main State Machine"
      onRiveEventReceived={handleRiveEvent}
      textRuns={[
        {
          name: 'primary-color',
          text: theme.colors.primary,
        },
        {
          name: 'background-color', 
          text: theme.colors.background,
        },
      ]}
    />
  );
};
```

#### 2. Rive with React Native Reanimated

```tsx
import React, { useEffect } from 'react';
import RiveComponent from 'react-native-rive';
import { useSharedValue, useAnimatedStyle, withSpring } from 'react-native-reanimated';
import Animated from 'react-native-reanimated';

export const AnimatedRiveContainer: React.FC<{
  isVisible: boolean;
  children: React.ReactNode;
}> = ({ isVisible, children }) => {
  const scale = useSharedValue(0);
  const opacity = useSharedValue(0);

  useEffect(() => {
    scale.value = withSpring(isVisible ? 1 : 0);
    opacity.value = withSpring(isVisible ? 1 : 0);
  }, [isVisible, scale, opacity]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  return (
    <Animated.View style={animatedStyle}>
      {children}
    </Animated.View>
  );
};
```

---

## Performance Optimization

### General Performance Guidelines

#### 1. Canvas Sizing
```tsx
// ✅ Use consistent, predefined sizes
const SIZES = {
  small: { width: 100, height: 100 },
  medium: { width: 200, height: 200 },
  large: { width: 300, height: 300 },
} as const;

// ✅ Avoid dynamic sizing when possible
export const PerformantSkiaComponent = () => {
  return <Canvas style={SIZES.medium}>{/* content */}</Canvas>;
};
```

#### 2. Shared Values Synchronization
```tsx
import { useSharedValueEffect } from '@shopify/react-native-skia';

// ✅ Efficient Reanimated ↔ Skia sync
const reanimatedValue = useSharedValue(0);
const skiaValue = useValue(0);

useSharedValueEffect(() => {
  skiaValue.current = reanimatedValue.value;
}, reanimatedValue);
```

#### 3. Conditional Rendering
```tsx
// ✅ Avoid rendering complex graphics when not visible
export const ConditionalSkiaComponent = ({ isVisible }: { isVisible: boolean }) => {
  if (!isVisible) {
    return null; // Don't render canvas at all
  }

  return (
    <Canvas style={SIZES.medium}>
      {/* Complex graphics */}
    </Canvas>
  );
};
```

### Memory Management

#### 1. Dispose Resources
```tsx
import { useEffect } from 'react';
import { Skia } from '@shopify/react-native-skia';

export const ResourceManagementExample = () => {
  useEffect(() => {
    const image = Skia.Image.MakeImageFromEncoded(/* image data */);
    
    return () => {
      // Clean up resources
      image?.dispose?.();
    };
  }, []);

  return (
    <Canvas style={SIZES.medium}>
      {/* Use image */}
    </Canvas>
  );
};
```

---

## Integration with Theme System

### Theme-Aware Skia Components

```tsx
import React from 'react';
import { Canvas, Circle, LinearGradient, vec } from '@shopify/react-native-skia';
import { useTheme } from '@/src/core/theme';

export const ThemedSkiaComponent: React.FC<{ size: number }> = ({ size }) => {
  const theme = useTheme();
  
  return (
    <Canvas style={{ width: size, height: size }}>
      <Circle cx={size / 2} cy={size / 2} r={size / 4}>
        <LinearGradient
          start={vec(0, 0)}
          end={vec(size, size)}
          colors={[
            theme.colors.primary,
            theme.colors.secondary,
          ]}
        />
      </Circle>
    </Canvas>
  );
};
```

### Responsive Skia Graphics

```tsx
import React from 'react';
import { useResponsive } from '@/src/core/theme';
import { Canvas, Rect } from '@shopify/react-native-skia';

export const ResponsiveSkiaComponent = () => {
  const { select } = useResponsive();
  
  const size = select({
    phone: 100,
    tablet: 150,
    desktop: 200,
  });

  const strokeWidth = select({
    phone: 2,
    tablet: 3,
    desktop: 4,
  });

  return (
    <Canvas style={{ width: size, height: size }}>
      <Rect
        x={10}
        y={10}
        width={size - 20}
        height={size - 20}
        color="transparent"
        style="stroke"
        strokeWidth={strokeWidth}
      />
    </Canvas>
  );
};
```

---

## Testing Strategies

### Skia Testing

```tsx
// __tests__/SkiaComponent.test.tsx
import React from 'react';
import { render } from '@testing-library/react-native';
import { SkiaComponent } from '../SkiaComponent';

jest.mock('@shopify/react-native-skia', () => ({
  Canvas: ({ children }: any) => children,
  Circle: () => null,
  Group: ({ children }: any) => children,
}));

describe('SkiaComponent', () => {
  it('renders without crashing', () => {
    render(<SkiaComponent size={100} />);
  });

  it('handles size prop correctly', () => {
    const { rerender } = render(<SkiaComponent size={100} />);
    rerender(<SkiaComponent size={200} />);
    // Component should adapt to new size
  });
});
```

### Rive Testing

```tsx
// __tests__/RiveComponent.test.tsx
import React from 'react';
import { render } from '@testing-library/react-native';
import { RiveAnimation } from '../RiveAnimation';

jest.mock('react-native-rive', () => {
  return {
    __esModule: true,
    default: jest.fn(() => null),
  };
});

describe('RiveAnimation', () => {
  it('renders with correct props', () => {
    render(
      <RiveAnimation
        animationName="test-animation"
        width={200}
        height={200}
      />
    );
  });
});
```

---

## Best Practices

### Do's ✅

1. **Use appropriate technology for each use case**
   - Reanimated for UI animations
   - Skia for custom graphics
   - Rive for complex vector animations

2. **Optimize for performance**
   - Use shared values for animation synchronization
   - Dispose of resources properly
   - Avoid unnecessary re-renders

3. **Integrate with theme system**
   - Use theme colors in Skia components
   - Make animations responsive
   - Support light/dark modes

4. **Test thoroughly**
   - Mock animation libraries in tests
   - Test component behavior, not animation details
   - Use accessibility testing

### Don'ts ❌

1. **Don't mix animation libraries unnecessarily**
   - Avoid using Skia for simple UI animations
   - Don't use Reanimated for complex graphics

2. **Don't ignore performance**
   - Avoid creating new paths on every render
   - Don't use large canvas sizes when unnecessary
   - Don't forget to clean up resources

3. **Don't hardcode values**
   - Use theme colors instead of hex values
   - Use responsive sizing
   - Make animations configurable

4. **Don't skip accessibility**
   - Provide meaningful accessibility labels
   - Test with screen readers
   - Ensure animations don't cause seizures

### File Organization

```
src/
├── components/
│   ├── animations/
│   │   ├── skia/
│   │   │   ├── charts/
│   │   │   ├── effects/
│   │   │   └── interactive/
│   │   ├── rive/
│   │   │   ├── characters/
│   │   │   ├── icons/
│   │   │   └── transitions/
│   │   └── reanimated/
│   │       ├── gestures/
│   │       ├── layouts/
│   │       └── transitions/
│   └── ...
├── assets/
│   ├── animations/
│   │   ├── rive/
│   │   │   ├── characters/
│   │   │   └── icons/
│   │   └── lottie/ (if needed)
│   └── ...
└── ...
```

---

## Example Integration

Here's how to create a comprehensive animated component using all three technologies:

```tsx
import React from 'react';
import { View } from 'react-native';
import { useSharedValue, withSpring } from 'react-native-reanimated';
import { Canvas, Circle, useValue, useSharedValueEffect } from '@shopify/react-native-skia';
import RiveComponent from 'react-native-rive';
import { useTheme } from '@/src/core/theme';

export const ComprehensiveAnimatedComponent: React.FC<{
  isActive: boolean;
}> = ({ isActive }) => {
  const theme = useTheme();
  
  // Reanimated for state management
  const progress = useSharedValue(0);
  
  // Skia for custom graphics
  const skiaProgress = useValue(0);
  
  useSharedValueEffect(() => {
    skiaProgress.current = progress.value;
  }, progress);

  React.useEffect(() => {
    progress.value = withSpring(isActive ? 1 : 0);
  }, [isActive, progress]);

  return (
    <View style={{ width: 200, height: 300 }}>
      {/* Rive character animation */}
      <RiveComponent
        style={{ width: 200, height: 200 }}
        source={require('@/assets/animations/character.riv')}
        autoplay={isActive}
        textRuns={[
          { name: 'theme-color', text: theme.colors.primary }
        ]}
      />
      
      {/* Skia progress indicator */}
      <Canvas style={{ width: 200, height: 100 }}>
        <Circle
          cx={100}
          cy={50}
          r={30}
          color={theme.colors.primary}
          opacity={skiaProgress}
        />
      </Canvas>
    </View>
  );
};
```

This comprehensive approach leverages each technology's strengths while maintaining consistency with the app's theme system and performance requirements.