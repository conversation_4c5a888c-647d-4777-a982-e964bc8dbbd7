# Token Refresh Background Task Setup

## Overview

The token refresh system now uses Expo's native background fetch capabilities instead of JavaScript timeouts. This provides:

- ✅ Continues working when app is backgrounded
- ✅ Battery efficient - respects system resources
- ✅ Works even when app is closed (iOS with BGTaskScheduler)
- ✅ Automatic scheduling by the OS

## How It Works

1. **Background Task Registration**
   - Automatically registered when user logs in (tokens are set)
   - Automatically unregistered when user logs out
   - Runs every 15 minutes minimum (iOS limitation)

2. **Dual Approach**
   - **Background**: Native task runs even when app is closed
   - **Foreground**: JavaScript timer for immediate needs (5-minute checks)

3. **Smart Refresh Logic**
   - Background: Refreshes if token expires within 10 minutes
   - Foreground: Refreshes if token expires within 5 minutes
   - Only refreshes when actually needed

## iOS Setup

Add to `app.json` or `app.config.js`:

```json
{
  "expo": {
    "ios": {
      "infoPlist": {
        "UIBackgroundModes": ["fetch", "processing"]
      }
    }
  }
}
```

## Android Setup

No additional configuration needed - works out of the box!

## Testing

### Development Testing

```typescript
// In your component
import { useTokenRefresh } from '@/src/core/hooks/useTokenRefresh';

function TestComponent() {
  const { testBackgroundTask, getBackgroundStatus } = useTokenRefresh();
  
  // Test the background task (dev only)
  const handleTest = async () => {
    await testBackgroundTask();
  };
  
  // Check status
  const checkStatus = async () => {
    const status = await getBackgroundStatus();
    console.log('Background status:', status);
  };
}
```

### Production Testing

1. **iOS Simulator**:
   - Use Debug > Simulate Background Fetch
   - Or wait ~15 minutes with app backgrounded

2. **Android**:
   - Background tasks run more frequently
   - Can test by backgrounding app for a few minutes

## Permissions

- **iOS**: May prompt user for background app refresh permission
- **Android**: Granted at install time

## Monitoring

Check logs for:
- `✅ Token refresh background task registered` - Task registered successfully
- `🔄 [Background] Token refresh task started` - Task is running
- `✅ [Background] Token refreshed successfully` - Token was refreshed
- `⏰ [Background] Token still valid for X minutes` - No refresh needed

## Troubleshooting

1. **Task Not Running**:
   - Check if background refresh is enabled in device settings
   - Verify `UIBackgroundModes` is set in iOS config
   - Check `getBackgroundStatus()` for permission status

2. **Token Still Expiring**:
   - Foreground refresh handles immediate needs
   - Background task has OS-controlled timing
   - Check server token expiry time (should be > 30 minutes)

3. **Battery Concerns**:
   - Task only runs when token needs refresh
   - OS manages execution for optimal battery life
   - No impact when token is valid

## Best Practices

1. **Token Expiry**: Set server tokens to expire after at least 1 hour
2. **Refresh Buffer**: 10 minutes for background, 5 for foreground
3. **Error Handling**: Failed refreshes handled gracefully
4. **User Experience**: Seamless - users never see auth errors
