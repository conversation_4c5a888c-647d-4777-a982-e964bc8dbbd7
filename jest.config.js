module.exports = {
  preset: 'jest-expo',
  transformIgnorePatterns: [
    'node_modules/(?!(jest-)?@react-native|react-native|react-navigation|@react-navigation|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation-stack|@react-navigation/.*|@unimodules/.*|unimodules|react-native-svg|react-native-reanimated|@shopify/react-native-skia)',
  ],
  setupFiles: [
    './jestSetup.js',
    './node_modules/react-native-gesture-handler/jestSetup.js',
    '@shopify/react-native-skia/jestSetup.js', // Note: .js not .mjs for CommonJS
  ],
  collectCoverage: true,
};
