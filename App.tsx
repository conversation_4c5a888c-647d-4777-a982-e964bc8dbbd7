// import SplashScreenController from './src/app/splash';
import {
  Urbanist_300Light,
  Urbanist_400Regular,
  Urbanist_500Medium,
  Urbanist_600SemiBold,
  Urbanist_700Bold,
  Urbanist_800ExtraBold,
  Urbanist_900Black,
  useFonts,
} from '@expo-google-fonts/urbanist';
import { StatusBar } from 'expo-status-bar';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { KeyboardProvider } from 'react-native-keyboard-controller';
import { MagicModalPortal } from 'react-native-magic-modal';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import Toast from 'react-native-toast-message';

import { GraphQLProvider } from './src/core/api/context';
import { AppStateManager } from './src/core/components/AppStateManager';
// Import i18n initialization
import { initializeI18n } from './src/core/i18n';
import AppNavigator from './src/core/navigation';
import { TabBarHeightProvider } from './src/core/navigation/ui/TopTabNavigation';
import { QueryClientProvider, queryClient } from './src/core/query';
import { ThemeProvider } from './src/core/theme';
import toastConfig from './src/core/theme/config/ToastConfig';
import { AuthProvider } from './src/features/auth/services/context';

// Initialize i18n before rendering the app
initializeI18n();

export default function App() {
  const [fontsLoaded] = useFonts({
    Urbanist_300Light,
    Urbanist_400Regular,
    Urbanist_500Medium,
    Urbanist_600SemiBold,
    Urbanist_700Bold,
    Urbanist_800ExtraBold,
    Urbanist_900Black,
  });

  if (!fontsLoaded) {
    return null;
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <KeyboardProvider>
        <ThemeProvider defaultTheme="system">
          <QueryClientProvider client={queryClient}>
            <GraphQLProvider>
              <AuthProvider>
                <AppStateManager>
                  {/* <SplashScreenController /> */}
                  <SafeAreaProvider>
                    <TabBarHeightProvider>
                      <AppNavigator />
                      <MagicModalPortal />
                      <StatusBar style="auto" />
                      <Toast config={toastConfig} />
                    </TabBarHeightProvider>
                  </SafeAreaProvider>
                </AppStateManager>
              </AuthProvider>
            </GraphQLProvider>
          </QueryClientProvider>
        </ThemeProvider>
      </KeyboardProvider>
    </GestureHandlerRootView>
  );
}
