name: Release App

on:
  workflow_dispatch:
    inputs:
      version_bump:
        description: 'Which version to bump?'
        required: true
        default: 'patch'
        type: choice
        options:
          - patch
          - minor
          - major
      profile:
        description: 'Which EAS build profile to use?'
        required: true
        default: 'preview'
        type: choice
        options:
          - preview
          - production
      submit_stores:
        description: 'Which store(s) to submit the build to?'
        required: true
        default: 'none'
        type: choice
        options:
          - none
          - google
          - apple
          - both

jobs:
  release:
    name: Release
    runs-on: ubuntu-latest
    
    # Use GitHub Environments for secrets and protection rules
    environment: ${{ github.event.inputs.profile == 'production' && 'production' || 'staging' }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1

      - name: Install dependencies
        run: bun install --frozen-lockfile

      - name: Configure Git
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"

      - name: Bump version
        run: |
          # Bump the version in package.json
          bun version ${{ github.event.inputs.version_bump }} --no-git-tag-version
          
          # Get the new version from package.json
          NEW_VERSION=$(node -p "require('./package.json').version")
          echo "NEW_VERSION=$NEW_VERSION" >> $GITHUB_ENV

      - name: Commit and Tag new version
        run: |
          git add package.json
          git commit -m "chore: release v${{ env.NEW_VERSION }}"
          git tag -a "v${{ env.NEW_VERSION }}" -m "Release v${{ env.NEW_VERSION }}"
          git push
          git push --tags

      - name: EAS Build
        run: |
          eas build --platform all --profile ${{ github.event.inputs.profile }} --non-interactive
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}

      - name: EAS Submit to Google Play Store
        if: ${{ contains(github.event.inputs.submit_stores, 'google') || contains(github.event.inputs.submit_stores, 'both') }}
        run: |
          eas submit --platform android --latest --non-interactive
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}

      - name: EAS Submit to Apple App Store
        if: ${{ contains(github.event.inputs.submit_stores, 'apple') || contains(github.event.inputs.submit_stores, 'both') }}
        run: |
          eas submit --platform ios --latest --non-interactive
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}

      - name: Create GitHub Release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: v${{ env.NEW_VERSION }}
          release_name: Release v${{ env.NEW_VERSION }}
          body: |
            Released version v${{ env.NEW_VERSION }} for the ${{ github.event.inputs.profile }} environment.
          draft: false
          prerelease: ${{ github.event.inputs.profile != 'production' }}