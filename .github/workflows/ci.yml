name: <PERSON><PERSON> Checks

on:
  pull_request:
    branches: [ dev, main ]
  push:
    branches: [ dev, main ]

jobs:
  build-and-test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1

      - name: Install dependencies
        run: bun install --frozen-lockfile

      - name: Run linter
        run: bun run lint:check

      - name: Run TypeScript check
        run: bunx tsc --noEmit

      # - name: Run tests
      #   run: bun run test:ci
