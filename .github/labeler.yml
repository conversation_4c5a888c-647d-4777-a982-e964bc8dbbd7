# GitHub Labeler Configuration

feature:
  - changed-files:
      - any-glob-to-any-file: 'src/features/**/*'

core:
  - changed-files:
      - any-glob-to-any-file: 'src/core/**/*'

ui:
  - changed-files:
      - any-glob-to-any-file: 'src/shared/components/**/*'

ci:
  - changed-files:
      - any-glob-to-any-file: '.github/workflows/**/*'

docs:
  - changed-files:
      - any-glob-to-any-file: 'docs/**/*'

tests:
  - changed-files:
      - any-glob-to-any-file: '**/__tests__/**/*'

infra:
  - changed-files:
      - any-glob-to-any-file:
          - 'app.config.ts'
          - 'babel.config.js'
          - 'jest.config.js'
          - 'metro.config.js'
          - 'package.json'
          - 'tsconfig.json'
          - 'eas.json'

translations:
  - changed-files:
      - any-glob-to-any-file:
          - 'translations/**/*'
          - 'languages/**/*'
