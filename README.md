# Movuca Mobile App

A React Native mobile application built with Expo, React Native, and Restyle for theming.

## Theme System

The app uses Shopify's Restyle library for consistent theming. The theme system provides:

- Light and dark mode with automatic system detection
- Comprehensive color palette with semantic naming
- Spacing, typography, and border radius tokens
- Themed components (Box, Text, Button, Card, etc.)
- Responsive utilities for different screen sizes

### Usage

#### ThemeProvider

The `ThemeProvider` is already set up at the root of the application. It handles theme switching between light and dark modes.

```tsx
// In _layout.tsx
<ThemeProvider defaultTheme="system">
  {/* App content */}
</ThemeProvider>
```

Options for `defaultTheme`:
- `"light"`: Force light mode
- `"dark"`: Force dark mode
- `"system"`: Use system preference (default)

#### Themed Components

The theme system provides several pre-styled components:

- `Box`: Basic building block for layout (replaces View)
- `Text`: Typography component with variant support
- `Button`: Themed button with variants and sizes
- `Card`: Container with preset styles (default, elevated, outlined)
- `TextInput`: Styled text input with label and error support
- `Row` & `Column`: Flex layout helpers with spacing support
- `SafeAreaWrapper`: Safe area with theme integration
- `ScrollableContainer`: Scrollable container with theme support

Example:

```tsx
import { Box, Text, Button, Card } from '@/src/core/theme';

export default function MyScreen() {
  return (
    <Box flex={1} padding="md_16">
      <Text variant="h_32Light_intro">Welcome</Text>
      <Card variant="elevated" padding="md_16" marginTop="md_16">
        <Text variant="b_16Regular_input">Card content</Text>
        <Button 
          title="Press Me" 
          variant="primary" 
          marginTop="md_16" 
        />
      </Card>
    </Box>
  );
}
```

#### Theme Hooks

- `useTheme()`: Access the current theme object
- `useThemeContext()`: Access theme context (current theme, toggle theme, etc.)
- `useResponsive()`: Utilities for responsive design

```tsx
import { useTheme, useThemeContext, useResponsive } from '@/src/core/theme';

export default function MyComponent() {
  const theme = useTheme();
  const { themeName, toggleTheme } = useThemeContext();
  const { isPhone, select, breakpoint } = useResponsive();
  
  return (
    <Box 
      padding="md_16"
      backgroundColor="background"
      width={select({ 
        phone: '100%', 
        tablet: '80%', 
        desktop: '60%' 
      })}
    >
      <Text color="textSecondary">
        Current theme: {themeName}
      </Text>
      <Button 
        title="Toggle Theme" 
        onPress={toggleTheme} 
      />
    </Box>
  );
}
```

#### Creating Custom Styles

Use the `makeStyles` function to create styles that have access to the theme:

```tsx
import { makeStyles, Text } from '@/src/core/theme';

const MyComponent = () => {
  const styles = useStyles();
  
  return (
    <Box style={styles.container}>
      <Text style={styles.title}>Hello World</Text>
    </Box>
  );
};

const useStyles = makeStyles((theme) => ({
  container: {
    backgroundColor: theme.colors.background,
    padding: theme.spacing.md_16,
    borderRadius: theme.borderRadii.sm_12,
  },
  title: {
    color: theme.colors.primary,
    fontSize: 24,
    fontWeight: 'bold',
  },
}));
```

### Theme Values

#### Colors

The theme includes a comprehensive color palette:

- Semantic colors (primary, secondary, background, text, etc.)
- Raw palette colors (purple500, blue300, gray200, etc.)
- State colors (error, success, warning)

Usage:
```tsx
<Box backgroundColor="primary" />
<Text color="textSecondary" />
```

#### Spacing

Consistent spacing values for margins, padding, and positioning:

```
none_0: 0,
xxxs_2: 2,
xxs_4: 4,
xs_8: 8,
sm_12: 12,
md_16: 16,
ml_20: 20,
...etc
```

Usage:
```tsx
<Box padding="md_16" margin="xs_8" />
```

#### Typography

Predefined text styles:

```
heading1, heading2, ..., heading6
subtitle1, subtitle2
body1, body2
button
caption
overline
```

Usage:
```tsx
<Text variant="h_32Light_intro">Title</Text>
<Text variant="b_16Regular_input">Paragraph text</Text>
```

#### Border Radius

Consistent border radius values:

```
none_0: 0,
xs_4: 4,
s_8: 8,
sm_12: 12,
...etc
circle_9999: 9999,
```

Usage:
```tsx
<Box borderRadius="sm_12" />
```

## Getting Started

### Installation

```bash
# Install dependencies
npm install
# or
yarn install
```

### Running the app

```bash
# Start development server
npm start
# or
yarn start

# Run on iOS
npm run ios
# or
yarn ios

# Run on Android
npm run android
# or
yarn android
```

## Project Structure

```
├── src/
│   ├── app/            # Expo Router screens
│   ├── components/     # Shared components
│   ├── hooks/          # Custom hooks
│   ├── lib/            # Utilities and helpers
│   ├── store/          # State management
│   └── theme/          # Theme system
├── assets/             # Static assets
├── translations/       # i18n translations
└── ...
```
