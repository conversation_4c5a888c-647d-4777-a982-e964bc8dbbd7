// https://docs.expo.dev/guides/using-eslint/
const { defineConfig } = require('eslint/config');
const expoConfig = require('eslint-config-expo/flat');
const eslintPluginPrettierRecommended = require('eslint-plugin-prettier/recommended');
const eslintPluginReactCompiler = require('eslint-plugin-react-compiler');
const typescriptParser = require('@typescript-eslint/parser');
const typescriptPlugin = require('@typescript-eslint/eslint-plugin');

module.exports = defineConfig([
  expoConfig,
  eslintPluginPrettierRecommended,
  {
    ignores: ['dist/*', 'node_modules/*', '**/*.test.js', '**/*.test.ts', '**/*.test.tsx'],
    files: ['**/*.js', '**/*.jsx', '**/*.ts', '**/*.tsx'],
    languageOptions: {
      parser: typescriptParser,
      parserOptions: {
        ecmaVersion: 'latest',
        sourceType: 'module',
        ecmaFeatures: {
          jsx: true,
        },
      },
    },
    plugins: {
      'react-compiler': eslintPluginReactCompiler,
      '@typescript-eslint': typescriptPlugin,
    },
    rules: {
      'react-compiler/react-compiler': 'error',
      '@typescript-eslint/no-unused-vars': [
        'warn',
        {
          argsIgnorePattern: '^_',
          varsIgnorePattern: '^_',
          ignoreRestSiblings: true,
        },
      ],
      'prettier/prettier': 'warn', // Change to warn for MVP
      'no-unused-vars': 'off',
      'import/no-unresolved': 'off', // Turn off for MVP - path issues
      'import/export': 'off', // Turn off for MVP - export conflicts
      'import/namespace': 'off', // Turn off for MVP
      'import/no-named-as-default': 'off', // Turn off for MVP
      'import/no-named-as-default-member': 'off', // Turn off for MVP
      'react-hooks/exhaustive-deps': 'warn', // Change to warn
    },
  },
]);
