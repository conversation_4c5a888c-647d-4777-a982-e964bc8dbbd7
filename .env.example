# GraphQL API Configuration
EXPO_PUBLIC_GRAPHQL_URL=http://localhost:8080/query

# Alternative URLs for different environments
# EXPO_PUBLIC_GRAPHQL_URL=https://your-production-api.com/query
# EXPO_PUBLIC_GRAPHQL_URL=https://your-staging-api.com/query

# API Configuration
EXPO_PUBLIC_API_TIMEOUT=10000
EXPO_PUBLIC_API_RETRY_COUNT=3

# Foursquare Places API
EXPO_PUBLIC_FOURSQUARE_API_KEY=your_foursquare_api_key_here
# Optional: Override the default base URL if needed
# EXPO_PUBLIC_FOURSQUARE_BASE_URL=https://api.foursquare.com/v3/places

# Mapbox API Keys
# Get your keys from https://account.mapbox.com/
EXPO_PUBLIC_MAPBOX_ACCESS_TOKEN=your_mapbox_public_token_here
MAPBOX_SECRET_API_KEY=your_mapbox_secret_token_here

# Google Maps API Key
# Get your key from https://console.cloud.google.com/
EXPO_PUBLIC_GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here

# Environment
EXPO_PUBLIC_ENV=development