{"name": "mcp-graphql-introspection", "version": "1.0.0", "description": "MCP server for GraphQL schema introspection and query generation", "type": "module", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsx src/index.ts"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.0.0", "graphql": "^16.8.1", "node-fetch": "^3.3.2"}, "devDependencies": {"@types/node": "^20.0.0", "tsx": "^4.0.0", "typescript": "^5.0.0"}, "keywords": ["mcp", "graphql", "introspection", "schema"], "author": "Your Name", "license": "MIT"}