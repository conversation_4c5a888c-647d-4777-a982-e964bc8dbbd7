# GraphQL Introspection MCP Server

A Model Context Protocol (MCP) server that provides comprehensive GraphQL schema introspection and code generation capabilities.

## Features

- 🔍 **Schema Introspection** - Fetch and analyze GraphQL schemas
- 📋 **Operation Listing** - List queries, mutations, and subscriptions
- 🏷️ **Type Exploration** - Browse and inspect GraphQL types
- 🔎 **Smart Search** - Find operations and types by name or description
- 🧩 **Code Generation** - Generate queries, mutations, fragments, and type definitions
- ⚡ **Performance** - Fast introspection with caching
- 🛡️ **Type Safety** - Full TypeScript support

## Installation

```bash
cd mcp-graphql-introspection
npm install
npm run build
```

## Usage

### Configure Claude Desktop

Add to your Claude Desktop configuration:

```json
{
  "mcpServers": {
    "graphql-introspection": {
      "command": "node",
      "args": ["/path/to/mcp-graphql-introspection/dist/index.js"]
    }
  }
}
```

### Available Tools

#### 1. Configure Endpoint
```
configure_endpoint
```
Set up the GraphQL endpoint URL and optional headers (like Authorization).

**Parameters:**
- `url` (required) - GraphQL endpoint URL
- `headers` (optional) - Custom headers object

**Example:**
```json
{
  "url": "https://api-dev.movuca.app/query",
  "headers": {
    "Authorization": "Bearer your-token"
  }
}
```

#### 2. Introspect Schema
```
introspect_schema
```
Fetch and analyze the GraphQL schema from the configured endpoint.

#### 3. List Queries
```
list_queries
```
List all available GraphQL queries with signatures and descriptions.

**Parameters:**
- `filter` (optional) - Filter queries by name (regex)
- `includeDeprecated` (optional) - Include deprecated fields

#### 4. List Mutations
```
list_mutations
```
List all available GraphQL mutations with signatures and descriptions.

**Parameters:**
- `filter` (optional) - Filter mutations by name (regex)
- `includeDeprecated` (optional) - Include deprecated fields

#### 5. List Types
```
list_types
```
List all GraphQL types in the schema.

**Parameters:**
- `filter` (optional) - Filter types by name (regex)
- `kind` (optional) - Filter by type kind (OBJECT, INPUT_OBJECT, ENUM, etc.)

#### 6. Get Type Details
```
get_type_details
```
Get detailed information about a specific GraphQL type.

**Parameters:**
- `typeName` (required) - Name of the type to inspect

#### 7. Search Operations
```
search_operations
```
Search across queries, mutations, and subscriptions.

**Parameters:**
- `searchTerm` (required) - Search term for operation names and descriptions

#### 8. Generate Query
```
generate_query
```
Generate a complete GraphQL query with proper field selection.

**Parameters:**
- `operationName` (required) - Name of the query operation
- `includeComments` (optional) - Include field descriptions as comments
- `formatStyle` (optional) - Output format: 'compact' or 'readable'

#### 9. Generate Mutation
```
generate_mutation
```
Generate a complete GraphQL mutation with proper field selection.

**Parameters:**
- `operationName` (required) - Name of the mutation operation
- `includeComments` (optional) - Include field descriptions as comments
- `formatStyle` (optional) - Output format: 'compact' or 'readable'

#### 10. Generate Fragment
```
generate_fragment
```
Generate a GraphQL fragment for a specific type.

**Parameters:**
- `typeName` (required) - Name of the type for the fragment
- `includeComments` (optional) - Include field descriptions as comments
- `formatStyle` (optional) - Output format: 'compact' or 'readable'

#### 11. Generate Type Definition
```
generate_type_definition
```
Generate GraphQL or TypeScript type definitions.

**Parameters:**
- `typeName` (required) - Name of the type
- `outputFormat` (optional) - Output format: 'graphql' or 'typescript'
- `includeComments` (optional) - Include descriptions as comments

## Example Workflow

1. **Configure the endpoint:**
   ```
   configure_endpoint
   {
     "url": "https://api-dev.movuca.app/query"
   }
   ```

2. **Introspect the schema:**
   ```
   introspect_schema
   ```

3. **Search for venue-related operations:**
   ```
   search_operations
   {
     "searchTerm": "venue"
   }
   ```

4. **Generate a venue query:**
   ```
   generate_query
   {
     "operationName": "venue",
     "includeComments": true,
     "formatStyle": "readable"
   }
   ```

5. **Generate a create venue mutation:**
   ```
   generate_mutation
   {
     "operationName": "createVenue",
     "includeComments": true
   }
   ```

6. **Generate venue type fragment:**
   ```
   generate_fragment
   {
     "typeName": "Venue",
     "includeComments": true
   }
   ```

## Advanced Features

### Custom Headers Support
Support for authentication and custom headers:

```json
{
  "url": "https://api.example.com/graphql",
  "headers": {
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIs...",
    "X-API-Version": "2024-01",
    "Content-Type": "application/json"
  }
}
```

### Smart Code Generation
- **Intelligent field selection** - Automatically selects appropriate fields based on type
- **Depth limiting** - Prevents infinite recursion in nested types
- **Scalar detection** - Prioritizes scalar fields for clean queries
- **Fragment optimization** - Generates reusable fragments for complex types

### Search and Filtering
- **Regex support** - Use regular expressions for powerful filtering
- **Multi-criteria search** - Search across names, descriptions, and arguments
- **Type filtering** - Filter by specific GraphQL type kinds
- **Deprecation handling** - Option to include/exclude deprecated fields

## Development

### Build
```bash
npm run build
```

### Development Mode
```bash
npm run dev
```

### Project Structure
```
src/
├── index.ts          # MCP server implementation
├── introspection.ts  # GraphQL introspection logic
├── generator.ts      # Code generation utilities
└── types.ts         # TypeScript type definitions
```

## Error Handling

The server provides comprehensive error handling:

- **Configuration errors** - Clear messages for endpoint setup issues
- **Network errors** - Detailed error information for connection problems
- **GraphQL errors** - Proper handling of GraphQL-specific errors
- **Validation errors** - Input validation with helpful messages

## Performance

- **Caching** - Schema results are cached for fast subsequent operations
- **Efficient introspection** - Optimized introspection queries
- **Lazy loading** - Operations only fetch required data
- **Memory management** - Proper cleanup and resource management

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details.