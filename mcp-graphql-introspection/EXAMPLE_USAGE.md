# GraphQL Introspection MCP - Example Usage

## Step-by-Step Example with Movuca API

### 1. Configure the GraphQL Endpoint

First, configure the endpoint to point to your GraphQL API:

```
Tool: configure_endpoint
Parameters:
{
  "url": "https://api-dev.movuca.app/query"
}
```

Response:
```
✅ GraphQL endpoint configured: https://api-dev.movuca.app/query
Headers: 0 custom headers
```

### 2. Introspect the Schema

Fetch and analyze the GraphQL schema:

```
Tool: introspect_schema
Parameters: {}
```

Response:
```
✅ Schema introspected successfully!

📊 Statistics:
- Queries: 142
- Mutations: 98
- Types: 245
```

### 3. Search for Venue Operations

Search for all venue-related operations:

```
Tool: search_operations
Parameters:
{
  "searchTerm": "venue"
}
```

Response:
```
🔍 Search results for "venue":

📋 Queries (15):
  • venue
  • venues
  • searchVenues
  • venuesNearby
  • venueCheckIns
  • venueTypes
  • venueImages
  • primaryVenueImage
  • ...

⚡ Mutations (12):
  • createVenue
  • updateVenue
  • deleteVenue
  • uploadVenueImage
  • uploadVenueImages
  • activateVenue
  • verifyVenue
  • ...
```

### 4. List All Queries with Filter

List all queries containing "venue":

```
Tool: list_queries
Parameters:
{
  "filter": "venue",
  "includeDeprecated": false
}
```

Response:
```
🔍 GraphQL Queries (15 found):

• venue(id: UUID!): Venue

• venues(limit: Int, offset: Int): [Venue!]!

• searchVenues(input: VenueSearchInput!): VenueSearchResult!

• venuesNearby(latitude: Float!, longitude: Float!, radius: Float, limit: Int): [Venue!]!

• venueTypes: [VenueTypeHierarchy!]!
```

### 5. Get Type Details

Get detailed information about the Venue type:

```
Tool: get_type_details
Parameters:
{
  "typeName": "Venue"
}
```

Response:
```
🏷️ Type: Venue (OBJECT)

🔧 Fields (20):
  • id: UUID!
  • name: String!
  • description: String
  • address: String!
  • city: String
  • state: String
  • country: String
  • postalCode: String
  • latitude: Float
  • longitude: Float
  • phone: String
  • email: String
  • website: String
  • capacity: Int
  • rating: Float!
  • reviewCount: Int!
  • isActive: Boolean!
  • venueTypeId: Int
  • venueType: VenueTypeHierarchy
  • content: VenueContent
  • distance: Float
  • createdAt: DateTime!
  • updatedAt: DateTime!
```

### 6. Generate a Query

Generate a complete GraphQL query for fetching a single venue:

```
Tool: generate_query
Parameters:
{
  "operationName": "venue",
  "includeComments": true,
  "formatStyle": "readable"
}
```

Response:
```
🔍 Generated GraphQL Query:

```graphql
query Venue($id: UUID!) {
  venue(id: $id) {
    id
    name
    description
    address
    city
    state
    country
    postalCode
    latitude
    longitude
    phone
    email
    website
    capacity
    rating
    reviewCount
    isActive
    venueTypeId
    createdAt
    updatedAt
    venueType {
      id
      name
      description
    }
    content {
      primaryImage
      images
      description
    }
    distance
  }
}
```
```

### 7. Generate a Mutation

Generate a create venue mutation:

```
Tool: generate_mutation
Parameters:
{
  "operationName": "createVenue",
  "includeComments": true,
  "formatStyle": "readable"
}
```

Response:
```
⚡ Generated GraphQL Mutation:

```graphql
mutation CreateVenue($input: CreateVenueInput!) {
  createVenue(input: $input) {
    id
    name
    description
    address
    city
    state
    country
    postalCode
    latitude
    longitude
    phone
    email
    website
    capacity
    rating
    reviewCount
    isActive
    venueTypeId
    createdAt
    updatedAt
    venueType {
      id
      name
      description
    }
    content {
      primaryImage
      images
      description
    }
    distance
  }
}
```
```

### 8. Generate a Fragment

Generate a reusable fragment for venue data:

```
Tool: generate_fragment
Parameters:
{
  "typeName": "Venue",
  "includeComments": true,
  "formatStyle": "readable"
}
```

Response:
```
🧩 Generated GraphQL Fragment:

```graphql
fragment VenueFields on Venue {
  id
  name
  description
  address
  city
  state
  country
  postalCode
  latitude
  longitude
  phone
  email
  website
  capacity
  rating
  reviewCount
  isActive
  venueTypeId
  createdAt
  updatedAt
  venueType {
    id
    name
    description
  }
  content {
    primaryImage
    images
    description
  }
  distance
}
```
```

### 9. Get Input Type Details

Check the structure of CreateVenueInput:

```
Tool: get_type_details
Parameters:
{
  "typeName": "CreateVenueInput"
}
```

Response:
```
🏷️ Type: CreateVenueInput (INPUT_OBJECT)

📥 Input Fields (15):
  • name: String!
  • description: String
  • address: String!
  • city: String!
  • state: String!
  • country: String!
  • postalCode: String
  • longitude: Float!
  • latitude: Float!
  • capacity: Int
  • venueType: VenueType!
  • venueTypeId: Int
  • tagIds: [Int!]
  • amenities: [String!]
  • tags: [String!]
```

### 10. List Venue Types

See what venue types are available:

```
Tool: list_types
Parameters:
{
  "filter": "VenueType",
  "kind": "ENUM"
}
```

Response:
```
📋 GraphQL Types (1 found):

• VenueType (ENUM) - Enumeration of venue types
```

### 11. Generate Type Definition

Generate a TypeScript interface for the Venue type:

```
Tool: generate_type_definition
Parameters:
{
  "typeName": "Venue",
  "outputFormat": "typescript",
  "includeComments": true
}
```

Response:
```
🏷️ Generated TYPESCRIPT Type Definition:

```typescript
interface Venue {
  id: string;
  name: string;
  description?: string;
  address: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  latitude?: number;
  longitude?: number;
  phone?: string;
  email?: string;
  website?: string;
  capacity?: number;
  rating: number;
  reviewCount: number;
  isActive: boolean;
  venueTypeId?: number;
  venueType?: VenueTypeHierarchy;
  content?: VenueContent;
  distance?: number;
  createdAt: string;
  updatedAt: string;
}
```
```

## Advanced Usage

### Search with Complex Filters

Search for operations related to authentication:

```
Tool: search_operations
Parameters:
{
  "searchTerm": "auth|login|register"
}
```

### List Specific Type Kinds

List only input object types:

```
Tool: list_types
Parameters:
{
  "kind": "INPUT_OBJECT"
}
```

### Generate Compact Queries

Generate more compact query format:

```
Tool: generate_query
Parameters:
{
  "operationName": "venues",
  "includeComments": false,
  "formatStyle": "compact"
}
```

## Integration with Development Workflow

This MCP server is perfect for:

1. **API Documentation** - Quickly explore and understand GraphQL APIs
2. **Code Generation** - Generate accurate queries, mutations, and fragments
3. **Type Definitions** - Create TypeScript interfaces from GraphQL types
4. **Testing** - Understand API structure for writing tests
5. **Development** - Rapid prototyping of GraphQL operations

## Best Practices

1. **Always introspect first** - Run `introspect_schema` after configuring endpoint
2. **Use search functionality** - Use `search_operations` to find relevant operations
3. **Check type details** - Use `get_type_details` to understand complex types
4. **Generate fragments** - Create reusable fragments for complex types
5. **Include comments** - Use `includeComments: true` for better code documentation