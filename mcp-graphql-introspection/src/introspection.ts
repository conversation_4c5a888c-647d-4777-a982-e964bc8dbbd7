import fetch from 'node-fetch';

import type {
  FilterOptions,
  GraphQLEndpointConfig,
  GraphQLField,
  GraphQLSchema,
  GraphQLType,
  IntrospectionResult,
} from './types.js';

export class GraphQLIntrospector {
  private config: GraphQLEndpointConfig;

  constructor(config: GraphQLEndpointConfig) {
    this.config = config;
  }

  private getIntrospectionQuery(): string {
    return `
      query IntrospectionQuery {
        __schema {
          queryType { name }
          mutationType { name }
          subscriptionType { name }
          types {
            ...FullType
          }
          directives {
            name
            description
            locations
            args {
              ...InputValue
            }
          }
        }
      }

      fragment FullType on __Type {
        kind
        name
        description
        fields(includeDeprecated: true) {
          name
          description
          args {
            ...InputValue
          }
          type {
            ...TypeRef
          }
          isDeprecated
          deprecationReason
        }
        inputFields {
          ...InputValue
        }
        interfaces {
          ...TypeRef
        }
        enumValues(includeDeprecated: true) {
          name
          description
          isDeprecated
          deprecationReason
        }
        possibleTypes {
          ...TypeRef
        }
      }

      fragment InputValue on __InputValue {
        name
        description
        type { ...TypeRef }
        defaultValue
      }

      fragment TypeRef on __Type {
        kind
        name
        ofType {
          kind
          name
          ofType {
            kind
            name
            ofType {
              kind
              name
              ofType {
                kind
                name
                ofType {
                  kind
                  name
                  ofType {
                    kind
                    name
                    ofType {
                      kind
                      name
                    }
                  }
                }
              }
            }
          }
        }
      }
    `;
  }

  async introspect(): Promise<GraphQLSchema> {
    const response = await fetch(this.config.url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...this.config.headers,
      },
      body: JSON.stringify({
        query: this.getIntrospectionQuery(),
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = (await response.json()) as IntrospectionResult;

    if (!result.data || !result.data.__schema) {
      throw new Error('Invalid introspection result');
    }

    return result.data.__schema;
  }

  getQueries(schema: GraphQLSchema, options: FilterOptions = {}): GraphQLField[] {
    if (!schema.queryType) return [];

    const queryType = schema.types.find(type => type.name === schema.queryType?.name);
    if (!queryType?.fields) return [];

    return this.filterFields(queryType.fields, options);
  }

  getMutations(schema: GraphQLSchema, options: FilterOptions = {}): GraphQLField[] {
    if (!schema.mutationType) return [];

    const mutationType = schema.types.find(type => type.name === schema.mutationType?.name);
    if (!mutationType?.fields) return [];

    return this.filterFields(mutationType.fields, options);
  }

  getSubscriptions(schema: GraphQLSchema, options: FilterOptions = {}): GraphQLField[] {
    if (!schema.subscriptionType) return [];

    const subscriptionType = schema.types.find(type => type.name === schema.subscriptionType?.name);
    if (!subscriptionType?.fields) return [];

    return this.filterFields(subscriptionType.fields, options);
  }

  getTypes(schema: GraphQLSchema, options: FilterOptions = {}): GraphQLType[] {
    let types = schema.types.filter(
      type => !type.name?.startsWith('__') // Filter out introspection types
    );

    if (options.typeFilter && options.typeFilter.length > 0) {
      const filterRegex = new RegExp(options.typeFilter.join('|'), 'i');
      types = types.filter(type => type.name && filterRegex.test(type.name));
    }

    return types;
  }

  getType(schema: GraphQLSchema, typeName: string): GraphQLType | undefined {
    return schema.types.find(type => type.name === typeName);
  }

  searchOperations(
    schema: GraphQLSchema,
    searchTerm: string
  ): {
    queries: GraphQLField[];
    mutations: GraphQLField[];
    subscriptions: GraphQLField[];
  } {
    const regex = new RegExp(searchTerm, 'i');

    const filterBySearch = (fields: GraphQLField[]) =>
      fields.filter(
        field =>
          regex.test(field.name) ||
          regex.test(field.description || '') ||
          field.args.some(arg => regex.test(arg.name))
      );

    return {
      queries: filterBySearch(this.getQueries(schema)),
      mutations: filterBySearch(this.getMutations(schema)),
      subscriptions: filterBySearch(this.getSubscriptions(schema)),
    };
  }

  private filterFields(fields: GraphQLField[], options: FilterOptions): GraphQLField[] {
    let filteredFields = fields;

    if (!options.includeDeprecated) {
      filteredFields = filteredFields.filter(field => !field.isDeprecated);
    }

    if (options.fieldFilter && options.fieldFilter.length > 0) {
      const filterRegex = new RegExp(options.fieldFilter.join('|'), 'i');
      filteredFields = filteredFields.filter(field => filterRegex.test(field.name));
    }

    return filteredFields;
  }

  formatTypeString(type: GraphQLType): string {
    if (type.kind === 'NON_NULL') {
      return `${this.formatTypeString(type.ofType!)}!`;
    }

    if (type.kind === 'LIST') {
      return `[${this.formatTypeString(type.ofType!)}]`;
    }

    return type.name || 'Unknown';
  }

  getFieldSignature(field: GraphQLField): string {
    const args =
      field.args.length > 0
        ? `(${field.args.map(arg => `${arg.name}: ${this.formatTypeString(arg.type)}`).join(', ')})`
        : '';

    return `${field.name}${args}: ${this.formatTypeString(field.type)}`;
  }
}
