export interface GraphQLField {
  name: string;
  description?: string;
  type: GraphQLType;
  args: GraphQLInputValue[];
  isDeprecated?: boolean;
  deprecationReason?: string;
}

export interface GraphQLType {
  kind: 'SCALAR' | 'OBJECT' | 'INTERFACE' | 'UNION' | 'ENUM' | 'INPUT_OBJECT' | 'LIST' | 'NON_NULL';
  name?: string;
  description?: string;
  fields?: GraphQLField[];
  inputFields?: GraphQLInputValue[];
  interfaces?: GraphQLType[];
  enumValues?: GraphQLEnumValue[];
  possibleTypes?: GraphQLType[];
  ofType?: GraphQLType;
}

export interface GraphQLInputValue {
  name: string;
  description?: string;
  type: GraphQLType;
  defaultValue?: string;
}

export interface GraphQLEnumValue {
  name: string;
  description?: string;
  isDeprecated?: boolean;
  deprecationReason?: string;
}

export interface GraphQLSchema {
  queryType?: GraphQLType;
  mutationType?: GraphQLType;
  subscriptionType?: GraphQLType;
  types: GraphQLType[];
  directives: GraphQLDirective[];
}

export interface GraphQLDirective {
  name: string;
  description?: string;
  locations: string[];
  args: GraphQLInputValue[];
}

export interface IntrospectionResult {
  data: {
    __schema: GraphQLSchema;
  };
}

export interface GraphQLEndpointConfig {
  url: string;
  headers?: Record<string, string>;
}

export interface FilterOptions {
  includeDeprecated?: boolean;
  typeFilter?: string[];
  fieldFilter?: string[];
}

export interface GenerateOptions {
  includeFragments?: boolean;
  includeComments?: boolean;
  formatStyle?: 'compact' | 'readable';
}
