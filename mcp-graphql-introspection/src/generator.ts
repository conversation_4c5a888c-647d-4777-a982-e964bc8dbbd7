import { GraphQLIntrospector } from './introspection.js';
import type { GenerateOptions, GraphQLField, GraphQLSchema, GraphQLType } from './types.js';

export class GraphQLGenerator {
  private introspector: GraphQLIntrospector;

  constructor(introspector: GraphQLIntrospector) {
    this.introspector = introspector;
  }

  generateQuery(
    schema: GraphQLSchema,
    operationName: string,
    options: GenerateOptions = {}
  ): string {
    const queries = this.introspector.getQueries(schema);
    const query = queries.find(q => q.name === operationName);

    if (!query) {
      throw new Error(`Query '${operationName}' not found`);
    }

    return this.generateOperation('query', query, schema, options);
  }

  generateMutation(
    schema: GraphQLSchema,
    operationName: string,
    options: GenerateOptions = {}
  ): string {
    const mutations = this.introspector.getMutations(schema);
    const mutation = mutations.find(m => m.name === operationName);

    if (!mutation) {
      throw new Error(`Mutation '${operationName}' not found`);
    }

    return this.generateOperation('mutation', mutation, schema, options);
  }

  generateFragment(schema: GraphQLSchema, typeName: string, options: GenerateOptions = {}): string {
    const type = this.introspector.getType(schema, typeName);

    if (!type || !type.fields) {
      throw new Error(`Type '${typeName}' not found or has no fields`);
    }

    const fields = this.generateFieldSelection(type, schema, options, 0);
    const fragmentName = `${typeName}Fields`;

    return options.formatStyle === 'compact'
      ? `fragment ${fragmentName} on ${typeName} { ${fields} }`
      : `fragment ${fragmentName} on ${typeName} {\n${this.indent(fields, 1)}\n}`;
  }

  generateTypeDefinition(
    schema: GraphQLSchema,
    typeName: string,
    options: GenerateOptions = {}
  ): string {
    const type = this.introspector.getType(schema, typeName);

    if (!type) {
      throw new Error(`Type '${typeName}' not found`);
    }

    return this.generateTypeString(type, schema, options);
  }

  generateAllFragments(schema: GraphQLSchema, options: GenerateOptions = {}): string {
    const types = this.introspector
      .getTypes(schema)
      .filter(type => type.kind === 'OBJECT' && type.fields && type.fields.length > 0);

    return types
      .map(type => {
        try {
          return this.generateFragment(schema, type.name!, options);
        } catch {
          return null;
        }
      })
      .filter(Boolean)
      .join('\n\n');
  }

  private generateOperation(
    operationType: 'query' | 'mutation' | 'subscription',
    operation: GraphQLField,
    schema: GraphQLSchema,
    options: GenerateOptions
  ): string {
    const operationName = this.capitalize(operation.name);
    const variables = this.generateVariables(operation);
    const args = this.generateArguments(operation);
    const fields = this.generateReturnFields(operation.type, schema, options);

    const comment =
      options.includeComments && operation.description ? `# ${operation.description}\n` : '';

    if (options.formatStyle === 'compact') {
      return `${comment}${operationType} ${operationName}${variables} { ${operation.name}${args} { ${fields} } }`;
    }

    return [
      comment,
      `${operationType} ${operationName}${variables} {`,
      this.indent(`${operation.name}${args} {`, 1),
      this.indent(fields, 2),
      this.indent('}', 1),
      '}',
    ]
      .filter(Boolean)
      .join('\n');
  }

  private generateVariables(operation: GraphQLField): string {
    if (operation.args.length === 0) return '';

    const variables = operation.args.map(
      arg => `$${arg.name}: ${this.introspector.formatTypeString(arg.type)}`
    );

    return `(${variables.join(', ')})`;
  }

  private generateArguments(operation: GraphQLField): string {
    if (operation.args.length === 0) return '';

    const args = operation.args.map(arg => `${arg.name}: $${arg.name}`);
    return `(${args.join(', ')})`;
  }

  private generateReturnFields(
    type: GraphQLType,
    schema: GraphQLSchema,
    options: GenerateOptions,
    depth: number = 0
  ): string {
    const maxDepth = 3; // Prevent infinite recursion

    if (depth > maxDepth) {
      return 'id'; // Fallback to simple field
    }

    // Unwrap NON_NULL and LIST types
    let actualType = type;
    while (actualType.ofType) {
      actualType = actualType.ofType;
    }

    const typeDefinition = this.introspector.getType(schema, actualType.name!);

    if (!typeDefinition || !typeDefinition.fields) {
      return actualType.name || 'id';
    }

    return this.generateFieldSelection(typeDefinition, schema, options, depth);
  }

  private generateFieldSelection(
    type: GraphQLType,
    schema: GraphQLSchema,
    options: GenerateOptions,
    depth: number = 0
  ): string {
    if (!type.fields) return '';

    const scalarFields = type.fields.filter(field => this.isScalarField(field.type));
    const objectFields = type.fields.filter(field => !this.isScalarField(field.type));

    let fields = scalarFields.map(field => field.name);

    // Add some object fields with limited depth
    if (depth < 2) {
      const limitedObjectFields = objectFields.slice(0, 3); // Limit object fields
      fields.push(
        ...limitedObjectFields.map(field => {
          const subFields = this.generateReturnFields(field.type, schema, options, depth + 1);
          return `${field.name} { ${subFields} }`;
        })
      );
    }

    return options.formatStyle === 'compact' ? fields.join(' ') : fields.join('\n');
  }

  private isScalarField(type: GraphQLType): boolean {
    // Unwrap NON_NULL and LIST types
    let actualType = type;
    while (actualType.ofType) {
      actualType = actualType.ofType;
    }

    return actualType.kind === 'SCALAR' || actualType.kind === 'ENUM';
  }

  private generateTypeString(
    type: GraphQLType,
    schema: GraphQLSchema,
    options: GenerateOptions
  ): string {
    const comment = options.includeComments && type.description ? `# ${type.description}\n` : '';

    switch (type.kind) {
      case 'OBJECT':
        return this.generateObjectType(type, comment, options);
      case 'INPUT_OBJECT':
        return this.generateInputType(type, comment, options);
      case 'ENUM':
        return this.generateEnumType(type, comment, options);
      case 'INTERFACE':
        return this.generateInterfaceType(type, comment, options);
      case 'UNION':
        return this.generateUnionType(type, comment, options);
      default:
        return `${comment}scalar ${type.name}`;
    }
  }

  private generateObjectType(type: GraphQLType, comment: string, options: GenerateOptions): string {
    if (!type.fields) return `${comment}type ${type.name}`;

    const fields = type.fields
      .map(field => {
        const fieldComment =
          options.includeComments && field.description ? `  # ${field.description}\n` : '';
        const signature = this.introspector.getFieldSignature(field);
        return `${fieldComment}  ${signature}`;
      })
      .join('\n');

    return `${comment}type ${type.name} {\n${fields}\n}`;
  }

  private generateInputType(type: GraphQLType, comment: string, options: GenerateOptions): string {
    if (!type.inputFields) return `${comment}input ${type.name}`;

    const fields = type.inputFields
      .map(field => {
        const fieldComment =
          options.includeComments && field.description ? `  # ${field.description}\n` : '';
        return `${fieldComment}  ${field.name}: ${this.introspector.formatTypeString(field.type)}`;
      })
      .join('\n');

    return `${comment}input ${type.name} {\n${fields}\n}`;
  }

  private generateEnumType(type: GraphQLType, comment: string, options: GenerateOptions): string {
    if (!type.enumValues) return `${comment}enum ${type.name}`;

    const values = type.enumValues
      .map(value => {
        const valueComment =
          options.includeComments && value.description ? `  # ${value.description}\n` : '';
        return `${valueComment}  ${value.name}`;
      })
      .join('\n');

    return `${comment}enum ${type.name} {\n${values}\n}`;
  }

  private generateInterfaceType(
    type: GraphQLType,
    comment: string,
    options: GenerateOptions
  ): string {
    if (!type.fields) return `${comment}interface ${type.name}`;

    const fields = type.fields
      .map(field => {
        const fieldComment =
          options.includeComments && field.description ? `  # ${field.description}\n` : '';
        const signature = this.introspector.getFieldSignature(field);
        return `${fieldComment}  ${signature}`;
      })
      .join('\n');

    return `${comment}interface ${type.name} {\n${fields}\n}`;
  }

  private generateUnionType(type: GraphQLType, comment: string, options: GenerateOptions): string {
    if (!type.possibleTypes) return `${comment}union ${type.name}`;

    const types = type.possibleTypes.map(t => t.name).join(' | ');
    return `${comment}union ${type.name} = ${types}`;
  }

  private capitalize(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  private indent(text: string, level: number): string {
    const spaces = '  '.repeat(level);
    return text
      .split('\n')
      .map(line => spaces + line)
      .join('\n');
  }
}
