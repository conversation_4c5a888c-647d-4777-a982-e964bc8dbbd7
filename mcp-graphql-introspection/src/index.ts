#!/usr/bin/env node
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ErrorCode,
  ListToolsRequestSchema,
  McpError,
} from '@modelcontextprotocol/sdk/types.js';

import { GraphQLGenerator } from './generator.js';
import { GraphQLIntrospector } from './introspection.js';
import type { GraphQLEndpointConfig, GraphQLSchema } from './types.js';

class GraphQLMCPServer {
  private server: Server;
  private introspector: GraphQLIntrospector | null = null;
  private generator: GraphQLGenerator | null = null;
  private schema: GraphQLSchema | null = null;

  constructor() {
    this.server = new Server(
      {
        name: 'graphql-introspection',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.setupToolHandlers();
  }

  private setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'configure_endpoint',
            description: 'Configure the GraphQL endpoint URL and headers',
            inputSchema: {
              type: 'object',
              properties: {
                url: {
                  type: 'string',
                  description: 'GraphQL endpoint URL',
                },
                headers: {
                  type: 'object',
                  description: 'Optional headers (e.g., Authorization)',
                  additionalProperties: {
                    type: 'string',
                  },
                },
              },
              required: ['url'],
            },
          },
          {
            name: 'introspect_schema',
            description: 'Fetch and introspect the GraphQL schema',
            inputSchema: {
              type: 'object',
              properties: {},
            },
          },
          {
            name: 'list_queries',
            description: 'List all available GraphQL queries',
            inputSchema: {
              type: 'object',
              properties: {
                filter: {
                  type: 'string',
                  description: 'Filter queries by name (regex)',
                },
                includeDeprecated: {
                  type: 'boolean',
                  description: 'Include deprecated fields',
                  default: false,
                },
              },
            },
          },
          {
            name: 'list_mutations',
            description: 'List all available GraphQL mutations',
            inputSchema: {
              type: 'object',
              properties: {
                filter: {
                  type: 'string',
                  description: 'Filter mutations by name (regex)',
                },
                includeDeprecated: {
                  type: 'boolean',
                  description: 'Include deprecated fields',
                  default: false,
                },
              },
            },
          },
          {
            name: 'list_types',
            description: 'List all GraphQL types',
            inputSchema: {
              type: 'object',
              properties: {
                filter: {
                  type: 'string',
                  description: 'Filter types by name (regex)',
                },
                kind: {
                  type: 'string',
                  enum: ['OBJECT', 'INPUT_OBJECT', 'ENUM', 'INTERFACE', 'UNION', 'SCALAR'],
                  description: 'Filter by type kind',
                },
              },
            },
          },
          {
            name: 'get_type_details',
            description: 'Get detailed information about a specific type',
            inputSchema: {
              type: 'object',
              properties: {
                typeName: {
                  type: 'string',
                  description: 'Name of the type to inspect',
                },
              },
              required: ['typeName'],
            },
          },
          {
            name: 'search_operations',
            description: 'Search queries, mutations, and subscriptions',
            inputSchema: {
              type: 'object',
              properties: {
                searchTerm: {
                  type: 'string',
                  description: 'Search term for operation names and descriptions',
                },
              },
              required: ['searchTerm'],
            },
          },
          {
            name: 'generate_query',
            description: 'Generate a GraphQL query with proper field selection',
            inputSchema: {
              type: 'object',
              properties: {
                operationName: {
                  type: 'string',
                  description: 'Name of the query operation',
                },
                includeComments: {
                  type: 'boolean',
                  description: 'Include field descriptions as comments',
                  default: true,
                },
                formatStyle: {
                  type: 'string',
                  enum: ['compact', 'readable'],
                  description: 'Output format style',
                  default: 'readable',
                },
              },
              required: ['operationName'],
            },
          },
          {
            name: 'generate_mutation',
            description: 'Generate a GraphQL mutation with proper field selection',
            inputSchema: {
              type: 'object',
              properties: {
                operationName: {
                  type: 'string',
                  description: 'Name of the mutation operation',
                },
                includeComments: {
                  type: 'boolean',
                  description: 'Include field descriptions as comments',
                  default: true,
                },
                formatStyle: {
                  type: 'string',
                  enum: ['compact', 'readable'],
                  description: 'Output format style',
                  default: 'readable',
                },
              },
              required: ['operationName'],
            },
          },
          {
            name: 'generate_fragment',
            description: 'Generate a GraphQL fragment for a type',
            inputSchema: {
              type: 'object',
              properties: {
                typeName: {
                  type: 'string',
                  description: 'Name of the type for the fragment',
                },
                includeComments: {
                  type: 'boolean',
                  description: 'Include field descriptions as comments',
                  default: true,
                },
                formatStyle: {
                  type: 'string',
                  enum: ['compact', 'readable'],
                  description: 'Output format style',
                  default: 'readable',
                },
              },
              required: ['typeName'],
            },
          },
          {
            name: 'generate_type_definition',
            description: 'Generate TypeScript interface or GraphQL type definition',
            inputSchema: {
              type: 'object',
              properties: {
                typeName: {
                  type: 'string',
                  description: 'Name of the type',
                },
                outputFormat: {
                  type: 'string',
                  enum: ['graphql', 'typescript'],
                  description: 'Output format',
                  default: 'graphql',
                },
                includeComments: {
                  type: 'boolean',
                  description: 'Include descriptions as comments',
                  default: true,
                },
              },
              required: ['typeName'],
            },
          },
        ],
      };
    });

    this.server.setRequestHandler(CallToolRequestSchema, async request => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          case 'configure_endpoint':
            return await this.configureEndpoint(args);
          case 'introspect_schema':
            return await this.introspectSchema();
          case 'list_queries':
            return await this.listQueries(args);
          case 'list_mutations':
            return await this.listMutations(args);
          case 'list_types':
            return await this.listTypes(args);
          case 'get_type_details':
            return await this.getTypeDetails(args);
          case 'search_operations':
            return await this.searchOperations(args);
          case 'generate_query':
            return await this.generateQuery(args);
          case 'generate_mutation':
            return await this.generateMutation(args);
          case 'generate_fragment':
            return await this.generateFragment(args);
          case 'generate_type_definition':
            return await this.generateTypeDefinition(args);
          default:
            throw new McpError(ErrorCode.MethodNotFound, `Unknown tool: ${name}`);
        }
      } catch (error) {
        const message = error instanceof Error ? error.message : 'Unknown error';
        throw new McpError(ErrorCode.InternalError, message);
      }
    });
  }

  private async configureEndpoint(args: any) {
    const config: GraphQLEndpointConfig = {
      url: args.url,
      headers: args.headers || {},
    };

    this.introspector = new GraphQLIntrospector(config);
    this.generator = new GraphQLGenerator(this.introspector);

    return {
      content: [
        {
          type: 'text',
          text: `✅ GraphQL endpoint configured: ${config.url}\\nHeaders: ${Object.keys(config.headers || {}).length} custom headers`,
        },
      ],
    };
  }

  private async introspectSchema() {
    if (!this.introspector) {
      throw new Error('Please configure the endpoint first using configure_endpoint');
    }

    this.schema = await this.introspector.introspect();

    const stats = {
      queries: this.introspector.getQueries(this.schema).length,
      mutations: this.introspector.getMutations(this.schema).length,
      types: this.introspector.getTypes(this.schema).length,
    };

    return {
      content: [
        {
          type: 'text',
          text: `✅ Schema introspected successfully!\\n\\n📊 Statistics:\\n- Queries: ${stats.queries}\\n- Mutations: ${stats.mutations}\\n- Types: ${stats.types}`,
        },
      ],
    };
  }

  private async listQueries(args: any) {
    this.ensureSchemaLoaded();

    const options = {
      includeDeprecated: args.includeDeprecated || false,
      fieldFilter: args.filter ? [args.filter] : undefined,
    };

    const queries = this.introspector!.getQueries(this.schema!, options);

    const output = queries
      .map(query => {
        const signature = this.introspector!.getFieldSignature(query);
        const deprecated = query.isDeprecated ? ' [DEPRECATED]' : '';
        const description = query.description ? `\\n  ${query.description}` : '';

        return `• ${signature}${deprecated}${description}`;
      })
      .join('\\n\\n');

    return {
      content: [
        {
          type: 'text',
          text: `🔍 GraphQL Queries (${queries.length} found):\\n\\n${output}`,
        },
      ],
    };
  }

  private async listMutations(args: any) {
    this.ensureSchemaLoaded();

    const options = {
      includeDeprecated: args.includeDeprecated || false,
      fieldFilter: args.filter ? [args.filter] : undefined,
    };

    const mutations = this.introspector!.getMutations(this.schema!, options);

    const output = mutations
      .map(mutation => {
        const signature = this.introspector!.getFieldSignature(mutation);
        const deprecated = mutation.isDeprecated ? ' [DEPRECATED]' : '';
        const description = mutation.description ? `\\n  ${mutation.description}` : '';

        return `• ${signature}${deprecated}${description}`;
      })
      .join('\\n\\n');

    return {
      content: [
        {
          type: 'text',
          text: `⚡ GraphQL Mutations (${mutations.length} found):\\n\\n${output}`,
        },
      ],
    };
  }

  private async listTypes(args: any) {
    this.ensureSchemaLoaded();

    let types = this.introspector!.getTypes(this.schema!);

    if (args.kind) {
      types = types.filter(type => type.kind === args.kind);
    }

    if (args.filter) {
      const regex = new RegExp(args.filter, 'i');
      types = types.filter(type => type.name && regex.test(type.name));
    }

    const output = types
      .map(type => {
        const description = type.description ? ` - ${type.description}` : '';
        return `• ${type.name} (${type.kind})${description}`;
      })
      .join('\\n');

    return {
      content: [
        {
          type: 'text',
          text: `📋 GraphQL Types (${types.length} found):\\n\\n${output}`,
        },
      ],
    };
  }

  private async getTypeDetails(args: any) {
    this.ensureSchemaLoaded();

    const type = this.introspector!.getType(this.schema!, args.typeName);

    if (!type) {
      throw new Error(`Type '${args.typeName}' not found`);
    }

    let details = `🏷️ Type: ${type.name} (${type.kind})\\n`;

    if (type.description) {
      details += `📝 Description: ${type.description}\\n`;
    }

    if (type.fields && type.fields.length > 0) {
      details += `\\n🔧 Fields (${type.fields.length}):\\n`;
      type.fields.forEach(field => {
        const signature = this.introspector!.getFieldSignature(field);
        const description = field.description ? ` - ${field.description}` : '';
        details += `  • ${signature}${description}\\n`;
      });
    }

    if (type.inputFields && type.inputFields.length > 0) {
      details += `\\n📥 Input Fields (${type.inputFields.length}):\\n`;
      type.inputFields.forEach(field => {
        const typeStr = this.introspector!.formatTypeString(field.type);
        const description = field.description ? ` - ${field.description}` : '';
        details += `  • ${field.name}: ${typeStr}${description}\\n`;
      });
    }

    if (type.enumValues && type.enumValues.length > 0) {
      details += `\\n🏷️ Enum Values (${type.enumValues.length}):\\n`;
      type.enumValues.forEach(value => {
        const description = value.description ? ` - ${value.description}` : '';
        const deprecated = value.isDeprecated ? ' [DEPRECATED]' : '';
        details += `  • ${value.name}${deprecated}${description}\\n`;
      });
    }

    return {
      content: [
        {
          type: 'text',
          text: details.trim(),
        },
      ],
    };
  }

  private async searchOperations(args: any) {
    this.ensureSchemaLoaded();

    const results = this.introspector!.searchOperations(this.schema!, args.searchTerm);

    let output = `🔍 Search results for "${args.searchTerm}":\\n\\n`;

    if (results.queries.length > 0) {
      output += `📋 Queries (${results.queries.length}):\\n`;
      results.queries.forEach(query => {
        output += `  • ${query.name}\\n`;
      });
      output += '\\n';
    }

    if (results.mutations.length > 0) {
      output += `⚡ Mutations (${results.mutations.length}):\\n`;
      results.mutations.forEach(mutation => {
        output += `  • ${mutation.name}\\n`;
      });
      output += '\\n';
    }

    if (results.subscriptions.length > 0) {
      output += `📡 Subscriptions (${results.subscriptions.length}):\\n`;
      results.subscriptions.forEach(subscription => {
        output += `  • ${subscription.name}\\n`;
      });
      output += '\\n';
    }

    const total = results.queries.length + results.mutations.length + results.subscriptions.length;
    if (total === 0) {
      output += 'No operations found matching your search term.';
    }

    return {
      content: [
        {
          type: 'text',
          text: output.trim(),
        },
      ],
    };
  }

  private async generateQuery(args: any) {
    this.ensureSchemaLoaded();

    const options = {
      includeComments: args.includeComments !== false,
      formatStyle: (args.formatStyle || 'readable') as 'compact' | 'readable',
    };

    const query = this.generator!.generateQuery(this.schema!, args.operationName, options);

    return {
      content: [
        {
          type: 'text',
          text: `🔍 Generated GraphQL Query:\\n\\n\`\`\`graphql\\n${query}\\n\`\`\``,
        },
      ],
    };
  }

  private async generateMutation(args: any) {
    this.ensureSchemaLoaded();

    const options = {
      includeComments: args.includeComments !== false,
      formatStyle: (args.formatStyle || 'readable') as 'compact' | 'readable',
    };

    const mutation = this.generator!.generateMutation(this.schema!, args.operationName, options);

    return {
      content: [
        {
          type: 'text',
          text: `⚡ Generated GraphQL Mutation:\\n\\n\`\`\`graphql\\n${mutation}\\n\`\`\``,
        },
      ],
    };
  }

  private async generateFragment(args: any) {
    this.ensureSchemaLoaded();

    const options = {
      includeComments: args.includeComments !== false,
      formatStyle: (args.formatStyle || 'readable') as 'compact' | 'readable',
    };

    const fragment = this.generator!.generateFragment(this.schema!, args.typeName, options);

    return {
      content: [
        {
          type: 'text',
          text: `🧩 Generated GraphQL Fragment:\\n\\n\`\`\`graphql\\n${fragment}\\n\`\`\``,
        },
      ],
    };
  }

  private async generateTypeDefinition(args: any) {
    this.ensureSchemaLoaded();

    const options = {
      includeComments: args.includeComments !== false,
      formatStyle: 'readable' as const,
    };

    const typeDefinition = this.generator!.generateTypeDefinition(
      this.schema!,
      args.typeName,
      options
    );
    const format = args.outputFormat || 'graphql';

    return {
      content: [
        {
          type: 'text',
          text: `🏷️ Generated ${format.toUpperCase()} Type Definition:\\n\\n\`\`\`${format}\\n${typeDefinition}\\n\`\`\``,
        },
      ],
    };
  }

  private ensureSchemaLoaded() {
    if (!this.schema || !this.introspector || !this.generator) {
      throw new Error('Please configure endpoint and introspect schema first');
    }
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('GraphQL Introspection MCP Server running on stdio');
  }
}

const server = new GraphQLMCPServer();
server.run().catch(console.error);
