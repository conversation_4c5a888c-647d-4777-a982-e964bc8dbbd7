# fastlane/Fastfile (for iOS)

default_platform(:ios)

platform :ios do
  desc "Build and archive the iOS app"
  lane :build do
    # Ensure you have your Apple Developer Portal credentials set up as environment variables
    # or use Fastlane match for code signing.
    # Example using match:
    # match(type: "appstore", readonly: is_ci) # 'appstore' for distribution, 'readonly: true' for CI

    gym(
      scheme: "Movuca", # Replace with your app's scheme (usually your app name)
      export_method: "app-store", # or "development", "ad-hoc", "enterprise"
      output_directory: "./build",
      output_name: "Movuca.ipa"
    )
  end

  desc "Submit to TestFlight"
  lane :testflight do
    # Ensure you have your App Store Connect API Key set up as environment variables
    # or use the 'app_store_connect_api_key' action.
    # Example using App Store Connect API Key:
    # app_store_connect_api_key(
    #   key_id: ENV["APP_STORE_CONNECT_KEY_ID"],
    #   issuer_id: ENV["APP_STORE_CONNECT_ISSUER_ID"],
    #   key_content: ENV["APP_STORE_CONNECT_KEY_CONTENT"]
    # )

    upload_to_testflight(
      ipa: "./build/Movuca.ipa",
      skip_waiting_for_build_processing: true
    )
  end
end
