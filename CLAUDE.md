# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Essential Commands

- `bun start` - Start Expo development server with dev client
- `bun ios` - Run on iOS simulator
- `bun android` - Run on Android emulator
- `bun web` - Start web version

### Testing & Quality

- `bun test` - Run tests in watch mode
- `bun test:ci` - Run tests for CI (silent, max workers)
- `bun test:dev` - Development testing with coverage
- `bun lint` - ESLint + Prettier check
- `bun format` - Auto-fix linting and formatting

### Building

- `bun build:dev` - EAS development build
- `bun build:preview` - EAS preview build
- `bun build:prod` - EAS production build

## Project Architecture

### Technology Stack

- **React Native 0.79** with **React 19** and React Compiler enabled
- **Expo SDK 53** (no Expo Router, navigation handled by React Navigation)
- **Shopify Restyle** for comprehensive theming system (docs https://shopify.github.io/restyle/fundamentals)
- **<PERSON>ustand** for persistent state management with **MMKV persistence**
- **TanStack Query** for async operations and loading/error states
- **Apollo Client** for GraphQL (configured but ready for backend)
- **react-native-reanimated** for animations
- **React Native Skia v2.0.0-next.4** for high-performance graphics and animations
- **React Native Rive** for complex vector animations (when added)
- **React Native MMKV** for high-performance storage
- **date-fns & date-fns-tz** for timezone/localization
- **crypto-js** for encryption and security
- **i18next** for internationalization
- **Bun** package manager

### Global MVP Architecture

The app is designed for worldwide deployment with:

- **Internationalization**: Multi-language support (English, Spanish, Portuguese BR)
- **Timezone Handling**: Automatic timezone detection and formatting
- **Performance Optimization**: API caching, lazy loading, network adaptation
- **Security**: Encryption, secure token management, input sanitization
- **Network Resilience**: Retry logic, offline handling, data-saving modes

### Key Architectural Patterns

#### State Management Architecture

The app uses a hybrid state management approach optimized for modern React patterns:

**Zustand + MMKV** (Persistent State):

- User authentication data (`user`, `tokens`, `isAuthenticated`)
- App-level state (`isInitializing`, `isSigningOut`, `isOnboardingComplete`)
- Persisted to MMKV for offline access and session restoration

**TanStack Query** (Async Operations):

- All authentication operations (`login`, `register`, `verify`, etc.)
- Individual loading/error states per operation (no more conflicts!)
- Built-in retry logic, caching, and error handling
- Perfect for concurrent operations without state collisions

**Usage Pattern:**

```typescript
// Old way (conflicts possible):
const { emailLogin, isOperationLoading, error } = useAuth();

// New way (perfect isolation):
const emailLogin = useEmailLogin();
const emailRegister = useEmailRegister();

// Each operation has independent state:
emailLogin.isPending    // individual loading
emailLogin.error        // individual error
emailRegister.isPending // independent loading
```

#### Theming System (Shopify Restyle)

The app uses a sophisticated design system built on Restyle with:

- **Dual themes**: Light/dark mode with system detection
- **Semantic colors**: Use semantic color names, never hex codes
- **Spacing tokens**: Use precise spacing tokens, never hardcoded numbers
- **Typography variants**: Use predefined text variants for consistency
- **Component variants**: Pre-defined styles for buttons, cards, inputs, alerts, avatars

## Theme Token Reference

### Spacing Tokens (Format: `semantic_value`)

```
none_0, xxxs_2, xxs_4, xs_8, sm_12, md_16, ml_20, lg_24,
xl_32, xxl_40, xxxl_48, huge_64, giant_80, massive_128
```

### Border Radius Tokens

```
none_0, xxs_2, xs_4, sm_8, md_12, lg_16, xl_20, xxl_24, xxxl_32, circle_9999
```

### Semantic Color Names

**Backgrounds:**

- `background`, `surfaceBackground`, `surface`, `card`

**Text:**

- `text`, `textSecondary`, `textTertiary`, `textInverted`

**Brand:**

- `primary`, `primaryLight`, `primaryDark`
- `secondary`, `secondaryLight`, `secondaryDark`

**Feedback:**

- `success`, `warning`, `error`

**Interactive:**

- `border`, `disabled`, `transparent`

### Typography Variants (Format: `category_size_weight_usage`)

**Display/Headers:**

- `d_56Black_hero` - Hero text
- `h_48Black_splash` - Splash screens
- `H_40Bold_title` - Page titles
- `h_32SemiBold_Page` - Section headers
- `h_24SemiBold_section` - Subsection headers
- `h_20Medium_subsection` - Minor headers
- `h_18Bold_formTitle` - Form titles
- `h_18SemiBold_cardTitle` - Card titles

**Body Text:**

- `b_16Regular_input` - Input text, body content
- `b_16SemiBold_button` - Button text
- `b_14Regular_content` - General content
- `b_14Medium_button` - Small button text

**Labels:**

- `l_14Medium_formHelperText` - Form helper text
- `l_12Regular_helperText` - Helper text
- `l_12Medium_message` - Error messages
- `l_10SemiBold_tag` - Tags and chips

### Component Variants

**Avatar Sizes:**
`xs` (24px), `s` (32px), `m` (40px), `l` (48px), `xl` (56px), `2xl` (64px), `3xl` (102px)

**Button Variants:**
`primary`, `secondary`, `outline`, `ghost`, `danger`

**Alert Types:**
`success`, `info`, `warning`, `error`

**Always use themed components:**

```tsx
// ✅ Correct
import { Box, Text, Button } from '@/src/core/theme';
<Box padding="md_16" backgroundColor="background">
  <Text variant="h_24SemiBold_section" color="text">Title</Text>
  <Text variant="b_16Regular_input" color="textSecondary">Description</Text>
</Box>

// ❌ Never do this
import { View, Text } from 'react-native';
<View style={{ padding: 16, backgroundColor: '#fff' }}>
  <Text style={{ fontSize: 24, fontWeight: 'bold' }}>Title</Text>
</View>
```

#### Component Development Pattern

Follow the structured template in `docs/DEVELOPMENT_GUIDELINES.md`:

1. Use `forwardRef` for composition
2. Define TypeScript interfaces for props
3. Use Restyle variants for styling
4. Implement proper accessibility
5. Add animation with react-native-reanimated
6. Write comprehensive tests

#### React 19 Features

The app leverages cutting-edge React 19 features:

- **React Compiler**: Automatic optimization (no manual `useMemo`/`useCallback` needed)
- **useOptimistic**: For immediate UI feedback
- **useActionState**: For form and async state management
- **use API**: For conditional promise/context reading

### File Structure Conventions (Feature-Driven MVVM Architecture)

- `src/app/` - App-level screens (entry points for navigation)
- `src/shared/` - **Cross-feature shared utilities**
  - `components/` - Reusable UI components
  - `hooks/` - Custom React hooks (including `useGlobalPerformance`)
  - `forms/` - Form handling utilities (react-hook-form + Zod)
- `src/core/` - **Global infrastructure layer**
  - `storage/` - MMKV storage abstractions with multiple instances
  - `security/` - Encryption, token management, input validation
  - `api/` - HTTP client with caching, retry logic, and offline support
  - `i18n/` - Internationalization with timezone handling
  - `integrations/` - Third-party library abstractions for testing
  - `theme/` - Shopify Restyle theming system
  - `navigation/` - React Navigation configuration
- `src/features/` - **Feature modules following MVVM pattern**
  - `{feature}/screens/` - Screen components (View layer)
  - `{feature}/components/` - Feature-specific UI components (View layer)
  - `{feature}/hooks/` - Feature ViewModels (business logic)
  - `{feature}/services/` - Model layer (API calls, data processing)
  - `{feature}/store/` - Zustand state management with MMKV persistence
  - `{feature}/validation/` - Zod schemas for type-safe validation
  - `{feature}/types/` - Feature-specific TypeScript interfaces

### Component Naming and Exports

Components are typically exported as default and follow PascalCase. The main themed components are re-exported from `src/core/theme/index.ts` for convenience.

### Testing

- **Jest** with React Native Testing Library
- Test files use `.test.tsx` extension
- Setup files: `jestSetup.js`, `jestAfterEnvSetup.js`
- Coverage enabled by default
- Mock Expo modules and Reanimated for testing

## Development Guidelines

### When Creating New Components

1. **Read the development guidelines**: Always check `docs/DEVELOPMENT_GUIDELINES.md` first
2. **Use the component template**: Follow the structured pattern provided
3. **Leverage theme system**: Use variants, semantic colors, and spacing tokens
4. **Add TypeScript interfaces**: Comprehensive prop documentation
5. **Include accessibility**: Proper labels, roles, and state attributes
6. **Write tests**: Unit tests for functionality and user interactions

### Theme Usage Rules

1. **Always use semantic colors**: Use meaningful color names like `background`, `text`, `primary`, `textSecondary` - never hex codes
2. **Use spacing tokens**: Use format `semantic_value` like `xs_8`, `md_16`, `lg_24`, `xl_32` instead of hardcoded values
3. **Apply text variants**: Use descriptive variants like `h_24SemiBold_section`, `b_16Regular_input`, `l_12Medium_message` for consistent typography
4. **Component variants**: Leverage pre-defined variants for buttons (`primary`, `secondary`), alerts (`success`, `error`), avatars (`xs`, `m`, `xl`)
5. **Border radius**: Use tokens like `sm_8`, `md_12`, `lg_16`, `circle_9999` for consistent rounded corners
6. **Responsive design**: Use `useResponsive` hook for screen size adaptations

### Performance Best Practices

- **Let React Compiler optimize**: Don't manually add `useMemo`/`useCallback`
- **Use themed components**: They're optimized for performance
- **Implement animations with Reanimated**: Not Animated API
- **Use Skia for graphics**: High-performance custom drawings, gradients, effects
- **Use Rive for complex animations**: When vector animations require advanced features
- **Use Flash List**: For long lists instead of FlatList

### React 19 Development

- **useOptimistic**: For immediate user feedback (likes, adding items)
- **useActionState**: For managing form and async operation states
- **Actions**: For async operations with automatic pending states
- **No manual memoization**: React Compiler handles optimization automatically

### Authentication Development

The authentication system uses TanStack Query for perfect operation isolation:

**Available Hooks:**

```typescript
// Phone Authentication
usePhoneRegister();
usePhoneLogin();
useVerifyPhoneOTP();

// Email Authentication
useEmailRegister();
useEmailLogin();

// OTP Authentication
useOTPRegister();
useOTPLogin();
useVerifyEmailOTP();

// Passwordless Authentication
useRequestPasswordlessLogin();
useVerifyPasswordlessLogin();

// Social & Others
useSocialLogin();
useVerifyAccount();
useResendVerification();
useResetPassword();
```

**Usage Pattern:**

```typescript
// In components:
const emailLogin = useEmailLogin();

const handleLogin = data => {
  emailLogin.mutate(data, {
    onSuccess: response => {
      if (response.success) {
        // Handle success - tokens/user automatically saved to Zustand
        navigate('MainTabs');
      }
    },
    onError: error => {
      // Error already shown via toast in mutation
      console.error('Login failed:', error);
    },
  });
};

// Access loading/error states:
emailLogin.isPending; // individual loading state
emailLogin.error; // individual error state
emailLogin.isSuccess; // success state
```

**Key Benefits:**

- **No race conditions**: Each operation has isolated state
- **Concurrent operations**: Login + reset password simultaneously without conflicts
- **Better UX**: Individual loading states for each button/form
- **Automatic error handling**: Toast notifications built-in
- **Retry logic**: Configurable retry policies per operation

## Universal Development Patterns

### Service Layer Architecture (All Features)

The authentication implementation establishes a **foundational pattern** that should be applied to ALL features in the application:

#### 1. Service Layer Pattern

```typescript
// Feature Service (e.g., postService.ts, profileService.ts, etc.)
export type ServiceResponse<T> =
  | { success: true; data: T; meta?: any }
  | { success: false; errors: ErrorResponse[]; networkError?: any };

class FeatureService {
  async operation(input: InputType): Promise<ServiceResponse<DataType>> {
    try {
      const { data, errors } = await this.client.mutate({
        mutation: OPERATION_MUTATION,
        variables: { input },
      });

      if (data?.operation?.success) {
        return {
          success: true,
          data: data.operation,
        };
      }

      return {
        success: false,
        errors: errors as ErrorResponse[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }
}
```

#### 2. TanStack Query Hooks Pattern

```typescript
// Feature Hooks (e.g., usePostMutations.ts, useProfileMutations.ts)
export const useCreatePost = () => {
  const toast = useToast();

  return useMutation({
    mutationFn: (input: CreatePostInput) => postService.createPost(input),
    onSuccess: response => {
      if (response.success) {
        toast.show({
          type: 'success',
          text1: 'Post created successfully',
        });
        // Handle success logic
      } else {
        toast.show({
          type: 'error',
          text1: 'Failed to create post',
          text2: extractErrorMessage(response.errors),
        });
      }
    },
    onError: error => {
      toast.show({
        type: 'error',
        text1: 'Network error',
        text2: error.message,
      });
    },
  });
};
```

#### 3. State Management Pattern (Zustand + TanStack Query)

```typescript
// Feature Store (persistent state only)
interface FeatureStore {
  // Persistent data only
  cachedData: DataType[];
  preferences: PreferenceType;

  // Actions for persistent state
  setCachedData: (data: DataType[]) => void;
  updatePreferences: (prefs: PreferenceType) => void;
}

// TanStack Query handles:
// - Operation states (loading, error, success)
// - Server state management
// - Caching and invalidation
// - Background refetching
```

#### 4. Component Usage Pattern

```typescript
// In any feature component
const MyFeatureComponent = () => {
  const createPost = useCreatePost();
  const updateProfile = useUpdateProfile();

  const handleCreate = (data) => {
    createPost.mutate(data); // Independent loading state
  };

  const handleUpdate = (data) => {
    updateProfile.mutate(data); // Concurrent operation, no conflicts
  };

  return (
    <Box>
      <Button
        onPress={handleCreate}
        loading={createPost.isPending}
        disabled={updateProfile.isPending}
      >
        Create Post
      </Button>

      <Button
        onPress={handleUpdate}
        loading={updateProfile.isPending}
        disabled={createPost.isPending}
      >
        Update Profile
      </Button>
    </Box>
  );
};
```

### Universal Benefits of This Pattern

1. **Isolated Operation States**: No more global loading/error conflicts
2. **Concurrent Operations**: Multiple requests without state pollution
3. **Consistent Error Handling**: Standardized error format across all features
4. **Better UX**: Individual loading states for every action
5. **Automatic Retry Logic**: Built-in retry policies per operation
6. **Type Safety**: Full TypeScript coverage for requests/responses
7. **Testing Friendly**: Easy to mock and test individual operations
8. **Scalable Architecture**: Pattern works for any feature complexity

### Implementation Checklist for New Features

When implementing any new feature, follow this checklist:

- [ ] Create service class with consistent error handling (no throwing)
- [ ] Define typed Response unions with success discriminator
- [ ] Implement TanStack Query mutation hooks with toast notifications
- [ ] Use Zustand only for persistent state (user data, preferences, cache)
- [ ] Extract error handling into reusable helper functions
- [ ] Test concurrent operations to ensure no state conflicts
- [ ] Verify individual loading states work correctly

This pattern ensures consistency, maintainability, and excellent user experience across the entire application.

## Optimal Form Validation Pattern

### Real-time Button Enabling with Excellent UX

For forms that need immediate button enabling without showing premature validation errors, use this proven pattern:

```typescript
// Form setup with onBlur mode to prevent premature errors
const { control, handleSubmit, formState, trigger, watch } = useZodForm(schema, {
  mode: 'onBlur',           // Errors only show when user leaves field
  reValidateMode: 'onChange', // Clear errors immediately when fixed
  defaultValues: {          // Required for watch() to work from start
    field1: '',
    field2: '',
  },
});

// Watch form values for real-time validation
const watchedValues = watch();

// Use Zod safeParse for button enabling (doesn't affect form error state)
const isInputValid = React.useMemo(() => {
  const result = schema.safeParse({
    field1: watchedValues.field1 || '',
    field2: watchedValues.field2 || '',
  });
  return result.success;
}, [watchedValues.field1, watchedValues.field2]);

// Enable button when input passes validation OR form is officially valid
const isFormValid = formState.isValid || isInputValid;

// In field onChange handlers
onChangeText={value => {
  field.onChange(value);
  // Only trigger validation if there's an error (to clear it immediately)
  if (formState.errors.fieldName?.message) {
    trigger('fieldName');
  }
}}
```

**Key Benefits:**

- ✅ **Button enables immediately** when input is valid
- ✅ **No premature error messages** while typing
- ✅ **Errors appear only onBlur** (when user finishes with field)
- ✅ **Errors clear immediately** when user fixes them
- ✅ **Uses actual Zod schema** for validation logic consistency
- ✅ **No duplicate validation logic** - single source of truth

**User Experience Flow:**

1. User types valid data → Button enables immediately
2. User leaves field with invalid data → Error appears
3. User fixes error → Error disappears immediately
4. No need to blur fields when data is valid

This approach provides the optimal balance between immediate feedback and non-intrusive validation.

## Testing Requirements

Before committing changes:

1. **Run lint and format**: `bun lint` and `bun format`
2. **Run tests**: `bun test:ci` for full test suite
3. **Check TypeScript**: Ensure no type errors
4. **Test component functionality**: User interactions and state changes
5. **Verify accessibility**: Screen reader compatibility and proper labels

## Multi-language Support

The app supports internationalization with:

- **i18next** configuration in `src/core/i18n/`
- Language files in `languages/` directory
- Supported locales: en, en-GB, es, pt-BR
- Use translation keys in components, not hardcoded strings

## GraphQL Integration

Apollo Client is configured but ready for backend integration:

- Configuration in `src/core/api/config.ts`
- Authentication link setup for token handling
- Ready to connect to GraphQL endpoint

## Animation and Graphics Architecture

### Animation Layer Selection

The app uses a three-tier animation architecture:

1. **React Native Reanimated**: For UI animations (gestures, transitions, layout animations)
2. **React Native Skia**: For high-performance graphics, custom drawings, shaders, and visual effects
3. **React Native Rive**: For complex vector animations created in Rive editor

**Decision Matrix:**

- **Reanimated**: UI animations, gesture-driven interactions, layout transitions
- **Skia**: Custom graphics, data visualizations, shaders, complex drawings
- **Rive**: Character animations, complex vector graphics from design tools

### Skia Integration

- Version: `v2.0.0-next.4` (latest with React 19 compatibility)
- Use for: Custom graphics, gradients, paths, image effects, data visualizations
- Performance: 60fps+ guaranteed for complex graphics operations
- See `docs/SKIA_RIVE_GUIDELINES.md` for detailed implementation patterns

### Rive Integration (Planned)

- For complex vector animations created in Rive editor
- Character animations, interactive graphics, state machines
- Design-to-code workflow for animations

## Global MVP Features for Worldwide Deployment

### Internationalization & Localization

- **Multi-language support**: English, Spanish, Portuguese (Brazil), English (UK)
- **Automatic locale detection**: System locale detection with fallbacks
- **Timezone handling**: Automatic timezone detection and formatting
- **Date/time localization**: Regional date, time, and number formatting
- **Currency support**: Multi-currency formatting based on locale
- **RTL ready**: Architecture supports RTL languages (future expansion)

### Performance Optimization for Global Users

- **API Caching**: Intelligent caching with TTL and category management
- **Network adaptation**: Automatic optimization for slow/expensive connections
- **Lazy loading**: Component and image lazy loading for faster initial load
- **Data-saving mode**: Reduced data usage on expensive connections
- **Offline resilience**: Graceful handling of poor connectivity
- **Background optimization**: Cache cleanup and prefetching strategies

### Security for Global Compliance

- **Encryption**: AES-256 encryption for sensitive data storage
- **Secure token management**: JWT token storage with automatic refresh
- **Input sanitization**: XSS prevention and data validation
- **Password validation**: Strength checking and secure generation
- **Biometric integration**: Ready for fingerprint/face authentication

### Storage & Caching Architecture

- **MMKV Integration**: High-performance storage with multiple instances
  - `appStorage`: General app preferences and settings
  - `cacheStorage`: API response and temporary data caching
  - `secureStorage`: Sensitive data with encryption
  - `userStorage`: User-specific data with Zustand persistence
- **Automatic cleanup**: Expired cache removal and size management
- **Migration ready**: Version-aware storage for smooth updates

### API Layer for Global Scale

- **Retry logic**: Exponential backoff for unreliable connections
- **Request timeout**: Adaptive timeouts based on connection quality
- **Error handling**: Comprehensive error types and user-friendly messages
- **File uploads**: Progress tracking and chunked uploads
- **Background sync**: Ready for background data synchronization

## Important Notes

- **Package manager**: Use `bun` commands, not npm/yarn
- **Import paths**: Use `@/` alias for absolute imports from src directory
- **Storage**: Always use MMKV abstractions, never direct AsyncStorage
- **Internationalization**: Use `useTranslation` hook and translation keys
- **Performance**: Leverage `useGlobalPerformance` for network optimization
- **Security**: Use core security utilities for encryption and validation
- **New Architecture**: React Native's New Architecture is enabled
- **React Compiler**: Eliminates need for manual performance optimizations
- **React Navigation**: Use React Navigation strategies for stack, tab, and drawer navigators; follow its best practices for navigation structure
- **EAS Build**: Three profiles (development, preview, production)
- **Graphics Performance**: Leverage Skia for 60fps+ graphics and Rive for complex animations
