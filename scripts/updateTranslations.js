#!/usr/bin/env node
/**
 * Update translation JSON files by ensuring they all contain the same key set
 * as the English base (en.json). Missing keys are added with an empty string
 * so translators can easily spot what still needs work. Additionally, a
 * `translation_matrix.json` file is generated that lists every key and the
 * translation for each language. This can be imported into spreadsheets or
 * other tooling.
 *
 * Usage:
 *   node scripts/updateTranslations.js          # update files + generate matrix
 *   node scripts/updateTranslations.js --matrix-only   # only generate matrix
 */

const fs = require('fs');
const path = require('path');
const process = require('process');

const translationsDir = path.join(process.cwd(), 'translations');

if (!fs.existsSync(translationsDir)) {
  console.error(`Translations directory not found: ${translationsDir}`);
  process.exit(1);
}

const files = fs.readdirSync(translationsDir).filter(f => f.endsWith('.json'));

if (!files.includes('en.json')) {
  console.error('Missing base translation file "en.json". Aborting.');
  process.exit(1);
}

const matrixOnly = process.argv.includes('--matrix-only');

/**
 * Recursively flatten a nested object using dot-notation keys.
 */
function flatten(obj, prefix = '') {
  const result = {};
  for (const [key, value] of Object.entries(obj)) {
    const nextKey = prefix ? `${prefix}.${key}` : key;
    if (value && typeof value === 'object' && !Array.isArray(value)) {
      Object.assign(result, flatten(value, nextKey));
    } else {
      result[nextKey] = value;
    }
  }
  return result;
}

/**
 * Convert a flattened dot-notation object back to a nested structure.
 */
function unflatten(flat) {
  const result = {};
  for (const [compoundKey, value] of Object.entries(flat)) {
    const parts = compoundKey.split('.');
    let current = result;
    parts.forEach((part, index) => {
      if (index === parts.length - 1) {
        current[part] = value;
      } else {
        current[part] = current[part] || {};
        current = current[part];
      }
    });
  }
  return result;
}

const enPath = path.join(translationsDir, 'en.json');
const enRaw = fs.readFileSync(enPath, 'utf8');
const enData = JSON.parse(enRaw);
const enFlat = flatten(enData);

// Build matrix object where each key maps to per-language translations
const matrix = {};
Object.keys(enFlat).forEach(k => {
  matrix[k] = { en: enFlat[k] };
});

for (const file of files) {
  const lang = path.basename(file, '.json');
  if (lang === 'en') continue; // skip English; it's the base

  const filePath = path.join(translationsDir, file);
  const raw = fs.readFileSync(filePath, 'utf8');
  const data = JSON.parse(raw);
  const flat = flatten(data);

  // Fill matrix columns (default to English for missing/empty values)
  Object.keys(enFlat).forEach(key => {
    const engVal = enFlat[key];
    const val = flat[key];
    matrix[key][lang] = val === undefined || val === '' ? engVal : val;
  });

  if (matrixOnly) continue; // No file mutation requested

  // Add missing or empty keys with English fallback
  let updated = false;
  for (const key of Object.keys(enFlat)) {
    if (flat[key] === undefined || flat[key] === '') {
      flat[key] = enFlat[key];
      updated = true;
    }
  }

  if (updated) {
    const newNested = unflatten(flat);
    fs.writeFileSync(filePath, `${JSON.stringify(newNested, null, 2)}\n`, 'utf8');
    console.log(`Added missing keys to ${file}`);
  }
}

// Write the matrix file
const matrixPath = path.join(translationsDir, 'translation_matrix.json');
fs.writeFileSync(matrixPath, `${JSON.stringify(matrix, null, 2)}\n`, 'utf8');
console.log(`Translation matrix written to ${matrixPath}`);

// Optional CSV generation
const csvRequested = process.argv.includes('--csv') || process.argv.includes('--csv-only');
if (csvRequested) {
  // Collect all languages present in the matrix (ensure 'en' comes first)
  const languages = Array.from(
    new Set(Object.values(matrix).flatMap(obj => Object.keys(obj)))
  ).sort((a, b) => {
    if (a === 'en') return -1;
    if (b === 'en') return 1;
    return a.localeCompare(b);
  });

  // CSV header
  const rows = [];
  rows.push(['key', ...languages].join(','));

  const escapeCsv = value => {
    if (value === null || value === undefined) return '';
    const str = String(value);
    if (/[",\n]/.test(str)) {
      return '"' + str.replace(/"/g, '""') + '"';
    }
    return str;
  };

  // CSV body
  for (const key of Object.keys(matrix)) {
    const row = [escapeCsv(key)];
    for (const lang of languages) {
      row.push(escapeCsv(matrix[key][lang] ?? ''));
    }
    rows.push(row.join(','));
  }

  const csvContent = rows.join('\n') + '\n';
  const csvPath = path.join(translationsDir, 'translation_matrix.csv');
  fs.writeFileSync(csvPath, csvContent, 'utf8');
  console.log(`Translation CSV written to ${csvPath}`);
}
