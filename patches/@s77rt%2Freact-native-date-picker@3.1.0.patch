diff --git a/ios/RTNDatePickerView.swift b/ios/RTNDatePickerView.swift
index 8789a77fea25a61efd645b8c8a2050d9359751c3..458f2751c2e670cfabc8a30d60a8dac4bac3aae3 100644
--- a/ios/RTNDatePickerView.swift
+++ b/ios/RTNDatePickerView.swift
@@ -12,7 +12,7 @@ struct RTNDatePickerView: View {
   var datePicker: some View {
     if viewModel.isMultiple {
       MultiDatePicker(selection: $viewModel.valueMulti, in: viewModel.range)
-        .onChange(of: viewModel.valueMulti) {
+        .onChange(of: viewModel.valueMulti) { _ in
           var dates = Set<Date>(minimumCapacity: viewModel.valueMulti.count)
           for dateComponents in viewModel.valueMulti {
             if let date = calendar.date(from: dateComponents) {
@@ -33,7 +33,9 @@ struct RTNDatePickerView: View {
         locale: viewModel.locale
       )
       .id("\(viewModel.type)-\(viewModel.mode)")
-      .onChange(of: viewModel.value) { onChange(Set(arrayLiteral: viewModel.value)) }
+      .onChange(of: viewModel.value) { _ in
+        onChange(Set(arrayLiteral: viewModel.value))
+      }
     }
   }
 
@@ -65,7 +67,7 @@ struct RTNDatePickerView: View {
           }
           .frame(width: 320)
           .padding()
-          .presentationBackground(Color.black.opacity(0.3))
+          .backgroundOverlayCompatible()
           .background(Color(UIColor.systemBackground).cornerRadius(20))
           .accentColor(viewModel.accentColor)
         }
@@ -75,3 +77,15 @@ struct RTNDatePickerView: View {
     }
   }
 }
+
+extension View {
+  /// Adds `presentationBackground` only on iOS 16.4+
+  @ViewBuilder
+  func backgroundOverlayCompatible() -> some View {
+    if #available(iOS 16.4, *) {
+      self.presentationBackground(Color.black.opacity(0.3))
+    } else {
+      self
+    }
+  }
+}
