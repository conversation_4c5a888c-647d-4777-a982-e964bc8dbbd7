import configPlugins from '@expo/config-plugins';
import { ExpoConfig } from 'expo/config';

const { withAndroidManifest } = configPlugins;

const withGoogleMapsApiKey = (config: ExpoConfig) => {
  return withAndroidManifest(config, async config => {
    const androidManifest = config.modResults;

    // Get the API key from the config
    const apiKey = config.android?.config?.googleMaps?.apiKey;

    if (!apiKey) {
      console.warn('Google Maps API key not found in app.json/app.config.ts');
      return config;
    }

    // Get the application element
    const mainApplication = androidManifest.manifest.application?.[0];

    if (mainApplication) {
      // Check if meta-data array exists, if not create it
      if (!mainApplication['meta-data']) {
        mainApplication['meta-data'] = [];
      }

      // Check if the Google Maps API key meta-data already exists
      const existingMetaData = mainApplication['meta-data'].find(
        (meta: any) => meta.$?.['android:name'] === 'com.google.android.geo.API_KEY'
      );

      if (existingMetaData) {
        // Update existing meta-data
        existingMetaData.$['android:value'] = apiKey;
      } else {
        // Add new meta-data
        mainApplication['meta-data'].push({
          $: {
            'android:name': 'com.google.android.geo.API_KEY',
            'android:value': apiKey,
          },
        });
      }
    }

    return config;
  });
};

export default withGoogleMapsApiKey;
