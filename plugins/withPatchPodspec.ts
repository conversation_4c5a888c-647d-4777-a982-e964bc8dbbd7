import configPlugins from '@expo/config-plugins';
import { ExpoConfig } from 'expo/config';
import fs from 'fs';
import path from 'path';

const { withDangerousMod } = configPlugins;

function withPatchPodspec(config: ExpoConfig) {
  return withDangerousMod(config, [
    'ios',
    async config => {
      const podspecPath = path.resolve(
        config.modRequest.projectRoot,
        'node_modules/@s77rt/react-native-date-picker/react-native-date-picker.podspec'
      );
      let content = fs.readFileSync(podspecPath, 'utf8');

      // Replace any iOS 17.0 requirement with 16.0
      content = content.replace(/:ios => ["']17\.0["']/, ':ios => "16.5"');
      content = content.replace(
        /s\.platform\s*=\s*:ios,\s*["']17\.0["']/,
        's.platform = :ios, "16.5"'
      );
      content = content.replace(
        /s\.ios\.deployment_target\s*=\s*["']17\.0["']/,
        's.ios.deployment_target = "16.5"'
      );

      fs.writeFileSync(podspecPath, content, 'utf8');
      return config;
    },
  ]);
}

export default withPatchPodspec;
