import configPlugins from '@expo/config-plugins';
import { ExpoConfig } from 'expo/config';

const { withAndroidManifest } = configPlugins;

const withHardwareAccelerated = (config: ExpoConfig) => {
  return withAndroidManifest(config, config => {
    const application = config?.modResults?.manifest?.application?.[0];
    if (application) {
      application.$['android:hardwareAccelerated'] = 'true';
    }
    return config;
  });
};

export default withHardwareAccelerated;
