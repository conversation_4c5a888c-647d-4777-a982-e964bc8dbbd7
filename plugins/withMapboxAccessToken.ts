import configPlugins from '@expo/config-plugins';
import { ExpoConfig } from 'expo/config';

const { setStringItem } = configPlugins.AndroidConfig.Strings;

const withMapboxAccessToken = (config: ExpoConfig) => {
  return configPlugins.withStringsXml(config, async config => {
    // Get the Mapbox access token from environment variables
    const accessToken = process.env.MAPBOX_SECRET_API_KEY;

    if (!accessToken) {
      console.warn('Mapbox access token not found in environment variables');
      return config;
    }

    // Add or update the mapbox_access_token string resource
    config.modResults = setStringItem(
      [
        {
          $: {
            name: 'mapbox_access_token',
            translatable: 'false',
          },
          _: accessToken,
        },
      ],
      config.modResults
    );

    return config;
  });
};

export default withMapboxAccessToken;
