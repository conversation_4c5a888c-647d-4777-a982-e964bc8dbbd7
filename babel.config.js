const ReactCompilerConfig = {
  target: '19', // '17' | '18' | '19'
};

module.exports = function (api) {
  api.cache(true);
  const plugins = [['babel-plugin-react-compiler', ReactCompilerConfig]];

  return {
    presets: [
      [
        'babel-preset-expo',
        {
          // Enable the import.meta polyfill for Hermes
          unstable_transformImportMeta: true,
        },
      ],
    ],

    plugins,
  };
};
