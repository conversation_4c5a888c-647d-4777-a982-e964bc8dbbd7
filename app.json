{"expo": {"android": {"adaptiveIcon": {"backgroundColor": "#FAFAFA", "foregroundImage": "./assets/adaptive_icon.png"}, "edgeToEdgeEnabled": true, "package": "com.movuca.app", "permissions": ["android.permission.CAMERA", "android.permission.LOCATION", "android.permission.READ_PHONE_STATE"], "userInterfaceStyle": "automatic", "config": {"googleMaps": {"apiKey": "process.env.EXPO_PUBLIC_GOOGLE_MAPS_API_KEY"}}}, "assetBundlePatterns": ["**/*"], "backgroundColor": "#FAFAFA", "experiments": {"reactCompiler": true, "tsconfigPaths": true}, "icon": "./assets/icon.png", "ios": {"usesAppleSignIn": true, "bundleIdentifier": "com.movuca.app", "infoPlist": {"CFBundleAllowMixedLocalizations": true, "ITSAppUsesNonExemptEncryption": false, "UIBackgroundModes": ["fetch", "processing"]}, "supportsTablet": true}, "locales": {"en": "./languages/en.json", "en-GB": "./languages/en-GB.json", "it": "./languages/it.json", "fr": "./languages/fr.json", "es": "./languages/es.json", "pt": "./languages/pt-BR.json"}, "name": "movuca", "newArchEnabled": true, "orientation": "portrait", "plugins": ["expo-apple-authentication", ["expo-dev-launcher", {"launchMode": "most-recent"}], "expo-web-browser", "expo-localization", "expo-font", ["react-native-edge-to-edge", {"android": {"parentTheme": "Material3"}}], ["expo-asset", {"assets": ["./assets"]}], "expo-background-task"], "primaryColor": "#FAFAFA", "scheme": "movuca", "slug": "movuca", "splash": {"backgroundColor": "#ffffff", "image": "./assets/splash.png", "resizeMode": "contain"}, "userInterfaceStyle": "automatic", "version": "1.0.0", "web": {"bundler": "metro", "favicon": "./assets/favicon.png"}, "extra": {"fact": "kittens are cool", "eas": {"projectId": "1e51a21c-4353-48e9-858f-25c19fc833f4"}}, "owner": "movuca"}}