/* globals jest, require, global */
/**
 * Set up of the testing environment
 */

jest.mock('react-native-reanimated', () => {
  const Reanimated = require('react-native-reanimated/mock');

  // The mock misses the `addWhitelistedUIProps` implementation
  // So we override it with a no-op

  Reanimated.default.addWhitelistedUIProps = () => {};

  // Mock Keyframe constructor with proper chaining methods
  const mockKeyframe = function (config) {
    this.config = config;
    this.duration = jest.fn().mockReturnThis();
    this.delay = jest.fn().mockReturnThis();
    return this;
  };

  // Mock interpolate function
  const mockInterpolate = (value, inputRange, outputRange, extrapolate) => {
    if (typeof value === 'number') {
      // Simple linear interpolation for tests
      const ratio = (value - inputRange[0]) / (inputRange[1] - inputRange[0]);
      const clampedRatio = Math.max(0, Math.min(1, ratio));
      return outputRange[0] + (outputRange[1] - outputRange[0]) * clampedRatio;
    }
    return outputRange[0]; // fallback
  };

  return {
    ...Reanimated,
    useScrollViewOffset: jest.fn,
    useReducedMotion: jest.fn,
    Keyframe: mockKeyframe,
    LayoutAnimationConfig: ({ children }) => children,
    interpolate: mockInterpolate,
  };
});

global.__reanimatedWorkletInit = () => jest.fn();
