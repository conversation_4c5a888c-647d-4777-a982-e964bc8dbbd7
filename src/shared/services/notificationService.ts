/**
 * Notification Service - High-level service for managing notifications
 * Integrates with Notifee, provides localization, and manages notification state
 */
import { Platform } from 'react-native';

import { AndroidImportance } from '@notifee/react-native';
import { format } from 'date-fns';

import i18n from '@/src/core/i18n/config';
import { notification } from '@/src/core/libs/notification';
import { appStorage, userStorage } from '@/src/core/storage';
import type {
  AppNotification,
  NotificationChannel,
  NotificationPriority,
  NotificationSettings,
  NotificationTemplate,
  NotificationType,
  PushNotificationPayload,
  ScheduledNotification,
} from '@/src/shared/types/notification';

/**
 * Notification Service class with full localization and persistence
 */
export class NotificationService {
  private isInitialized = false;
  private eventCleanup?: () => void;
  private locale = i18n.language;

  /**
   * Initialize the notification service
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Create default notification channels
      await this.createDefaultChannels();

      // Set up event handlers
      this.setupEventHandlers();

      // Request permissions if not already granted
      await this.ensurePermissions();

      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize notification service:', error);
      throw error;
    }
  }

  /**
   * Clean up the notification service
   */
  cleanup(): void {
    if (this.eventCleanup) {
      this.eventCleanup();
      this.eventCleanup = undefined;
    }
    this.isInitialized = false;
  }

  /**
   * Request notification permissions
   */
  async requestPermissions(): Promise<boolean> {
    try {
      const permissions = await notification.requestPermission();
      return permissions.authorizationStatus === 1; // Authorized
    } catch (error) {
      console.error('Failed to request notification permissions:', error);
      return false;
    }
  }

  /**
   * Check if notifications are enabled
   */
  async areNotificationsEnabled(): Promise<boolean> {
    try {
      const permissions = await notification.getPermissions();
      return permissions.authorizationStatus === 1; // Authorized
    } catch (error) {
      console.error('Failed to check notification permissions:', error);
      return false;
    }
  }

  /**
   * Display a localized notification
   */
  async displayNotification(
    type: NotificationType,
    title: string,
    message: string,
    options: {
      priority?: NotificationPriority;
      data?: Record<string, any>;
      actions?: { id: string; label: string }[];
      image?: string;
      avatar?: string;
      deepLink?: string;
      groupId?: string;
      threadId?: string;
    } = {}
  ): Promise<string> {
    try {
      const settings = this.getNotificationSettings();

      // Check if this notification type is enabled
      if (!settings.enabled || !settings.categories[type]?.enabled) {
        console.log(`Notifications disabled for type: ${type}`);
        return '';
      }

      // Check quiet hours
      if (this.isInQuietHours(settings)) {
        console.log('In quiet hours, suppressing notification');
        return '';
      }

      const channelId = this.getChannelForType(type);
      const priority = options.priority || settings.categories[type]?.priority || 'normal';
      const categorySettings = settings.categories[type];

      const notificationId = await notification.displayNotification({
        title,
        body: message,
        data: {
          type,
          priority,
          timestamp: Date.now(),
          locale: this.locale,
          ...options.data,
        },
        sound: categorySettings?.sound && settings.sound ? 'default' : undefined,
        largeIcon: options.avatar || options.image,
        actions: options.actions?.map(action => ({
          id: action.id,
          title: action.label,
        })),
        ...(Platform.OS === 'android' && {
          channelId,
          importance: this.mapPriorityToImportance(priority),
          vibrationPattern:
            categorySettings?.vibration && settings.vibration ? [0, 250, 250, 250] : undefined,
          groupId: options.groupId,
          tag: options.threadId,
          largeIcon: options.avatar,
          bigPicture: options.image,
          autoCancel: true,
        }),
        ...(Platform.OS === 'ios' && {
          category: type,
          threadId: options.threadId,
          critical: priority === 'critical',
          interruptionLevel: this.mapPriorityToInterruptionLevel(priority),
        }),
      });

      // Store notification in local storage for history
      await this.storeNotificationHistory({
        id: notificationId,
        type,
        priority,
        status: 'unread',
        title,
        message,
        createdAt: new Date(),
        data: options.data,
        avatar: options.avatar,
        image: options.image,
        deepLink: options.deepLink,
        groupId: options.groupId,
        threadId: options.threadId,
      });

      return notificationId;
    } catch (error) {
      console.error('Failed to display notification:', error);
      throw error;
    }
  }

  /**
   * Schedule a notification for later
   */
  async scheduleNotification(
    type: NotificationType,
    title: string,
    message: string,
    scheduledFor: Date,
    options: {
      repeatFrequency?: 'daily' | 'weekly' | 'monthly' | 'yearly';
      data?: Record<string, any>;
      priority?: NotificationPriority;
    } = {}
  ): Promise<string> {
    try {
      const settings = this.getNotificationSettings();

      if (!settings.enabled || !settings.categories[type]?.enabled) {
        throw new Error(`Notifications disabled for type: ${type}`);
      }

      const channelId = this.getChannelForType(type);
      const priority = options.priority || 'normal';

      const notificationId = await notification.scheduleNotification({
        title,
        body: message,
        data: {
          type,
          priority,
          scheduled: true,
          locale: this.locale,
          ...options.data,
        },
        ...(Platform.OS === 'android' && {
          channelId,
          importance: this.mapPriorityToImportance(priority),
        }),
        ...(Platform.OS === 'ios' && {
          category: type,
        }),
        trigger: {
          type: 'timestamp',
          timestamp: scheduledFor.getTime(),
          repeatFrequency: options.repeatFrequency,
        },
      });

      // Store scheduled notification
      const scheduledNotification: ScheduledNotification = {
        id: notificationId,
        type,
        title,
        message,
        scheduledFor,
        repeatFrequency: options.repeatFrequency,
        data: options.data,
        active: true,
      };

      this.storeScheduledNotification(scheduledNotification);

      return notificationId;
    } catch (error) {
      console.error('Failed to schedule notification:', error);
      throw error;
    }
  }

  /**
   * Cancel a scheduled notification
   */
  async cancelScheduledNotification(notificationId: string): Promise<void> {
    try {
      await notification.cancelNotification(notificationId);
      this.removeScheduledNotification(notificationId);
    } catch (error) {
      console.error('Failed to cancel scheduled notification:', error);
      throw error;
    }
  }

  /**
   * Display notification from template
   */
  async displayFromTemplate(
    templateId: string,
    variables: Record<string, string> = {}
  ): Promise<string> {
    const template = this.getNotificationTemplate(templateId);
    if (!template) {
      throw new Error(`Template not found: ${templateId}`);
    }

    let title = template.title;
    let message = template.message;

    // Replace variables in title and message
    template.variables.forEach(variable => {
      const value = variables[variable] || `{{${variable}}}`;
      title = title.replace(new RegExp(`{{${variable}}}`, 'g'), value);
      message = message.replace(new RegExp(`{{${variable}}}`, 'g'), value);
    });

    return this.displayNotification(template.type, title, message, {
      priority: template.priority,
      actions: template.actions,
    });
  }

  /**
   * Handle push notification payload
   */
  async handlePushNotification(payload: PushNotificationPayload): Promise<void> {
    try {
      const { aps, notification: notificationData, data, android } = payload;

      // Extract notification details
      const title = aps?.alert?.title || notificationData?.title || 'Notification';
      const body = aps?.alert?.body || notificationData?.body || '';
      const type = (data?.type as NotificationType) || 'system';

      // Display the notification using our service
      await this.displayNotification(type, title, body, {
        data: data || {},
        priority: this.mapRemotePriorityToLocal(android?.priority),
        avatar: android?.largeIcon || notificationData?.icon,
        deepLink: data?.deepLink || notificationData?.click_action,
        groupId: data?.groupId,
        threadId: aps?.['thread-id'] || data?.threadId,
      });
    } catch (error) {
      console.error('Failed to handle push notification:', error);
    }
  }

  /**
   * Get notification settings
   */
  getNotificationSettings(): NotificationSettings {
    const defaultSettings: NotificationSettings = {
      enabled: true,
      sound: true,
      vibration: true,
      badge: true,
      showPreview: true,
      quietHours: {
        enabled: false,
        startTime: '22:00',
        endTime: '08:00',
      },
      categories: {
        system: { enabled: true, sound: true, vibration: true, priority: 'normal' },
        social: { enabled: true, sound: true, vibration: true, priority: 'normal' },
        marketing: { enabled: true, sound: false, vibration: false, priority: 'low' },
        security: { enabled: true, sound: true, vibration: true, priority: 'high' },
        reminder: { enabled: true, sound: true, vibration: false, priority: 'normal' },
        achievement: { enabled: true, sound: true, vibration: true, priority: 'normal' },
        chat: { enabled: true, sound: true, vibration: true, priority: 'high' },
        post: { enabled: true, sound: true, vibration: false, priority: 'normal' },
        follow: { enabled: true, sound: false, vibration: false, priority: 'low' },
        like: { enabled: true, sound: false, vibration: false, priority: 'low' },
        comment: { enabled: true, sound: true, vibration: false, priority: 'normal' },
        mention: { enabled: true, sound: true, vibration: true, priority: 'high' },
        trending: { enabled: true, sound: false, vibration: false, priority: 'low' },
      },
    };

    const saved = userStorage.getObject<NotificationSettings>('notification_settings');
    return { ...defaultSettings, ...saved };
  }

  /**
   * Update notification settings
   */
  updateNotificationSettings(settings: Partial<NotificationSettings>): void {
    const current = this.getNotificationSettings();
    const updated = { ...current, ...settings };
    userStorage.setObject('notification_settings', updated);
  }

  /**
   * Clear all notifications
   */
  async clearAllNotifications(): Promise<void> {
    try {
      await notification.cancelAllNotifications();
      await notification.cancelDisplayedNotifications([]);
    } catch (error) {
      console.error('Failed to clear all notifications:', error);
    }
  }

  /**
   * Get notification history
   */
  getNotificationHistory(limit = 50): AppNotification[] {
    const history = userStorage.getObject<AppNotification[]>('notification_history') || [];
    return history
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, limit);
  }

  /**
   * Mark notification as read
   */
  markAsRead(notificationId: string): void {
    const history = userStorage.getObject<AppNotification[]>('notification_history') || [];
    const notification = history.find(n => n.id === notificationId);
    if (notification && notification.status === 'unread') {
      notification.status = 'read';
      notification.readAt = new Date();
      userStorage.setObject('notification_history', history);
    }
  }

  /**
   * Get badge count
   */
  async getBadgeCount(): Promise<number> {
    try {
      return await notification.getBadgeCount();
    } catch (error) {
      console.error('Failed to get badge count:', error);
      return 0;
    }
  }

  /**
   * Set badge count
   */
  async setBadgeCount(count: number): Promise<void> {
    try {
      await notification.setBadgeCount(count);
    } catch (error) {
      console.error('Failed to set badge count:', error);
    }
  }

  /**
   * Update badge count based on unread notifications
   */
  async updateBadgeCount(): Promise<void> {
    const history = this.getNotificationHistory();
    const unreadCount = history.filter(n => n.status === 'unread').length;
    await this.setBadgeCount(unreadCount);
  }

  // Private helper methods

  private async createDefaultChannels(): Promise<void> {
    if (Platform.OS !== 'android') return;

    const channels: NotificationChannel[] = [
      {
        id: 'default',
        name: 'General',
        description: 'General notifications',
        importance: AndroidImportance.DEFAULT,
      },
      {
        id: 'social',
        name: 'Social',
        description: 'Social interactions and activities',
        importance: AndroidImportance.DEFAULT,
      },
      {
        id: 'chat',
        name: 'Messages',
        description: 'Chat messages and conversations',
        importance: AndroidImportance.HIGH,
      },
      {
        id: 'security',
        name: 'Security',
        description: 'Security alerts and warnings',
        importance: AndroidImportance.HIGH,
      },
      {
        id: 'marketing',
        name: 'Promotions',
        description: 'Marketing and promotional content',
        importance: AndroidImportance.LOW,
      },
    ];

    try {
      await notification.createChannels(channels);
    } catch (error) {
      console.error('Failed to create notification channels:', error);
    }
  }

  private setupEventHandlers(): void {
    this.eventCleanup = notification.setEventHandlers({
      onNotificationReceived: notif => {
        console.log('Notification received:', notif);
      },
      onNotificationPressed: notif => {
        console.log('Notification pressed:', notif);
        if (notif.id) {
          this.markAsRead(notif.id);
        }
        this.handleNotificationPress(notif);
      },
      onActionPressed: ({ actionId, notification: notif }) => {
        console.log('Notification action pressed:', actionId, notif);
        this.handleNotificationAction(actionId, notif);
      },
    });
  }

  private async ensurePermissions(): Promise<void> {
    const enabled = await this.areNotificationsEnabled();
    if (!enabled) {
      console.log('Notification permissions not granted');
    }
  }

  private getChannelForType(type: NotificationType): string {
    const channelMap: Record<NotificationType, string> = {
      system: 'default',
      social: 'social',
      marketing: 'marketing',
      security: 'security',
      reminder: 'default',
      achievement: 'social',
      chat: 'chat',
      post: 'social',
      follow: 'social',
      like: 'social',
      comment: 'social',
      mention: 'social',
      trending: 'social',
    };

    return channelMap[type] || 'default';
  }

  private mapPriorityToImportance(priority: NotificationPriority): AndroidImportance {
    const priorityMap: Record<NotificationPriority, AndroidImportance> = {
      low: AndroidImportance.LOW,
      normal: AndroidImportance.DEFAULT,
      high: AndroidImportance.HIGH,
      critical: AndroidImportance.MAX,
    };

    return priorityMap[priority] || AndroidImportance.DEFAULT;
  }

  private mapPriorityToInterruptionLevel(
    priority: NotificationPriority
  ): 'passive' | 'active' | 'timeSensitive' | 'critical' {
    const levelMap: Record<
      NotificationPriority,
      'passive' | 'active' | 'timeSensitive' | 'critical'
    > = {
      low: 'passive',
      normal: 'active',
      high: 'timeSensitive',
      critical: 'critical',
    };

    return levelMap[priority] || 'active';
  }

  private mapRemotePriorityToLocal(
    remotePriority?: 'min' | 'low' | 'default' | 'high' | 'max'
  ): NotificationPriority {
    const priorityMap: Record<string, NotificationPriority> = {
      min: 'low',
      low: 'low',
      default: 'normal',
      high: 'high',
      max: 'critical',
    };

    return priorityMap[remotePriority || 'default'] || 'normal';
  }

  private isInQuietHours(settings: NotificationSettings): boolean {
    if (!settings.quietHours.enabled) return false;

    const now = new Date();
    const currentTime = format(now, 'HH:mm');
    const { startTime, endTime } = settings.quietHours;

    // Handle overnight quiet hours (e.g., 22:00 to 08:00)
    if (startTime > endTime) {
      return currentTime >= startTime || currentTime <= endTime;
    }

    // Handle same-day quiet hours (e.g., 12:00 to 14:00)
    return currentTime >= startTime && currentTime <= endTime;
  }

  private async storeNotificationHistory(notification: AppNotification): Promise<void> {
    const history = userStorage.getObject<AppNotification[]>('notification_history') || [];
    history.unshift(notification);

    // Keep only the last 100 notifications
    const trimmed = history.slice(0, 100);
    userStorage.setObject('notification_history', trimmed);

    // Update badge count
    await this.updateBadgeCount();
  }

  private storeScheduledNotification(notification: ScheduledNotification): void {
    const scheduled =
      userStorage.getObject<ScheduledNotification[]>('scheduled_notifications') || [];
    scheduled.push(notification);
    userStorage.setObject('scheduled_notifications', scheduled);
  }

  private removeScheduledNotification(notificationId: string): void {
    const scheduled =
      userStorage.getObject<ScheduledNotification[]>('scheduled_notifications') || [];
    const filtered = scheduled.filter(n => n.id !== notificationId);
    userStorage.setObject('scheduled_notifications', filtered);
  }

  private getNotificationTemplate(templateId: string): NotificationTemplate | undefined {
    const templates = appStorage.getObject<NotificationTemplate[]>('notification_templates') || [];
    return templates.find(t => t.id === templateId);
  }

  private handleNotificationPress(notification: any): void {
    // Handle deep linking or navigation based on notification data
    const { data } = notification;
    if (data?.deepLink) {
      // Navigate to deep link
      console.log('Navigate to:', data.deepLink);
    }
  }

  private handleNotificationAction(actionId: string, notification: any): void {
    // Handle notification actions
    console.log('Handle action:', actionId, 'for notification:', notification);
  }
}

// Export singleton instance
export const notificationService = new NotificationService();
