import React, { forwardRef } from 'react';

import { TouchableOpacity } from 'react-native';

import { Calendar, CalendarBlank, Clock } from 'phosphor-react-native';
import { Controller, type FieldPath, type FieldValues } from 'react-hook-form';

import { Box, Text as ThemedText, useTheme } from '@/src/core/theme';

import {
  DatePicker,
  type DatePickerHandle,
  type DatePickerProps,
  useDatePicker,
} from './DatePicker';

export interface DatePickerFieldProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
  Multiple extends boolean = false,
> extends Omit<DatePickerProps<Multiple>, 'value' | 'onChange'> {
  name: TName;
  control: any; // Control<TFieldValues>
  label?: string;
  placeholder?: string;
  error?: string;
  required?: boolean;
  disabled?: boolean;
  formatDate?: (date: Date | Date[] | null) => string;
  renderTrigger?: (props: {
    value: Multiple extends true ? Date[] : Date | null;
    onPress: () => void;
    disabled?: boolean;
    placeholder?: string;
    error?: string;
  }) => React.ReactElement;
}

const DatePickerFieldComponent = <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
  Multiple extends boolean = false,
>(
  props: DatePickerFieldProps<TFieldValues, TName, Multiple>,
  ref: React.Ref<DatePickerHandle>
) => {
  const {
    name,
    control,
    label,
    placeholder,
    error,
    required,
    disabled,
    formatDate,
    renderTrigger,
    type = 'date',
    ...datePickerProps
  } = props;

  const theme = useTheme();
  const { ref: datePickerRef, show } = useDatePicker();

  // Combine refs
  React.useImperativeHandle(ref, () => datePickerRef.current!, [datePickerRef]);

  const getIcon = () => {
    switch (type) {
      case 'time':
        return Clock;
      case 'datetime':
        return Calendar;
      case 'yearmonth':
        return CalendarBlank;
      default:
        return Calendar;
    }
  };

  const Icon = getIcon();

  const defaultFormatDate = (value: Date | Date[] | null): string => {
    if (!value) return placeholder || 'Select date';

    if (Array.isArray(value)) {
      if (value.length === 0) return placeholder || 'Select dates';
      return value
        .map(d => {
          switch (type) {
            case 'time':
              return d.toLocaleTimeString();
            case 'datetime':
              return d.toLocaleString();
            case 'yearmonth':
              return d.toLocaleDateString(undefined, { year: 'numeric', month: 'long' });
            default:
              return d.toLocaleDateString();
          }
        })
        .join(', ');
    }

    switch (type) {
      case 'time':
        return value.toLocaleTimeString();
      case 'datetime':
        return value.toLocaleString();
      case 'yearmonth':
        return value.toLocaleDateString(undefined, { year: 'numeric', month: 'long' });
      default:
        return value.toLocaleDateString();
    }
  };

  const formatter = formatDate || defaultFormatDate;

  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <Box>
          {label && (
            <Box marginBottom="xs_8">
              <ThemedText variant="b_14Medium_button" color="text">
                {label}
                {required && <ThemedText color="error"> *</ThemedText>}
              </ThemedText>
            </Box>
          )}

          {renderTrigger ? (
            renderTrigger({
              value: field.value,
              onPress: show,
              disabled,
              placeholder,
              error,
            })
          ) : (
            <TouchableOpacity onPress={show} disabled={disabled} activeOpacity={0.7}>
              <Box
                backgroundColor="surface"
                borderWidth={1}
                borderColor={error ? 'error' : 'border'}
                borderRadius="sm_8"
                paddingHorizontal="md_16"
                paddingVertical="sm_12"
                flexDirection="row"
                alignItems="center"
                opacity={disabled ? 0.5 : 1}>
                <Icon size={20} color={error ? theme.colors.error : theme.colors.textSecondary} />
                <Box flex={1} marginLeft="xs_8">
                  <ThemedText
                    variant="b_16Regular_input"
                    color={field.value ? 'text' : 'textSecondary'}>
                    {formatter(field.value)}
                  </ThemedText>
                </Box>
              </Box>
            </TouchableOpacity>
          )}

          {error && (
            <Box marginTop="xs_8">
              <ThemedText variant="l_12Medium_message" color="error">
                {error}
              </ThemedText>
            </Box>
          )}

          {/* Only render DatePicker when component is properly initialized */}
          {typeof field.value !== 'undefined' && (
            <DatePicker
              ref={datePickerRef}
              type={type}
              value={field.value}
              onChange={field.onChange}
              {...datePickerProps}
            />
          )}
        </Box>
      )}
    />
  );
};

export const DatePickerField = forwardRef(DatePickerFieldComponent) as <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
  Multiple extends boolean = false,
>(
  props: DatePickerFieldProps<TFieldValues, TName, Multiple> & {
    ref?: React.Ref<DatePickerHandle>;
  }
) => React.ReactElement;
