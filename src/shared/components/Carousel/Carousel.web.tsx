import React, {
  ReactElement,
  forwardRef,
  isValidElement,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';

import { View } from 'react-native';

import ReanimatedCarousel from 'react-native-reanimated-carousel';

import type { CarouselProps, CarouselRef } from './types';

export const Carousel = forwardRef<CarouselRef, CarouselProps>(
  (
    { children, style, initialPage = 0, onPageSelected, onPageScroll, scrollEnabled = true },
    ref
  ) => {
    const carouselRef = useRef<any>(null);
    const [currentIndex, setCurrentIndex] = useState(initialPage);

    // Filter and validate children to ensure they are valid ReactElements
    const validChildren = React.Children.toArray(children).filter((child): child is ReactElement =>
      isValidElement(child)
    );

    useImperativeHandle(ref, () => ({
      setPage: (index: number) => {
        carouselRef.current?.scrollTo({ index, animated: true });
      },
      getCurrentPage: () => {
        return currentIndex;
      },
    }));

    const handleSnapToItem = (index: number) => {
      setCurrentIndex(index);
      onPageSelected?.(index);
    };

    const handleProgressChange = (progress: number, absoluteProgress: number) => {
      // Convert reanimated-carousel progress to PagerView-like format
      const position = Math.floor(absoluteProgress);
      const offset = absoluteProgress - position;
      onPageScroll?.({ position, offset });
    };

    return (
      <View style={style}>
        <ReanimatedCarousel
          ref={carouselRef}
          data={validChildren}
          renderItem={({ item }) => item as ReactElement}
          width={Number(style?.width || 300)}
          height={Number(style?.height || 200)}
          defaultIndex={initialPage}
          onSnapToItem={handleSnapToItem}
          onProgressChange={handleProgressChange}
          enabled={scrollEnabled}
          onConfigurePanGesture={gesture => {
            'worklet';
            gesture.activeOffsetX([-10, 10]);
          }}
          mode="parallax"
          modeConfig={{
            parallaxScrollingScale: 0.9,
            parallaxScrollingOffset: 50,
          }}
        />
      </View>
    );
  }
);

Carousel.displayName = 'Carousel';
