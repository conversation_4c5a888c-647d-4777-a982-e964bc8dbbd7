import React from 'react';

import Animated, { useAnimatedStyle } from 'react-native-reanimated';

import { Box, useTheme } from '@/src/core/theme';

import type { PaginationDotProps } from './types';

const AnimatedBox = Animated.createAnimatedComponent(Box);

export const PaginationDot = ({ index, scrollProgress }: PaginationDotProps) => {
  const { colors } = useTheme();

  const animatedStyle = useAnimatedStyle(() => {
    const distance = Math.abs(scrollProgress.value - index);
    const isActive = distance < 0.5;
    const progress = Math.max(0, 1 - distance);

    const scale = 0.8 + 0.2 * progress;
    const width = 8 + 16 * progress;

    return {
      transform: [{ scale }],
      width,
      backgroundColor: isActive ? colors.primary : colors.primaryLight,
    };
  });

  return <AnimatedBox height={8} borderRadius="circle_9999" style={animatedStyle} />;
};
