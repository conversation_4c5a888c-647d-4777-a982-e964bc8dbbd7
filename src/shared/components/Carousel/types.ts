import type { ReactElement, ReactNode } from 'react';

import type { ViewStyle } from 'react-native';

import type Animated from 'react-native-reanimated';

export interface CarouselProps {
  children: ReactElement[] | ReactNode;
  style?: ViewStyle;
  initialPage?: number;
  onPageSelected?: (index: number) => void;
  onPageScroll?: (event: { position: number; offset: number }) => void;
  scrollEnabled?: boolean;
}

export interface CarouselRef {
  setPage: (index: number) => void;
  getCurrentPage: () => number;
}

export interface PaginationDotProps {
  index: number;
  scrollProgress: Animated.SharedValue<number>;
  currentIndex: number;
}
