import React, { forwardRef, useImperativeHandle, useRef } from 'react';

import PagerView from 'react-native-pager-view';

import type { CarouselProps, CarouselRef } from './types';

export const Carousel = forwardRef<CarouselRef, CarouselProps>(
  (
    { children, style, initialPage = 0, onPageSelected, onPageScroll, scrollEnabled = true },
    ref
  ) => {
    const pagerRef = useRef<PagerView>(null);

    useImperativeHandle(ref, () => ({
      setPage: (index: number) => {
        pagerRef.current?.setPage(index);
      },
      getCurrentPage: () => {
        // For native, we'll track this via state in the parent component
        return initialPage;
      },
    }));

    const handlePageSelected = (event: any) => {
      const index = event.nativeEvent.position;
      onPageSelected?.(index);
    };

    const handlePageScroll = (event: any) => {
      const { position, offset } = event.nativeEvent;
      onPageScroll?.({ position, offset });
    };

    return (
      <PagerView
        ref={pagerRef}
        style={style}
        initialPage={initialPage}
        onPageSelected={handlePageSelected}
        onPageScroll={handlePageScroll}
        scrollEnabled={scrollEnabled}>
        {children}
      </PagerView>
    );
  }
);

Carousel.displayName = 'Carousel';
