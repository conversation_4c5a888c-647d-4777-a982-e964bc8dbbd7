import React, { useMemo } from 'react';

import { Dimensions } from 'react-native';

import { Canvas, Group, ImageSVG, SkSVG, fitbox, rect } from '@shopify/react-native-skia';

type Props = {
  skSVG: SkSVG | null;
  percentVisible?: number; // 0 to 1 (e.g., 0.5 = 50%)
};

export const ResponsiveSVG: React.FC<Props> = ({ skSVG, percentVisible = 0.4 }) => {
  const { width: deviceWidth, height: deviceHeight } = Dimensions.get('screen');

  const { svg, transform } = useMemo(() => {
    if (!skSVG) return { svg: null, transform: [] };

    const sourceBox = rect(0, 0, skSVG.width(), skSVG.height()) ?? rect(0, 0, 100, 100);
    const displayHeight = deviceHeight * percentVisible;
    const targetBox = rect(0, 0, deviceWidth, displayHeight);

    const matrix = fitbox('contain', sourceBox, targetBox);
    return { svg: skSVG, transform: matrix };
  }, [skSVG, percentVisible, deviceWidth, deviceHeight]);

  if (!svg) return null;

  return (
    <Canvas style={{ width: deviceWidth, height: deviceHeight * percentVisible }}>
      <Group transform={transform}>
        <ImageSVG svg={svg} />
      </Group>
    </Canvas>
  );
};
