import { Skia } from '@shopify/react-native-skia';

import { ResponsiveSVG } from './ResponsiveSkiaSVG';

const svg = Skia.SVG.MakeFromString(`
   <svg xmlns="http://www.w3.org/2000/svg"  width="651" height="519">
  <path fill="#0052CC" fill-rule="evenodd" d="m448.333 77 1.714-1.5c9.774 22.897 13.537 37.861 11.076 63.5-.634 4.141-1.318 7.979-2.075 11.578l-.015.283-.033.608 1.5-1c16.038-12.854 22.5-23.5 25.5-36.5s0-22-3-29.5-5.581-10.89-12-16a504.989 504.989 0 0 1-2.895-2.42C458.629 58.085 453.258 53.572 439.5 55l.084.247.014.042c.468.068.935.139 1.402.211l9.047 20 .453 1-2.167.5ZM185.616 75.017 185.5 75c1.567-2.793 2.675-5.133 3.69-7.276 1.987-4.196 3.616-7.637 7.636-12.242-14.956-1.524-19.223 1.575-30.584 10.987-6.419 5.11-9 8.5-12 16s-6 16.5-3 29.5 9.462 23.646 25.5 36.5l.492.328c-.205-.911-.408-1.843-.611-2.797-1.853-32.824.223-48.269 8.993-70.983Z" clip-rule="evenodd"/>
  <path fill="#B2DDFF" fill-rule="evenodd" d="m557.489 106.255-.013-.118-.003-.***************.118ZM406.495 513.866C400.059 341.382 377.307 302.529 314 302c-56.459 0-76.833 57.401-83.985 211.66 63.855 5.905 113.112 5.877 176.48.206Z" clip-rule="evenodd"/>
  <path fill="#66B0F6" d="M230 514c68.928 5.223 107.572 5.301 176.5 0l-.005-.134C400.059 341.382 377.307 302.529 314 302c-56.459 0-76.833 57.401-83.985 211.66L230 514Z"/>
  <path fill="#B2DDFF" d="M447.871 77h.462l1.714-1.5c9.774 22.897 13.537 37.861 11.076 63.5-.634 4.141-1.318 7.979-2.075 11.578-4.658 22.153-12.066 35.259-27.425 54.422-14.464 18.4-17.678 32.878-16 65l.327 1.704c81.134-53.317 112.081-86.799 142.55-156.35-.317-2.906-.658-5.952-1.009-9.081l-.002-.018-.013-.118-.003-.024c-2.965-26.464-6.589-58.807-1.973-62.26 1.611-1.257 2.641-1.64 5-1 5.835 2.585 10.983 22.784 12.819 29.99l.094.367c.31 1.217.511 1.993.587 2.144.5 1 2.5 1.5 3 0 .055-.166.216-.97.477-2.275 2.098-10.483 10.67-53.321 23.023-57.725 2-.5 4.5-1 7 2.5 2.256 3.159-6.009 31.458-8.071 38.516l-.003.012c-.221.754-.37 1.264-.426 1.471-.586 2.146 3.457 4.026 4.5 2 .132-.255.477-1.117 1.001-2.421 3.623-9.025 15.762-39.263 24.499-37.079 12.777-1.306-7 45.5-8.5 48.5s-1.071 3.688.5 4.5c.867.449 1.63.444 2.5 0 .627-.32.67-.626.729-1.05.037-.261.08-.567.271-.95 10.83-22.25 15.806-28.041 23-25.353 0 0 2.772 1.06 3 3 2 17-8.555 36.066-42.5 84.354l-.5 1.5c-47.373 108.853-79.462 162.367-163.321 243.49 7.247 44.361 10.277 72.679 13.444 130.156-18.249 2.145-35.138 3.935-51.128 5.366C400.059 341.382 377.307 302.529 314 302c-56.459 0-76.833 57.401-83.985 211.66-15.03-1.39-30.87-3.109-47.892-5.16 2.938-51.696 5.837-80.905 13.353-132.492-77.145-79.405-109.823-133.133-153.319-240.136l-.463-1.512C8.936 85.26-1.151 65.942 1.263 48.996c.275-1.934 3.072-2.926 3.072-2.926 7.257-2.513 12.09 3.398 22.375 25.906.182.387.217.694.248.957.049.425.084.732.703 1.067.859.465 1.621.488 2.5.061 1.59-.774 2.035-1.45.609-4.486-1.427-3.036-20.057-50.31-7.315-48.693 8.787-1.97 20.186 28.554 23.587 37.664.492 1.317.817 2.187.942 2.446.994 2.05 5.081.27 4.548-1.89-.052-.21-.19-.729-.393-1.493-1.888-7.103-9.463-35.599-7.13-38.702 2.585-3.438 5.072-2.878 7.06-2.329 12.24 4.704 19.766 47.738 21.608 58.27.229 1.31.37 2.118.421 2.285.464 1.512 2.475 1.06 3 .073.087-.163.343-1.077.743-2.5 2.012-7.159 7.648-27.22 13.545-29.66 2.373-.584 3.394-.176 4.974 1.12 4.53 3.565.119 35.81-3.49 62.194-.435 3.175-.858 6.266-1.252 9.212 27.785 67.884 58.201 103.422 132.182 155.594l.323-1.166-1-26c-.19-5.14-.753-8.212-2.5-14l-4-11.5-5.5-10-7.5-9.5-7-9c-10.715-15.063-15.269-24.874-19.389-43.203-.205-.911-.408-1.843-.611-2.797-1.853-32.824.223-48.269 8.993-70.983l.007-.017.304.028c2.754.261 5.193.618 6.573.972.243.062.499.11.762.152l.284.046c.474.075.968.153 1.454.302.***************.305.**************.22.318.255 0 16.5 2.377 28.5 6.377 32.5 11.5 21 22 25.5 63 20.5 31.5-11 37-49.5 37-49.5s18.209-4.62 32.123 0c.985 15.426 3.311 25.983 13 38.5 18.935 13.832 30.484 14.94 54.877 12.5 28.631 0 38.862-25.83 42.005-53.433l.008-.07.11-.997h3.748Z"/>
  <path fill="#B2DDFF" d="M447.871 77h.462l1.714-1.5c9.774 22.897 13.537 37.861 11.076 63.5-.634 4.141-1.318 7.979-2.075 11.578-4.658 22.153-12.066 35.259-27.425 54.422-14.464 18.4-17.678 32.878-16 65l.327 1.704c81.134-53.317 112.081-86.799 142.55-156.35-.317-2.906-.658-5.952-1.009-9.081l-.002-.018-.013-.118-.003-.024c-2.965-26.464-6.589-58.807-1.973-62.26 1.611-1.257 2.641-1.64 5-1 5.835 2.585 10.983 22.784 12.819 29.99l.094.367c.31 1.217.511 1.993.587 2.144.5 1 2.5 1.5 3 0 .055-.166.216-.97.477-2.275 2.098-10.483 10.67-53.321 23.023-57.725 2-.5 4.5-1 7 2.5 2.256 3.159-6.009 31.458-8.071 38.516l-.003.012c-.221.754-.37 1.264-.426 1.471-.586 2.146 3.457 4.026 4.5 2 .132-.255.477-1.117 1.001-2.421 3.623-9.025 15.762-39.263 24.499-37.079 12.777-1.306-7 45.5-8.5 48.5s-1.071 3.688.5 4.5c.867.449 1.63.444 2.5 0 .627-.32.67-.626.729-1.05.037-.261.08-.567.271-.95 10.83-22.25 15.806-28.041 23-25.353 0 0 2.772 1.06 3 3 2 17-8.555 36.066-42.5 84.354l-.5 1.5c-47.373 108.853-79.462 162.367-163.321 243.49 7.247 44.361 10.277 72.679 13.444 130.156-18.249 2.145-35.138 3.935-51.128 5.366C400.059 341.382 377.307 302.529 314 302c-56.459 0-76.833 57.401-83.985 211.66-15.03-1.39-30.87-3.109-47.892-5.16 2.938-51.696 5.837-80.905 13.353-132.492-77.145-79.405-109.823-133.133-153.319-240.136l-.463-1.512C8.936 85.26-1.151 65.942 1.263 48.996c.275-1.934 3.072-2.926 3.072-2.926 7.257-2.513 12.09 3.398 22.375 25.906.182.387.217.694.248.957.049.425.084.732.703 1.067.859.465 1.621.488 2.5.061 1.59-.774 2.035-1.45.609-4.486-1.427-3.036-20.057-50.31-7.315-48.693 8.787-1.97 20.186 28.554 23.587 37.664.492 1.317.817 2.187.942 2.446.994 2.05 5.081.27 4.548-1.89-.052-.21-.19-.729-.393-1.493-1.888-7.103-9.463-35.599-7.13-38.702 2.585-3.438 5.072-2.878 7.06-2.329 12.24 4.704 19.766 47.738 21.608 58.27.229 1.31.37 2.118.421 2.285.464 1.512 2.475 1.06 3 .073.087-.163.343-1.077.743-2.5 2.012-7.159 7.648-27.22 13.545-29.66 2.373-.584 3.394-.176 4.974 1.12 4.53 3.565.119 35.81-3.49 62.194-.435 3.175-.858 6.266-1.252 9.212 27.785 67.884 58.201 103.422 132.182 155.594l.323-1.166-1-26c-.19-5.14-.753-8.212-2.5-14l-4-11.5-5.5-10-7.5-9.5-7-9c-10.715-15.063-15.269-24.874-19.389-43.203-.205-.911-.408-1.843-.611-2.797-1.853-32.824.223-48.269 8.993-70.983l.007-.017.304.028c2.754.261 5.193.618 6.573.972.243.062.499.11.762.152l.284.046c.474.075.968.153 1.454.302.***************.305.**************.22.318.255 0 16.5 2.377 28.5 6.377 32.5 11.5 21 22 25.5 63 20.5 31.5-11 37-49.5 37-49.5s18.209-4.62 32.123 0c.985 15.426 3.311 25.983 13 38.5 18.935 13.832 30.484 14.94 54.877 12.5 28.631 0 38.862-25.83 42.005-53.433l.008-.07.11-.997h3.748Z"/>
  <path fill="#B2DDFF" fill-rule="evenodd" d="M322.5.5c-58.774.765-89.256 12.65-125.282 54.745 25.611-4.597 40.056-4.945 65.782-3.461 19.44.903 27.759 4.217 39.493 12.3.56-.152 1.111-.298 1.653-.436 11.41-3.425 18.265-2.96 30.29.832 12.038-8.372 20.31-11.779 40.064-12.696 25.322-1.461 40.196-.938 65.084 3.463l.014.042c.468.068.935.139 1.402.211C398.804 10.74 371.376.394 322.5.5Z" clip-rule="evenodd"/>
  <path fill="#00307A" fill-rule="evenodd" d="M439.598 55.289c.468.068.935.139 1.402.211l-1.416-.253.014.042ZM185.616 75.017 192.5 76c-1.38-.354-3.819-.71-6.573-.972l-.304-.028-.007.017Zm258.397 2.98 4.32-.997h-4.21l-.11.997Z" clip-rule="evenodd"/>
  <path fill="#00307A" d="M258.986 163.661c-7.5-3.214-10.212-2.213-6 7l9 14.5 9.5 14 10.5 14c8.069 9.78 13.584 14.603 35 15.5 21.416-.897 28.431-5.72 36.5-15.5l10.5-14 9.5-14 9-14.5c4.213-9.213 1.5-10.214-6-7-2.268.972-4.273 1.887-6.145 2.741-6.796 3.099-11.842 5.4-21.355 6.759-11.533 1.194-32 0-32 0s-18.966 1.194-30.5 0c-9.512-1.359-14.558-3.66-21.354-6.759-1.873-.854-3.878-1.769-6.146-2.741ZM299 211c0-4.275 7.964-7.767 17.986-7.989A48.67 48.67 0 0 1 318 203c10.493 0 19 3.582 19 8s-8.507 8-19 8c-.34 0-.678-.004-1.014-.011C306.964 218.767 299 215.275 299 211Z"/>
  <path fill="#B2DDFF" d="M316.986 218.989c.336.007.674.011 1.014.011 10.493 0 19-3.582 19-8s-8.507-8-19-8c-.34 0-.678.004-1.014.011C306.964 203.233 299 206.725 299 211s7.964 7.767 17.986 7.989Z"/>
  <path fill="#00307A" d="M347 147c0 11.598-12.984 21-29 21-16.017 0-29-9.402-29-21s12.983-21 29-21c16.016 0 29 9.402 29 21Z"/>
  <path fill="#00307A" fill-rule="evenodd" d="M444.005 78.067C440.862 105.67 430.631 131.5 402 131.5c-24.393 2.44-35.942 1.332-54.877-12.5-9.689-12.517-12.015-23.074-13-38.5-13.914-4.62-32.123 0-32.123 0s-5.5 38.5-37 49.5c-41 5-51.5.5-63-20.5-4-4-6.377-16-6.377-32.5-.111-.035-.215-.145-.318-.255-.101-.107-.201-.213-.305-.245-1.585 21.852 2.077 36.065 19 50.284 19.5 13.716 55 8 64 2 8.922-5.948 25.216-23.479 25.496-45.428 11.432-3.283 18.95-3.517 30.494.14-.23 20.554 4.717 36.234 25.51 45.288 21.033 9.158 56.763 4.187 64-2 7.229-6.181 22.11-26.717 20.505-48.717ZM450.047 75.5l-1.714 1.5 2.167-.5-.453-1ZM195 76.5c-.486-.149-.98-.227-1.454-.302l-.284-.046L195 76.5Z" clip-rule="evenodd"/>
  <path fill="#00307A" fill-rule="evenodd" d="M193.546 76.198c.474.075.968.153 1.454.302l-1.738-.348.284.046Z" clip-rule="evenodd"/>
  <path fill="#00307A" d="m448.333 77 1.714-1.5-9.047-20c-.467-.072-.934-.143-1.402-.211l-.014-.042c-24.888-4.401-39.762-4.924-65.084-3.463-19.754.917-28.026 4.324-40.064 12.696-12.025-3.792-18.88-4.257-30.29-.832-.377.113-.759.23-1.146.352v.435a77.707 77.707 0 0 0-.279-.193l-.228-.158c-11.734-8.083-20.053-11.397-39.493-12.3-25.726-1.484-40.171-1.136-65.782 3.46l-.218.04-.093.105-.081.093c-4.02 4.605-5.649 8.046-7.636 12.242-1.015 2.143-2.123 4.483-3.69 7.276l.116.017.007-.017.304.028c2.754.261 5.193.618 6.573.972l.762.152.284.046c.474.075.968.153 1.454.302.***************.305.**************.22.318.255 0 16.5 2.377 28.5 6.377 32.5 11.5 21 22 25.5 63 20.5 31.5-11 37-49.5 37-49.5s18.209-4.62 32.123 0c.985 15.426 3.311 25.983 13 38.5 18.935 13.832 30.484 14.94 54.877 12.5 28.631 0 38.862-25.83 42.005-53.433L444 78l.013-.003.11-.997h4.21Z"/>
</svg>
  `);

export const SVG = () => {
  return <ResponsiveSVG skSVG={svg} percentVisible={0.3} />;
};

export default SVG;
