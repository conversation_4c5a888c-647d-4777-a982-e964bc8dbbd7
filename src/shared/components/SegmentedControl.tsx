import React from 'react';

import { useTheme } from '@/src/core/theme';
import Box from '@/src/core/theme/Box';
import Pressable from '@/src/core/theme/Pressable';
import Text from '@/src/core/theme/Text';

interface SegmentedControlProps {
  options: string[];
  selectedIndex: number;
  onSelect: (index: number) => void;
}

export default function SegmentedControl({
  options,
  selectedIndex,
  onSelect,
}: SegmentedControlProps) {
  const theme = useTheme();

  return (
    <Box
      flexDirection="row"
      backgroundColor="subtleBackground"
      borderRadius="md_12"
      padding="xxs_4"
      gap="xxs_4">
      {options.map((option, index) => {
        const isSelected = index === selectedIndex;
        return (
          <Pressable
            key={option}
            onPress={() => onSelect(index)}
            flex={1}
            backgroundColor={isSelected ? 'mainBackground' : 'transparent'}
            paddingVertical="xs_8"
            paddingHorizontal="sm_12"
            borderRadius="md_12"
            alignItems="center"
            justifyContent="center">
            <Text variant="h_16Medium_formLabel" color={isSelected ? 'mainText' : 'secondaryText'}>
              {option}
            </Text>
          </Pressable>
        );
      })}
    </Box>
  );
}
