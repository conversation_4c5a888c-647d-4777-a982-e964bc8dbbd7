/**
 * LocationPicker Component
 * Integrated location picker that navigates to the event location search screen
 */
import React, { forwardRef } from 'react';

import { Pressable, TextInput as RNTextInput } from 'react-native';

import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '@shopify/restyle';

import type { EventsStackScreenProps } from '@/src/core/navigation/types';
import { Box, Text, Theme } from '@/src/core/theme';
import TextInput from '@/src/core/theme/TextInput';
import { useResponsive } from '@/src/core/theme/useResponsive';

interface LocationPickerProps {
  value?: LocationData | null;
  onSelect: (location: LocationData) => void;
  placeholder?: string;
  label?: string;
  error?: string;
}

export interface LocationData {
  id?: string;
  name: string;
  address: string;
  latitude: number;
  longitude: number;
}

const LocationPicker = forwardRef<RNTextInput, LocationPickerProps>(
  ({ value, onSelect, placeholder = 'Search for a venue...', label = 'Location', error }, ref) => {
    const theme = useTheme<Theme>();
    const { select } = useResponsive();
    const navigation = useNavigation<EventsStackScreenProps<'CreateEvent'>['navigation']>();

    // Handle location picker press
    const handleLocationPickerPress = React.useCallback(() => {
      navigation.navigate('EventLocationSearch');
    }, [navigation]);

    // Listen for location selection via navigation events
    React.useEffect(() => {
      const unsubscribe = navigation.addListener('focus', () => {
        // Check if we received location data from the search screen
        const state = navigation.getState();
        const currentRoute = state.routes[state.index];

        if (currentRoute.params?.selectedLocation) {
          onSelect(currentRoute.params.selectedLocation);
          // Clear the selected location from params to prevent re-triggering
          navigation.setParams({ selectedLocation: undefined });
        }
      });

      return unsubscribe;
    }, [navigation, onSelect]);

    return (
      <Pressable onPress={handleLocationPickerPress}>
        <TextInput
          ref={ref}
          value={value?.name || ''}
          placeholder={placeholder}
          label={label}
          error={error}
          editable={false}
          pointerEvents="none"
          leading={
            <MaterialIcons
              name="location-on"
              size={select({ phone: 20, tablet: 24 })}
              color={theme.colors.textSecondary}
            />
          }
          trailing={
            <MaterialIcons
              name="chevron-right"
              size={select({ phone: 20, tablet: 24 })}
              color={theme.colors.textSecondary}
            />
          }
        />
      </Pressable>
    );
  }
);

LocationPicker.displayName = 'LocationPicker';

export default LocationPicker;
