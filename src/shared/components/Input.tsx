import React from 'react';

import {
  Control,
  Controller,
  ControllerRenderProps,
  FieldPath,
  FieldValues,
} from 'react-hook-form';

import { Box, Text } from '@/src/core/theme';
import TextInput from '@/src/core/theme/TextInput';

interface InputProps<
  T extends FieldValues = FieldValues,
  TName extends FieldPath<T> = FieldPath<T>,
> {
  control: Control<T>;
  name: TName;
  label: string;
  placeholder?: string;
  error?: string;
  render?: (props: { field: ControllerRenderProps<T, TName> }) => React.ReactElement;
  // Forward all TextInput props
  [key: string]: any;
}

export const Input = <
  T extends FieldValues = FieldValues,
  TName extends FieldPath<T> = FieldPath<T>,
>({
  control,
  name,
  label,
  placeholder,
  error,
  render,
  ...textInputProps
}: InputProps<T, TName>) => {
  return (
    <Controller
      control={control}
      name={name}
      render={({ field, fieldState }) => {
        // If custom render prop is provided, use it
        if (render) {
          return (
            <Box>
              {render({ field })}
              {(fieldState.error || error) && (
                <Box marginTop="xxs_4">
                  <Text variant="b_14Regular_content" color="errorMain">
                    {fieldState.error?.message || error}
                  </Text>
                </Box>
              )}
            </Box>
          );
        }

        // Default TextInput rendering
        return (
          <TextInput
            label={label}
            placeholder={placeholder}
            error={fieldState.error?.message || error}
            value={field.value || ''}
            onChangeText={field.onChange}
            onBlur={field.onBlur}
            {...textInputProps}
          />
        );
      }}
    />
  );
};

export default Input;
