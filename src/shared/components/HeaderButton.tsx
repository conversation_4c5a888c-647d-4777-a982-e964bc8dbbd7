import { forwardRef } from 'react';

import { Pressable, StyleSheet } from 'react-native';

import FontAwesome from '@expo/vector-icons/FontAwesome';

export const HeaderButton = forwardRef<typeof Pressable, { onPress?: () => void }>(
  function HeaderButton({ onPress }, ref) {
    return (
      <Pressable onPress={onPress}>
        {({ pressed }) => (
          <FontAwesome
            name="info-circle"
            size={25}
            color="gray"
            style={[
              styles.headerRight,
              {
                opacity: pressed ? 0.5 : 1,
              },
            ]}
          />
        )}
      </Pressable>
    );
  }
);

export const styles = StyleSheet.create({
  headerRight: {
    marginRight: 15,
  },
});
