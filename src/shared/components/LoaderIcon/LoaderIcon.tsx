import React, { useMemo } from 'react';

import { Canvas, Path, Skia } from '@shopify/react-native-skia';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from 'react-native-reanimated';

export const AnimatedLoaderIcon = () => {
  const size = 120;
  const strokeWidth = 4;
  const radius = (size - strokeWidth) / 2;
  const canvasSize = size + 10;

  // Create a circular path for the loader
  const circle = useMemo(() => {
    const skPath = Skia.Path.Make();
    skPath.addCircle(canvasSize / 2, canvasSize / 2, radius);
    return skPath;
  }, [canvasSize, radius]);

  // Shared value for animation progress
  const progress = useSharedValue(0);

  // Start infinite rotation animation
  React.useEffect(() => {
    progress.value = withRepeat(withTiming(1, { duration: 500 }), -1, false);
  }, [progress]);

  // Animated style for rotation
  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${2 * Math.PI * progress.value}rad` }],
  }));

  return (
    <Animated.View style={animatedStyle}>
      <Canvas style={{ width: canvasSize, height: canvasSize }}>
        <Path
          path={circle}
          color="#6370CA"
          style="stroke"
          strokeWidth={strokeWidth}
          start={0.6}
          end={1}
          strokeCap="round"
        />
      </Canvas>
    </Animated.View>
  );
};
