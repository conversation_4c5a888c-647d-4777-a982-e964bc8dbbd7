import { useEffect } from 'react';

import { AccessibilityProps, Pressable, ViewStyle } from 'react-native';

import { Canvas, Circle, Path, interpolateColors } from '@shopify/react-native-skia';
import Animated, {
  interpolate,
  interpolateColor,
  useAnimatedStyle,
  useDerivedValue,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';

import { useTheme } from '@/src/core/theme';

const SwitchAnimated = Animated.createAnimatedComponent(Pressable);
export interface SwitchProps extends AccessibilityProps {
  isChecked: boolean;
  onToggle: (checked: boolean) => void;
  circleScale?: number;
  iconScale?: number;
  disabled?: boolean;
  style?: ViewStyle | ViewStyle[];
}

const WIDTH = 60;
const HEIGHT = 30;
const RADIUS = 9999;

export const Switch = ({
  isChecked,
  onToggle,
  circleScale = 1.0,
  iconScale = 0.8,
  disabled = false,
  style,
  ...props
}: SwitchProps) => {
  const { colors } = useTheme();
  const uncheckedColor = colors.inputPlaceholder;
  const checkedColor = colors.primary;
  const handlePress = async () => {
    // await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onToggle(!isChecked);
  };

  const progress = useSharedValue(isChecked ? 1 : 0);

  useEffect(() => {
    progress.value = withTiming(isChecked ? 1 : 0, { duration: 500 });
  }, [isChecked, progress]);

  // Circle movement
  const leftCx = HEIGHT / 2;
  const rightCx = WIDTH - HEIGHT / 2;
  const cx = useDerivedValue(() => interpolate(progress.value, [0, 1], [leftCx, rightCx]));
  const cy = HEIGHT / 2;

  // Circle radius with scale
  const baseCircleRadius = (HEIGHT - 6) / 2;
  const circleRadius = baseCircleRadius * circleScale;

  // Effective r for paths
  const r = (circleRadius / 2) * iconScale;

  // Path 1: First stroke of checkmark (left part going down)
  const startX1 = useDerivedValue(() =>
    interpolate(progress.value, [0, 1], [cx.value - r, cx.value - r * 1.1])
  );
  const startY1 = useDerivedValue(() => interpolate(progress.value, [0, 1], [cy - r, cy]));
  const endX1 = useDerivedValue(() =>
    interpolate(progress.value, [0, 1], [cx.value + r, cx.value - r * 0.2])
  );
  const endY1 = useDerivedValue(() => interpolate(progress.value, [0, 1], [cy + r, cy + r * 0.8]));

  const path1Str = useDerivedValue(
    () => `M ${startX1.value} ${startY1.value} L ${endX1.value} ${endY1.value}`
  );

  // Path 2: Second stroke of checkmark (right part going up)
  const startX2 = useDerivedValue(() =>
    interpolate(progress.value, [0, 1], [cx.value + r, cx.value - r * 0.2])
  );
  const startY2 = useDerivedValue(() =>
    interpolate(progress.value, [0, 1], [cy - r, cy + r * 0.8])
  );
  const endX2 = useDerivedValue(() =>
    interpolate(progress.value, [0, 1], [cx.value - r, cx.value + r])
  );
  const endY2 = useDerivedValue(() => interpolate(progress.value, [0, 1], [cy + r, cy * 0.75]));
  const path2Str = useDerivedValue(
    () => `M ${startX2.value} ${startY2.value} L ${endX2.value} ${endY2.value}`
  );

  const animatedStyle = useAnimatedStyle(() => ({
    backgroundColor: interpolateColor(progress.value, [0, 1], [uncheckedColor, checkedColor]),
  }));

  const pathColors = useDerivedValue(() =>
    interpolateColors(progress.value, [0, 1], [uncheckedColor, checkedColor])
  );

  return (
    <SwitchAnimated
      style={[
        {
          borderRadius: RADIUS,
          width: WIDTH,
          height: HEIGHT,
          opacity: disabled ? 0.5 : 1,
        },
        style,
        animatedStyle,
      ]}
      onPress={handlePress}
      disabled={disabled}
      accessibilityRole="switch"
      accessibilityState={{ checked: isChecked }}
      accessibilityLabel={isChecked ? 'Checked' : 'Unchecked'}
      {...props}>
      <Canvas style={{ width: '100%', height: '100%' }}>
        <Circle cx={cx} cy={cy} r={circleRadius} color="white" />
        <Path path={path1Str} color={pathColors} style="stroke" strokeWidth={3} strokeCap="round" />
        <Path path={path2Str} color={pathColors} style="stroke" strokeWidth={3} strokeCap="round" />
      </Canvas>
    </SwitchAnimated>
  );
};

Switch.displayName = 'Switch';

export default Switch;
