/**
 * Switch Component Constants
 *
 * Based on Figma design specifications:
 * - Width: 50px
 * - Border radius: 1000px (fully rounded)
 * - Icon size: 24x24px
 * - Smooth animations with spring configuration
 */

export const SWITCH_CONSTANTS = {
  // Dimensions based on Figma design
  WIDTH: 50,
  HEIGHT: 24,
  BORDER_RADIUS: 1000, // Fully rounded
  ICON_SIZE: 24,

  // Animation timing
  ANIMATION_DURATION: 200,
  SPRING_DAMPING: 20,
  SPRING_STIFFNESS: 300,

  // Icon transition timing
  ICON_FADE_DURATION: 150,
  SCALE_PRESSED: 0.95,
  SCALE_DEFAULT: 1,

  // Accessibility
  HIT_SLOP: {
    top: 8,
    bottom: 8,
    left: 8,
    right: 8,
  },
} as const;
// Color constants for theming
export const SWITCH_COLORS = {
  ENABLED_BACKGROUND: '#0052CC',
  DISABLED_BACKGROUND: '#E0E0E0',
  ICON_COLOR: '#FFFFFF',
} as const;

// Animation spring configuration
export const SWITCH_SPRING_CONFIG = {
  damping: SWITCH_CONSTANTS.SPRING_DAMPING,
  stiffness: SWITCH_CONSTANTS.SPRING_STIFFNESS,
  mass: 1,
  overshootClamping: false,
  restDisplacementThreshold: 0.01,
  restSpeedThreshold: 0.01,
} as const;
