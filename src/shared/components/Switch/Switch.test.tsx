import React from 'react';

import { cleanup, fireEvent, render, screen } from '@testing-library/react-native';

import { ThemeProvider } from '@/src/theme';

import SwitchComponent, { SwitchProps } from './Switch';

// Test wrapper with theme provider
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ThemeProvider defaultTheme={'light'}>{children}</ThemeProvider>
);

// Helper to render Switch with theme
const renderSwitch = (props: Partial<SwitchProps> = {}) => {
  const defaultProps: SwitchProps = {
    isChecked: false,
    onToggle: jest.fn(),
    ...props,
  };

  return render(
    <TestWrapper>
      <SwitchComponent {...defaultProps} />
    </TestWrapper>
  );
};

describe('Switch Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders with required props', () => {
      renderSwitch({ isChecked: false });
      expect(screen.getByRole('switch')).toBeDefined();
    });

    it('renders enabled state correctly', () => {
      renderSwitch({ isChecked: true });
      const switchElement = screen.getByRole('switch');
      expect(switchElement).toHaveProp('accessibilityState', {
        checked: true,
        disabled: false,
        busy: false,
      });
    });
    it('renders disabled state correctly', () => {
      renderSwitch({ isChecked: false, disabled: true });
      const switchElement = screen.getByRole('switch');
      expect(switchElement).toHaveProp('accessibilityState', {
        checked: false,
        disabled: true,
        busy: false,
      });
    });
  });

  describe('Interaction', () => {
    it('calls onValueChange when pressed', () => {
      const onToggle = jest.fn();
      renderSwitch({ isChecked: false, onToggle });

      fireEvent.press(screen.getByRole('switch'));
      expect(onToggle).toHaveBeenCalledWith(true);
    });

    it('toggles value correctly', () => {
      const onToggle = jest.fn();
      renderSwitch({ isChecked: true, onToggle });

      fireEvent.press(screen.getByRole('switch'));
      expect(onToggle).toHaveBeenCalledWith(false);
    });

    it('does not call onValueChange when disabled', () => {
      const onToggle = jest.fn();
      renderSwitch({ isChecked: false, disabled: true, onToggle });

      fireEvent.press(screen.getByRole('switch'));
      expect(onToggle).not.toHaveBeenCalled();
    });
  });
  describe('Accessibility', () => {
    it('has proper accessibility attributes', () => {
      renderSwitch({ isChecked: false });
      const switchElement = screen.getByRole('switch');

      expect(switchElement).toHaveProp('accessibilityRole', 'switch');
      expect(switchElement).toHaveProp('accessibilityLabel', 'Switch, off');
      expect(switchElement).toHaveProp('accessibilityHint', 'Double tap to toggle');
    });

    it('uses custom accessibility label when provided', () => {
      const customLabel = 'Enable notifications';
      renderSwitch({ isChecked: false, accessibilityLabel: customLabel });

      const switchElement = screen.getByRole('switch');
      expect(switchElement).toHaveProp('accessibilityLabel', customLabel);
    });

    it('indicates disabled state in accessibility label', () => {
      renderSwitch({ isChecked: true, disabled: true });

      const switchElement = screen.getByRole('switch');
      expect(switchElement).toHaveProp('accessibilityLabel', 'Switch, on, disabled');
    });

    it('has proper hit slop for accessibility', () => {
      renderSwitch({ isChecked: false });
      const switchElement = screen.getByRole('switch');

      expect(switchElement).toHaveProp('hitSlop', {
        top: 8,
        bottom: 8,
        left: 8,
        right: 8,
      });
    });
  });
});

afterEach(cleanup);
