import React, { forwardRef } from 'react';

import { StyleSheet, View, ViewStyle } from 'react-native';

import Animated, { FadeIn } from 'react-native-reanimated';

import { Box, Text } from '@/src/core/theme';

export interface RiveAnimationPlaceholderProps {
  /**
   * The name/type of animation that will be loaded
   * This helps developers understand what animation belongs here
   */
  animationName: string;

  /**
   * Description of the animation's purpose and behavior
   */
  description: string;

  /**
   * Dimensions for the animation container
   */
  width?: number;
  height?: number;

  /**
   * Additional styles for the container
   */
  style?: ViewStyle;

  /**
   * Background color for the placeholder
   */
  backgroundColor?: string;

  /**
   * Show labels in development mode
   */
  showLabel?: boolean;
}

/**
 * Placeholder component for Rive animations
 * Will be replaced with actual Rive animations once assets are added
 *
 * Usage:
 * <RiveAnimationPlaceholder
 *   animationName="celebration-confetti"
 *   description="Confetti burst animation for celebrations"
 *   width={200}
 *   height={200}
 * />
 */
export const RiveAnimationPlaceholder = forwardRef<View, RiveAnimationPlaceholderProps>(
  (
    {
      animationName,
      description,
      width = 100,
      height = 100,
      style,
      backgroundColor = 'primaryLight',
      showLabel = __DEV__,
    },
    ref
  ) => {
    return (
      <Animated.View
        ref={ref}
        entering={FadeIn.duration(300)}
        style={[styles.container, { width, height }, style]}>
        <Box
          width={width}
          height={height}
          backgroundColor={backgroundColor as any}
          borderRadius="md_12"
          justifyContent="center"
          alignItems="center"
          opacity={0.3}>
          {showLabel && (
            <Box padding="xs_8" alignItems="center">
              <Text variant="l_10SemiBold_tag" color="primary" textAlign="center" numberOfLines={2}>
                {animationName}
              </Text>
              <Text
                variant="l_10SemiBold_tag"
                color="textSecondary"
                textAlign="center"
                numberOfLines={3}
                marginTop="xxs_4">
                {description}
              </Text>
            </Box>
          )}
        </Box>
      </Animated.View>
    );
  }
);

RiveAnimationPlaceholder.displayName = 'RiveAnimationPlaceholder';

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
  },
});

/**
 * Pre-defined animation types for consistency
 */
export const RiveAnimations = {
  // Celebrations
  CELEBRATION_CONFETTI: {
    name: 'celebration-confetti',
    description: 'Colorful confetti burst for achievements and celebrations',
  },
  CELEBRATION_FIREWORKS: {
    name: 'celebration-fireworks',
    description: 'Fireworks animation for major milestones',
  },

  // Interactions
  HEART_LIKE: {
    name: 'heart-like',
    description: 'Heart animation that scales and fills when liked',
  },
  HEART_BURST: {
    name: 'heart-burst',
    description: 'Multiple hearts floating up when double-tapped',
  },
  COMMENT_BUBBLE: {
    name: 'comment-bubble',
    description: 'Speech bubble that bounces when tapped',
  },
  SHARE_RIPPLE: {
    name: 'share-ripple',
    description: 'Ripple effect emanating from share icon',
  },
  BOOKMARK_FILL: {
    name: 'bookmark-fill',
    description: 'Bookmark icon that fills with color when saved',
  },

  // Onboarding
  WELCOME_WAVE: {
    name: 'welcome-wave',
    description: 'Friendly character waving hello',
  },
  COMMUNITY_CONNECT: {
    name: 'community-connect',
    description: 'People connecting with animated lines',
  },
  ACHIEVEMENT_TROPHY: {
    name: 'achievement-trophy',
    description: 'Trophy with sparkles for achievement sharing',
  },

  // Loading States
  LOADING_SPINNER: {
    name: 'loading-spinner',
    description: 'Smooth loading animation',
  },
  SKELETON_PULSE: {
    name: 'skeleton-pulse',
    description: 'Content placeholder pulsing animation',
  },

  // Empty States
  EMPTY_SEARCH: {
    name: 'empty-search',
    description: 'Magnifying glass searching animation',
  },
  NO_CONNECTION: {
    name: 'no-connection',
    description: 'WiFi disconnected animation',
  },

  // Success/Error States
  SUCCESS_CHECK: {
    name: 'success-check',
    description: 'Checkmark with circle completion',
  },
  ERROR_CROSS: {
    name: 'error-cross',
    description: 'Error X with shake animation',
  },
  WARNING_ALERT: {
    name: 'warning-alert',
    description: 'Warning triangle pulsing',
  },
} as const;
