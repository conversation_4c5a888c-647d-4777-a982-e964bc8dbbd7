import React, { forwardRef } from 'react';

import { StyleSheet, View, ViewStyle } from 'react-native';

import Animated, { FadeIn } from 'react-native-reanimated';

import { Box, Text } from '@/src/core/theme';

export interface IllustrationPlaceholderProps {
  /**
   * The name/type of illustration that will be loaded
   */
  illustrationName: string;

  /**
   * Description of the illustration's content
   */
  description: string;

  /**
   * Dimensions for the illustration
   */
  width?: number;
  height?: number;

  /**
   * Additional styles
   */
  style?: ViewStyle;

  /**
   * Background color for the placeholder
   */
  backgroundColor?: string;

  /**
   * Show labels in development mode
   */
  showLabel?: boolean;
}

/**
 * Placeholder component for static illustrations
 * Will be replaced with actual SVG/PNG illustrations once assets are added
 *
 * Usage:
 * <IllustrationPlaceholder
 *   illustrationName="venue-restaurant"
 *   description="Restaurant icon with plate and utensils"
 *   width={24}
 *   height={24}
 * />
 */
export const IllustrationPlaceholder = forwardRef<View, IllustrationPlaceholderProps>(
  (
    {
      illustrationName,
      description,
      width = 100,
      height = 100,
      style,
      backgroundColor = 'secondaryLight',
      showLabel = __DEV__,
    },
    ref
  ) => {
    return (
      <Animated.View
        ref={ref}
        entering={FadeIn.duration(300)}
        style={[styles.container, { width, height }, style]}>
        <Box
          width={width}
          height={height}
          backgroundColor={backgroundColor as any}
          borderRadius="sm_8"
          justifyContent="center"
          alignItems="center"
          opacity={0.3}>
          {showLabel && width >= 50 && (
            <Box padding="xxxs_2" alignItems="center">
              <Text
                variant="l_10SemiBold_tag"
                color="secondary"
                textAlign="center"
                numberOfLines={1}
                fontSize={Math.min(10, width / 10)}>
                {illustrationName}
              </Text>
            </Box>
          )}
        </Box>
      </Animated.View>
    );
  }
);

IllustrationPlaceholder.displayName = 'IllustrationPlaceholder';

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
  },
});

/**
 * Pre-defined illustration types for consistency
 */
export const Illustrations = {
  // Venue Categories
  VENUE_RESTAURANT: {
    name: 'venue-restaurant',
    description: 'Restaurant with plate, fork and knife',
  },
  VENUE_CAFE: {
    name: 'venue-cafe',
    description: 'Coffee cup with steam',
  },
  VENUE_BAR: {
    name: 'venue-bar',
    description: 'Cocktail glass or beer mug',
  },
  VENUE_CLUB: {
    name: 'venue-club',
    description: 'Dancing figure with music notes',
  },
  VENUE_EVENT: {
    name: 'venue-event',
    description: 'Ticket or calendar with star',
  },
  VENUE_HOTEL: {
    name: 'venue-hotel',
    description: 'Building with bed icon',
  },
  VENUE_SHOPPING: {
    name: 'venue-shopping',
    description: 'Shopping bag with tag',
  },
  VENUE_ENTERTAINMENT: {
    name: 'venue-entertainment',
    description: 'Film reel or theater masks',
  },
  VENUE_OUTDOORS: {
    name: 'venue-outdoors',
    description: 'Tree or mountain landscape',
  },
  VENUE_CULTURAL: {
    name: 'venue-cultural',
    description: 'Museum building or art palette',
  },
  VENUE_SPORTS: {
    name: 'venue-sports',
    description: 'Ball or sports equipment',
  },
  VENUE_OTHER: {
    name: 'venue-other',
    description: 'Generic location pin',
  },

  // Analytics Icons
  ANALYTICS_ENGAGEMENT: {
    name: 'analytics-engagement',
    description: 'Graph with upward trend',
  },
  ANALYTICS_REACH: {
    name: 'analytics-reach',
    description: 'Expanding circles or broadcast icon',
  },
  ANALYTICS_TIME: {
    name: 'analytics-time',
    description: 'Clock with activity indicators',
  },
  ANALYTICS_AUDIENCE: {
    name: 'analytics-audience',
    description: 'Group of people silhouettes',
  },

  // Onboarding Illustrations
  ONBOARDING_WELCOME: {
    name: 'onboarding-welcome',
    description: 'Friendly character or warm scene',
  },
  ONBOARDING_SHARE: {
    name: 'onboarding-share',
    description: 'People sharing content together',
  },
  ONBOARDING_CONNECT: {
    name: 'onboarding-connect',
    description: 'Network of connected people',
  },

  // Empty States
  EMPTY_POSTS: {
    name: 'empty-posts',
    description: 'Empty feed illustration',
  },
  EMPTY_NOTIFICATIONS: {
    name: 'empty-notifications',
    description: 'Silent bell or inbox',
  },
  EMPTY_SEARCH_RESULTS: {
    name: 'empty-search-results',
    description: 'Magnifying glass with question mark',
  },

  // Status Icons
  RATING_STAR: {
    name: 'rating-star',
    description: 'Filled star for ratings',
  },
  STATUS_OPEN: {
    name: 'status-open',
    description: 'Green circle or open sign',
  },
  STATUS_CLOSED: {
    name: 'status-closed',
    description: 'Red circle or closed sign',
  },
} as const;
