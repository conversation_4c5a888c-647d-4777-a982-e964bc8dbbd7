import React, { ReactNode, useRef } from 'react';

import { Feather } from '@expo/vector-icons';
import { BoxProps } from '@shopify/restyle';

import Chip from '@/src/shared/components/Chip';

import Box from '../../core/theme/Box';
import Pressable, { PressableAnimated } from '../../core/theme/Pressable';
import Text from '../../core/theme/Text';
import { Theme } from '../../core/theme/theme';
import { useResponsive } from '../../core/theme/useResponsive';

interface ListProps extends BoxProps<Theme> {
  children: ReactNode;
  layout?: 'list' | 'grid' | 'responsive';
  columns?: number;
}

interface ListItemProps extends BoxProps<Theme> {
  title: string;
  subtitle?: string;
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
  onPress?: () => void;
  children?: ReactNode;
  showArrow?: boolean;
}

interface ListItemOptionProps extends BoxProps<Theme> {
  title: string;
  selected?: boolean;
  onPress?: () => void;
  leftIcon?: ReactNode;
}

interface ListItemShortcutProps extends BoxProps<Theme> {
  label: string;
  leading?: ReactNode;
  onPress?: () => void;
}

function List({ children, layout = 'responsive', columns = 4, ...rest }: ListProps) {
  const { isTablet, isDesktop, select } = useResponsive();
  const shouldUseGrid = layout === 'grid' || (layout === 'responsive' && (isTablet || isDesktop));

  const processedChildrenRef = useRef<React.ReactNode[]>([]);
  const processChildrenWithDividers = (childrenToProcess: React.ReactNode) => {
    const childrenArray = React.Children.toArray(childrenToProcess);

    if (processedChildrenRef.current.length > 0) {
      processedChildrenRef.current = [];
    }

    childrenArray.forEach((child, index) => {
      processedChildrenRef.current.push(child);

      if (
        index < childrenArray.length - 1 &&
        React.isValidElement(child) &&
        child.type === List.Item
      ) {
        processedChildrenRef.current.push(
          <Box key={`divider-${index}`} height={1} backgroundColor="border" />
        );
      }
    });

    return processedChildrenRef.current;
  };

  if (shouldUseGrid) {
    // Convert children to array for the grid
    const childrenArray = React.Children.toArray(children);

    // Determine appropriate columns based on screen size
    const effectiveColumns = select({
      phone: 1,
      tablet: Math.min(columns, 3), // Max 3 columns on tablet
      desktop: columns,
    });

    // Group items into rows
    const rows = [];
    for (let i = 0; i < childrenArray.length; i += effectiveColumns) {
      const rowItems = childrenArray.slice(i, i + effectiveColumns);
      rows.push(rowItems);
    }

    return (
      <Box {...rest}>
        {rows.map((rowItems, rowIndex) => (
          <React.Fragment key={`row-${rowIndex}`}>
            <Box flexDirection="row" flexWrap="wrap" alignItems="center">
              {rowItems.map((child, colIndex) => {
                const isLastColumn =
                  colIndex === effectiveColumns - 1 || colIndex === rowItems.length - 1;

                return (
                  <Box
                    key={`grid-item-${rowIndex}-${colIndex}`}
                    width={`${100 / effectiveColumns}%`}
                    borderRightWidth={isLastColumn ? 0 : 1}
                    borderColor="border"
                    padding="xxs_4">
                    {child}
                  </Box>
                );
              })}
              {/* Add empty cells to complete the row if needed */}
              {rowItems.length < effectiveColumns &&
                Array(effectiveColumns - rowItems.length)
                  .fill(null)
                  .map((_, index) => (
                    <Box
                      key={`empty-${rowIndex}-${index}`}
                      width={`${100 / effectiveColumns}%`}
                      padding="xxs_4"
                    />
                  ))}
            </Box>

            {/* Add divider after each row except the last one */}
            {rowIndex < rows.length - 1 && (
              <Box height={1} backgroundColor="border" marginVertical="xs_8" />
            )}
          </React.Fragment>
        ))}
      </Box>
    );
  }

  const processedChildren = processChildrenWithDividers(children);
  return <Box {...rest}>{processedChildren}</Box>;
}

List.Item = function ListItem({
  title,
  subtitle,
  leftIcon,
  rightIcon,
  onPress,
  children,
  showArrow = true,
  ...rest
}: ListItemProps) {
  const { select } = useResponsive();

  return (
    <Pressable
      gap="xxs_4"
      px="md_16"
      flex={1}
      onPress={onPress}
      rippleColor={null}
      rippleRadius={null}
      activeOpacity={0}>
      <PressableAnimated onPress={onPress}>
        <Box flexDirection="row" alignItems={{ phone: 'center', tablet: 'flex-start' }} {...rest}>
          {leftIcon && (
            <Box marginRight="sm_12" alignItems="center">
              {leftIcon}
            </Box>
          )}
          <Box flex={1}>
            <Text variant="b_16SemiBold_button">{title}</Text>
            {subtitle && (
              <Text variant="l_12SemiBold_button" color="textSecondary">
                {subtitle}
              </Text>
            )}
          </Box>
          {rightIcon && <Box marginLeft="sm_12">{rightIcon}</Box>}
          {showArrow && onPress && (
            <Box marginLeft="sm_12">
              <Feather
                name="chevron-right"
                size={select({ phone: 20, tablet: 24, desktop: 24 }, 20)}
                color="#8E8E93"
              />
            </Box>
          )}
        </Box>
      </PressableAnimated>
      {children && (
        <Box flexWrap="wrap" flexDirection="row" gap="xs_8">
          {children}
        </Box>
      )}
    </Pressable>
  );
};

List.ItemOption = function ListItemOption({
  title,
  selected,
  onPress,
  leftIcon,
  ...rest
}: ListItemOptionProps) {
  return (
    <PressableAnimated onPress={onPress}>
      <Box
        flexDirection="row"
        alignItems="center"
        backgroundColor={selected ? 'surfaceBackground' : 'background'}
        {...rest}>
        {leftIcon && <Box marginRight="sm_12">{leftIcon}</Box>}
        <Text variant="b_16Regular_input" flex={1}>
          {title}
        </Text>
        {selected && (
          <Box marginLeft="sm_12">
            <Feather name="check" size={20} color="#007AFF" />
          </Box>
        )}
      </Box>
    </PressableAnimated>
  );
};

List.ItemShortcut = function ListItemShortcut({ label, leading, onPress }: ListItemShortcutProps) {
  return (
    <Box flexDirection="row" accessibilityRole="button" accessibilityState={{ selected: false }}>
      <Chip
        label={label}
        onPress={onPress}
        chipVariant="secondarySmall"
        textProps={{ color: 'primaryText' }}
        leftIcon={leading}
      />
    </Box>
  );
};

export default List;
