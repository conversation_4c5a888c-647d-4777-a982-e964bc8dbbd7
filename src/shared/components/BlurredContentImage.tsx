import React, { forwardRef, useState } from 'react';

import { ViewStyle, useWindowDimensions } from 'react-native';

import FastImage, { FastImageProps } from '@d11/react-native-fast-image';
import {
  Blur,
  Canvas,
  Group,
  LinearGradient,
  Rect,
  Image as SkiaImage,
  rect,
  useImage,
  vec,
} from '@shopify/react-native-skia';

import { Box, Text, useTheme } from '@/src/core/theme';

export interface BlurredContentImageProps {
  /**
   * Image source URI
   */
  source: string;

  /**
   * Container style
   */
  style?: ViewStyle;

  /**
   * Image dimensions
   */
  width?: number;
  height?: number;

  /**
   * Blur configuration
   */
  blur?: {
    /** Blur intensity (0-20) */
    intensity?: number;
    /** Region to blur: 'top' | 'bottom' */
    region?: 'top' | 'bottom';
    /** Height percentage of blur region (0-1) */
    regionHeight?: number;
    /** Enable gradient transition */
    useGradient?: boolean;
  };

  /**
   * Text content configuration
   */
  content?: React.ReactNode;

  /**
   * FastImage props
   */
  imageProps?: Omit<FastImageProps, 'source' | 'style'>;

  /**
   * Loading state callback
   */
  onLoadingChange?: (loading: boolean) => void;

  /**
   * Error callback
   */
  onError?: () => void;
}

export const BlurredContentImage = forwardRef<any, BlurredContentImageProps>(
  (
    {
      source,
      style,
      width: propWidth,
      height: propHeight,
      blur = {},
      content,
      imageProps,
      onLoadingChange,
      onError,
    },
    ref
  ) => {
    const theme = useTheme();
    const screen = useWindowDimensions();
    const [isLoading, setIsLoading] = useState(true);
    const [hasError, setHasError] = useState(false);

    // Default dimensions
    const defaultWidth = screen.width - theme.spacing.xl_32;
    const defaultHeight = 240;

    const width = (propWidth || defaultWidth) - theme.spacing.xxs_4;
    const height = propHeight || defaultHeight;

    // Blur configuration with defaults
    const { intensity = 8, region = 'bottom', regionHeight = 0.4, useGradient = true } = blur;

    // Load image for Skia
    const skiaImage = useImage(source);

    // Calculate blur region
    const blurRegionHeight = height * regionHeight;
    const blurRect =
      region === 'bottom'
        ? rect(0, height - blurRegionHeight, width, blurRegionHeight)
        : rect(0, 0, width, blurRegionHeight);

    // Handle FastImage events
    const handleImageLoad = () => {
      setIsLoading(false);
      setHasError(false);
      onLoadingChange?.(false);
    };

    const handleImageError = () => {
      setIsLoading(false);
      setHasError(true);
      onLoadingChange?.(false);
      onError?.();
    };

    const handleLoadStart = () => {
      setIsLoading(true);
      onLoadingChange?.(true);
    };

    if (hasError) {
      return (
        <Box
          ref={ref}
          width={width}
          height={height}
          backgroundColor="mainBackground"
          borderRadius="lg_16"
          justifyContent="center"
          alignItems="center"
          style={style}>
          <Text variant="b_14Regular_content" color="mutedText">
            Failed to load image
          </Text>
        </Box>
      );
    }

    return (
      <Box
        ref={ref}
        width={width}
        height={height}
        position="relative"
        borderRadius="lg_16"
        overflow="hidden"
        style={style}>
        {/* FastImage Background */}
        <FastImage
          source={{ uri: source }}
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width,
            height,
          }}
          resizeMode="cover"
          onLoadStart={handleLoadStart}
          onLoad={handleImageLoad}
          onError={handleImageError}
          {...imageProps}
        />

        {/* Loading State */}
        {isLoading && (
          <Box
            position="absolute"
            top={0}
            left={0}
            width={width}
            height={height}
            backgroundColor="mainBackground"
            justifyContent="center"
            alignItems="center">
            <Text variant="b_14Regular_content" color="mutedText">
              Loading...
            </Text>
          </Box>
        )}

        {/* Skia Blur Overlay - Only render when image is loaded and available */}
        {!isLoading && !hasError && skiaImage && (
          <Canvas
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width,
              height,
            }}>
            {/* Render image for blur effect */}
            <Group clip={blurRect}>
              <SkiaImage x={0} y={0} width={width} height={height} image={skiaImage} fit="cover" />
              <Blur blur={intensity} mode="clamp" />

              {/* Optional gradient overlay for better text readability */}
              {useGradient && (
                <Rect x={0} y={0} width={width} height={height}>
                  <LinearGradient
                    start={vec(0, region === 'bottom' ? height - blurRegionHeight : 0)}
                    end={vec(0, region === 'bottom' ? height : blurRegionHeight)}
                    colors={
                      region === 'bottom'
                        ? ['rgba(0,0,0,0)', 'rgba(0,0,0,0.4)']
                        : ['rgba(0,0,0,0.4)', 'rgba(0,0,0,0)']
                    }
                  />
                </Rect>
              )}
            </Group>
          </Canvas>
        )}

        <Box justifyContent="flex-end" flex={1}>
          {content}
        </Box>
      </Box>
    );
  }
);

BlurredContentImage.displayName = 'BlurredContentImage';
