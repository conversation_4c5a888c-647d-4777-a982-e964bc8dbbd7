import React, { ComponentProps, forwardRef } from 'react';

import { View } from 'react-native';

import { BoxProps, VariantProps, createRestyleComponent, createVariant } from '@shopify/restyle';
import { IconProps } from 'phosphor-react-native';
import Animated, { useAnimatedStyle, useSharedValue, withSpring } from 'react-native-reanimated';

import { Text } from '@/src/core/theme';
import Box from '@/src/core/theme/Box';
import { Row } from '@/src/core/theme/Layout';
import { PressableAnimated } from '@/src/core/theme/Pressable';
import { Theme, useTheme } from '@/src/core/theme/theme';

import { ButtonVariantAnimations } from '../constants/animation';

// Create base components with variants
const BaseChip = createRestyleComponent<
  VariantProps<Theme, 'chipVariants'> & BoxProps<Theme> & { children: React.ReactNode },
  Theme
>([createVariant({ themeKey: 'chipVariants' })], Animated.View);

type ChipProps = {
  label: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  onPress?: () => void;
  selected?: boolean;
  enabled?: boolean;
  dismissible?: boolean;
  onDismiss?: () => void;
  chipVariant?: ComponentProps<typeof BaseChip>['variant'];
  chipProps?: Partial<ComponentProps<typeof BaseChip>>;
  textProps?: Partial<ComponentProps<typeof Text>>;
};

/**
 * Chip component for displaying tags, filters, and selections
 */
const Chip = forwardRef<View, ChipProps>(function Chip(props, ref) {
  const {
    label,
    leftIcon,
    rightIcon,
    onPress,
    selected = false,
    enabled = true,
    dismissible = false,
    onDismiss,
    chipVariant = 'solidSmall',
    chipProps,
    textProps,
  } = props;

  const scale = useSharedValue(1);

  const handleActiveStateChange = (active: boolean) => {
    const variantAnimation = ButtonVariantAnimations.primary;
    scale.set(
      withSpring(
        active ? variantAnimation.scale.pressed : variantAnimation.scale.default,
        variantAnimation.config
      )
    );
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const theme = useTheme();

  const baseChipVariant = !enabled ? 'disabled' : selected ? 'selected' : chipVariant;

  const textColor = chipVariant.toString().toLowerCase().includes('solid')
    ? 'buttonTintedText'
    : 'buttonOutlineText';

  return (
    <PressableAnimated
      ref={ref}
      onPress={onPress}
      activeOpacity={0}
      onActiveStateChange={handleActiveStateChange}
      style={animatedStyle}
      enabled={enabled}
      accessibilityRole="button"
      accessibilityState={{
        disabled: !enabled,
        selected,
      }}>
      <BaseChip variant={baseChipVariant} {...chipProps}>
        <Row align="center" justify="center">
          {leftIcon && <Box marginRight="xxs_4">{leftIcon}</Box>}

          <Text variant="l_10SemiBold_chip" color={textColor} {...textProps}>
            {label}
          </Text>

          {(rightIcon || dismissible) && (
            <Box marginLeft="xxs_4">
              {dismissible ? (
                <PressableAnimated
                  onPress={enabled ? onDismiss : undefined}
                  variant="ghost"
                  enabled={!enabled}
                  hitSlop={8}
                  accessibilityRole="button"
                  accessibilityLabel={`Remove ${label}`}>
                  <Text variant="b_14Regular_content" color={textColor}>
                    X
                  </Text>
                </PressableAnimated>
              ) : (
                React.cloneElement(rightIcon as React.ReactElement<IconProps>, {
                  size: 16,
                  color: rightIcon?.props.color || theme.colors[textColor],
                })
              )}
            </Box>
          )}
        </Row>
      </BaseChip>
    </PressableAnimated>
  );
});

export default Chip;
