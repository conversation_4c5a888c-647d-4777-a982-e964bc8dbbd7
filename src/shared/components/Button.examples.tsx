import React from 'react';

import { ScrollView } from 'react-native';

import { AppleLogo, GoogleLogo, Heart, Plus, Share } from 'phosphor-react-native';

import { Box, Text, useTheme } from '@/src/core/theme';

import Button from './Button.new';

/**
 * Button Examples showcasing the multi-dimensional variant system
 * Demonstrates all combinations of intent, type, size, and special variants
 */
export const ButtonExamples = () => {
  const theme = useTheme();

  return (
    <ScrollView style={{ flex: 1, backgroundColor: theme.colors.background }}>
      <Box padding="lg_24" gap="xl_32">
        {/* Primary Intent Examples */}
        <Box gap="md_16">
          <Text variant="h_24SemiBold_section">Primary Intent</Text>

          <Box gap="sm_12">
            <Button title="Primary Filled" intent="primary" type="filled" />
            <Button title="Primary Outlined" intent="primary" type="outlined" />
            <Button title="Primary Borderless" intent="primary" type="borderless" />
            <Button title="Primary Tinted" intent="primary" type="tinted" />
            <Button title="Primary Ghost" intent="primary" type="ghost" />
          </Box>
        </Box>

        {/* Secondary Intent Examples */}
        <Box gap="md_16">
          <Text variant="h_24SemiBold_section">Secondary Intent</Text>

          <Box gap="sm_12">
            <Button title="Secondary Filled" intent="secondary" type="filled" />
            <Button title="Secondary Outlined" intent="secondary" type="outlined" />
            <Button title="Secondary Tinted" intent="secondary" type="tinted" />
          </Box>
        </Box>

        {/* Danger Intent Examples */}
        <Box gap="md_16">
          <Text variant="h_24SemiBold_section">Danger Intent</Text>

          <Box gap="sm_12">
            <Button title="Danger Filled" intent="danger" type="filled" />
            <Button title="Danger Outlined" intent="danger" type="outlined" />
            <Button title="Danger Tinted" intent="danger" type="tinted" />
          </Box>
        </Box>

        {/* Size Variations */}
        <Box gap="md_16">
          <Text variant="h_24SemiBold_section">Size Variations</Text>

          <Box flexDirection="row" gap="sm_12" flexWrap="wrap">
            <Button title="Small" size="small" />
            <Button title="Default" size="default" />
            <Button title="Large" size="large" />
          </Box>
        </Box>

        {/* With Icons */}
        <Box gap="md_16">
          <Text variant="h_24SemiBold_section">With Icons</Text>

          <Box gap="sm_12">
            <Button
              title="With Left Icon"
              leftIcon={<Heart size={20} color={theme.colors.white} />}
            />
            <Button
              title="With Right Icon"
              rightIcon={<Share size={20} color={theme.colors.white} />}
            />
            <Button
              title="Both Icons"
              leftIcon={<Heart size={20} color={theme.colors.white} />}
              rightIcon={<Share size={20} color={theme.colors.white} />}
            />
          </Box>
        </Box>

        {/* Icon Only Buttons */}
        <Box gap="md_16">
          <Text variant="h_24SemiBold_section">Icon Only Buttons</Text>

          <Box flexDirection="row" gap="sm_12" flexWrap="wrap">
            <Button
              iconOnly
              iconVariant="small"
              leftIcon={<Plus size={14} color={theme.colors.primary} />}
            />
            <Button
              iconOnly
              iconVariant="default"
              leftIcon={<Heart size={20} color={theme.colors.primary} />}
            />
            <Button
              iconOnly
              iconVariant="large"
              leftIcon={<Share size={24} color={theme.colors.primary} />}
            />
          </Box>
        </Box>

        {/* Social Login Buttons */}
        <Box gap="md_16">
          <Text variant="h_24SemiBold_section">Social Login Buttons</Text>

          <Box gap="sm_12">
            <Button
              title="Continue with Apple"
              socialVariant="apple"
              leftIcon={<AppleLogo size={20} color={theme.colors.white} weight="fill" />}
              fullWidth
            />
            <Button
              title="Continue with Google"
              socialVariant="google"
              leftIcon={<GoogleLogo size={20} color={theme.colors.text} weight="bold" />}
              fullWidth
            />
          </Box>
        </Box>

        {/* States */}
        <Box gap="md_16">
          <Text variant="h_24SemiBold_section">States</Text>

          <Box gap="sm_12">
            <Button title="Enabled" />
            <Button title="Disabled" enabled={false} />
            <Button title="Loading" loading />
            <Button title="Full Width" fullWidth />
          </Box>
        </Box>

        {/* Complex Combinations */}
        <Box gap="md_16">
          <Text variant="h_24SemiBold_section">Complex Combinations</Text>

          <Box gap="sm_12">
            <Button
              title="Small Secondary Outlined"
              intent="secondary"
              type="outlined"
              size="small"
            />
            <Button
              title="Large Danger Tinted"
              intent="danger"
              type="tinted"
              size="large"
              fullWidth
            />
            <Button
              title="Success with Icon"
              intent="success"
              type="filled"
              leftIcon={<Heart size={20} color={theme.colors.white} />}
            />
          </Box>
        </Box>
      </Box>
    </ScrollView>
  );
};
