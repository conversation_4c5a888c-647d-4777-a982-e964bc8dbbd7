/**
 * Checkbox Component Export
 *
 * An advanced checkbox component featuring sophisticated Skia-based checkmark animations.
 * Demonstrates cutting-edge mobile graphics programming with mathematical path construction,
 * stroke-dasharray animation techniques, and coordinated multi-phase animations.
 *
 * Key Features:
 * - Multiple shape variants (circular, rounded, squared)
 * - Advanced animation styles including signature "draw" effect
 * - Mathematical path construction for perfect proportions
 * - Performance-optimized Skia rendering
 * - Comprehensive accessibility support
 * - Seamless theme system integration
 */

export { default } from './Checkbox';
export { default as Checkbox } from './Checkbox';
export type { CheckboxProps } from './Checkbox';
export {
  CHECKBOX_CONSTANTS,
  CHECKBOX_COLORS,
  CHECKBOX_SPRING_CONFIG,
  CHECKBOX_ANIMATION_TIMINGS,
  CHECKBOX_CHECKMARK_GEOMETRY,
  CHECKBOX_PERFORMANCE,
  CheckboxShape,
  CheckboxAnimationStyle,
} from './constants';
