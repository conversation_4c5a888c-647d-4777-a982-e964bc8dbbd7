/**
 * Simplified Checkbox Constants for React Native Skia 2.0
 */

export const CHECKBOX_CONSTANTS = {
  DEFAULT_SIZE: 24,
  MIN_SIZE: 16,
  MAX_SIZE: 48,

  SHAPES: {
    CIRCULAR: 'circular',
    ROUNDED: 'rounded',
    SQUARED: 'squared',
  } as const,

  BORDER_RADIUS: {
    CIRCULAR: 9999,
    ROUNDED: 8,
    SQUARED: 4,
  },

  CHECKMARK: {
    STROKE_WIDTH: 4,
    PADDING_RATIO: 0.25,
  },

  BORDER_WIDTH_DEFAULT: 2,
  BORDER_WIDTH_CHECKED: 0,

  HIT_SLOP: {
    top: 8,
    bottom: 8,
    left: 8,
    right: 8,
  },
} as const;

export const CHECKBOX_SPRING_CONFIG = {
  damping: 18,
  stiffness: 300,
  mass: 0.9,
} as const;

export const CHECKBOX_ANIMATION_STYLES = {
  DRAW: 'draw',
  FADE: 'fade',
  POP: 'pop',
  ELASTIC: 'elastic',
} as const;

export const CHECKBOX_ANIMATION_TIMINGS = {
  DRAW: {
    TOTAL_DURATION: 350,
    EXIT: { TOTAL_DURATION: 120 },
  },
  FADE: {
    TOTAL_DURATION: 250,
    EXIT: { TOTAL_DURATION: 180 },
  },
  POP: {
    TOTAL_DURATION: 300,
    EXIT: { TOTAL_DURATION: 150 },
  },
  ELASTIC: {
    TOTAL_DURATION: 400,
    EXIT: { TOTAL_DURATION: 200 },
  },
} as const;
// TypeScript types
export type CheckboxShape =
  (typeof CHECKBOX_CONSTANTS.SHAPES)[keyof typeof CHECKBOX_CONSTANTS.SHAPES];
export type CheckboxAnimationStyle =
  (typeof CHECKBOX_ANIMATION_STYLES)[keyof typeof CHECKBOX_ANIMATION_STYLES];
