import { useEffect, useMemo } from 'react';

import {
  Canvas,
  DashPathEffect,
  DiffRect,
  Path,
  RoundedRect,
  Skia,
  interpolate,
  interpolateColors,
  rect,
  rrect,
} from '@shopify/react-native-skia';
import { useDerivedValue, useSharedValue, withTiming } from 'react-native-reanimated';

import { PressableAnimated } from '@/src/core/theme/Pressable';
import { useTheme } from '@/src/core/theme/theme';

type CheckboxProps = {
  isChecked: boolean;
  onChange: (isChecked: boolean) => void;
  size?: number;
};

const SimpleCheckbox = ({ isChecked = false, onChange, size = 24 }: CheckboxProps) => {
  const { colors, borderRadii, strokes } = useTheme();
  const borderColor = colors.primary;
  const checkmarkColor = 'white';
  const borderRadius = borderRadii.xxs_2;

  // Shared value for animation progress
  const progress = useSharedValue(isChecked ? 1 : 0);

  // Animate progress when isChecked changes
  useEffect(() => {
    progress.value = withTiming(isChecked ? 1 : 0, { duration: 300 });
  }, [isChecked, progress]);

  // Animated background color for Skia
  const animatedBackgroundColor = useDerivedValue(() => {
    return interpolateColors(progress.value, [0, 1], [colors.transparent, colors.primary]);
  });

  // Define the checkmark path (a "V" shape)
  const checkmarkPath = useMemo(() => {
    return Skia.Path.MakeFromSVGString(
      `M ${0.25 * size} ${0.55 * size} L ${0.42 * size} ${0.72 * size} L ${0.75 * size} ${0.32 * size}`
    );
  }, [size]);

  // Calculate the path length for the dash effect
  const pathLength = useMemo(() => {
    if (checkmarkPath) {
      const contourIter = Skia.ContourMeasureIter(checkmarkPath, false, 1);
      const contour = contourIter.next();
      return contour ? contour.length() : 0;
    }
    return 0;
  }, [checkmarkPath]);

  // Animate dash phase based on progress
  const dashPhase = useDerivedValue(() => {
    return interpolate(progress.value, [0, 1], [pathLength, 0]);
  });

  const outer = rrect(rect(0, 0, size, size), borderRadius * 2, borderRadius * 2);
  const inner = rrect(
    rect(borderRadius, borderRadius, size - borderRadius * 2, size - borderRadius * 2),
    borderRadius,
    borderRadius
  );
  const background = rrect(rect(0, 0, size, size), borderRadius * 2, borderRadius * 2);

  return (
    <PressableAnimated onPress={() => onChange(!isChecked)} style={{ width: size, height: size }}>
      <Canvas style={{ width: size, height: size }}>
        <RoundedRect rect={background} color={animatedBackgroundColor} />

        {/* Border */}
        <DiffRect inner={inner} outer={outer} color={borderColor} />

        {/* Checkmark */}
        {checkmarkPath && (
          <Path
            path={checkmarkPath}
            color={checkmarkColor}
            style="stroke"
            strokeWidth={strokes.bold_3}
            strokeCap="round"
            strokeJoin="round">
            <DashPathEffect intervals={[pathLength, pathLength]} phase={dashPhase} />
          </Path>
        )}
      </Canvas>
    </PressableAnimated>
  );
};

export default SimpleCheckbox;
