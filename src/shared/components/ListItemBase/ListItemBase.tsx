import React from 'react';

import { Box, Row, Text, useTheme } from '@/src/core/theme';
import { PressableAnimated } from '@/src/core/theme/Pressable';

export type ListItemBaseProps = {
  /**
   * Icon to display on the left side of the list item
   */
  icon?: React.ReactElement<{ color?: string; weight?: string; size?: number }>;

  /**
   * Primary text to display
   */
  title: string;

  /**
   * Optional secondary text to display above the title
   */
  label?: string;

  /**
   * Optional description text to display below the title
   */
  description?: string;

  /**
   * Optional right side component (e.g., Switch, icon, etc.)
   */
  rightElement?: React.ReactNode;

  /**
   * Function called when the item is pressed (if pressable)
   */
  onPress?: () => void;

  /**
   * Whether the item is in a disabled state
   */
  disabled?: boolean;

  /**
   * Whether the item is dangerous (e.g., delete account)
   */
  danger?: boolean;
};

/**
 * A base component for list items that can be used throughout the app
 * Provides consistent layout and styling for items in lists
 */
export const ListItemBase: React.FC<ListItemBaseProps> = ({
  icon,
  title,
  label,
  description,
  rightElement,
  onPress,
  disabled = false,
  danger = false,
}) => {
  const { colors } = useTheme();

  const iconColor = danger ? colors.error : disabled ? colors.textTertiary : colors.iconActiveInput;
  const titleColor = danger ? 'error' : disabled ? 'textTertiary' : 'text';

  const content = (
    <Row width="100%" justifyContent="space-between" alignItems="center">
      <Row gap="xs_8" flex={1}>
        {icon &&
          React.cloneElement(icon, {
            color: iconColor,
            weight: 'regular',
            size: icon.props.size || 20,
          })}
        <Box flex={1} pr="md_16">
          {label && (
            <Text variant="l_8SemiBold_hint" color="mutedText">
              {label}
            </Text>
          )}
          <Text variant="b_14SemiBold_listTitle" color={titleColor}>
            {title}
          </Text>
          {description && (
            <Text variant="l_12Regular_helperText" color="textSecondary" marginTop="xxxs_2">
              {description}
            </Text>
          )}
        </Box>
      </Row>
      {rightElement}
    </Row>
  );

  if (onPress && !disabled) {
    return (
      <PressableAnimated onPress={onPress} enabled={!disabled} style={{ width: '100%' }}>
        {content}
      </PressableAnimated>
    );
  }

  return content;
};
