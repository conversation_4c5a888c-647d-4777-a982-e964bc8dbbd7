import React from 'react';

import { createRestyleComponent, createVariant } from '@shopify/restyle';
import { CaretLeft } from 'phosphor-react-native';

import { Text, Theme, useTheme } from '@/src/core/theme';
import { PressableAnimated, PressableProps } from '@/src/core/theme/Pressable';
import { RoundedButtonVariants } from '@/src/core/theme/variants/roundedButton';

type VariantSize = 'extraSmall' | 'small' | 'medium';

type RoundedButtonComponentProps = {
  onPress?: () => void;
  variant?: VariantSize;
  children?: React.ReactNode;
} & Omit<RoundedButtonVariants, 'variant'> &
  Omit<PressableProps, 'borderColor'>;

const BaseRoundedButton = createRestyleComponent<
  RoundedButtonVariants & Omit<PressableProps, 'borderColor'>,
  Theme
>([createVariant({ themeKey: 'roundedButtonVariants' })], PressableAnimated);

const iconSizes: Record<VariantSize, number> = {
  extraSmall: 12,
  small: 18,
  medium: 24,
};

const RoundedButton = ({
  variant = 'medium',
  onPress,
  children,
  ...rest
}: RoundedButtonComponentProps) => {
  const theme = useTheme();

  const size = iconSizes[variant];

  const content = children || (
    <CaretLeft size={size} color={theme.colors['iconActiveInput']} weight="bold" />
  );

  return (
    <BaseRoundedButton variant={variant} onPress={onPress} {...rest}>
      {content}
    </BaseRoundedButton>
  );
};

export { RoundedButton };
