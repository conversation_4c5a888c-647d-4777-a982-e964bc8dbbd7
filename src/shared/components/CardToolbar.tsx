import React from 'react';

import {
  BoxProps,
  VariantProps,
  composeRestyleFunctions,
  createVariant,
  useRestyle,
} from '@shopify/restyle';
import { At, PencilSimple } from 'phosphor-react-native';

import { Box, Text } from '@/src/core/theme';
import Pressable from '@/src/core/theme/Pressable';
import { Theme, useTheme } from '@/src/core/theme/theme';

type CardToolbarProps = {
  title?: string;
  actionText?: string;
  layout?: 'default' | 'twoIcons' | 'header';
  onPress?: () => void;
  onSecondPress?: () => void; // For twoIcons layout
};

// Define props without ref to avoid TypeScript errors with Restyle
type CardToolbarComponentProps = BoxProps<Theme> &
  VariantProps<Theme, 'cardToolbarVariants'> &
  CardToolbarProps;

// Create the card toolbar variant restyle function
const cardToolbarVariant = createVariant<Theme, 'cardToolbarVariants'>({
  themeKey: 'cardToolbarVariants',
});

// Compose the restyle function
const restyleFunction = composeRestyleFunctions<Theme, CardToolbarComponentProps>([
  cardToolbarVariant,
]);

/**
 * Card Toolbar component with multiple layout variants
 * Supports default (text + icon), twoIcons, and header layouts
 */
const CardToolbar: React.FC<CardToolbarComponentProps> = props => {
  const theme = useTheme();
  const restyleProps = useRestyle(restyleFunction, props);

  const { title = 'Details', layout = 'default', onPress, onSecondPress, ...rest } = props;

  const renderDefaultLayout = () => (
    <>
      <Text variant="h_18Bold_formTitle" color="mainText">
        {title}
      </Text>
    </>
  );

  const renderTwoIconsLayout = () => (
    <>
      <Text variant="h_18Bold_formTitle" color="mainText">
        {title}
      </Text>
      <Box flexDirection="row" alignItems="center" gap="xs_8">
        <Pressable onPress={onPress}>
          <At size={20} color={theme.colors.iconPrimary} weight="bold" />
        </Pressable>
        <Pressable onPress={onSecondPress}>
          <At size={20} color={theme.colors.iconPrimary} weight="bold" />
        </Pressable>
      </Box>
    </>
  );

  const renderHeaderLayout = () => (
    <>
      <Text variant="l_12SemiBold_button" color="mutedText">
        {title.toUpperCase()}
      </Text>
      <Pressable onPress={onPress}>
        <PencilSimple size={12} color={theme.colors.mutedText} weight="thin" />
      </Pressable>
    </>
  );

  const getLayoutContent: () => React.ReactNode = () => {
    switch (layout) {
      case 'twoIcons':
        return renderTwoIconsLayout();
      case 'header':
        return renderHeaderLayout();
      default:
        return renderDefaultLayout();
    }
  };

  const getLayoutStyle = () => {
    switch (layout) {
      case 'header':
        return {
          gap: 'xxs_4',
        };
      default:
        return {
          gap: 'xs_8',
        };
    }
  };

  return (
    // @ts-ignore
    <Box
      flexDirection="row"
      alignItems="center"
      justifyContent="space-between"
      width={380}
      {...getLayoutStyle()}
      {...restyleProps}
      {...rest}>
      {getLayoutContent()}
    </Box>
  );
};

export default CardToolbar;
