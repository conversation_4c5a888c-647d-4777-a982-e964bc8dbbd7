import { ScrollView } from 'react-native';

import { Box, Text } from '@/src/core/theme';

import { TicketCard } from './TicketCard';

/**
 * Example usage of ticket components
 * This file demonstrates how to use the different ticket variants
 */
export const TicketExamples = () => {
  return (
    <ScrollView>
      <Box padding="lg_24" gap="xl_32">
        <Text variant="h_24SemiBold_section">Ticket Component Examples</Text>

        {/* Transfer Ticket Example */}
        <Box>
          <Text variant="h_18Bold_formTitle" marginBottom="sm_12">
            Transfer Ticket
          </Text>
          <TicketCard
            variant="transfer"
            props={{
              eventName: 'Rock in Rio 2024',
              recipientName: '<PERSON>',
              ticketType: 'Pista comum - Entrada geral',
              eventDate: 'Sábado, 22 de Julho, 20:00',
              eventLocation: 'Cidade do Rock - Rio de Janeiro',
              expiresIn: '24 horas',
              onCancel: () => console.log('Cancel transfer'),
              onMessage: () => console.log('Send message'),
              onContact: () => console.log('Contact'),
              onViewDetails: () => console.log('View details'),
              onResendInvite: () => console.log('Resend invite'),
            }}
          />
        </Box>

        {/* Urgent Ticket Example */}
        <Box>
          <Text variant="h_18Bold_formTitle" marginBottom="sm_12">
            Urgent Ticket
          </Text>
          <TicketCard
            variant="urgent"
            props={{
              eventName: 'Festival de Verão 2024',
              urgentMessage: 'Últimos 5 ingressos disponíveis!',
              eventDate: 'Sexta-feira, 15 de Janeiro',
              eventTime: '22:00',
              eventLocation: 'Marina da Glória - RJ',
              ticketType: 'Pista Premium',
              remainingTime: '2h30min',
              onBuyNow: () => console.log('Buy now'),
              onViewDetails: () => console.log('View details'),
            }}
          />
        </Box>

        {/* Event Ticket with Early Bird Example */}
        <Box>
          <Text variant="h_18Bold_formTitle" marginBottom="sm_12">
            Event Ticket (Early Bird)
          </Text>
          <TicketCard
            variant="event"
            props={{
              eventName: 'Sunset Music Festival',
              eventType: 'Festival de Música Eletrônica',
              eventDate: 'Sábado, 28 de Janeiro',
              eventTime: '16:00 - 06:00',
              eventLocation: 'Praia de Copacabana - RJ',
              features: ['Open Bar', 'Food Trucks', 'Área VIP', 'After Party'],
              earlyBird: {
                price: 150,
                originalPrice: 250,
                endsIn: '3 dias',
              },
              capacity: '5000 pessoas',
              musicGenre: 'House, Techno, Progressive',
              onBuyTicket: () => console.log('Buy ticket'),
              onViewDetails: () => console.log('View details'),
            }}
          />
        </Box>

        {/* Marketplace Ticket - Sell Example */}
        <Box>
          <Text variant="h_18Bold_formTitle" marginBottom="sm_12">
            Marketplace Ticket (Venda)
          </Text>
          <TicketCard
            variant="marketplace"
            props={{
              eventName: 'Lollapalooza Brasil 2024',
              ticketType: 'Passe 3 dias - Lolla Comfort',
              eventDate: '22-24 de Março',
              eventLocation: 'Autódromo de Interlagos - SP',
              swapType: 'sell',
              price: 800,
              originalPrice: 1200,
              sellerName: 'Maria Santos',
              sellerRating: 4.8,
              onContact: () => console.log('Contact seller'),
              onViewDetails: () => console.log('View details'),
            }}
          />
        </Box>

        {/* Marketplace Ticket - Trade Example */}
        <Box>
          <Text variant="h_18Bold_formTitle" marginBottom="sm_12">
            Marketplace Ticket (Troca)
          </Text>
          <TicketCard
            variant="marketplace"
            props={{
              eventName: 'The Town 2024',
              ticketType: 'Pista - Dia 1',
              eventDate: 'Sexta-feira, 2 de Setembro',
              eventLocation: 'Cidade da Música - SP',
              swapType: 'trade',
              tradePreference: 'Ingresso para o Dia 2 ou Rock in Rio',
              sellerName: 'Pedro Oliveira',
              sellerRating: 5.0,
              onContact: () => console.log('Propose trade'),
              onViewDetails: () => console.log('View details'),
            }}
          />
        </Box>

        {/* Marketplace Ticket - Donate Example */}
        <Box>
          <Text variant="h_18Bold_formTitle" marginBottom="sm_12">
            Marketplace Ticket (Doação)
          </Text>
          <TicketCard
            variant="marketplace"
            props={{
              eventName: 'Show Beneficente',
              ticketType: 'Entrada Geral',
              eventDate: 'Domingo, 5 de Fevereiro',
              eventLocation: 'Teatro Municipal - SP',
              swapType: 'donate',
              sellerName: 'Ana Costa',
              onContact: () => console.log('Request ticket'),
              onViewDetails: () => console.log('View details'),
            }}
          />
        </Box>
      </Box>
    </ScrollView>
  );
};
