import React from 'react';

import { Box, Text } from '@/src/core/theme';

interface TicketFeatureTagProps {
  label: string;
  variant?: 'default' | 'highlight';
}

export const TicketFeatureTag = ({ label, variant = 'default' }: TicketFeatureTagProps) => {
  const backgroundColor = variant === 'highlight' ? 'primaryLight' : 'tagInfoBackground';
  const textColor = variant === 'highlight' ? 'primary' : 'tagInfoMain';

  return (
    <Box
      backgroundColor={backgroundColor}
      borderRadius="lg_16"
      paddingHorizontal="sm_12"
      paddingVertical="xs_8"
      alignItems="center"
      justifyContent="center">
      <Text variant="b_14Medium_button" color={textColor}>
        {label}
      </Text>
    </Box>
  );
};
