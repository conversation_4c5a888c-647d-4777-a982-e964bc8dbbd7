import { Calendar, Clock, Lightning, MapPin, Warning } from 'phosphor-react-native';

import { Box, Text, useTheme } from '@/src/core/theme';

import Button from '../Button.new';
import Card from '../Card';
import { TicketDivider } from './TicketDivider';
import { TicketFeatureItem } from './TicketFeatureItem';
import { TicketHeader } from './TicketHeader';

interface TicketUrgentProps {
  eventName: string;
  urgentMessage: string;
  eventDate: string;
  eventTime: string;
  eventLocation: string;
  ticketType: string;
  remainingTime?: string;
  onBuyNow: () => void;
  onViewDetails: () => void;
}

export const TicketUrgent = ({
  eventName,
  urgentMessage,
  eventDate,
  eventTime,
  eventLocation,
  ticketType,
  remainingTime,
  onBuyNow,
  onViewDetails,
}: TicketUrgentProps) => {
  const theme = useTheme();

  return (
    <Card variant="elevated" paddingHorizontal="none_0" pb="sm_12">
      {/* Header Section */}
      <TicketHeader
        title={eventName}
        subtitle={ticketType}
        location={eventLocation}
        status={{ label: urgentMessage, color: 'error' }}
      />

      {/* Decorative Divider */}
      <TicketDivider />

      {/* Content Section */}
      <Box gap="xs_8" px="md_16">
        {/* Urgent Notice */}
        <Box
          backgroundColor="errorLight"
          borderRadius="md_12"
          padding="sm_12"
          flexDirection="row"
          alignItems="center"
          gap="xs_8">
          <Warning size={20} color={theme.colors.error} weight="fill" />
          <Box flex={1}>
            <Text variant="b_14Medium_button" color="error">
              {urgentMessage}
            </Text>
            {remainingTime && (
              <Text variant="l_12Regular_helperText" color="error">
                Restam apenas {remainingTime}
              </Text>
            )}
          </Box>
        </Box>

        {/* Event Details */}
        <Box gap="sm_12">
          <TicketFeatureItem
            icon={<Calendar size={16} color={theme.colors.textSecondary} />}
            label={eventDate}
          />
          <TicketFeatureItem
            icon={<Clock size={16} color={theme.colors.textSecondary} />}
            label={eventTime}
          />
          <TicketFeatureItem
            icon={<MapPin size={16} color={theme.colors.textSecondary} />}
            label={eventLocation}
          />
        </Box>

        {/* Divider */}
        <Box height={1} backgroundColor="border" marginVertical="xs_8" />

        {/* Action Buttons */}
        <Box flexDirection="row" gap="sm_12" justifyContent="space-between">
          <Button
            intent="secondary"
            type="filled"
            size="small"
            title="Ver detalhes"
            onPress={onViewDetails}
          />
          <Button
            intent="danger"
            type="filled"
            size="small"
            title="Comprar agora!"
            onPress={onBuyNow}
            rightIcon={<Lightning size={16} color={theme.colors.white} weight="bold" />}
          />
        </Box>
      </Box>
    </Card>
  );
};
