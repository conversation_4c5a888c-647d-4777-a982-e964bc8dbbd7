import {
  Calendar,
  CurrencyCircleDollar,
  Gift,
  MapPin,
  ChatCircle as MessageCircle,
  Swap,
  Tag,
} from 'phosphor-react-native';

import { Box, Text, useTheme } from '@/src/core/theme';
import Button from '@/src/shared/components/Button.new';
import Card from '@/src/shared/components/Card';

import { TicketDivider } from './TicketDivider';
import { TicketFeatureItem } from './TicketFeatureItem';
import { TicketHeader } from './TicketHeader';

interface TicketMarketplaceProps {
  eventName: string;
  ticketType: string;
  eventDate: string;
  eventLocation: string;
  swapType: 'sell' | 'trade' | 'donate';
  price?: number;
  originalPrice?: number;
  tradePreference?: string;
  sellerName: string;
  sellerRating?: number;
  onContact: () => void;
  onViewDetails: () => void;
}

export const TicketMarketplace = ({
  eventName,
  ticketType,
  eventDate,
  eventLocation,
  swapType,
  price,
  originalPrice,
  tradePreference,
  sellerName,
  sellerRating,
  onContact,
  onViewDetails,
}: TicketMarketplaceProps) => {
  const theme = useTheme();

  const getSwapInfo = () => {
    switch (swapType) {
      case 'sell':
        return {
          icon: <CurrencyCircleDollar size={24} color={theme.colors.success} />,
          label: 'Venda',
          color: 'success' as const,
          action: 'Comprar',
        };
      case 'trade':
        return {
          icon: <Swap size={24} color={theme.colors.primary} />,
          label: 'Troca',
          color: 'info' as const,
          action: 'Propor troca',
        };
      case 'donate':
        return {
          icon: <Gift size={24} color={theme.colors.warning} />,
          label: 'Doação',
          color: 'warning' as const,
          action: 'Solicitar',
        };
    }
  };

  const swapInfo = getSwapInfo();

  return (
    <Card variant="elevated" paddingHorizontal="none_0" pb="sm_12">
      {/* Header Section */}
      <TicketHeader
        title={eventName}
        subtitle={`Por: ${sellerName}${sellerRating ? ` ⭐ ${sellerRating}` : ''}`}
        location={eventLocation}
        status={{ label: swapInfo.label, color: swapInfo.color }}
      />

      {/* Decorative Divider */}
      <TicketDivider />

      {/* Content Section */}
      <Box gap="xs_8" px="md_16">
        {/* Price/Trade Section */}
        {swapType === 'sell' && price && (
          <Box
            backgroundColor="successLight"
            borderRadius="md_12"
            padding="md_16"
            flexDirection="row"
            justifyContent="space-between"
            alignItems="center">
            <Box>
              <Text variant="l_12Regular_helperText" color="textSecondary">
                Preço do ingresso
              </Text>
              <Box flexDirection="row" alignItems="baseline" gap="xs_8">
                <Text variant="h_24SemiBold_section" color="success">
                  R$ {price}
                </Text>
                {originalPrice && originalPrice > price && (
                  <Text
                    variant="b_14Regular_content"
                    color="textTertiary"
                    textDecorationLine="line-through">
                    R$ {originalPrice}
                  </Text>
                )}
              </Box>
              {originalPrice && originalPrice > price && (
                <Text variant="l_12Regular_helperText" color="success">
                  {Math.round(((originalPrice - price) / originalPrice) * 100)}% mais barato
                </Text>
              )}
            </Box>
          </Box>
        )}

        {swapType === 'trade' && tradePreference && (
          <Box backgroundColor="primaryLight" borderRadius="md_12" padding="md_16">
            <Text variant="l_12Medium_message" color="primary" marginBottom="xxs_4">
              ACEITA TROCA POR:
            </Text>
            <Text variant="b_16Regular_input" color="text">
              {tradePreference}
            </Text>
          </Box>
        )}

        {swapType === 'donate' && (
          <Box
            backgroundColor="warningLight"
            borderRadius="md_12"
            padding="md_16"
            alignItems="center"
            gap="xs_8">
            <Gift size={48} color={theme.colors.warning} weight="fill" />
            <Box alignItems="center" gap="xxs_4">
              <Text variant="b_16Medium_button" color="warning">
                Ingresso gratuito disponível!
              </Text>
              <Text variant="l_12Regular_helperText" color="warningDark" textAlign="center">
                O vendedor está doando este ingresso
              </Text>
            </Box>
          </Box>
        )}

        {/* Ticket Details */}
        <Box gap="sm_12">
          <TicketFeatureItem
            icon={<Tag size={16} color={theme.colors.textSecondary} />}
            label={ticketType}
          />
          <TicketFeatureItem
            icon={<Calendar size={16} color={theme.colors.textSecondary} />}
            label={eventDate}
          />
          <TicketFeatureItem
            icon={<MapPin size={16} color={theme.colors.textSecondary} />}
            label={eventLocation}
          />
        </Box>

        {/* Divider */}
        <Box height={1} backgroundColor="border" marginVertical="xs_8" />

        {/* Seller Trust Badge */}
        <Box
          backgroundColor="surfaceBackground"
          borderRadius="sm_8"
          padding="xs_8"
          flexDirection="row"
          alignItems="center"
          gap="xs_8">
          <Box width={8} height={8} borderRadius="circle_9999" backgroundColor="success" />
          <Text variant="l_12Regular_helperText" color="textSecondary">
            Vendedor verificado • Transferência segura garantida
          </Text>
        </Box>

        {/* Action Buttons */}
        <Box
          flexDirection="row"
          gap="xs_8"
          backgroundColor="surfaceBackground"
          borderRadius="lg_16"
          padding="xs_8">
          <Box flex={1}>
            <Button
              intent="secondary"
              type="borderless"
              size="small"
              title="Ver detalhes"
              onPress={onViewDetails}
              fullWidth
            />
          </Box>
          <Box flex={1}>
            <Button
              intent="primary"
              type="filled"
              size="small"
              title={swapInfo.action}
              onPress={onContact}
              rightIcon={<MessageCircle size={16} color={theme.colors.white} />}
              fullWidth
            />
          </Box>
        </Box>
      </Box>
    </Card>
  );
};
