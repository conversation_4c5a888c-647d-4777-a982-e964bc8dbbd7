import React from 'react';

import { useWindowDimensions } from 'react-native';

import { Canvas, Line, Path, Skia } from '@shopify/react-native-skia';

import { Box, useTheme } from '@/src/core/theme';

interface TicketDividerProps {
  variant?: 'default' | 'subtle';
}

export const TicketDivider = ({ variant = 'default' }: TicketDividerProps) => {
  const theme = useTheme();
  const { width } = useWindowDimensions();

  const height = 24;
  const cutoutRadius = 12;
  const lineY = height / 2;
  const lineStrokeWidth = 2;

  if (variant === 'subtle') {
    return (
      <Box paddingHorizontal="md_16" paddingVertical="xs_8">
        <Canvas style={{ width: width - 32, height: 1 }}>
          {Array.from({ length: Math.floor((width - 32) / 8) }).map((_, i) => (
            <Line
              key={i}
              p1={{ x: i * 8, y: 0 }}
              p2={{ x: i * 8 + 4, y: 0 }}
              color={theme.colors.border}
              strokeWidth={1}
              opacity={0.5}
            />
          ))}
        </Canvas>
      </Box>
    );
  }

  // Left Semicircle Path
  const leftCutoutPath = Skia.Path.Make();
  const leftRect = { x: -cutoutRadius, y: 0, width: cutoutRadius * 2, height };
  leftCutoutPath.addArc(leftRect, -90, 180);

  // Right Semicircle Path - FIXED positioning
  const rightCutoutPath = Skia.Path.Make();
  const rightRect = {
    x: width - cutoutRadius * 4,
    y: 0,
    width: cutoutRadius * 2,
    height,
  };
  rightCutoutPath.addArc(rightRect, 90, 180);

  // Also, you need to fill the paths with the background color to create the cutout effect
  return (
    <Canvas style={{ width, height }}>
      {/* Left semicircle cutout */}
      <Path
        path={leftCutoutPath}
        color={theme.colors.primary} // Changed to background color
      />

      {/* Right semicircle cutout */}
      <Path
        path={rightCutoutPath}
        color={theme.colors.primary} // Changed to background color
      />

      {/* Dashed line - drawn only between the cutouts */}
      {Array.from({ length: Math.floor((width - cutoutRadius * 4) / 12) }).map((_, i) => (
        <Line
          key={i}
          p1={{ x: cutoutRadius + i * 12, y: lineY }}
          p2={{ x: cutoutRadius + i * 12 + 6, y: lineY }}
          color={theme.colors.border}
          strokeWidth={lineStrokeWidth}
          opacity={0.8}
        />
      ))}
    </Canvas>
  );
};
