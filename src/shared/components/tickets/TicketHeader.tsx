import { Info, MapPin, WarningCircle } from 'phosphor-react-native';

import { Box, Row, Text, useTheme } from '@/src/core/theme';

import { Pill } from '../Pill';

interface TicketHeaderProps {
  title: string;
  subtitle?: string;
  location?: string;
  price?: string;
  status?: {
    label: string;
    color: 'info' | 'success' | 'warning' | 'error';
  };
}

export const TicketHeader = ({ title, subtitle, location, price, status }: TicketHeaderProps) => {
  const theme = useTheme();

  return (
    <Box backgroundColor="elevatedBackground" borderRadius="md_12" padding="sm_12" gap="xs_8">
      {/* Title and Price Row */}
      <Box flexDirection="row" justifyContent="space-between" alignItems="flex-start">
        <Box flex={1}>
          <Text variant="h_18Bold_formTitle" color="text">
            {title}
          </Text>
          {/* Location Row */}
          {location && (
            <Box flexDirection="row" alignItems="center" gap="xxs_4">
              <MapPin size={10} color={theme.colors.textSecondary} />
              <Text
                variant="l_10SemiBold_tag"
                color="textSecondary"
                textTransform="uppercase"
                letterSpacing={0.2}>
                {location}
              </Text>
            </Box>
          )}
        </Box>
        {price && (
          <Box
            backgroundColor="primaryAlpha20"
            paddingHorizontal="xs_8"
            paddingVertical="xxs_4"
            borderRadius="sm_8"
            marginLeft="xs_8">
            <Text variant="l_12Bold_highlight" color="primary">
              {price}
            </Text>
          </Box>
        )}
      </Box>

      {/* Subtitle Row */}
      {subtitle && (
        <Row gap="xxs_4">
          <WarningCircle size={12} color={theme.colors.primary} />
          <Text variant="b_10Medium_description" color="primaryText">
            {subtitle}
          </Text>
        </Row>
      )}

      {/* Status Pill */}
      {status && (
        <Box alignSelf="flex-start">
          <Pill
            variant="reasonDefault"
            text={status.label}
            textColor="tagInfoMain"
            customIcon={status.color === 'info' ? <Info size={12} /> : undefined}
            backgroundColor="tagWarningBackground"
          />
        </Box>
      )}
    </Box>
  );
};
