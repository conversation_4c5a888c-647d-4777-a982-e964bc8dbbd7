import { ReactElement } from 'react';

import { TouchableOpacity } from 'react-native';

import { Box } from '@/src/core/theme';

import { TicketEvent, TicketEventProps } from './TicketEvent';
import { TicketMarketplace, TicketMarketplaceProps } from './TicketMarketplace';
import { TicketTransfer, TicketTransferProps } from './TicketTransfer';
import { TicketUrgent, TicketUrgentProps } from './TicketUrgent';

export type TicketCardVariant = 'transfer' | 'urgent' | 'event' | 'marketplace';

interface BaseTicketCardProps {
  variant: TicketCardVariant;
  onPress?: () => void;
}

interface TransferTicketCardProps extends BaseTicketCardProps {
  variant: 'transfer';
  props: TicketTransferProps;
}

interface UrgentTicketCardProps extends BaseTicketCardProps {
  variant: 'urgent';
  props: TicketUrgentProps;
}

interface EventTicketCardProps extends BaseTicketCardProps {
  variant: 'event';
  props: TicketEventProps;
}

interface MarketplaceTicketCardProps extends BaseTicketCardProps {
  variant: 'marketplace';
  props: TicketMarketplaceProps;
}

export type TicketCardProps =
  | TransferTicketCardProps
  | UrgentTicketCardProps
  | EventTicketCardProps
  | MarketplaceTicketCardProps;

export const TicketCard = ({ variant, props, onPress }: TicketCardProps): ReactElement => {
  const renderTicket = () => {
    switch (variant) {
      case 'transfer':
        return <TicketTransfer {...(props as TicketTransferProps)} />;
      case 'urgent':
        return <TicketUrgent {...(props as TicketUrgentProps)} />;
      case 'event':
        return <TicketEvent {...(props as TicketEventProps)} />;
      case 'marketplace':
        return <TicketMarketplace {...(props as TicketMarketplaceProps)} />;
    }
  };

  if (onPress) {
    return (
      <TouchableOpacity onPress={onPress} activeOpacity={0.9}>
        <Box marginBottom="md_16">{renderTicket()}</Box>
      </TouchableOpacity>
    );
  }

  return <Box marginBottom="md_16">{renderTicket()}</Box>;
};
