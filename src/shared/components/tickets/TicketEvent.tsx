import { Calendar, Clock, MapPin, MusicNote, Users } from 'phosphor-react-native';

import { Box, Text, useTheme } from '@/src/core/theme';

import Button from '../Button.new';
import Card from '../Card';
import { TicketDivider } from './TicketDivider';
import { TicketFeatureItem } from './TicketFeatureItem';
import { TicketFeatureTag } from './TicketFeatureTag';
import { TicketHeader } from './TicketHeader';

interface TicketEventProps {
  eventName: string;
  eventType: string;
  eventDate: string;
  eventTime: string;
  eventLocation: string;
  features?: string[];
  earlyBird?: {
    price: number;
    originalPrice: number;
    endsIn: string;
  };
  capacity?: string;
  musicGenre?: string;
  onBuyTicket: () => void;
  onViewDetails: () => void;
}

export const TicketEvent = ({
  eventName,
  eventType,
  eventDate,
  eventTime,
  eventLocation,
  features = [],
  earlyBird,
  capacity,
  musicGenre,
  onBuyTicket,
  onViewDetails,
}: TicketEventProps) => {
  const theme = useTheme();

  const discount = earlyBird
    ? Math.round(((earlyBird.originalPrice - earlyBird.price) / earlyBird.originalPrice) * 100)
    : 0;

  return (
    <Card variant="elevated" paddingHorizontal="none_0" pb="sm_12">
      {/* Header Section */}
      <TicketHeader title={eventName} subtitle={eventType} location={eventLocation} />

      {/* Decorative Divider */}
      <TicketDivider />

      {/* Content Section */}
      <Box gap="xs_8" px="md_16">
        {/* Early Bird Section */}
        {earlyBird && (
          <Box
            backgroundColor="successLight"
            borderRadius="md_12"
            padding="md_16"
            flexDirection="row"
            justifyContent="space-between"
            alignItems="center">
            <Box>
              <Text variant="l_10SemiBold_tag" color="success" marginBottom="xxs_4">
                EARLY BIRD - {discount}% OFF
              </Text>
              <Box flexDirection="row" alignItems="baseline" gap="xs_8">
                <Text variant="h_24SemiBold_section" color="success">
                  R$ {earlyBird.price}
                </Text>
                <Text
                  variant="b_14Regular_content"
                  color="textTertiary"
                  textDecorationLine="line-through">
                  R$ {earlyBird.originalPrice}
                </Text>
              </Box>
              <Text variant="l_12Regular_helperText" color="successDark">
                Termina em {earlyBird.endsIn}
              </Text>
            </Box>
            <Box
              backgroundColor="success"
              borderRadius="circle_9999"
              width={40}
              height={40}
              alignItems="center"
              justifyContent="center">
              <Text variant="b_16SemiBold_button" color="white">
                -{discount}%
              </Text>
            </Box>
          </Box>
        )}

        {/* Event Details */}
        <Box gap="sm_12">
          <TicketFeatureItem
            icon={<Calendar size={16} color={theme.colors.textSecondary} />}
            label={eventDate}
          />
          <TicketFeatureItem
            icon={<Clock size={16} color={theme.colors.textSecondary} />}
            label={eventTime}
          />
          <TicketFeatureItem
            icon={<MapPin size={16} color={theme.colors.textSecondary} />}
            label={eventLocation}
          />
          {capacity && (
            <TicketFeatureItem
              icon={<Users size={16} color={theme.colors.textSecondary} />}
              label={`Capacidade: ${capacity}`}
            />
          )}
          {musicGenre && (
            <TicketFeatureItem
              icon={<MusicNote size={16} color={theme.colors.textSecondary} />}
              label={musicGenre}
            />
          )}
        </Box>

        {/* Features */}
        {features.length > 0 && (
          <>
            <Box height={1} backgroundColor="border" marginVertical="xs_8" />
            <Box flexDirection="row" flexWrap="wrap" gap="xs_8">
              {features.map((feature, index) => (
                <TicketFeatureTag key={index} label={feature} />
              ))}
            </Box>
          </>
        )}

        {/* Divider */}
        <Box height={1} backgroundColor="border" marginVertical="xs_8" />

        {/* Action Buttons */}
        <Box
          flexDirection="row"
          gap="xs_8"
          backgroundColor="surfaceBackground"
          borderRadius="lg_16"
          padding="xs_8">
          <Box flex={1}>
            <Button
              intent="secondary"
              type="borderless"
              size="small"
              title="Ver detalhes"
              onPress={onViewDetails}
              fullWidth
            />
          </Box>
          <Box flex={1}>
            <Button
              intent="primary"
              type="filled"
              size="small"
              title="Comprar ingresso"
              onPress={onBuyTicket}
              fullWidth
            />
          </Box>
        </Box>
      </Box>
    </Card>
  );
};
