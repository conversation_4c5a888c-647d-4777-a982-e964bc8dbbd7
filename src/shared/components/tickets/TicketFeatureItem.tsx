import React, { ReactElement } from 'react';

import { Box, Text, useTheme } from '@/src/core/theme';

interface TicketFeatureItemProps {
  icon: ReactElement;
  label: string;
}

export const TicketFeatureItem = ({ icon, label }: TicketFeatureItemProps) => {
  const theme = useTheme();

  return (
    <Box flexDirection="row" alignItems="center" gap="xxs_4">
      <Box
        backgroundColor="primaryAlpha20"
        borderRadius="circle_9999"
        padding="xs_8"
        alignItems="center"
        justifyContent="center">
        {React.cloneElement(icon as ReactElement<{ size: number; color: string }>, {
          size: 15,
          color: theme.colors.primary,
        })}
      </Box>
      <Text variant="l_10SemiBold_tag" color="textSecondary" letterSpacing={0.4}>
        {label}
      </Text>
    </Box>
  );
};
