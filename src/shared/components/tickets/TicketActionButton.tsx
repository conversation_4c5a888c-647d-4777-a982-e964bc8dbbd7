import React, { ReactElement } from 'react';

import { TouchableOpacity } from 'react-native';

import { useTheme } from '@/src/core/theme';

import { Pill } from '../Pill';

type TicketActionButtonVariant = 'action' | 'reason';
type TicketActionButtonStatus = 'default' | 'focused';
type TicketActionButtonSize = 'default' | 'large';

interface TicketActionButtonProps {
  icon: React.ReactNode; // Icon component from Phosphor or similar
  label: string;
  onPress: () => void;
  variant?: TicketActionButtonVariant;
  status?: TicketActionButtonStatus;
  size?: TicketActionButtonSize;
}

export const TicketActionButton = ({
  icon,
  label,
  onPress,
  variant = 'action',
  status = 'default',
  size = 'default',
}: TicketActionButtonProps) => {
  return (
    <TouchableOpacity onPress={onPress} activeOpacity={0.7}>
      <Pill
        text={label}
        type={variant}
        status={status}
        size={size}
        customIcon={icon}
        backgroundColor="elevatedBackground"
      />
    </TouchableOpacity>
  );
};
