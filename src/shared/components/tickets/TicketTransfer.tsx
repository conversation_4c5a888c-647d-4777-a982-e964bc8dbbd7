import {
  Calendar,
  MapPin,
  ChatCircle as MessageCircle,
  Phone,
  Ticket,
  X,
} from 'phosphor-react-native';

import { Box, Text } from '@/src/core/theme';
import Button from '@/src/shared/components/Button.new';
import Card from '@/src/shared/components/Card';

import { TicketActionButton } from './TicketActionButton';
import { TicketDivider } from './TicketDivider';
import { TicketFeatureItem } from './TicketFeatureItem';
import { TicketHeader } from './TicketHeader';
import { TicketTransferProgress, TransferStep } from './TicketTransferProgress';

interface TicketTransferProps {
  eventName: string;
  recipientName: string;
  ticketType: string;
  eventDate: string;
  eventLocation: string;
  expiresIn?: string;
  onCancel: () => void;
  onMessage: () => void;
  onContact: () => void;
  onViewDetails: () => void;
  onResendInvite: () => void;
}

export const TicketTransfer = ({
  eventName,
  recipientName,
  ticketType,
  eventDate,
  eventLocation,
  expiresIn = '24 horas',
  onCancel,
  onMessage,
  onContact,
  onViewDetails,
  onResendInvite,
}: TicketTransferProps) => {
  return (
    <Card variant="elevated" paddingHorizontal="none_0" pb="sm_12">
      {/* Header Section */}
      <TicketHeader
        title={eventName}
        location={eventLocation}
        subtitle={`Aguardando confirmação de ${recipientName}`}
        price="R$ 200,00"
        status={{ label: 'Transferindo', color: 'warning' }}
      />

      {/* Decorative Divider */}
      <TicketDivider />

      {/* Content Section */}
      <Box gap="xs_8" px="md_16">
        {/* Transfer Progress */}
        <TicketTransferProgress
          steps={[
            { label: 'Ingresso enviado', completed: true },
            { label: 'Aguardando confirmação', completed: false, current: true },
            { label: 'Transferência concluída', completed: false },
          ]}
        />

        {/* Transfer Notice */}
        <Box
          backgroundColor="warningLight"
          borderRadius="md_12"
          padding="sm_12"
          alignItems="center">
          <Text
            variant="l_10SemiBold_tag"
            color="warningDark"
            textTransform="uppercase"
            letterSpacing={0.5}>
            Transferência expira em {expiresIn}
          </Text>
        </Box>

        {/* Action Pills */}
        <Box flexDirection="row" gap="xxs_4">
          <TicketActionButton
            icon={<X size={14} />}
            label="Cancelar"
            onPress={onCancel}
            variant="action"
          />

          <TicketActionButton
            icon={<MessageCircle size={14} />}
            label="Mensagem"
            onPress={onMessage}
            variant="action"
          />

          <TicketActionButton
            icon={<Phone size={14} />}
            label="Contatar"
            onPress={onContact}
            variant="action"
          />
        </Box>

        {/* Divider */}
        <Box height={1} backgroundColor="border" marginVertical="xs_8" />

        {/* Ticket Details */}
        <Box gap="sm_12">
          <TicketFeatureItem icon={<Ticket size={15} />} label={ticketType} />
          <TicketFeatureItem icon={<Calendar size={15} />} label={eventDate} />
          <TicketFeatureItem icon={<MapPin size={15} />} label={eventLocation} />
        </Box>

        {/* Divider */}
        <Box height={1} backgroundColor="border" marginVertical="xs_8" />

        {/* Action Buttons */}
        <Box
          flexDirection="row"
          gap="xs_8"
          backgroundColor="surfaceBackground"
          borderRadius="lg_16"
          padding="xs_8">
          <Box flex={1}>
            <Button
              intent="secondary"
              type="borderless"
              size="small"
              title="Ver detalhes"
              onPress={onViewDetails}
              fullWidth
            />
          </Box>
          <Box flex={1}>
            <Button
              intent="primary"
              type="filled"
              size="small"
              title="Reenviar convite"
              onPress={onResendInvite}
              fullWidth
            />
          </Box>
        </Box>
      </Box>
    </Card>
  );
};
