import React from 'react';

import { ArrowsLeftRight, Check, Circle } from 'phosphor-react-native';

import { Box, Row, Text, useTheme } from '@/src/core/theme';

export type TransferStep = {
  label: string;
  completed: boolean;
  current?: boolean;
};

interface TicketTransferProgressProps {
  steps: TransferStep[];
}

export const TicketTransferProgress = ({ steps }: TicketTransferProgressProps) => {
  const theme = useTheme();

  return (
    <Box gap="xs_8" bg="elevatedBackground" padding="md_16" borderRadius="sm_8">
      <Row alignItems="center" gap="xs_8" marginBottom="sm_12">
        <ArrowsLeftRight size={12} color={theme.colors.primary} weight="fill" />
        <Text variant="l_12Medium_message" color="textSecondary">
          Status da transferência
        </Text>
      </Row>

      <Box flexDirection="row" alignItems="center">
        {steps.map((step, index) => (
          <React.Fragment key={index}>
            {/* Step indicator */}
            <Box
              width={24}
              height={24}
              borderRadius="circle_9999"
              backgroundColor={
                step.completed ? 'success' : step.current ? 'primary' : 'surfaceBackground'
              }
              borderWidth={step.current || !step.completed ? 2 : 0}
              borderColor={step.current ? 'primary' : 'border'}
              alignItems="center"
              justifyContent="center">
              {step.completed ? (
                <Check size={14} color={theme.colors.white} weight="bold" />
              ) : step.current ? (
                <Circle size={8} color={theme.colors.primary} weight="fill" />
              ) : null}
            </Box>

            {/* Connector line */}
            {index < steps.length - 1 && (
              <Box flex={1} height={2} marginHorizontal="xs_8">
                <Box
                  height={2}
                  backgroundColor={step.completed ? 'success' : 'border'}
                  borderRadius="xxs_2"
                />
              </Box>
            )}
          </React.Fragment>
        ))}
      </Box>

      {/* Step labels */}
      <Box flexDirection="row" marginTop="xs_8">
        {steps.map((step, index) => (
          <Box
            key={index}
            flex={index === 0 || index === steps.length - 1 ? 0 : 1}
            width={index === 0 || index === steps.length - 1 ? 80 : undefined}
            alignItems={
              index === 0 ? 'flex-start' : index === steps.length - 1 ? 'flex-end' : 'center'
            }>
            <Text
              variant="l_10SemiBold_tag"
              textAlign={index === 0 ? 'left' : index === steps.length - 1 ? 'right' : 'center'}
              color={step.completed || step.current ? 'text' : 'textTertiary'}
              numberOfLines={2}>
              {step.label}
            </Text>
          </Box>
        ))}
      </Box>
    </Box>
  );
};
