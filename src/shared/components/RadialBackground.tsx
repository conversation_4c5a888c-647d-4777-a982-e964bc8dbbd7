import React from 'react';

import { Dimensions } from 'react-native';

import { Canvas, RadialGradient, Rect, vec } from '@shopify/react-native-skia';

import { useTheme } from '@/src/core/theme';

const { width, height } = Dimensions.get('screen');

export const RadialBackground = () => {
  const theme = useTheme();
  const baseColor = theme.colors.primary;

  const center = vec(width * 0.5013, height * 0.5481);
  const radius = Math.max(width * 0.5816, height * 0.4519);

  return (
    <Canvas style={{ flex: 1, backgroundColor: baseColor }}>
      <Rect x={0} y={0} width={width} height={height}>
        <RadialGradient
          c={center}
          r={radius}
          colors={['rgba(0,0,0,0)', 'rgba(0,0,0,0.6)']}
          positions={[0, 1]}
          mode="clamp"
        />
      </Rect>
    </Canvas>
  );
};
