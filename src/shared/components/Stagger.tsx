import {
  Stagger as StaggerComponent,
  StaggerProps as StaggerComponentProps,
} from '@animatereactnative/stagger';
import {
  BackgroundColorProps,
  BorderProps,
  LayoutProps,
  SpacingProps,
  backgroundColor,
  border,
  composeRestyleFunctions,
  layout,
  spacing,
  useRestyle,
} from '@shopify/restyle';

import { Theme } from '@/src/core/theme';

type RestyleProps = SpacingProps<Theme> &
  BorderProps<Theme> &
  BackgroundColorProps<Theme> &
  LayoutProps<Theme>;

const restyleFunctions = composeRestyleFunctions<Theme, RestyleProps>([
  spacing,
  border,
  backgroundColor,
  layout,
]);

type StaggerProps = RestyleProps & StaggerComponentProps;

const Stagger = ({ children, ...props }: { children: React.ReactNode } & StaggerProps) => {
  const { stagger, duration, exitDirection, entering, exiting, ...restyleProps } = props;
  const style = useRestyle(restyleFunctions, restyleProps);

  return (
    <StaggerComponent
      stagger={stagger}
      duration={duration}
      exitDirection={exitDirection}
      entering={entering}
      exiting={exiting}
      {...style}>
      {children}
    </StaggerComponent>
  );
};

export default Stagger;
