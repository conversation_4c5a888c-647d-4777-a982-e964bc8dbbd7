import React from 'react';

import { VariantProps, createRestyleComponent, createVariant } from '@shopify/restyle';
import { IconProps, Star } from 'phosphor-react-native';

import { Box, Text, Theme } from '@/src/core/theme';
import { TokensThemeColors, useTheme } from '@/src/core/theme/theme';
import { PillVariants } from '@/src/core/theme/variants/pill';

// type PillVariants = Exclude<AllPillVariants, 'defaults'>;

const BasePill = createRestyleComponent<
  VariantProps<Theme, 'pillVariants'> & React.ComponentProps<typeof Box>,
  Theme
>([createVariant({ themeKey: 'pillVariants' })], Box);

export type PillType = 'action' | 'reason' | 'default';
export type PillStatus = 'default' | 'focused';
export type PillSize = 'default' | 'large' | 'small' | 'compact' | 'big';

type PillComponentProps = {
  variant?: PillVariants;
  type?: PillType;
  status?: PillStatus;
  size?: PillSize;
  text: string;
  customIcon?: React.ReactElement<IconProps> | React.ComponentType<IconProps> | React.ReactNode;
  hideIcon?: boolean;
  textColor?: TokensThemeColors;
  backgroundColor?: TokensThemeColors;
} & Omit<React.ComponentProps<typeof BasePill>, 'variant'>;

const textVariantMap: Record<PillVariants, Exclude<keyof Theme['textVariants'], 'defaults'>> = {
  small: 'l_10SemiBold_tag',
  compact: 'l_10SemiBold_tag',
  default: 'l_12Bold_highlight',
  big: 'l_12Bold_highlight',
  actionDefault: 'l_10SemiBold_tag',
  actionFocused: 'l_10SemiBold_tag',
  reasonDefault: 'l_10SemiBold_tag',
  reasonLarge: 'b_14Medium_button',
};

const iconSizeMap: Record<PillVariants, number> = {
  default: 12,
  small: 12,
  compact: 12,
  big: 16,
  actionDefault: 14,
  actionFocused: 14,
  reasonDefault: 14,
  reasonLarge: 14,
};

const textColorMap: Record<PillVariants, TokensThemeColors> = {
  small: 'tagDefaultText',
  compact: 'tagDefaultText',
  default: 'tagDefaultText',
  big: 'tagDefaultText',
  actionDefault: 'textSecondary',
  actionFocused: 'white',
  reasonDefault: 'textSecondary',
  reasonLarge: 'textSecondary',
};

const getVariantFromTypeAndStatus = (
  type?: PillType,
  status?: PillStatus,
  size?: PillSize,
  variant?: PillVariants
): PillVariants => {
  // If a specific variant is provided, use it
  if (variant) return variant;

  // For legacy size-based usage
  if (!type || type === 'default') {
    return (size as PillVariants) || 'default';
  }

  // Map type + status + size to variants
  if (type === 'action') {
    return status === 'focused' ? 'actionFocused' : 'actionDefault';
  }

  if (type === 'reason') {
    return size === 'large' ? 'reasonLarge' : 'reasonDefault';
  }

  return 'default';
};

const Pill = ({
  variant,
  type,
  status = 'default',
  size,
  text,
  customIcon: CustomIcon,
  textColor,
  backgroundColor,
  hideIcon = false,
  ...rest
}: PillComponentProps) => {
  const theme = useTheme();
  const computedVariant = getVariantFromTypeAndStatus(type, status, size, variant);
  const iconSize = iconSizeMap[computedVariant];
  const defaultTextColor = textColorMap[computedVariant];
  const finalTextColor = textColor || defaultTextColor;
  const iconColor = theme.colors[finalTextColor] || finalTextColor;

  // Don't override backgroundColor if it's already set in the variant
  const shouldApplyBackgroundColor = ![
    'actionDefault',
    'actionFocused',
    'reasonDefault',
    'reasonLarge',
  ].includes(computedVariant);

  return (
    <BasePill
      variant={computedVariant}
      backgroundColor={
        shouldApplyBackgroundColor ? backgroundColor || 'tagDefaultBackground' : undefined
      }
      {...rest}>
      {hideIcon ? null : CustomIcon ? (
        React.cloneElement(CustomIcon as React.ReactElement<{ size: number; color: string }>, {
          size: iconSize,
          color: iconColor,
        })
      ) : (
        <Star size={iconSize} color={iconColor} />
      )}
      <Text variant={textVariantMap[computedVariant]} color={finalTextColor} alignSelf="flex-start">
        {text}
      </Text>
    </BasePill>
  );
};

export { Pill };
