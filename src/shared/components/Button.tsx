/* eslint-disable react-compiler/react-compiler */
import { forwardRef } from 'react';

import { ActivityIndicator, View } from 'react-native';

import {
  BoxProps,
  VariantProps,
  composeRestyleFunctions,
  createVariant,
  useRestyle,
} from '@shopify/restyle';
import { TOptions } from 'i18next';
import Animated, {
  Easing,
  interpolateColor,
  useAnimatedStyle,
  useDerivedValue,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';

import { TxKeyPath } from '@/src/core/i18n/translationTypes';
import { Text } from '@/src/core/theme';
import Box from '@/src/core/theme/Box';
import Pressable, { PressableProps } from '@/src/core/theme/Pressable';
import { Theme, TokensThemeColors, useTheme } from '@/src/core/theme/theme';

import { ButtonVariantAnimations } from '../constants/animation';

type ButtonProps = {
  title?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  loading?: boolean;
  fullWidth?: boolean;
  enabled?: boolean;
  textColor?: TokensThemeColors;
  size?: 'small' | 'medium' | 'large';
};

// Define props without ref to avoid TypeScript errors with Restyle
type ButtonComponentProps = BoxProps<Theme> &
  VariantProps<Theme, 'buttonVariants'> &
  ButtonProps &
  Omit<PressableProps, 'ref'> & {
    tx?: TxKeyPath;
    txOptions?: TOptions;
  };

// Create the button variant restyle function
const buttonVariant = createVariant<Theme, 'buttonVariants'>({
  themeKey: 'buttonVariants',
});

// Compose the restyle function
const restyleFunction = composeRestyleFunctions<Theme, ButtonComponentProps>([buttonVariant]);

// Create an animated version of Pressable
const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

/**
 * Themed Button component with smooth scale and background color animations
 */
const Button = forwardRef<View, ButtonComponentProps>(function Button(props, ref) {
  const theme = useTheme();
  const restyleProps = useRestyle(restyleFunction, props);

  const {
    variant,
    title,
    leftIcon,
    rightIcon,
    loading = false,
    enabled = true,
    fullWidth = false,
    textColor,
    tx,
    txOptions,
    style,
    ...rest
  } = props;

  const _size = props.size || 'medium';

  // Shared values for animations
  const scale = useSharedValue(1);
  const isPressed = useSharedValue(0);

  // Background color logic
  const getBackgroundColor = (): TokensThemeColors => {
    switch (variant) {
      case 'primary':
        return 'primary';
      case 'secondary':
        return 'secondary';
      case 'danger':
        return 'error';
      case 'outline':
      case 'ghost':
        return 'transparent';
      case 'tertiary':
        return 'elevatedBackground';
      default:
        return 'primary';
    }
  };

  const getPressedBackgroundColor = (): TokensThemeColors => {
    switch (variant) {
      case 'primary':
        return 'primaryDark';
      case 'secondary':
        return 'secondaryDark';
      case 'danger':
        return 'errorDark';
      case 'outline':
        return 'primaryLight';
      case 'ghost':
        return 'transparent';
      case 'tertiary':
        return 'elevatedBackground';
      default:
        return 'primaryDark';
    }
  };

  const getDisabledBgColor = (): TokensThemeColors => {
    if (variant === 'outline' || variant === 'ghost') return 'transparent';
    return 'disabledBackground';
  };

  const backgroundColor = enabled ? getBackgroundColor() : getDisabledBgColor();
  const pressedBackgroundColor = getPressedBackgroundColor();

  // Track theme colors with derived value to ensure reactivity
  const backgroundColorValue = useDerivedValue(() => {
    return theme.colors[backgroundColor];
  }, [backgroundColor, theme.colors]);

  const pressedBackgroundColorValue = useDerivedValue(() => {
    return theme.colors[pressedBackgroundColor];
  }, [pressedBackgroundColor, theme.colors]);

  // Animated style for scale and background color
  const animatedStyle = useAnimatedStyle(() => ({
    backgroundColor: interpolateColor(
      isPressed.value,
      [0, 1],
      [backgroundColorValue.value, pressedBackgroundColorValue.value]
    ),
    transform: [{ scale: scale.value }],
  }));

  const getAnimationForVariant = () => {
    const baseVariant =
      typeof variant === 'string'
        ? variant
        : (variant?.phone ?? variant?.tablet ?? variant?.desktop ?? 'primary');

    // Ensure we're using a valid key that exists in ButtonVariantAnimations
    const variantKey = baseVariant.split('Hover')[0].split('Pressed')[0].split('Disabled')[0];

    return (
      ButtonVariantAnimations[variantKey as keyof typeof ButtonVariantAnimations] ??
      ButtonVariantAnimations.primary
    );
  };

  // Handle press and release with animations
  const handleActiveStateChange = (active: boolean) => {
    isPressed.value = withTiming(active ? 1 : 0, {
      duration: 200,
      easing: Easing.bezier(0.5, 0.01, 0, 1),
    });

    const variantAnimation = getAnimationForVariant();
    scale.value = withSpring(
      active ? variantAnimation.scale.pressed : variantAnimation.scale.default,
      variantAnimation.config
    );
  };

  // Text color based on variant and state
  const getTextColor = (textColor?: TokensThemeColors): TokensThemeColors => {
    if (textColor) return textColor;
    if (!enabled) return 'textTertiary';
    switch (variant) {
      case 'outline':
      case 'ghost':
        return 'buttonOutlineText';
      case 'primary':
      case 'secondary':
      case 'danger':
        return 'buttonFilledText';
      case 'tertiary':
        return 'mainText';
      default:
        return 'buttonFilledText';
    }
  };

  // Text variant based on size
  const getTextVariant = () => {
    switch (_size) {
      case 'small':
        return 'b_14Medium_button';
      case 'large':
        return 'b_16SemiBold_button';
      case 'medium':
      default:
        return 'b_16SemiBold_button';
    }
  };

  // Padding based on size
  const getPaddingHorizontal = () => {
    switch (_size) {
      case 'small':
        return theme.spacing.sm_12;
      case 'large':
        return theme.spacing.lg_24;
      case 'medium':
      default:
        return theme.spacing.md_16;
    }
  };

  const getPaddingVertical = () => {
    switch (_size) {
      case 'small':
        return theme.spacing.xs_8;
      case 'large':
        return theme.spacing.md_16;
      case 'medium':
      default:
        return theme.spacing.sm_12;
    }
  };

  // Activity indicator size based on button size
  const getActivityIndicatorSize = () => {
    if (_size === 'small' || variant === 'outline' || variant === 'ghost') {
      return 'small';
    }
    return 'large';
  };

  return (
    <AnimatedPressable
      ref={ref}
      activeOpacity={0}
      enabled={enabled && !loading}
      onActiveStateChange={handleActiveStateChange}
      {...rest}
      style={[
        restyleProps.style,
        animatedStyle,
        {
          paddingHorizontal: getPaddingHorizontal(),
          paddingVertical: getPaddingVertical(),
        },
        fullWidth && { width: '100%' },
        !enabled && {
          borderColor: variant === 'outline' ? theme.colors.disabledBackground : undefined,
        },
        style,
      ]}>
      <Box flexDirection="row" alignItems="center" justifyContent="center">
        {loading ? (
          <ActivityIndicator
            color={theme.colors[getTextColor(textColor)]}
            size={getActivityIndicatorSize()}
            style={{ marginHorizontal: 8 }}
          />
        ) : (
          <>
            {leftIcon && <Box marginRight="xs_8">{leftIcon}</Box>}
            {(title || tx) && (
              <Text
                tx={tx}
                txOptions={txOptions}
                variant={getTextVariant()}
                color={getTextColor(textColor)}>
                {title}
              </Text>
            )}
            {rightIcon && <Box marginLeft="xs_8">{rightIcon}</Box>}
          </>
        )}
      </Box>
    </AnimatedPressable>
  );
});

export default Button;
