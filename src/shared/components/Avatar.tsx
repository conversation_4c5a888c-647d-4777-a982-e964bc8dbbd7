import React from 'react';

import { Image, ImageProps, StyleSheet } from 'react-native';

import { TextProps, VariantProps, createRestyleComponent, createVariant } from '@shopify/restyle';
import { IconProps, User } from 'phosphor-react-native';

import { Box, Text, Theme, useTheme } from '@/src/core/theme';
import { TokensThemeColors } from '@/src/core/theme/theme';

type AvatarSizeVariants = 'xs' | 's' | 'm' | 'l' | 'xl' | '2xl' | '3xl';

const BaseAvatarContainer = createRestyleComponent<
  VariantProps<Theme, 'avatarVariants'> &
    React.ComponentProps<typeof Box> & { children: React.ReactNode },
  Theme
>([createVariant({ themeKey: 'avatarVariants' })], Box);

type AvatarComponentProps = {
  source?: ImageProps['source'];
  fallbackText?: string;
  size?: AvatarSizeVariants;
  color?: TokensThemeColors;
  weight?: IconProps['weight'];
} & React.ComponentProps<typeof Box>;

const getInitials = (name?: string) => {
  if (!name) return null;

  const parts = name.trim().split(/\s+/);

  if (parts.length === 1) {
    return parts[0].substring(0, 2).toUpperCase();
  }

  const first = parts[0][0] || '';
  const last = parts[parts.length - 1][0] || '';
  return (first + last).toUpperCase();
};

const textVariantMap: Record<AvatarSizeVariants, NonNullable<TextProps<Theme>['variant']>> = {
  xs: 'l_12Bold_highlight',
  s: 'l_14SemiBold_action',
  m: 'b_16SemiBold_button',
  l: 'h_18Bold_formTitle',
  xl: 'h_18Bold_formTitle',
  '2xl': 'h_24SemiBold_section',
  '3xl': 'H_40Bold_title',
};

const Avatar = ({ source, size = 'l', fallbackText, ...rest }: AvatarComponentProps) => {
  const initials = getInitials(fallbackText);
  const theme = useTheme();

  return (
    <BaseAvatarContainer variant={size} {...rest}>
      {source ? (
        <Image source={source} resizeMode="cover" style={StyleSheet.absoluteFillObject} />
      ) : initials ? (
        <Text variant={textVariantMap[size]} color={rest.color || 'iconPrimary'}>
          {initials}
        </Text>
      ) : (
        <User size="100%" color={theme.colors[rest.color || 'iconPrimary']} weight={rest.weight} />
      )}
    </BaseAvatarContainer>
  );
};

export { Avatar };
