import React, { Children, ReactNode, RefObject, isValidElement, useRef } from 'react';

import { AccessibilityProps, View } from 'react-native';

import {
  BoxProps,
  VariantProps,
  composeRestyleFunctions,
  createVariant,
  useRestyle,
} from '@shopify/restyle';

import { Box, Theme, useResponsive } from '@/src/core/theme';
import { TokensThemeColors } from '@/src/core/theme/theme';

import CardToolbar from './CardToolbar';
import List from './List';

type RestyleCardProps = BoxProps<Theme> & VariantProps<Theme, 'cardVariants'> & AccessibilityProps;

const cardVariant = createVariant<Theme, 'cardVariants'>({ themeKey: 'cardVariants' });
const restyleFunction = composeRestyleFunctions<Theme, RestyleCardProps>([cardVariant]);

type CardProps = {
  children: ReactNode;
  ref?: RefObject<View>;
  listMode?: boolean;
  gridMode?: boolean;
  columns?: number;
  dividerColor?: TokensThemeColors;
  // CardToolbar integration props
  showToolbar?: boolean;
  toolbarTitle?: string;
  toolbarActionText?: string;
  toolbarLayout?: 'default' | 'twoIcons' | 'header';
  onToolbarPress?: () => void;
  onToolbarSecondPress?: () => void;
} & RestyleCardProps;

const Card = ({
  ref,
  children,
  listMode = false,
  gridMode = false,
  columns = 2,
  dividerColor = 'border',
  // CardToolbar props
  showToolbar = false,
  toolbarTitle,
  toolbarActionText,
  toolbarLayout = 'default',
  onToolbarPress,
  onToolbarSecondPress,
  ...rest
}: CardProps) => {
  const restyleProps = useRestyle(restyleFunction, rest);
  const { isTablet, isDesktop, select } = useResponsive();
  const processedChildrenRef = useRef<ReactNode[]>([]);

  // Render CardToolbar if enabled
  const renderToolbar = () => {
    if (!showToolbar) return null;

    return (
      <Box pb="xxs_4">
        <CardToolbar
          layout={toolbarLayout}
          title={toolbarTitle}
          actionText={toolbarActionText}
          onPress={onToolbarPress}
          onSecondPress={onToolbarSecondPress}
        />
      </Box>
    );
  };

  // Process children to add dividers
  const processChildrenWithDividers = (childrenToProcess: ReactNode) => {
    const childrenArray = Children.toArray(childrenToProcess);

    if (processedChildrenRef.current.length > 0) {
      processedChildrenRef.current = [];
    }

    childrenArray.forEach((child, index) => {
      processedChildrenRef.current.push(child);

      // Add a divider after each child except the last one
      if (index < childrenArray.length - 1 && isValidElement(child) && child.type === List.Item) {
        processedChildrenRef.current.push(
          <Box key={`divider-${index}`} height={1} backgroundColor={dividerColor} />
        );
      }
    });

    return processedChildrenRef.current;
  };

  // If gridMode is enabled, render children in a grid layout
  if (gridMode && children) {
    const shouldUseGrid = isTablet || isDesktop;

    if (shouldUseGrid) {
      // Convert children to array for the grid
      const childrenArray = Children.toArray(children);

      // Determine appropriate columns based on screen size
      const effectiveColumns = select({
        phone: 1,
        tablet: Math.min(columns, 3), // Max 3 columns on tablet
        desktop: columns,
      });

      // Group items into rows
      const rows = [];
      for (let i = 0; i < childrenArray.length; i += effectiveColumns) {
        const rowItems = childrenArray.slice(i, i + effectiveColumns);
        rows.push(rowItems);
      }

      return (
        <Box ref={ref}>
          {renderToolbar()}
          <Box {...restyleProps}>
            {/* Container with relative positioning for absolute dividers */}
            <Box position="relative">
              {/* Vertical dividers that span the full height */}
              {effectiveColumns > 1 &&
                Array(effectiveColumns - 1)
                  .fill(null)
                  .map((_, divIndex) => (
                    <Box
                      key={`full-vdivider-${divIndex}`}
                      position="absolute"
                      width={1}
                      top={0}
                      bottom={0}
                      backgroundColor={dividerColor}
                      left={`${(100 * (divIndex + 1)) / effectiveColumns}%`}
                      zIndex="base"
                    />
                  ))}

              {/* Grid rows and items */}
              {rows.map((rowItems, rowIndex) => (
                <React.Fragment key={`row-${rowIndex}`}>
                  <Box flexDirection="row" flexWrap="wrap" alignItems="center" py="md_16">
                    {rowItems.map((child, colIndex) => (
                      <Box
                        key={`grid-item-${rowIndex}-${colIndex}`}
                        width={`${100 / effectiveColumns}%`}
                        padding="xxs_4">
                        {child}
                      </Box>
                    ))}

                    {/* Add empty cells to complete the row if needed */}
                    {rowItems.length < effectiveColumns &&
                      Array(effectiveColumns - rowItems.length)
                        .fill(null)
                        .map((_, index) => (
                          <Box
                            key={`empty-${rowIndex}-${index}`}
                            width={`${100 / effectiveColumns}%`}
                            padding="xxs_4"
                          />
                        ))}
                  </Box>

                  {/* Add divider after each row except the last one */}
                  {rowIndex < rows.length - 1 && (
                    <Box
                      height={1}
                      backgroundColor={dividerColor}
                      marginVertical="xs_8"
                      zIndex="high"
                    />
                  )}
                </React.Fragment>
              ))}
            </Box>
          </Box>
        </Box>
      );
    }
  }

  // If listMode is enabled, process children to add dividers
  if (listMode && children) {
    const processedChildren = processChildrenWithDividers(children);
    return (
      <Box ref={ref}>
        {renderToolbar()}
        <Box {...restyleProps} py="md_16">
          {processedChildren}
        </Box>
      </Box>
    );
  }

  // If neither listMode nor gridMode is enabled, just render children normally
  return (
    <Box ref={ref}>
      {renderToolbar()}
      <Box px="md_16" overflow="hidden" {...restyleProps}>
        {children}
      </Box>
    </Box>
  );
};

export default Card;
