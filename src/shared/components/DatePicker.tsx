import React, { forwardRef, useMemo } from 'react';

import type { ViewProps } from 'react-native';

import {
  type DatePickerHandle,
  type Options as DatePickerOptions,
  type Styles as DatePickerStyles,
  type Type as DatePickerType,
  DatePicker as RNDatePicker,
} from '@s77rt/react-native-date-picker';

import { useTheme } from '@/src/core/theme/theme';

// Type helpers for single vs multiple selection
type DateValue<T extends boolean> = T extends true ? Date[] : Date | null;
type DateOnChange<T extends boolean> = T extends true
  ? (value: Date[]) => void
  : (value: Date | null) => void;

export interface DatePickerProps<Multiple extends boolean = false> extends ViewProps {
  type?: DatePickerType;
  value?: DateValue<Multiple>;
  onChange?: DateOnChange<Multiple>;
  min?: Date;
  max?: Date;
  step?: number;
  multiple?: Multiple;
  inline?: boolean;
  locale?: string;
  confirmText?: string;
  cancelText?: string;
  mode?: 'compact' | 'graphical' | 'wheel';
  title?: string;
  headline?: string;
  showModeToggle?: boolean;
  is24Hour?: boolean;
}

const DatePickerComponent = <Multiple extends boolean = false>(
  props: DatePickerProps<Multiple>,
  ref: React.Ref<DatePickerHandle>
) => {
  const {
    type = 'date',
    value,
    onChange,
    min,
    max,
    step,
    multiple,
    inline = false,
    locale,
    confirmText,
    cancelText,
    mode,
    title,
    headline,
    showModeToggle,
    is24Hour,
    style,
    ...viewProps
  } = props;

  // Ensure we have a valid value before passing to the date picker
  const safeValue = React.useMemo(() => {
    if (multiple) {
      // For multiple selection, ensure we have an array
      if (Array.isArray(value)) {
        return value.filter(date => date instanceof Date && !isNaN(date.getTime()));
      }
      return [];
    } else {
      // For single selection, ensure we have a valid date or null
      if (value instanceof Date && !isNaN(value.getTime())) {
        return value;
      }
      return null;
    }
  }, [value, multiple]);

  const theme = useTheme();

  // Memoize options to prevent unnecessary re-renders
  const options = useMemo<DatePickerOptions | undefined>(() => {
    if (
      !locale &&
      !confirmText &&
      !cancelText &&
      !mode &&
      !title &&
      !headline &&
      showModeToggle === undefined &&
      is24Hour === undefined
    ) {
      return undefined;
    }

    return {
      locale,
      confirmText,
      cancelText,
      mode,
      title,
      headline,
      showModeToggle,
      is24Hour,
    };
  }, [locale, confirmText, cancelText, mode, title, headline, showModeToggle, is24Hour]);

  // Memoize styles to prevent unnecessary re-renders
  const styles = useMemo<DatePickerStyles>(() => {
    const colors = theme.colors;

    return {
      // Main colors
      accentColor: colors.primary,
      containerColor: colors.surface,

      // Text colors
      titleContentColor: colors.text,
      headlineContentColor: colors.text,
      weekdayContentColor: colors.textSecondary,
      subheadContentColor: colors.textSecondary,
      navigationContentColor: colors.primary,

      // Year colors
      yearContentColor: colors.text,
      disabledYearContentColor: colors.disabledText,
      currentYearContentColor: colors.primary,
      selectedYearContentColor: colors.surface,
      disabledSelectedYearContentColor: colors.disabledText,
      selectedYearContainerColor: colors.primary,
      disabledSelectedYearContainerColor: colors.disabledBackground,

      // Day colors
      dayContentColor: colors.text,
      disabledDayContentColor: colors.disabledText,
      selectedDayContentColor: colors.surface,
      disabledSelectedDayContentColor: colors.disabledText,
      selectedDayContainerColor: colors.primary,
      disabledSelectedDayContainerColor: colors.disabledBackground,
      todayContentColor: colors.primary,
      todayDateBorderColor: colors.primary,
      dayInSelectionRangeContainerColor: colors.primaryLight,
      dayInSelectionRangeContentColor: colors.text,

      // UI elements
      dividerColor: colors.border,
      clockDialColor: colors.surfaceBackground,
      selectorColor: colors.primary,
      periodSelectorBorderColor: colors.border,

      // Clock colors
      clockDialSelectedContentColor: colors.surface,
      clockDialUnselectedContentColor: colors.text,

      // Period selector colors
      periodSelectorSelectedContainerColor: colors.primary,
      periodSelectorUnselectedContainerColor: colors.surface,
      periodSelectorSelectedContentColor: colors.surface,
      periodSelectorUnselectedContentColor: colors.text,

      // Time selector colors
      timeSelectorSelectedContainerColor: colors.primary,
      timeSelectorUnselectedContainerColor: colors.surface,
      timeSelectorSelectedContentColor: colors.surface,
      timeSelectorUnselectedContentColor: colors.text,
    };
  }, [theme.colors]);

  return (
    <RNDatePicker
      ref={ref}
      type={type}
      value={safeValue as any}
      onChange={onChange as any}
      min={min}
      max={max}
      step={step}
      multiple={multiple}
      inline={inline}
      options={options}
      styles={styles}
      style={[style]}
      {...viewProps}
    />
  );
};

// Export with proper generic forwarding
export const DatePicker = forwardRef(DatePickerComponent) as <Multiple extends boolean = false>(
  props: DatePickerProps<Multiple> & { ref?: React.Ref<DatePickerHandle> }
) => React.ReactElement;

// Re-export types for convenience
export type { DatePickerHandle, DatePickerType };

// Helper hook for modal date picker
export const useDatePicker = () => {
  const ref = React.useRef<DatePickerHandle>(null);

  const show = React.useCallback(() => {
    ref.current?.showPicker();
  }, []);

  return {
    ref,
    show,
  };
};
