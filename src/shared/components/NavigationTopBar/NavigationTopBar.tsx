import React from 'react';

import { Box, Row, Text } from '@/src/core/theme';

import { RoundedButton } from '../RoundedButton';

type NavigationTopBarProps = {
  title?: string;
  onPress?: () => void;
  hideBackButton?: boolean;
  trailingActions?: React.ReactNode;
};

export default function NavigationTopBar({
  title,
  onPress,
  hideBackButton = false,
  trailingActions,
}: NavigationTopBarProps) {
  return (
    <Row width="100%" maxHeight={56} gap="sm_12" flexShrink={0} px="md_16" pb="sm_12">
      <Box
        flex={1}
        style={{ flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center' }}>
        {!hideBackButton && onPress && <RoundedButton onPress={onPress} variant="medium" />}
      </Box>
      <Box style={{ justifyContent: 'center', alignItems: 'center' }}>
        <Text
          variant="h_18Bold_formTitle"
          color="mainText"
          style={{ width: '100%', textAlign: 'center' }}>
          {title}
        </Text>
      </Box>
      <Box
        flex={1}
        style={{ flexDirection: 'row', justifyContent: 'flex-end', alignItems: 'center' }}>
        {trailingActions}
      </Box>
    </Row>
  );
}
