import React from 'react';

import { VariantProps, createRestyleComponent, createVariant } from '@shopify/restyle';

import { Box, Text, Theme } from '@/src/core/theme';

type TagType =
  | 'default'
  | 'success'
  | 'info'
  | 'warning'
  | 'error'
  | 'highlight'
  | 'outline'
  | 'outlineSuccess'
  | 'outlineInfo'
  | 'outlineWarning'
  | 'outlineError'
  | 'ghost'
  | 'ghostSuccess'
  | 'ghostInfo'
  | 'ghostWarning'
  | 'ghostError';

const BaseTag = createRestyleComponent<
  VariantProps<Theme, 'tagVariants'> & React.ComponentProps<typeof Box>,
  Theme
>([createVariant({ themeKey: 'tagVariants' })], Box);

type TagComponentProps = {
  variant?: TagType;
  text: string;
} & Omit<React.ComponentProps<typeof BaseTag>, 'variant'>;

const getTextColor = (type: TagType = 'info'): keyof Theme['colors'] => {
  const colorMap: Partial<Record<TagType, keyof Theme['colors']>> = {
    default: 'tagDefaultText',
    success: 'tagSuccessText',
    info: 'tagInfoText',
    warning: 'tagWarningText',
    error: 'tagErrorMain',
    highlight: 'white',
    outline: 'tagDefaultInverseText',
    outlineSuccess: 'tagSuccessBackground',
    outlineInfo: 'tagInfoMain',
    outlineWarning: 'tagAlertMain',
    outlineError: 'tagErrorMain',
    ghost: 'tagDefaultInverseText',
    ghostSuccess: 'tagSuccessBackground',
    ghostInfo: 'tagInfoMain',
    ghostWarning: 'tagAlertMain',
    ghostError: 'tagErrorMain',
  };

  return colorMap[type] || 'tagDefaultText';
};

const Tag = ({ variant = 'default', text, ...rest }: TagComponentProps) => {
  const textColor = getTextColor(variant);

  return (
    <BaseTag variant={variant} {...rest}>
      <Text variant="l_10SemiBold_tag" color={textColor as keyof Theme['colors']}>
        {text}
      </Text>
    </BaseTag>
  );
};

export { Tag };
