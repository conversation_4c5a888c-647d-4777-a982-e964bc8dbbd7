import React, { useEffect } from 'react';

import { StyleSheet, View } from 'react-native';

import {
  BlurMask,
  Canvas,
  Group,
  Paint,
  Path,
  interpolateColors,
} from '@shopify/react-native-skia';
import { useDerivedValue, useSharedValue, withRepeat, withTiming } from 'react-native-reanimated';

interface AnimatedFireIconProps {
  size?: number;
  intensity?: number; // 0.5 to 2.0 for different fire intensities
}

const AnimatedFireIcon: React.FC<AnimatedFireIconProps> = ({ size = 100, intensity = 1.0 }) => {
  // Animation values
  const flicker = useSharedValue(0);
  const rotation = useSharedValue(0);
  const colorProgress = useSharedValue(0);
  const glowIntensity = useSharedValue(0);

  // Fire icon SVG path (flame shape)
  const firePath = `M 50 90 
                   C 30 90 20 70 25 50
                   C 30 30 40 20 50 15
                   C 55 10 60 8 65 12
                   C 70 5 80 8 85 15
                   C 90 25 85 35 80 45
                   C 85 55 80 65 75 70
                   C 70 80 60 90 50 90 Z`;

  // Start animations
  useEffect(() => {
    const flickerDuration = Math.max(50, 200 - intensity * 50);
    const rotationDuration = Math.max(4000, 10000 - intensity * 2000);
    const colorDuration = Math.max(800, 2000 - intensity * 400);
    const glowDuration = Math.max(400, 1000 - intensity * 200);

    // Flickering animation (faster with higher intensity)
    flicker.value = withRepeat(
      withTiming(1, { duration: flickerDuration + Math.random() * 100 }),
      -1,
      true
    );

    // Rotation animation (faster with higher intensity)
    rotation.value = withRepeat(withTiming(360, { duration: rotationDuration }), -1, false);

    // Color cycling animation (faster with higher intensity)
    colorProgress.value = withRepeat(withTiming(1, { duration: colorDuration }), -1, true);

    // Glow intensity animation (faster with higher intensity)
    glowIntensity.value = withRepeat(withTiming(1, { duration: glowDuration }), -1, true);
  }, [intensity]);

  // Derived values for smooth animations
  const animatedScale = useDerivedValue(() => {
    const baseScale = 0.85 + intensity * 0.1;
    const flickerAmount = 0.1 + intensity * 0.15;
    return baseScale + flicker.value * flickerAmount;
  });

  const animatedOpacity = useDerivedValue(() => {
    const baseOpacity = Math.min(0.95, 0.7 + intensity * 0.2);
    const flickerAmount = 0.1 + intensity * 0.1;
    return baseOpacity + flicker.value * flickerAmount;
  });

  const animatedColor = useDerivedValue(() => {
    // More intense colors with higher intensity
    const colors =
      intensity > 1.5
        ? ['#FF0000', '#FF4500', '#FF6B00', '#FF8C00', '#FFFF00'] // Hotter colors
        : intensity > 1.0
          ? ['#FF4500', '#FF6B00', '#FF8C00', '#FFA500', '#FFD700'] // Standard colors
          : ['#FF6B00', '#FF8C00', '#FFA500', '#FFD700', '#FFFF99']; // Cooler colors

    const progress = colorProgress.value;

    if (progress < 0.25) {
      return interpolateColors(progress * 4, [0, 1], [colors[0], colors[1]]);
    } else if (progress < 0.5) {
      return interpolateColors((progress - 0.25) * 4, [0, 1], [colors[1], colors[2]]);
    } else if (progress < 0.75) {
      return interpolateColors((progress - 0.5) * 4, [0, 1], [colors[2], colors[3]]);
    } else {
      return interpolateColors((progress - 0.75) * 4, [0, 1], [colors[3], colors[4]]);
    }
  });

  const animatedRotation = useDerivedValue(() => {
    const rotationAmount = 3 + intensity * 4;
    return (rotation.value + Math.sin(flicker.value * Math.PI) * rotationAmount) * (Math.PI / 180);
  });

  const glowRadius = useDerivedValue(() => {
    const baseGlow = 2 + intensity * 3;
    const flickerGlow = 2 + intensity * 4;
    return baseGlow + glowIntensity.value * flickerGlow;
  });

  const blendOpacity = useDerivedValue(() => {
    return Math.min(1.0, 0.6 + intensity * 0.3);
  });

  return (
    <View style={[styles.container, { width: size, height: size }]}>
      <Canvas style={{ width: size, height: size }}>
        <Group
          transform={[
            { translateX: size / 2 },
            { translateY: size / 2 },
            { scale: (size / 100) * animatedScale.value },
            { rotate: animatedRotation.value },
            { translateX: -50 },
            { translateY: -50 },
          ]}>
          {/* Outer glow layer with screen blend mode */}
          <Group>
            <Path path={firePath}>
              <Paint
                color={animatedColor.value}
                style="fill"
                opacity={0.3 * blendOpacity.value}
                blendMode="screen">
                <BlurMask blur={glowRadius.value * 1.5} style="normal" />
              </Paint>
            </Path>
          </Group>

          {/* Main fire shape with multiply blend for depth */}
          <Group>
            <Path path={firePath}>
              <Paint
                color={animatedColor.value}
                style="fill"
                opacity={animatedOpacity.value}
                blendMode="multiply">
                <BlurMask blur={glowRadius.value * 0.5} style="normal" />
              </Paint>
            </Path>
          </Group>

          {/* Core fire shape with normal blend */}
          <Group>
            <Path path={firePath}>
              <Paint color={animatedColor.value} style="fill" opacity={animatedOpacity.value} />
            </Path>
          </Group>

          {/* Inner flame with overlay blend for intensity */}
          <Group transform={[{ scale: 0.75 }, { translateX: 12 }, { translateY: 8 }]}>
            <Path path={firePath}>
              <Paint
                color="#FFAA00"
                style="fill"
                opacity={0.7 * animatedOpacity.value}
                blendMode="overlay"
              />
            </Path>
          </Group>

          {/* Hot core with screen blend for brightness */}
          <Group transform={[{ scale: 0.5 }, { translateX: 25 }, { translateY: 20 }]}>
            <Path path={firePath}>
              <Paint
                color="#FFFF00"
                style="fill"
                opacity={0.8 * animatedOpacity.value}
                blendMode="screen"
              />
            </Path>
          </Group>

          {/* Ultra-hot center with color dodge for maximum intensity */}
          {intensity > 1.2 && (
            <Group transform={[{ scale: 0.3 }, { translateX: 35 }, { translateY: 30 }]}>
              <Path path={firePath}>
                <Paint
                  color="#FFFFFF"
                  style="fill"
                  opacity={0.4 * animatedOpacity.value * (intensity - 1.2)}
                  blendMode="colorDodge"
                />
              </Path>
            </Group>
          )}

          {/* Additional intensity layers for extreme fire */}
          {intensity > 1.8 && (
            <>
              {/* Bright orange overlay */}
              <Group transform={[{ scale: 0.85 }, { translateX: 8 }, { translateY: 5 }]}>
                <Path path={firePath}>
                  <Paint
                    color="#FF4500"
                    style="fill"
                    opacity={0.3 * (intensity - 1.8)}
                    blendMode="screen"
                  />
                </Path>
              </Group>

              {/* Intense glow */}
              <Group>
                <Path path={firePath}>
                  <Paint
                    color="#FFFF00"
                    style="fill"
                    opacity={0.2 * (intensity - 1.8)}
                    blendMode="colorDodge">
                    <BlurMask blur={glowRadius.value * 2} style="normal" />
                  </Paint>
                </Path>
              </Group>
            </>
          )}
        </Group>
      </Canvas>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default AnimatedFireIcon;
