import React, { useEffect } from 'react';

import { Canvas, Path, Skia, usePathInterpolation } from '@shopify/react-native-skia';
import { useDerivedValue, useSharedValue, withRepeat, withTiming } from 'react-native-reanimated';

interface FlameIconProps {
  size?: number;
  color?: string;
  animated?: boolean;
  weight?: 'fill' | 'regular';
}

export const FlameIcon: React.FC<FlameIconProps> = ({
  size = 256,
  color = '#ff6b35',
  animated = false,
  weight = 'regular',
}) => {
  // Animation progress
  const progress = useSharedValue(0);
  // Sway value for horizontal movement (5% of original 256 width)
  const sway = useDerivedValue(() => {
    return animated ? Math.sin(progress.value * Math.PI * 2) * 12.8 : 0;
  });

  // Calculate stroke width with minimum for small sizes
  const calculateStrokeWidth = (iconSize: number) => {
    const minStroke = 20;
    const proportionalStroke = iconSize * 0.094;
    return Math.max(minStroke, proportionalStroke);
  };

  const strokeWidth = calculateStrokeWidth(size);

  // Create Skia paths from SVG strings - different flame intensities
  const normalFlame = Skia.Path.MakeFromSVGString(
    'M112,96l26.27-72C159.86,41.92,208,88.15,208,144a80,80,0,0,1-160,0c0-30.57,14.42-58.26,31-80Z'
  )!;

  const bigFlame = Skia.Path.MakeFromSVGString(
    'M110,94l28.27-74C161.86,39.92,210,86.15,210,142a82,82,0,0,1-164,0c0-31.57,15.42-60.26,32-82Z'
  )!;

  const smallFlame = Skia.Path.MakeFromSVGString(
    'M114,98l24.27-70C157.86,43.92,206,90.15,206,146a78,78,0,0,1-156,0c0-29.57,13.42-56.26,30-78Z'
  )!;

  const innerFlame = Skia.Path.MakeFromSVGString('M166.17,156A40.11,40.11,0,0,1,140,182.17')!;

  // Smooth path interpolation for flickering effect
  const animatedMainPath = usePathInterpolation(
    progress,
    [0, 0.5, 1],
    [normalFlame, bigFlame, smallFlame]
  );

  // Start animation
  useEffect(() => {
    if (animated) {
      progress.set(withRepeat(withTiming(1, { duration: 1500 }), -1, true));
    } else {
      progress.set(0);
    }
  }, [animated, progress]);

  return (
    <Canvas style={{ width: size, height: size }}>
      {/* Main flame body with fill/stroke variants */}
      <Path
        path={animated ? animatedMainPath : normalFlame}
        style={weight === 'fill' ? 'fill' : 'stroke'}
        strokeWidth={weight === 'regular' ? strokeWidth : undefined}
        strokeCap={weight === 'regular' ? 'round' : undefined}
        strokeJoin={weight === 'regular' ? 'round' : undefined}
        color={color}
        transform={[{ translateX: sway.value }, { scale: size / 256 }]}
      />

      {/* Inner flame detail - cutout for fill, stroke for regular */}
      {weight === 'regular' ? (
        <Path
          path={innerFlame}
          style="stroke"
          strokeWidth={strokeWidth}
          strokeCap="round"
          strokeJoin="round"
          color={color}
          transform={[{ translateX: sway.value }, { scale: size / 256 }]}
        />
      ) : (
        <Path
          path={innerFlame}
          style="stroke"
          strokeWidth={strokeWidth * 2}
          strokeCap="round"
          strokeJoin="round"
          color="white"
          blendMode="dstOut"
          transform={[{ translateX: sway.value }, { scale: size / 256 }]}
        />
      )}
    </Canvas>
  );
};

// Usage examples:
export const FlameIconExamples = () => {
  return (
    <>
      {/* Static flame */}
      <FlameIcon size={100} color="#ff6b35" />

      {/* Animated flame */}
      <FlameIcon size={100} color="#ff4444" animated={true} />

      {/* Large animated flame */}
      <FlameIcon size={200} color="#ff8c00" animated={true} />
    </>
  );
};
