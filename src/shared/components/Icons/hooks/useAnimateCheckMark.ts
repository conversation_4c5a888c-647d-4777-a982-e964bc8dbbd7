import { useEffect } from 'react';

import {
  DataSourceParam,
  SkImage,
  Transforms3d,
  interpolate,
  useImage,
} from '@shopify/react-native-skia';
import { SharedValue, useDerivedValue, useSharedValue, withTiming } from 'react-native-reanimated';

export interface UseAnimatedCheckMarkReturnType {
  circleOneScale: SharedValue<Transforms3d>;
  centerImage: SkImage | null;
  centerImageOpacity: SharedValue<number>;
  value: SharedValue<number>;
}
const useAnimatedCheckMark = (
  speed: number,
  centerImageSource: DataSourceParam
): UseAnimatedCheckMarkReturnType => {
  const value: SharedValue<number> = useSharedValue(0);
  const centerImage: SkImage | null = useImage(centerImageSource);
  const circleOneScale = useSharedValue<Transforms3d>([{ scale: 1 }]);
  const centerImageOpacity = useSharedValue<number>(0);

  useDerivedValue(() => {
    circleOneScale.value = [{ scale: interpolate(value.value, [0, 0.2, 1], [0, 1, 1]) }];
    centerImageOpacity.value = interpolate(value.value, [0, 0.2, 0.8, 1], [0, 0, 1, 1]);
  }, [value]);

  useEffect(() => {
    value.set(withTiming(1, { duration: speed }));
  }, [speed, value]);

  return {
    circleOneScale,
    centerImage,
    centerImageOpacity,
    value,
  };
};

export default useAnimatedCheckMark;
