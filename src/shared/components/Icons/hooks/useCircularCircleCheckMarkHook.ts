import { Skia, Transforms3d, interpolate, rect } from '@shopify/react-native-skia';
import { SharedValue, useDerivedValue, useSharedValue } from 'react-native-reanimated';

import { UseCircularCheckMarkReturnType } from '../types/CircularCircleCheckMark.types';

const useCircularCheckMark = (
  size: number,
  value: SharedValue<number>
): UseCircularCheckMarkReturnType => {
  const circularCenterImageScale = useSharedValue<Transforms3d>([{ scale: 1 }]);
  const topBorderTransform = useSharedValue<Transforms3d>([{ rotate: 2.7 }]);
  const rightBorderTransform = useSharedValue<Transforms3d>([{ rotate: 2.7 }]);
  const bottomBorderTransform = useSharedValue<Transforms3d>([{ rotate: 2.7 }]);
  const leftBorderTransform = useSharedValue<Transforms3d>([{ rotate: 2.7 }]);
  const arcPath = Skia.Path.Make();
  arcPath.addArc(rect(size / 22, size / 22, size / 1.1, size / 1.1), 0, 360);

  useDerivedValue(() => {
    circularCenterImageScale.value = [{ scale: interpolate(value.value, [0, 1], [0, 1]) }];
    topBorderTransform.value = [
      {
        rotate: interpolate(value.value, [0, 1], [2.7, 8.6]),
      },
    ];
    rightBorderTransform.value = [
      {
        rotate: interpolate(value.value, [0, 1], [2.7, 10.2]),
      },
    ];
    bottomBorderTransform.value = [
      {
        rotate: interpolate(value.value, [0, 1], [2.7, 11.8]),
      },
    ];
    leftBorderTransform.value = [{ rotate: interpolate(value.value, [0, 1], [2.7, 14.9]) }];
  }, [value]);

  return {
    circularCenterImageScale,
    topBorderTransform,
    rightBorderTransform,
    bottomBorderTransform,
    leftBorderTransform,
    arcPath,
  };
};

export default useCircularCheckMark;
