import { SkImage, SkPath, Transforms3d } from '@shopify/react-native-skia';
import { SharedValue } from 'react-native-reanimated';

export interface UseCircularCheckMarkReturnType {
  circularCenterImageScale: SharedValue<Transforms3d>;
  topBorderTransform: SharedValue<Transforms3d>;
  rightBorderTransform: SharedValue<Transforms3d>;
  bottomBorderTransform: SharedValue<Transforms3d>;
  leftBorderTransform: SharedValue<Transforms3d>;
  arcPath: SkPath; // Remains unchanged
}
export interface CircularCheckMarkType {
  size: number;
  value: SharedValue<number>; // Changed from SkiaValue
  multiColor: boolean;
  borderColor: string;
  topBorderColor: string;
  rightBorderColor: string;
  bottomBorderColor: string;
  leftBorderColor: string;
  centerImage: SkImage | null; // Remains unchanged
  centerImageColor: string;
  centerImageOpacity: SharedValue<number>; // Changed from SkiaMutableValue
}
