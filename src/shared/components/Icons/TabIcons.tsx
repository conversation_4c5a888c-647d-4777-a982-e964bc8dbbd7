import React from 'react';

import {
  CirclesThreePlus,
  Confetti,
  Fire,
  MagnifyingGlass,
  MapPin,
  Plus,
  Ticket,
} from 'phosphor-react-native';

import { useTheme } from '@/src/core/theme';

interface TabIconProps {
  focused: boolean;
  size?: number;
}

export const FireIcon: React.FC<TabIconProps> = ({ focused, size = 24 }) => {
  const theme = useTheme();
  const focusedColor = focused ? theme.colors.iconPrimary : theme.colors.iconDefault;

  return <Fire size={size} color={focusedColor} weight={focused ? 'fill' : 'regular'} />;
};

export const SearchIcon: React.FC<TabIconProps> = ({ focused, size = 24 }) => {
  const theme = useTheme();
  const focusedColor = focused ? theme.colors.iconPrimary : theme.colors.iconDefault;

  return <MagnifyingGlass size={size} color={focusedColor} weight={focused ? 'fill' : 'regular'} />;
};

export const PlusIcon: React.FC<TabIconProps> = ({ focused, size = 24 }) => {
  const theme = useTheme();
  const focusedColor = focused ? theme.colors.iconPrimary : theme.colors.iconDefault;

  return <Plus size={size} color={focusedColor} weight={focused ? 'fill' : 'regular'} />;
};

export const ConfettiIcon: React.FC<TabIconProps> = ({ focused, size = 24 }) => {
  const theme = useTheme();
  const focusedColor = focused ? theme.colors.iconPrimary : theme.colors.iconDefault;

  return <Confetti size={size} color={focusedColor} weight={focused ? 'fill' : 'regular'} />;
};

export const CirclesThreePlusIcon: React.FC<TabIconProps> = ({ focused, size = 24 }) => {
  const theme = useTheme();
  const focusedColor = focused ? theme.colors.iconPrimary : theme.colors.iconDefault;

  return (
    <CirclesThreePlus size={size} color={focusedColor} weight={focused ? 'fill' : 'regular'} />
  );
};

export const MapIcon: React.FC<TabIconProps> = ({ focused, size = 24 }) => {
  const theme = useTheme();
  const focusedColor = focused ? theme.colors.iconPrimary : theme.colors.iconDefault;

  return <MapPin size={size} color={focusedColor} weight={focused ? 'fill' : 'regular'} />;
};

export const TicketIcon: React.FC<TabIconProps> = ({ focused, size = 24 }) => {
  const theme = useTheme();
  const focusedColor = focused ? theme.colors.iconPrimary : theme.colors.iconDefault;

  return <Ticket size={size} color={focusedColor} weight={focused ? 'fill' : 'regular'} />;
};
