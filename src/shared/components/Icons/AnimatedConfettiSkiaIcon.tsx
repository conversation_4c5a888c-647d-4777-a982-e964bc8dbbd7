import React, { useEffect } from 'react';

import { Canvas, Line, Path, Skia, usePathInterpolation } from '@shopify/react-native-skia';
import {
  Easing,
  interpolate,
  useDerivedValue,
  useSharedValue,
  withDelay,
  withRepeat,
  withSequence,
  withTiming,
} from 'react-native-reanimated';

interface ConfettiIconProps {
  size?: number;
  color?: string;
  animated?: boolean;
  weight?: 'fill' | 'regular';
}

export const AnimatedConfettiSkiaIcon: React.FC<ConfettiIconProps> = ({
  size = 256,
  color = '#ff6b35',
  animated = false,
  weight = 'regular',
}) => {
  // Animation values
  const burstProgress = useSharedValue(0);
  const rotationProgress = useSharedValue(0);

  // Calculate stroke width with minimum for small sizes
  const calculateStrokeWidth = (iconSize: number) => {
    const minStroke = 10;
    const proportionalStroke = iconSize * 0.094;
    return Math.max(minStroke, proportionalStroke);
  };

  const strokeWidth = calculateStrokeWidth(size);

  // Main confetti popper body (static)
  const popperBody = Skia.Path.MakeFromSVGString(
    'M40.49,205.52,93,61.14a7.79,7.79,0,0,1,12.84-2.85l91.88,91.88A7.79,7.79,0,0,1,194.86,163L50.48,215.51A7.79,7.79,0,0,1,40.49,205.52Z'
  )!;

  // Decorative swirl
  const swirl = Skia.Path.MakeFromSVGString(
    'M168.42,68c1.19-6.83,5.8-20,23.58-20,24,0,24-24,24-24'
  )!;

  // Confetti pieces - based on original SVG coordinates
  const confetti1Initial = Skia.Path.MakeFromSVGString('M140,20L140,30')!;
  const confetti1Extended = Skia.Path.MakeFromSVGString('M140,16L140,40')!; // Original
  const confetti1Burst = Skia.Path.MakeFromSVGString('M140,14L140,42')!;

  const confetti2Initial = Skia.Path.MakeFromSVGString('M222,118L230,126')!;
  const confetti2Extended = Skia.Path.MakeFromSVGString('M220,116L236,132')!; // Original
  const confetti2Burst = Skia.Path.MakeFromSVGString('M218,114L238,134')!;

  const confetti3Initial = Skia.Path.MakeFromSVGString('M218,82L225,78')!;
  const confetti3Extended = Skia.Path.MakeFromSVGString('M216,80L240,72')!; // Original
  const confetti3Burst = Skia.Path.MakeFromSVGString('M214,82L242,70')!;

  const confetti4Initial = Skia.Path.MakeFromSVGString('M78.09,102.09L130,150')!;
  const confetti4Extended = Skia.Path.MakeFromSVGString('M78.09,102.09L153.91,177.91')!; // Original
  const confetti4Burst = Skia.Path.MakeFromSVGString('M75,100L156,180')!;

  const confetti5Initial = Skia.Path.MakeFromSVGString('M101.11,197.11L75,170')!;
  const confetti5Extended = Skia.Path.MakeFromSVGString('M101.11,197.11L58.89,154.89')!; // Original
  const confetti5Burst = Skia.Path.MakeFromSVGString('M103,200L56,152')!;

  // Animated confetti paths
  const animatedConfetti1 = usePathInterpolation(
    burstProgress,
    [0, 0.5, 1],
    [confetti1Initial, confetti1Extended, confetti1Burst]
  );

  const animatedConfetti2 = usePathInterpolation(
    burstProgress,
    [0, 0.5, 1],
    [confetti2Initial, confetti2Extended, confetti2Burst]
  );

  const animatedConfetti3 = usePathInterpolation(
    burstProgress,
    [0, 0.5, 1],
    [confetti3Initial, confetti3Extended, confetti3Burst]
  );

  const animatedConfetti4 = usePathInterpolation(
    burstProgress,
    [0, 0.5, 1],
    [confetti4Initial, confetti4Extended, confetti4Burst]
  );

  const animatedConfetti5 = usePathInterpolation(
    burstProgress,
    [0, 0.5, 1],
    [confetti5Initial, confetti5Extended, confetti5Burst]
  );

  // Animated opacity for confetti pieces
  const confettiOpacity = useDerivedValue(() => {
    if (!animated) return 1;
    return interpolate(burstProgress.value, [0, 0.3, 1], [0.3, 1, 0.6]);
  });

  // Start animations
  useEffect(() => {
    if (animated) {
      burstProgress.set(
        withRepeat(
          withSequence(
            withTiming(1, { duration: 800 }),
            withDelay(1200, withTiming(0, { duration: 600 }))
          ),
          -1,
          false
        )
      );

      rotationProgress.set(
        withRepeat(withTiming(1, { duration: 2000, easing: Easing.linear }), -1, false)
      );
    } else {
      burstProgress.set(0);
      rotationProgress.set(0);
    }
  }, [animated, burstProgress, rotationProgress]);

  return (
    <Canvas style={{ width: size, height: size }}>
      {/* Main confetti popper body */}
      <Path
        path={popperBody}
        style={weight === 'fill' ? 'fill' : 'stroke'}
        strokeWidth={weight === 'regular' ? strokeWidth : undefined}
        strokeCap={weight === 'regular' ? 'round' : undefined}
        strokeJoin={weight === 'regular' ? 'round' : undefined}
        color={color}
        transform={[{ scale: size / 256 }]}
      />

      {/* Decorative swirl with subtle rotation */}
      <Path
        path={swirl}
        style="stroke"
        strokeWidth={weight === 'regular' ? strokeWidth : strokeWidth * 0.8}
        strokeCap="round"
        strokeJoin="round"
        color={color}
        transform={[
          { scale: size / 256 },
          { rotate: animated ? rotationProgress.value : 0 },
          { translateX: animated ? (size / 256) * 192 : (size / 256) * 192 },
          { translateY: animated ? (size / 256) * 44 : (size / 256) * 44 },
          { translateX: animated ? -(size / 256) * 192 : -(size / 256) * 192 },
          { translateY: animated ? -(size / 256) * 44 : -(size / 256) * 44 },
        ]}
      />

      {/* Animated confetti pieces */}
      <Path
        path={animated ? animatedConfetti1 : confetti1Initial}
        style="stroke"
        strokeWidth={weight === 'regular' ? strokeWidth : strokeWidth * 0.7}
        strokeCap="round"
        strokeJoin="round"
        color={color}
        opacity={confettiOpacity}
        transform={[{ scale: size / 256 }]}
      />

      <Path
        path={animated ? animatedConfetti2 : confetti2Initial}
        style="stroke"
        strokeWidth={weight === 'regular' ? strokeWidth : strokeWidth * 0.7}
        strokeCap="round"
        strokeJoin="round"
        color={color}
        opacity={confettiOpacity}
        transform={[{ scale: size / 256 }]}
      />

      <Path
        path={animated ? animatedConfetti3 : confetti3Initial}
        style="stroke"
        strokeWidth={weight === 'regular' ? strokeWidth : strokeWidth * 0.7}
        strokeCap="round"
        strokeJoin="round"
        color={color}
        opacity={confettiOpacity}
        transform={[{ scale: size / 256 }]}
      />

      <Path
        path={animated ? animatedConfetti4 : confetti4Initial}
        style="stroke"
        strokeWidth={weight === 'regular' ? strokeWidth : strokeWidth * 1.5}
        strokeCap="round"
        strokeJoin="round"
        color={weight === 'regular' ? color : 'white'}
        opacity={confettiOpacity}
        blendMode={weight === 'fill' ? 'dstOut' : undefined}
        transform={[{ scale: size / 256 }]}
      />

      <Path
        path={animated ? animatedConfetti5 : confetti5Initial}
        style="stroke"
        strokeWidth={weight === 'regular' ? strokeWidth : strokeWidth * 1.5}
        strokeCap="round"
        strokeJoin="round"
        color={weight === 'regular' ? color : 'white'}
        opacity={confettiOpacity}
        blendMode={weight === 'fill' ? 'dstOut' : undefined}
        transform={[{ scale: size / 256 }]}
      />
    </Canvas>
  );
};

// Usage examples:
export const ConfettiIconExamples = () => {
  return (
    <>
      {/* Static confetti */}
      <AnimatedConfettiSkiaIcon size={100} color="#ff6b35" />

      {/* Filled confetti */}
      <AnimatedConfettiSkiaIcon size={100} color="#ff4444" weight="fill" />

      {/* Animated confetti burst */}
      <AnimatedConfettiSkiaIcon size={100} color="#ff4444" animated={true} />

      {/* Large filled animated confetti */}
      <AnimatedConfettiSkiaIcon size={200} color="#ff8c00" animated={true} weight="fill" />
    </>
  );
};
