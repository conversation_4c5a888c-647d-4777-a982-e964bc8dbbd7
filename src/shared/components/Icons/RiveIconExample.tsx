import Rive, { RiveRef } from 'rive-react-native';

/**
 * A component that renders a Rive animation from a resource named `checkmark_icon_2`.
 * This component is used to demonstrate the use of Rive in a React component.
 *
 * @param {React.RefObject<RiveRef | null>} ref - a reference to the Rive component
 * @returns {JSX.Element} The rendered Rive animation.
 */
function RiveIconExample({ ref }: { ref: React.RefObject<RiveRef | null> }) {
  return (
    <Rive resourceName="checkmark_icon_2" artboardName="Artboard" autoplay={false} ref={ref} />
  );
}

export default RiveIconExample;
