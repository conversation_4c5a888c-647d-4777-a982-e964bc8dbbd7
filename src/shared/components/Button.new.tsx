/* eslint-disable react-compiler/react-compiler */
import { forwardRef } from 'react';

import { ActivityIndicator, View } from 'react-native';

import {
  BoxProps,
  VariantProps,
  composeRestyleFunctions,
  createVariant,
  useRestyle,
} from '@shopify/restyle';
import { TOptions } from 'i18next';
import Animated, {
  Easing,
  interpolateColor,
  useAnimatedStyle,
  useDerivedValue,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';

import { TxKeyPath } from '@/src/core/i18n/translationTypes';
import { Box, Text, Theme, useTheme } from '@/src/core/theme';
import Pressable, { PressableProps } from '@/src/core/theme/Pressable';
import { TokensThemeColors } from '@/src/core/theme/theme';
import {
  ButtonIntentVariants,
  ButtonSizeVariants,
  ButtonStateVariants,
  ButtonTypeVariants,
  IconButtonVariants,
  SocialButtonVariants,
} from '@/src/core/theme/variants/button';

import { SpringPresets } from '../constants/animation';

// Define the multi-dimensional button props
type ButtonProps = {
  // Content props
  title?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  loading?: boolean;

  // Style props
  fullWidth?: boolean;

  // State props
  enabled?: boolean;
  pressed?: boolean;

  // Variant props
  intent?: ButtonIntentVariants;
  type?: ButtonTypeVariants;
  size?: ButtonSizeVariants;
  state?: ButtonStateVariants;

  // Icon button specific
  iconOnly?: boolean;
  iconVariant?: IconButtonVariants;

  // Social button specific
  socialVariant?: SocialButtonVariants;

  // Override colors
  textColor?: TokensThemeColors;

  // i18n
  tx?: TxKeyPath;
  txOptions?: TOptions;
};

// Define restyle props for standard multi-dimensional button
type StandardRestyleProps = BoxProps<Theme> & {
  variant?: ButtonIntentVariants;
  typeVariant?: ButtonTypeVariants;
  sizeVariant?: ButtonSizeVariants;
  stateVariant?: ButtonStateVariants;
};

// Define restyle props for icon button
type IconRestyleProps = BoxProps<Theme> & VariantProps<Theme, 'iconButtonVariants'>;

// Define restyle props for social button
type SocialRestyleProps = BoxProps<Theme> & VariantProps<Theme, 'socialButtonVariants'>;

// Combined component props
type ButtonComponentProps = ButtonProps & Omit<PressableProps, 'ref'>;

// Create the button variant restyle functions
const buttonIntentVariant = createVariant<Theme, 'buttonIntentVariants'>({
  themeKey: 'buttonIntentVariants',
});

const buttonTypeVariant = createVariant<Theme, 'buttonTypeVariants'>({
  themeKey: 'buttonTypeVariants',
});

const buttonSizeVariant = createVariant<Theme, 'buttonSizeVariants'>({
  themeKey: 'buttonSizeVariants',
});

const buttonStateVariant = createVariant<Theme, 'buttonStateVariants'>({
  themeKey: 'buttonStateVariants',
});

const iconButtonVariant = createVariant<Theme, 'iconButtonVariants'>({
  themeKey: 'iconButtonVariants',
});

const socialButtonVariant = createVariant<Theme, 'socialButtonVariants'>({
  themeKey: 'socialButtonVariants',
});

// Compose the restyle functions based on button type
const restyleFunction = composeRestyleFunctions<Theme, StandardRestyleProps>([
  buttonIntentVariant as any,
  buttonTypeVariant as any,
  buttonSizeVariant as any,
  buttonStateVariant as any,
]);

const iconRestyleFunction = composeRestyleFunctions<Theme, IconRestyleProps>([iconButtonVariant]);

const socialRestyleFunction = composeRestyleFunctions<Theme, SocialRestyleProps>([
  socialButtonVariant,
]);

// Create an animated version of Pressable
const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

/**
 * Multi-dimensional Button component with composed variants
 * Supports intent (primary, secondary, etc.), type (filled, outlined, etc.),
 * size (small, default, large), and special variants for icons and social login
 */
const Button = forwardRef<View, ButtonComponentProps>(function Button(props, ref) {
  const theme = useTheme();

  const {
    // Content
    title,
    leftIcon,
    rightIcon,
    loading = false,

    // Style
    fullWidth = false,

    // State
    enabled = true,
    pressed = false,

    // Variants
    intent = 'primary',
    type = 'filled',
    size = 'default',
    state,

    // Icon button
    iconOnly = false,
    iconVariant = 'default',

    // Social button
    socialVariant,

    // Colors
    textColor,

    // i18n
    tx,
    txOptions,

    style,
    ...rest
  } = props;

  // All hooks must be called unconditionally at the top level
  const socialRestyleProps = useRestyle(socialRestyleFunction, {
    variant: socialVariant,
  } as SocialRestyleProps);

  const iconRestyleProps = useRestyle(iconRestyleFunction, {
    variant: iconVariant,
  } as IconRestyleProps);

  // For multi-dimensional variants, we need to map our props to the expected variant props
  const standardRestyleProps = useRestyle(restyleFunction, {
    variant: intent,
    typeVariant: type,
    sizeVariant: size,
    stateVariant: !enabled ? 'disabled' : state,
  } as StandardRestyleProps);

  // Determine which restyle props to use
  const restyleProps = socialVariant
    ? socialRestyleProps
    : iconOnly
      ? iconRestyleProps
      : standardRestyleProps;

  // Shared values for animations
  const scale = useSharedValue(1);
  const isPressed = useSharedValue(pressed ? 1 : 0);

  // Get background colors based on variants and state
  const getBackgroundColors = () => {
    if (!enabled) return { normal: 'disabled', pressed: 'disabled' };

    if (socialVariant) {
      return {
        normal:
          socialVariant === 'apple'
            ? 'black'
            : socialVariant === 'google'
              ? 'white'
              : socialVariant === 'facebook'
                ? '#1877F2'
                : 'surfaceBackground',
        pressed:
          socialVariant === 'apple'
            ? 'gray800'
            : socialVariant === 'google'
              ? 'gray100'
              : socialVariant === 'facebook'
                ? '#1465CC'
                : 'gray200',
      };
    }

    if (type === 'outlined' || type === 'borderless' || type === 'ghost') {
      return { normal: 'transparent', pressed: 'primaryAlpha10' };
    }

    if (type === 'tinted') {
      return { normal: 'primaryAlpha20', pressed: 'primaryAlpha30' };
    }

    // Filled buttons
    const colorMap: Record<
      ButtonIntentVariants,
      { normal: TokensThemeColors; pressed: TokensThemeColors }
    > = {
      primary: { normal: 'primary', pressed: 'primaryDark' },
      secondary: { normal: 'buttonTintedBackground', pressed: 'buttonFilledBackgroundHover' },
      danger: { normal: 'error', pressed: 'errorDark' },
      success: { normal: 'success', pressed: 'successDark' },
      warning: { normal: 'warning', pressed: 'warningDark' },
      neutral: { normal: 'textSecondary', pressed: 'text' },
    };

    return colorMap[intent] || colorMap.primary;
  };

  const { normal: bgColor, pressed: bgColorPressed } = getBackgroundColors();

  // Track theme colors with derived values
  const backgroundColorValue = useDerivedValue(() => {
    return theme.colors[bgColor as keyof typeof theme.colors] || bgColor;
  }, [bgColor, theme.colors]);

  const pressedBackgroundColorValue = useDerivedValue(() => {
    return theme.colors[bgColorPressed as keyof typeof theme.colors] || bgColorPressed;
  }, [bgColorPressed, theme.colors]);

  // Animated style for scale and background color
  const animatedStyle = useAnimatedStyle(() => ({
    backgroundColor: interpolateColor(
      isPressed.value,
      [0, 1],
      [backgroundColorValue.value, pressedBackgroundColorValue.value]
    ),
    transform: [{ scale: scale.value }],
  }));

  // Handle press animations
  const handleActiveStateChange = (active: boolean) => {
    isPressed.value = withTiming(active ? 1 : 0, {
      duration: 200,
      easing: Easing.bezier(0.5, 0.01, 0, 1),
    });

    const targetScale = active ? 0.98 : 1;
    scale.value = withSpring(targetScale, SpringPresets.BUTTON_PRESS);
  };

  // Text color logic
  const getTextColor = (): TokensThemeColors => {
    if (textColor) return textColor;
    if (!enabled) return 'textTertiary';

    if (intent === 'secondary') {
      return type === 'filled' ? 'primary' : 'textSecondary';
    }
    if (socialVariant) {
      return socialVariant === 'apple' ? 'white' : 'text';
    }

    if (type === 'outlined' || type === 'borderless' || type === 'ghost') {
      return intent === 'danger'
        ? 'error'
        : intent === 'success'
          ? 'success'
          : intent === 'warning'
            ? 'warning'
            : 'primary';
    }

    if (type === 'tinted') {
      return intent === 'danger'
        ? 'error'
        : intent === 'success'
          ? 'success'
          : intent === 'warning'
            ? 'warning'
            : 'primary';
    }

    // Filled buttons
    return 'white';
  };

  // Text variant based on size
  const getTextVariant = () => {
    const sizeToVariant = {
      small: 'b_14Medium_button',
      default: 'b_16SemiBold_button',
      large: 'b_16SemiBold_button',
    };

    return sizeToVariant[size] || sizeToVariant.default;
  };

  // Activity indicator size
  const getActivityIndicatorSize = () => {
    return size === 'small' || iconOnly ? 'small' : 'large';
  };

  // Render content
  const renderContent = () => {
    if (loading) {
      return (
        <ActivityIndicator
          color={theme.colors[getTextColor()]}
          size={getActivityIndicatorSize()}
          style={{ marginHorizontal: 8 }}
        />
      );
    }

    if (iconOnly && leftIcon) {
      return leftIcon;
    }

    return (
      <>
        {leftIcon && <Box marginRight="xs_8">{leftIcon}</Box>}
        {(title || tx) && (
          <Text
            tx={tx}
            txOptions={txOptions}
            variant={getTextVariant() as any}
            color={getTextColor()}>
            {title}
          </Text>
        )}
        {rightIcon && <Box marginLeft="xs_8">{rightIcon}</Box>}
      </>
    );
  };

  return (
    <AnimatedPressable
      ref={ref}
      activeOpacity={0}
      enabled={enabled && !loading}
      onActiveStateChange={handleActiveStateChange}
      {...rest}
      style={[(restyleProps as any).style, animatedStyle, fullWidth && { width: '100%' }, style]}>
      <Box flexDirection="row" alignItems="center" justifyContent="center">
        {renderContent()}
      </Box>
    </AnimatedPressable>
  );
});

export default Button;
