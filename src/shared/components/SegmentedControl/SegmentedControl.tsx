import React, { useEffect, useState } from 'react';

import { View } from 'react-native';

import Animated, {
  interpolateColor,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';

import { Box, Row, Text } from '@/src/core/theme';
import { PressableAnimated } from '@/src/core/theme/Pressable';
import { useTheme } from '@/src/core/theme/theme';

const AnimatedText = Animated.createAnimatedComponent(Text);
const AnimatedBox = Animated.createAnimatedComponent(Box);

type SegmentControlItemProps = {
  title?: string;
  component?: React.ReactNode;
  isSelected?: boolean;
};

type SegmentedControlProps = {
  items: SegmentControlItemProps[];
  onValueChange?: (index: number) => void;
  selected?: number;
  initialValue?: number;
};

const SegmentedControlItem = ({ title, component, isSelected }: SegmentControlItemProps) => {
  const { colors } = useTheme();
  const progress = useSharedValue(isSelected ? 1 : 0);

  useEffect(() => {
    progress.value = withTiming(isSelected ? 1 : 0, { duration: 100 });
  }, [isSelected, progress]);

  const animatedTextStyle = useAnimatedStyle(() => {
    return {
      color: interpolateColor(
        progress.value,
        [0, 1],
        [colors.buttonTintedText, colors.buttonFilledText]
      ),
    };
  });

  if (component) {
    // Wrap component in View to ensure proper layout measurement
    return (
      <View
        style={{
          alignItems: 'center',
          justifyContent: 'center',
        }}>
        {React.cloneElement(
          component as React.ReactElement<{
            color: string;
            size: number;
            weight: string;
          }>,
          {
            color: isSelected ? colors.buttonFilledText : colors.buttonTintedText,

            size: 24,

            weight: !isSelected ? 'regular' : 'bold',
          }
        )}
      </View>
    );
  }

  return (
    <AnimatedText style={animatedTextStyle} variant="b_14SemiBold_listTitle">
      {title}
    </AnimatedText>
  );
};

export default function SegmentedControl({
  items,
  onValueChange: onPress,
  selected = 0,
  initialValue = 0,
}: SegmentedControlProps) {
  const { strokes, colors } = useTheme();
  const [value, setValue] = useState(selected ?? initialValue);
  const [segmentWidths, setSegmentWidths] = useState<number[]>(new Array(items.length).fill(0));
  const animatedTranslateX = useSharedValue(0);
  const animatedWidth = useSharedValue(0);

  const onPressHandler = (index: number) => {
    setValue(index);
    onPress?.(index);
  };

  // Calculate positions based on segment widths
  const positions = segmentWidths.reduce((acc, width, index) => {
    if (index === 0) {
      acc.push(0);
    } else {
      acc.push(acc[index - 1] + segmentWidths[index - 1]);
    }
    return acc;
  }, [] as number[]);

  // Ensure all widths are set and non-zero
  const allWidthsSet = segmentWidths.length === items.length && segmentWidths.every(w => w > 0);

  useEffect(() => {
    if (allWidthsSet) {
      animatedTranslateX.value = withTiming(positions[value], { duration: 200 });
      animatedWidth.value = withTiming(segmentWidths[value], { duration: 200 });
    }
  }, [value, allWidthsSet, positions, segmentWidths, animatedTranslateX, animatedWidth]);

  const backgroundStyle = useAnimatedStyle(() => ({
    width: animatedWidth.value,
    transform: [{ translateX: animatedTranslateX.value }],
  }));

  return (
    <Row
      bg="buttonFilledText"
      borderRadius="lg_16"
      borderColor="subtleBorder"
      p="xxxs_2"
      alignSelf="flex-start"
      borderWidth={strokes.spark_1}>
      <Row bg="buttonTintedBackground" borderRadius="lg_16" overflow="hidden">
        {allWidthsSet && (
          <AnimatedBox
            borderRadius="lg_16"
            style={[
              {
                position: 'absolute',
                top: 0,
                left: 0,
                bottom: 0,
                backgroundColor: colors.buttonFilledBackground,
              },
              backgroundStyle,
            ]}
          />
        )}
        {items.map((item, index) => (
          <Box
            key={index}
            onLayout={event => {
              const width = event.nativeEvent.layout.width;
              setSegmentWidths(prev => {
                const newWidths = [...prev];
                newWidths[index] = Math.max(width, 32); // Fallback minimum width
                return newWidths;
              });
            }}>
            <PressableAnimated
              onPress={() => onPressHandler(index)}
              bg="transparent"
              borderRadius="lg_16"
              px="xs_8"
              py="xxs_4"
              style={{ alignItems: 'center', justifyContent: 'center' }}>
              <SegmentedControlItem
                title={item.title}
                component={item.component}
                isSelected={value === index}
              />
            </PressableAnimated>
          </Box>
        ))}
      </Row>
    </Row>
  );
}
