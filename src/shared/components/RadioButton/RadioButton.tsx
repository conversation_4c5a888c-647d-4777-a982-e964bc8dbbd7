import React, { useCallback, useEffect } from 'react';

import { AccessibilityProps, Pressable, ViewStyle } from 'react-native';

import Animated, {
  Easing,
  interpolate,
  interpolateColor,
  useAnimatedStyle,
  useSharedValue,
  withSequence,
  withSpring,
  withTiming,
} from 'react-native-reanimated';

import { useTheme } from '@/src/core/theme/theme';

import { RADIO_BUTTON_CONSTANTS, RADIO_BUTTON_SPRING_CONFIG, RadioButtonShape } from './constants';

// Following your pattern of creating animated components from React Native components
const RadioButtonAnimated = Animated.createAnimatedComponent(Pressable);

/**
 * Animation Style Variants
 *
 * Different animation approaches to choose from based on the desired feel:
 * - 'bounce': Playful bounce effect with spring physics
 * - 'elastic': More pronounced elastic effect with overshoot
 * - 'fade': Smooth fade with gentle scaling
 * - 'pop': Quick, snappy appearance with secondary animation
 * - 'ripple': Expanding ripple effect (advanced)
 */
export type AnimationStyle = 'bounce' | 'elastic' | 'fade' | 'pop' | 'ripple';

/**
 * RadioButton Props Interface
 *
 * Enhanced with animation style options for maximum flexibility
 */
export interface RadioButtonProps extends AccessibilityProps {
  /** Whether the radio button is selected */
  isSelected: boolean;

  /** Callback when radio button is pressed */
  onPress: () => void;

  /** Visual shape variant - circular (default), rounded, or squared */
  shape?: RadioButtonShape;

  /** Size of the radio button in pixels */
  size?: number;

  /** Whether the radio button is disabled */
  disabled?: boolean;

  /** Custom style overrides */
  style?: ViewStyle | ViewStyle[];

  /**
   * Scale factor for the inner dot when selected
   * Default is 0.5 (50% of container size)
   */
  dotScale?: number;

  /**
   * Animation style for enter/exit transitions
   * Default is 'bounce' for a delightful, modern feel
   */
  animationStyle?: AnimationStyle;

  /**
   * Custom color overrides for different states
   * If not provided, will use theme colors
   */
  colors?: {
    uncheckedBorder?: string;
    uncheckedBackground?: string;
    checkedBorder?: string;
    checkedBackground?: string;
    dotColor?: string;
  };
}

/**
 * Enhanced RadioButton Component
 *
 * Now featuring sophisticated enter/exit animations that create delightful user experiences
 * through multi-phase animations, spring physics, and carefully choreographed timing.
 */
export const RadioButton = ({
  isSelected,
  onPress,
  shape = 'circular',
  size = RADIO_BUTTON_CONSTANTS.DEFAULT_SIZE,
  disabled = false,
  style,
  dotScale = RADIO_BUTTON_CONSTANTS.DOT_SCALE,
  animationStyle = 'bounce',
  colors: customColors,
  ...accessibilityProps
}: RadioButtonProps) => {
  const { colors } = useTheme();

  // Primary animation progress - controls the overall state transition
  const progress = useSharedValue(isSelected ? 1 : 0);

  // Separate animation values for sophisticated control
  const dotScale2 = useSharedValue(isSelected ? 1 : 0);
  const dotOpacity = useSharedValue(isSelected ? 1 : 0);
  const dotRotation = useSharedValue(0); // For subtle rotation effects
  const pressScale = useSharedValue(1);

  // Secondary animation for extra delight (like a subtle ripple or glow effect)
  const secondaryScale = useSharedValue(0);

  /**
   * Animation Configuration Functions
   *
   * These functions return different animation configurations based on the selected style.
   * Each style is designed to evoke different emotional responses and use cases.
   */

  const getEnterAnimation = useCallback((style: AnimationStyle) => {
    switch (style) {
      case 'bounce':
        // Bouncy entrance - feels playful and confident
        // Uses spring physics with carefully tuned parameters for a satisfying bounce
        return {
          scale: withSpring(1, {
            damping: 15, // Lower damping = more bounce
            stiffness: 350, // Higher stiffness = faster response
            mass: 1,
          }),
          opacity: withTiming(1, {
            duration: 150, // Opacity comes in quickly
            easing: Easing.out(Easing.quad),
          }),
          rotation: withSpring(0, { damping: 20, stiffness: 300 }),
        };

      case 'elastic':
        // Elastic entrance - more dramatic overshoot, feels energetic
        return {
          scale: withSpring(1, {
            damping: 8, // Much lower damping for pronounced overshoot
            stiffness: 400,
            mass: 1,
          }),
          opacity: withTiming(1, {
            duration: 100,
            easing: Easing.out(Easing.cubic),
          }),
          rotation: withSequence(
            withTiming(15, { duration: 100 }), // Slight rotation on entry
            withSpring(0, { damping: 15, stiffness: 300 })
          ),
        };

      case 'fade':
        // Gentle fade - feels sophisticated and subtle
        return {
          scale: withTiming(1, {
            duration: 250,
            easing: Easing.out(Easing.cubic),
          }),
          opacity: withTiming(1, {
            duration: 200,
            easing: Easing.out(Easing.quad),
          }),
          rotation: withTiming(0, { duration: 250 }),
        };

      case 'pop':
        // Quick pop - feels immediate and responsive
        return {
          scale: withSequence(
            withTiming(1.2, { duration: 100, easing: Easing.out(Easing.cubic) }), // Quick overshoot
            withTiming(1, { duration: 100, easing: Easing.out(Easing.quad) }) // Settle back
          ),
          opacity: withTiming(1, {
            duration: 80,
            easing: Easing.out(Easing.cubic),
          }),
          rotation: withTiming(0, { duration: 150 }),
        };

      case 'ripple':
        // Ripple effect - feels like material design with expanding motion
        return {
          scale: withTiming(1, {
            duration: 200,
            easing: Easing.out(Easing.cubic),
          }),
          opacity: withSequence(
            withTiming(0.7, { duration: 100 }),
            withTiming(1, { duration: 100 })
          ),
          rotation: withTiming(0, { duration: 200 }),
        };

      default:
        return getEnterAnimation('bounce');
    }
  }, []);

  const getExitAnimation = useCallback((style: AnimationStyle) => {
    switch (style) {
      case 'bounce':
        // Bouncy exit - quick but not abrupt
        return {
          scale: withSpring(0, {
            damping: 20, // More damping on exit for quicker settling
            stiffness: 400,
            mass: 0.8, // Lower mass for snappier response
          }),
          opacity: withTiming(0, {
            duration: 120,
            easing: Easing.in(Easing.quad),
          }),
          rotation: withSpring(0, { damping: 25, stiffness: 350 }),
        };

      case 'elastic':
        // Elastic exit - slight compression before disappearing
        return {
          scale: withSequence(
            withTiming(1.1, { duration: 80 }), // Brief compression
            withSpring(0, { damping: 12, stiffness: 350 })
          ),
          opacity: withTiming(0, {
            duration: 150,
            easing: Easing.in(Easing.cubic),
          }),
          rotation: withSpring(-10, { damping: 20, stiffness: 300 }),
        };

      case 'fade':
        // Gentle fade out - mirrors the entrance
        return {
          scale: withTiming(0.8, {
            duration: 200,
            easing: Easing.in(Easing.cubic),
          }),
          opacity: withTiming(0, {
            duration: 180,
            easing: Easing.in(Easing.quad),
          }),
          rotation: withTiming(0, { duration: 200 }),
        };

      case 'pop':
        // Quick pop out - immediate but not jarring
        return {
          scale: withTiming(0, {
            duration: 120,
            easing: Easing.in(Easing.cubic),
          }),
          opacity: withTiming(0, {
            duration: 100,
            easing: Easing.in(Easing.quad),
          }),
          rotation: withTiming(0, { duration: 120 }),
        };

      case 'ripple':
        // Ripple exit - contracts inward like reverse ripple
        return {
          scale: withTiming(0, {
            duration: 180,
            easing: Easing.in(Easing.cubic),
          }),
          opacity: withTiming(0, {
            duration: 150,
            easing: Easing.in(Easing.quad),
          }),
          rotation: withTiming(0, { duration: 180 }),
        };

      default:
        return getExitAnimation('bounce');
    }
  }, []);

  // Enhanced animation effect that orchestrates multiple animation properties
  useEffect(() => {
    // Update the main progress value for container animations
    progress.value = withTiming(isSelected ? 1 : 0, {
      duration: RADIO_BUTTON_CONSTANTS.ANIMATION_DURATION,
    });

    // Orchestrate the dot animations based on selection state
    if (isSelected) {
      // ENTER ANIMATIONS - Choreographed for maximum delight
      const enterConfig = getEnterAnimation(animationStyle);

      // Start the entrance sequence
      dotScale2.value = enterConfig.scale;
      dotOpacity.value = enterConfig.opacity;
      dotRotation.value = enterConfig.rotation;

      // Secondary effect for ripple style
      if (animationStyle === 'ripple') {
        secondaryScale.value = withSequence(
          withTiming(1.5, { duration: 150, easing: Easing.out(Easing.cubic) }),
          withTiming(0, { duration: 100, easing: Easing.in(Easing.quad) })
        );
      }
    } else {
      // EXIT ANIMATIONS - Quick but graceful departure
      const exitConfig = getExitAnimation(animationStyle);

      // Start the exit sequence
      dotScale2.value = exitConfig.scale;
      dotOpacity.value = exitConfig.opacity;
      dotRotation.value = exitConfig.rotation;

      // Reset secondary effects
      secondaryScale.value = withTiming(0, { duration: 100 });
    }
  }, [
    isSelected,
    animationStyle,
    dotScale2,
    dotOpacity,
    dotRotation,
    progress,
    secondaryScale,
    getEnterAnimation,
    getExitAnimation,
  ]);

  // Calculate styling properties
  const getBorderRadius = () => {
    switch (shape) {
      case 'circular':
        return RADIO_BUTTON_CONSTANTS.BORDER_RADIUS.CIRCULAR;
      case 'rounded':
        return RADIO_BUTTON_CONSTANTS.BORDER_RADIUS.ROUNDED;
      case 'squared':
        return RADIO_BUTTON_CONSTANTS.BORDER_RADIUS.SQUARED;
      default:
        return RADIO_BUTTON_CONSTANTS.BORDER_RADIUS.CIRCULAR;
    }
  };

  const dotSize = size * dotScale;
  const dotBorderRadius = shape === 'circular' ? dotSize / 2 : getBorderRadius() * 0.6;

  // Color resolution - use custom colors if provided, otherwise fall back to theme
  const resolvedColors = {
    uncheckedBorder: customColors?.uncheckedBorder || colors.inputPlaceholder,
    uncheckedBackground: customColors?.uncheckedBackground || 'transparent',
    checkedBorder: customColors?.checkedBorder || colors.primary,
    checkedBackground: customColors?.checkedBackground || colors.primary,
    dotColor: customColors?.dotColor || colors.buttonFilledText,
  };

  // Enhanced press handler with improved tactile feedback
  const handlePress = () => {
    if (disabled) return;

    // More sophisticated press feedback that adapts to animation style
    const pressIntensity = animationStyle === 'elastic' || animationStyle === 'pop' ? 0.9 : 0.95;

    pressScale.set(
      withSpring(pressIntensity, {
        ...RADIO_BUTTON_SPRING_CONFIG,
        damping: 25, // Quick press response
      })
    );

    setTimeout(() => {
      pressScale.set(
        withSpring(1, {
          ...RADIO_BUTTON_SPRING_CONFIG,
          damping: 20,
        })
      );
    }, 80);

    onPress();
  };

  // Container animation style - enhanced with better interpolation
  const containerAnimatedStyle = useAnimatedStyle(() => ({
    backgroundColor: interpolateColor(
      progress.value,
      [0, 1],
      [resolvedColors.uncheckedBackground, resolvedColors.checkedBackground]
    ),
    borderColor: interpolateColor(
      progress.value,
      [0, 1],
      [resolvedColors.uncheckedBorder, resolvedColors.checkedBorder]
    ),
    borderWidth: interpolate(
      progress.value,
      [0, 0.2, 1],
      [
        RADIO_BUTTON_CONSTANTS.BORDER_WIDTH_DEFAULT,
        RADIO_BUTTON_CONSTANTS.BORDER_WIDTH_FOCUSED,
        RADIO_BUTTON_CONSTANTS.BORDER_WIDTH_DEFAULT,
      ],
      'clamp'
    ),
    transform: [{ scale: pressScale.value }],
  }));

  // Enhanced dot animation style with sophisticated multi-property animations
  const dotAnimatedStyle = useAnimatedStyle(() => ({
    opacity: dotOpacity.value,
    transform: [{ scale: dotScale2.value }, { rotate: `${dotRotation.value}deg` }],
  }));

  // Secondary animation style for ripple effect
  const secondaryAnimatedStyle = useAnimatedStyle(() => ({
    opacity: interpolate(secondaryScale.value, [0, 1, 1.5], [0, 0.3, 0], 'clamp'),
    transform: [{ scale: secondaryScale.value }],
  }));

  return (
    <RadioButtonAnimated
      style={[
        {
          width: size,
          height: size,
          borderRadius: getBorderRadius(),
          alignItems: 'center',
          justifyContent: 'center',
          opacity: disabled ? 0.5 : 1,
        },
        style,
        containerAnimatedStyle,
      ]}
      onPress={handlePress}
      disabled={disabled}
      hitSlop={RADIO_BUTTON_CONSTANTS.HIT_SLOP}
      accessibilityRole="radio"
      accessibilityState={{
        selected: isSelected,
        disabled: disabled,
      }}
      accessibilityLabel={
        accessibilityProps.accessibilityLabel || (isSelected ? 'Selected' : 'Not selected')
      }
      accessibilityHint={accessibilityProps.accessibilityHint || 'Double tap to select this option'}
      {...accessibilityProps}>
      {/* Secondary ripple effect (only visible for ripple animation style) */}
      {animationStyle === 'ripple' && (
        <Animated.View
          style={[
            {
              position: 'absolute',
              width: dotSize * 2,
              height: dotSize * 2,
              borderRadius: dotSize,
              backgroundColor: resolvedColors.dotColor,
            },
            secondaryAnimatedStyle,
          ]}
        />
      )}

      {/* Main inner dot with enhanced animations */}
      <Animated.View
        style={[
          {
            width: dotSize,
            height: dotSize,
            borderRadius: dotBorderRadius,
            backgroundColor: resolvedColors.dotColor,
          },
          dotAnimatedStyle,
        ]}
      />
    </RadioButtonAnimated>
  );
};

RadioButton.displayName = 'RadioButton';

export default RadioButton;
