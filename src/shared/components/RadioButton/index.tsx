/**
 * RadioButton Component Export
 *
 * A flexible radio button component with shape variants and smooth animations.
 * Follows the architectural patterns established in the Movuca mobile app:
 * - Theme system integration
 * - React Native Reanimated animations
 * - Comprehensive accessibility support
 * - TypeScript interfaces with proper type safety
 */

export { default } from './RadioButton';
export { default as RadioButton } from './RadioButton';
export type { RadioButtonProps } from './RadioButton';
export { RADIO_BUTTON_CONSTANTS, RADIO_BUTTON_SPRING_CONFIG, RadioButtonShape } from './constants';
