/**
 * RadioButton Component Constants
 *
 * Following the pattern from Switch component with design specifications
 * for different shape variants: rounded, circular, and squared.
 *
 * Enhanced with sophisticated animation timing configurations for the new
 * multi-style animation system.
 */

export const RADIO_BUTTON_CONSTANTS = {
  // Base dimensions - can be scaled via props
  DEFAULT_SIZE: 24,
  MIN_SIZE: 16,
  MAX_SIZE: 48,

  // Shape variants
  SHAPES: {
    CIRCULAR: 'circular',
    ROUNDED: 'rounded',
    SQUARED: 'squared',
  } as const,

  // Border radius values for each shape
  BORDER_RADIUS: {
    CIRCULAR: 9999, // Fully circular
    ROUNDED: 8, // Moderately rounded corners
    SQUARED: 2, // Minimal rounding for crisp edges
  },

  // Inner dot size as percentage of container
  DOT_SCALE: 0.3,

  // Animation timing - following your app's patterns
  ANIMATION_DURATION: 200, // Same as SegmentedControl
  SPRING_DAMPING: 20, // Same as Switch
  SPRING_STIFFNESS: 300, // Same as Switch

  // Pressed state scale (for tactile feedback)
  SCALE_PRESSED: 0.95,
  SCALE_DEFAULT: 1,

  // Border widths
  BORDER_WIDTH_DEFAULT: 2,
  BORDER_WIDTH_FOCUSED: 3,

  // Accessibility hit area expansion
  HIT_SLOP: {
    top: 8,
    bottom: 8,
    left: 8,
    right: 8,
  },
} as const;

// Animation spring configuration - matches Switch component
export const RADIO_BUTTON_SPRING_CONFIG = {
  damping: RADIO_BUTTON_CONSTANTS.SPRING_DAMPING,
  stiffness: RADIO_BUTTON_CONSTANTS.SPRING_STIFFNESS,
  mass: 1,
  overshootClamping: false,
  restDisplacementThreshold: 0.01,
  restSpeedThreshold: 0.01,
} as const;

// Shape type for TypeScript
export type RadioButtonShape =
  (typeof RADIO_BUTTON_CONSTANTS.SHAPES)[keyof typeof RADIO_BUTTON_CONSTANTS.SHAPES];

// Enhanced Animation Configuration System
// These constants provide precise control over the sophisticated animation timing

export const RADIO_BUTTON_ANIMATION_TIMINGS = {
  // Enter animation timings (when dot appears)
  // Each style has carefully tuned parameters for optimal emotional impact

  BOUNCE_ENTER: {
    SCALE_DURATION: 300, // Spring animation, duration is approximate
    OPACITY_DURATION: 150,
    DAMPING: 15, // Lower damping creates more bounce
    STIFFNESS: 350, // Higher stiffness for responsive feel
    MASS: 1, // Standard mass for natural physics
  },

  ELASTIC_ENTER: {
    SCALE_DURATION: 400, // Longer duration allows for dramatic overshoot
    OPACITY_DURATION: 100, // Quick opacity for immediate visibility
    ROTATION_DURATION: 100, // Brief rotation adds personality
    DAMPING: 8, // Very low damping for pronounced elastic effect
    STIFFNESS: 400, // High stiffness for energetic response
    MASS: 1,
    ROTATION_ANGLE: 15, // Degrees of rotation during entrance
  },

  FADE_ENTER: {
    SCALE_DURATION: 250, // Moderate duration for sophistication
    OPACITY_DURATION: 200, // Slightly slower opacity for elegance
    ROTATION_DURATION: 250, // Consistent timing across properties
    EASING: 'cubic', // Smooth cubic easing for professional feel
  },

  POP_ENTER: {
    OVERSHOOT_DURATION: 100, // Quick overshoot phase
    OVERSHOOT_SCALE: 1.2, // 120% overshoot for satisfying pop
    SETTLE_DURATION: 100, // Quick settle back to final size
    OPACITY_DURATION: 80, // Very fast opacity for immediate feedback
    EASING_OVERSHOOT: 'cubic',
    EASING_SETTLE: 'quad',
  },

  RIPPLE_ENTER: {
    SCALE_DURATION: 200, // Moderate main scale timing
    OPACITY_DURATION: 100, // Quick opacity in two phases
    OPACITY_PEAK_DURATION: 100, // Time to reach peak opacity
    SECONDARY_EXPAND_DURATION: 150, // Ripple expansion timing
    SECONDARY_FADE_DURATION: 100, // Ripple fade timing
    SECONDARY_MAX_SCALE: 1.5, // Maximum ripple expansion
    SECONDARY_PEAK_OPACITY: 0.3, // Peak ripple opacity
  },

  // Exit animation timings (when dot disappears)
  // Generally faster than entrance for perceived responsiveness

  BOUNCE_EXIT: {
    SCALE_DURATION: 200, // Faster exit for responsiveness
    OPACITY_DURATION: 120, // Quick opacity fade
    DAMPING: 20, // Higher damping for quicker settling
    STIFFNESS: 400, // Higher stiffness for snappy response
    MASS: 0.8, // Lower mass for quicker animation
  },

  ELASTIC_EXIT: {
    COMPRESSION_DURATION: 80, // Brief compression before exit
    COMPRESSION_SCALE: 1.1, // 110% compression for anticipation
    SCALE_DURATION: 250, // Main exit timing
    OPACITY_DURATION: 150, // Moderate opacity fade
    ROTATION_ANGLE: -10, // Slight counter-rotation
    DAMPING: 12, // Moderate damping for visible spring effect
    STIFFNESS: 350,
    MASS: 1,
  },

  FADE_EXIT: {
    SCALE_DURATION: 200, // Consistent with entrance but slightly faster
    SCALE_TARGET: 0.8, // Scale down to 80% before complete fade
    OPACITY_DURATION: 180, // Slightly faster opacity fade
    EASING: 'cubic', // Matching entrance easing
  },

  POP_EXIT: {
    SCALE_DURATION: 120, // Quick exit for immediate response
    OPACITY_DURATION: 100, // Fast opacity fade
    EASING: 'cubic', // Smooth but quick departure
  },

  RIPPLE_EXIT: {
    SCALE_DURATION: 180, // Quick contraction
    OPACITY_DURATION: 150, // Moderate fade
    SECONDARY_DURATION: 100, // Quick ripple disappearance
    EASING: 'cubic', // Smooth contraction
  },
} as const;

// Animation easing curve presets for consistent timing across the app
export const RADIO_BUTTON_EASING = {
  // Entrance curves - generally easing out for smooth deceleration
  // These create the feeling that elements are "settling" into place
  ENTER_SMOOTH: 'cubic', // For sophisticated, professional interfaces
  ENTER_SNAPPY: 'quad', // For responsive, immediate feedback
  ENTER_GENTLE: 'sine', // For subtle, accessible animations

  // Exit curves - generally easing in for quick departure
  // These create the feeling that elements are "leaving" purposefully
  EXIT_SMOOTH: 'cubic', // Maintains elegance during departure
  EXIT_QUICK: 'quad', // For responsive state changes
  EXIT_IMMEDIATE: 'linear', // For accessibility or performance needs

  // Special curves for specific effects
  OVERSHOOT: 'back', // Creates anticipation and satisfaction (when supported)
  BOUNCE: 'bounce', // For playful, energetic interfaces (when supported)
} as const;

// Performance optimization constants
// These help balance animation delight with smooth performance
export const RADIO_BUTTON_PERFORMANCE = {
  // Maximum number of concurrent complex animations recommended
  MAX_CONCURRENT_ELASTIC: 5, // Elastic animations are most CPU intensive
  MAX_CONCURRENT_RIPPLE: 8, // Ripple effects with secondary elements
  MAX_CONCURRENT_STANDARD: 20, // Bounce, fade, pop animations

  // Recommended animation styles for different use cases
  HEAVY_LISTS: 'pop', // Quick, lightweight for scrolling performance
  FORMS: 'fade', // Professional, non-distracting
  GAMING: 'bounce', // Engaging, playful
  CREATIVE_TOOLS: 'elastic', // Expressive, memorable
  MATERIAL_DESIGN: 'ripple', // Platform-appropriate
} as const;
