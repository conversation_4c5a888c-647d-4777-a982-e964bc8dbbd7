import React from 'react';

import { fireEvent, render } from '@testing-library/react-native';

import { RadioButton } from './RadioButton';
import { RADIO_BUTTON_CONSTANTS } from './constants';

/**
 * RadioButton Component Tests
 *
 * Following the comprehensive testing patterns from your Switch component
 * to ensure reliability and maintain code quality standards.
 */

describe('RadioButton', () => {
  const defaultProps = {
    isSelected: false,
    onPress: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Functionality', () => {
    it('renders correctly in unselected state', () => {
      const { getByRole } = render(<RadioButton {...defaultProps} />);

      const radioButton = getByRole('radio');
      expect(radioButton).toBeTruthy();
      expect(radioButton.props.accessibilityState.selected).toBe(false);
    });

    it('renders correctly in selected state', () => {
      const { getByRole } = render(<RadioButton {...defaultProps} isSelected={true} />);

      const radioButton = getByRole('radio');
      expect(radioButton.props.accessibilityState.selected).toBe(true);
    });

    it('calls onPress when pressed', () => {
      const mockOnPress = jest.fn();
      const { getByRole } = render(<RadioButton {...defaultProps} onPress={mockOnPress} />);

      fireEvent.press(getByRole('radio'));
      expect(mockOnPress).toHaveBeenCalledTimes(1);
    });

    it('does not call onPress when disabled', () => {
      const mockOnPress = jest.fn();
      const { getByRole } = render(
        <RadioButton {...defaultProps} onPress={mockOnPress} disabled={true} />
      );

      fireEvent.press(getByRole('radio'));
      expect(mockOnPress).not.toHaveBeenCalled();
    });
  });

  describe('Shape Variants', () => {
    it('applies circular shape correctly', () => {
      const { getByRole } = render(<RadioButton {...defaultProps} shape="circular" size={24} />);

      const radioButton = getByRole('radio');
      expect(radioButton.props.style).toEqual(
        expect.objectContaining({
          borderRadius: RADIO_BUTTON_CONSTANTS.BORDER_RADIUS.CIRCULAR,
        })
      );
    });

    it('applies rounded shape correctly', () => {
      const { getByRole } = render(<RadioButton {...defaultProps} shape="rounded" size={24} />);

      const radioButton = getByRole('radio');
      expect(radioButton.props.style).toEqual(
        expect.objectContaining({
          borderRadius: RADIO_BUTTON_CONSTANTS.BORDER_RADIUS.ROUNDED,
        })
      );
    });

    it('applies squared shape correctly', () => {
      const { getByRole } = render(<RadioButton {...defaultProps} shape="squared" size={24} />);

      const radioButton = getByRole('radio');
      expect(radioButton.props.style).toEqual(
        expect.objectContaining({
          borderRadius: RADIO_BUTTON_CONSTANTS.BORDER_RADIUS.SQUARED,
        })
      );
    });
  });

  describe('Size Customization', () => {
    it('applies custom size correctly', () => {
      const customSize = 32;
      const { getByRole } = render(<RadioButton {...defaultProps} size={customSize} />);

      const radioButton = getByRole('radio');
      expect(radioButton.props.style).toEqual(
        expect.objectContaining({
          width: customSize,
          height: customSize,
        })
      );
    });

    it('uses default size when not specified', () => {
      const { getByRole } = render(<RadioButton {...defaultProps} />);

      const radioButton = getByRole('radio');
      expect(radioButton.props.style).toEqual(
        expect.objectContaining({
          width: RADIO_BUTTON_CONSTANTS.DEFAULT_SIZE,
          height: RADIO_BUTTON_CONSTANTS.DEFAULT_SIZE,
        })
      );
    });
  });

  describe('Accessibility', () => {
    it('has correct accessibility role', () => {
      const { getByRole } = render(<RadioButton {...defaultProps} />);

      const radioButton = getByRole('radio');
      expect(radioButton.props.accessibilityRole).toBe('radio');
    });

    it('provides default accessibility labels', () => {
      const { getByRole } = render(<RadioButton {...defaultProps} />);

      const radioButton = getByRole('radio');
      expect(radioButton.props.accessibilityLabel).toBe('Not selected');
    });

    it('updates accessibility labels based on selection state', () => {
      const { getByRole } = render(<RadioButton {...defaultProps} isSelected={true} />);

      const radioButton = getByRole('radio');
      expect(radioButton.props.accessibilityLabel).toBe('Selected');
    });

    it('accepts custom accessibility labels', () => {
      const customLabel = 'Custom radio button label';
      const { getByRole } = render(
        <RadioButton {...defaultProps} accessibilityLabel={customLabel} />
      );

      const radioButton = getByRole('radio');
      expect(radioButton.props.accessibilityLabel).toBe(customLabel);
    });

    it('includes disability state in accessibility', () => {
      const { getByRole } = render(<RadioButton {...defaultProps} disabled={true} />);

      const radioButton = getByRole('radio');
      expect(radioButton.props.accessibilityState.disabled).toBe(true);
    });
  });

  describe('Custom Colors', () => {
    it('accepts custom color configurations', () => {
      const customColors = {
        checkedBorder: '#FF0000',
        checkedBackground: '#FF0000',
        dotColor: '#FFFFFF',
      };

      const { getByRole } = render(<RadioButton {...defaultProps} colors={customColors} />);

      // Component should render without errors with custom colors
      expect(getByRole('radio')).toBeTruthy();
    });
  });

  describe('Dot Scale', () => {
    it('accepts custom dot scale factor', () => {
      const customDotScale = 0.8;
      const { getByRole } = render(<RadioButton {...defaultProps} dotScale={customDotScale} />);

      // Component should render without errors with custom dot scale
      expect(getByRole('radio')).toBeTruthy();
    });
  });

  describe('Display Name', () => {
    it('has correct display name for debugging', () => {
      expect(RadioButton.displayName).toBe('RadioButton');
    });
  });
});
