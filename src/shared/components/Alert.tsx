import React from 'react';

import { VariantProps, createRestyleComponent, createVariant } from '@shopify/restyle';
import { CheckCircle, Info, Warning, WarningCircle } from 'phosphor-react-native';

import { Box, Text, Theme, useTheme } from '@/src/core/theme';

type AlertType = 'success' | 'info' | 'warning' | 'error';

const BaseAlert = createRestyleComponent<
  VariantProps<Theme, 'alertVariants'> & React.ComponentProps<typeof Box>,
  Theme
>([createVariant({ themeKey: 'alertVariants' })], Box);

type AlertProps = {
  type?: AlertType;
  text: string;
  customIcon?: React.ReactNode;
} & Omit<React.ComponentProps<typeof BaseAlert>, 'variant'>;

const typeColorMap: Record<AlertType, keyof Theme['colors']> = {
  success: 'alertSuccessText',
  info: 'alertInfoText',
  warning: 'alertWarningText',
  error: 'alertErrorText',
};

const defaultIcons = {
  success: CheckCircle,
  info: Info,
  warning: Warning,
  error: WarningCircle,
};

const Alert = ({ type = 'info', text, customIcon, ...rest }: AlertProps) => {
  const theme = useTheme();
  const IconComponent = customIcon || defaultIcons[type];
  const textColor = theme.colors[typeColorMap[type]];

  return (
    <BaseAlert variant={type} {...rest}>
      {typeof IconComponent === 'function' ? (
        <IconComponent size={18} color={textColor} weight="fill" />
      ) : (
        <Box>{IconComponent}</Box>
      )}

      <Text variant="h_18SemiBold_cardTitle" color={typeColorMap[type]} flex={1}>
        {text}
      </Text>
    </BaseAlert>
  );
};

export { Alert };
