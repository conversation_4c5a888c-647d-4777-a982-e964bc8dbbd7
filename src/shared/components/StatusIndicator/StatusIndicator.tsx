import React from 'react';

import { CheckCircle, Warning, XCircle } from 'phosphor-react-native';

import { Box, Row, Text, useTheme } from '@/src/core/theme';

export type StatusType = 'success' | 'warning' | 'error' | 'info' | 'disabled';

export type StatusIndicatorProps = {
  /**
   * Label text to display
   */
  label: string;

  /**
   * The status type that determines colors and icons
   */
  status: StatusType;

  /**
   * Optional description text to display below the label
   */
  description?: string;

  /**
   * Optional custom icon to override the default status icon
   */
  customIcon?: React.ReactElement<{ color?: string; weight?: string; size?: number }>;

  /**
   * Icon size
   */
  iconSize?: number;
};

/**
 * A component for displaying status information with appropriate
 * icons and color-coding.
 */
export const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  label,
  status,
  description,
  customIcon,
  iconSize = 20,
}) => {
  const { colors } = useTheme();

  const getStatusIcon = () => {
    if (customIcon) {
      return React.cloneElement(customIcon, {
        size: iconSize,
        color: getIconColor(),
        weight: customIcon.props.weight || 'fill',
      });
    }

    switch (status) {
      case 'success':
        return <CheckCircle size={iconSize} color={colors.success} weight="fill" />;
      case 'warning':
        return <Warning size={iconSize} color={colors.warning} weight="fill" />;
      case 'error':
        return <XCircle size={iconSize} color={colors.error} weight="fill" />;
      case 'disabled':
        return <XCircle size={iconSize} color={colors.textTertiary} weight="regular" />;
      default:
        return <CheckCircle size={iconSize} color={colors.primary} weight="regular" />;
    }
  };

  const getIconColor = () => {
    switch (status) {
      case 'success':
        return colors.success;
      case 'warning':
        return colors.warning;
      case 'error':
        return colors.error;
      case 'disabled':
        return colors.textTertiary;
      default:
        return colors.primary;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'success':
        return 'success';
      case 'warning':
        return 'warning';
      case 'error':
        return 'error';
      case 'disabled':
        return 'textTertiary';
      default:
        return 'primary';
    }
  };

  return (
    <Row gap="sm_12" alignItems={description ? 'flex-start' : 'center'}>
      {getStatusIcon()}
      <Box flex={1}>
        <Text variant="b_14SemiBold_listTitle" color="text">
          {label}
        </Text>
        {description && (
          <Text variant="l_12Regular_helperText" color={getStatusColor()} marginTop="xxxs_2">
            {description}
          </Text>
        )}
      </Box>
    </Row>
  );
};
