/**
 * Notification types and interfaces for the application
 */

export type NotificationType =
  | 'system'
  | 'social'
  | 'marketing'
  | 'security'
  | 'reminder'
  | 'achievement'
  | 'chat'
  | 'post'
  | 'follow'
  | 'like'
  | 'comment'
  | 'mention'
  | 'trending';

export type NotificationPriority = 'low' | 'normal' | 'high' | 'critical';

export type NotificationStatus = 'unread' | 'read' | 'archived' | 'deleted';

export interface AppNotification {
  id: string;
  type: NotificationType;
  priority: NotificationPriority;
  status: NotificationStatus;
  title: string;
  message: string;
  createdAt: Date;
  readAt?: Date;
  expiresAt?: Date;
  data?: Record<string, any>;
  userId?: string;
  avatar?: string;
  image?: string;
  actions?: {
    id: string;
    label: string;
    action: string;
    destructive?: boolean;
  }[];
  deepLink?: string;
  category?: string;
  groupId?: string;
  threadId?: string;
}

export interface NotificationSettings {
  enabled: boolean;
  sound: boolean;
  vibration: boolean;
  badge: boolean;
  showPreview: boolean;
  quietHours: {
    enabled: boolean;
    startTime: string; // HH:mm format
    endTime: string; // HH:mm format
  };
  categories: Record<
    NotificationType,
    {
      enabled: boolean;
      sound: boolean;
      vibration: boolean;
      priority: NotificationPriority;
    }
  >;
}

export interface NotificationPermissionState {
  granted: boolean;
  provisional?: boolean;
  denied: boolean;
  notDetermined: boolean;
  settings: {
    alert: boolean;
    badge: boolean;
    sound: boolean;
    carPlay: boolean;
    criticalAlert: boolean;
    lockScreen: boolean;
    notificationCenter: boolean;
  };
}

export interface NotificationStatistics {
  total: number;
  unread: number;
  byType: Record<NotificationType, number>;
  byPriority: Record<NotificationPriority, number>;
  todayCount: number;
  weekCount: number;
  monthCount: number;
}

export interface NotificationFilter {
  types?: NotificationType[];
  priorities?: NotificationPriority[];
  status?: NotificationStatus[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  userId?: string;
  groupId?: string;
  search?: string;
}

export interface NotificationBatch {
  notifications: AppNotification[];
  hasMore: boolean;
  nextCursor?: string;
  total: number;
}

export interface ScheduledNotification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  scheduledFor: Date;
  repeatFrequency?: 'daily' | 'weekly' | 'monthly' | 'yearly';
  data?: Record<string, any>;
  active: boolean;
  lastTriggered?: Date;
  nextTrigger?: Date;
}

export interface NotificationTemplate {
  id: string;
  name: string;
  type: NotificationType;
  title: string;
  message: string;
  variables: string[];
  priority: NotificationPriority;
  actions?: {
    id: string;
    label: string;
    action: string;
  }[];
}

export interface NotificationChannel {
  id: string;
  name: string;
  description: string;
  importance: 'min' | 'low' | 'default' | 'high' | 'max';
  sound: boolean;
  vibration: boolean;
  lights: boolean;
  badge: boolean;
  showBadge: boolean;
  bypassDnd: boolean;
  lockscreenVisibility: 'public' | 'private' | 'secret';
}

export interface NotificationGroup {
  id: string;
  title: string;
  summary: string;
  notifications: AppNotification[];
  createdAt: Date;
  updatedAt: Date;
  collapsed: boolean;
}

export interface PushNotificationPayload {
  aps?: {
    alert?: {
      title?: string;
      body?: string;
      subtitle?: string;
    };
    badge?: number;
    sound?: string;
    'content-available'?: number;
    'mutable-content'?: number;
    category?: string;
    'thread-id'?: string;
    'target-content-id'?: string;
  };
  data?: Record<string, any>;
  notification?: {
    title?: string;
    body?: string;
    icon?: string;
    color?: string;
    sound?: string;
    tag?: string;
    click_action?: string;
  };
  android?: {
    channelId?: string;
    smallIcon?: string;
    largeIcon?: string;
    color?: string;
    sound?: string;
    vibrationPattern?: number[];
    lights?: {
      color: string;
      onMs: number;
      offMs: number;
    };
    priority?: 'min' | 'low' | 'default' | 'high' | 'max';
    visibility?: 'private' | 'public' | 'secret';
    autoCancel?: boolean;
    ongoing?: boolean;
    progress?: {
      max: number;
      current: number;
      indeterminate?: boolean;
    };
    actions?: {
      title: string;
      icon?: string;
      pressAction: {
        id: string;
        launchActivity?: string;
      };
    }[];
  };
  fcm_options?: {
    analytics_label?: string;
  };
}

export interface NotificationAnalytics {
  sent: number;
  delivered: number;
  opened: number;
  clicked: number;
  dismissed: number;
  byType: Record<
    NotificationType,
    {
      sent: number;
      delivered: number;
      opened: number;
      clicked: number;
    }
  >;
  engagement: {
    openRate: number;
    clickRate: number;
    dismissRate: number;
  };
  timeStats: {
    averageTimeToOpen: number;
    averageTimeToClick: number;
    peakHours: number[];
  };
}
