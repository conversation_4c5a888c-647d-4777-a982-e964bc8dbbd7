/**
 * Notification Store - Zustand store for notification management
 * Handles notification state, settings, history, and permissions
 */
import { devtools } from '@csark0812/zustand-expo-devtools';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

import { createZustandMMKVStorage, userStorage } from '@/src/core/storage';
import { notificationService } from '@/src/shared/services/notificationService';
import type {
  AppNotification,
  NotificationFilter,
  NotificationPermissionState,
  NotificationPriority,
  NotificationSettings,
  NotificationStatistics,
  NotificationType,
  ScheduledNotification,
} from '@/src/shared/types/notification';
import { NotificationPermissions } from '@/src/shared/utils/notificationPermissions';

/**
 * Notification store state interface
 */
interface NotificationState {
  // Notification data
  notifications: AppNotification[];
  scheduledNotifications: ScheduledNotification[];

  // Permission state
  permissionState: NotificationPermissionState | null;

  // Settings
  settings: NotificationSettings | null;

  // UI state
  isLoading: boolean;
  isInitializing: boolean;
  isRequestingPermission: boolean;
  error: string | null;

  // Badge and counts
  badgeCount: number;
  unreadCount: number;

  // Filters and pagination
  currentFilter: NotificationFilter | null;
  hasMoreNotifications: boolean;
  isLoadingMore: boolean;

  // Cache management
  lastFetched: number | null;
  lastPermissionCheck: number | null;

  // Statistics
  statistics: NotificationStatistics | null;
}

/**
 * Notification store actions interface
 */
interface NotificationActions {
  // Initialization
  initialize: () => Promise<void>;
  cleanup: () => void;

  // Permission management
  checkPermissions: () => Promise<NotificationPermissionState>;
  requestPermissions: () => Promise<boolean>;
  refreshPermissions: () => Promise<void>;

  // Notification management
  loadNotifications: (filter?: NotificationFilter) => Promise<void>;
  loadMoreNotifications: () => Promise<void>;
  refreshNotifications: () => Promise<void>;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (notificationId: string) => Promise<void>;
  clearAllNotifications: () => Promise<void>;

  // Scheduled notifications
  loadScheduledNotifications: () => Promise<void>;
  cancelScheduledNotification: (notificationId: string) => Promise<void>;

  // Settings management
  loadSettings: () => Promise<void>;
  updateSettings: (settings: Partial<NotificationSettings>) => Promise<void>;
  resetSettings: () => Promise<void>;

  // Badge management
  updateBadgeCount: () => Promise<void>;
  setBadgeCount: (count: number) => Promise<void>;
  clearBadge: () => Promise<void>;

  // Statistics
  loadStatistics: () => Promise<void>;

  // Display notifications
  displayNotification: (
    type: NotificationType,
    title: string,
    message: string,
    options?: {
      priority?: NotificationPriority;
      data?: Record<string, any>;
      actions?: { id: string; label: string }[];
      image?: string;
      avatar?: string;
      deepLink?: string;
    }
  ) => Promise<string>;

  // Schedule notifications
  scheduleNotification: (
    type: NotificationType,
    title: string,
    message: string,
    scheduledFor: Date,
    options?: {
      repeatFrequency?: 'daily' | 'weekly' | 'monthly' | 'yearly';
      data?: Record<string, any>;
      priority?: NotificationPriority;
    }
  ) => Promise<string>;

  // Filter and search
  setFilter: (filter: NotificationFilter | null) => void;
  searchNotifications: (query: string) => void;

  // UI state management
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

type NotificationStore = NotificationState & NotificationActions;

const CACHE_DURATION = 2 * 60 * 1000; // 2 minutes
const PERMISSION_CHECK_INTERVAL = 30 * 60 * 1000; // 30 minutes

/**
 * Create the notification store
 */
export const useNotificationStore = create<NotificationStore>()(
  devtools(
    persist(
      immer((set, get) => ({
        // Initial state
        notifications: [],
        scheduledNotifications: [],
        permissionState: null,
        settings: null,
        isLoading: false,
        isInitializing: false,
        isRequestingPermission: false,
        error: null,
        badgeCount: 0,
        unreadCount: 0,
        currentFilter: null,
        hasMoreNotifications: false,
        isLoadingMore: false,
        lastFetched: null,
        lastPermissionCheck: null,
        statistics: null,

        // Initialization
        initialize: async () => {
          const state = get();
          if (state.isInitializing) return;

          set(draft => {
            draft.isInitializing = true;
            draft.error = null;
          });

          try {
            // Initialize the notification service
            await notificationService.initialize();

            // Load initial data in parallel
            await Promise.all([
              get().checkPermissions(),
              get().loadSettings(),
              get().loadNotifications(),
              get().loadScheduledNotifications(),
              get().updateBadgeCount(),
            ]);

            set(draft => {
              draft.isInitializing = false;
            });
          } catch (error) {
            set(draft => {
              draft.isInitializing = false;
              draft.error =
                error instanceof Error ? error.message : 'Failed to initialize notifications';
            });
          }
        },

        cleanup: () => {
          notificationService.cleanup();
          set(draft => {
            draft.isInitializing = false;
            draft.isLoading = false;
            draft.isRequestingPermission = false;
          });
        },

        // Permission management
        checkPermissions: async () => {
          const state = get();

          // Use cached permission check if recent
          if (
            state.permissionState &&
            state.lastPermissionCheck &&
            Date.now() - state.lastPermissionCheck < PERMISSION_CHECK_INTERVAL
          ) {
            return state.permissionState;
          }

          try {
            const permissionState = await NotificationPermissions.getPermissionStatus();

            set(draft => {
              draft.permissionState = permissionState;
              draft.lastPermissionCheck = Date.now();
            });

            return permissionState;
          } catch (error) {
            console.error('Failed to check permissions:', error);
            set(draft => {
              draft.error = 'Failed to check notification permissions';
            });
            throw error;
          }
        },

        requestPermissions: async () => {
          set(draft => {
            draft.isRequestingPermission = true;
            draft.error = null;
          });

          try {
            const permissionState = await NotificationPermissions.requestPermissions({
              showRationale: true,
            });

            set(draft => {
              draft.permissionState = permissionState;
              draft.isRequestingPermission = false;
              draft.lastPermissionCheck = Date.now();
            });

            return permissionState.granted;
          } catch (error) {
            set(draft => {
              draft.isRequestingPermission = false;
              draft.error =
                error instanceof Error ? error.message : 'Failed to request permissions';
            });
            return false;
          }
        },

        refreshPermissions: async () => {
          set(draft => {
            draft.lastPermissionCheck = null;
          });
          await get().checkPermissions();
        },

        // Notification management
        loadNotifications: async (filter?: NotificationFilter) => {
          set(draft => {
            draft.isLoading = true;
            draft.error = null;
            if (filter) {
              draft.currentFilter = filter;
            }
          });

          try {
            const notifications = notificationService.getNotificationHistory(50);
            const filteredNotifications = filter
              ? notifications.filter(n => get().matchesFilter(n, filter))
              : notifications;

            set(draft => {
              draft.notifications = filteredNotifications;
              draft.unreadCount = filteredNotifications.filter(n => n.status === 'unread').length;
              draft.isLoading = false;
              draft.lastFetched = Date.now();
              draft.hasMoreNotifications = notifications.length >= 50;
            });
          } catch (error) {
            set(draft => {
              draft.isLoading = false;
              draft.error = error instanceof Error ? error.message : 'Failed to load notifications';
            });
          }
        },

        loadMoreNotifications: async () => {
          const state = get();
          if (state.isLoadingMore || !state.hasMoreNotifications) return;

          set(draft => {
            draft.isLoadingMore = true;
          });

          try {
            // TODO: Implement pagination when backend supports it
            const allNotifications = notificationService.getNotificationHistory(100);
            const currentLength = state.notifications.length;
            const newNotifications = allNotifications.slice(currentLength, currentLength + 25);

            set(draft => {
              draft.notifications.push(...newNotifications);
              draft.isLoadingMore = false;
              draft.hasMoreNotifications = newNotifications.length >= 25;
            });
          } catch (error) {
            set(draft => {
              draft.isLoadingMore = false;
              draft.error =
                error instanceof Error ? error.message : 'Failed to load more notifications';
            });
          }
        },

        refreshNotifications: async () => {
          set(draft => {
            draft.lastFetched = null;
          });
          await get().loadNotifications(get().currentFilter || undefined);
        },

        markAsRead: async (notificationId: string) => {
          try {
            notificationService.markAsRead(notificationId);

            set(draft => {
              const notification = draft.notifications.find(n => n.id === notificationId);
              if (notification && notification.status === 'unread') {
                notification.status = 'read';
                notification.readAt = new Date();
                draft.unreadCount = Math.max(0, draft.unreadCount - 1);
              }
            });

            await get().updateBadgeCount();
          } catch (error) {
            set(draft => {
              draft.error =
                error instanceof Error ? error.message : 'Failed to mark notification as read';
            });
          }
        },

        markAllAsRead: async () => {
          try {
            const unreadNotifications = get().notifications.filter(n => n.status === 'unread');

            unreadNotifications.forEach(notification => {
              notificationService.markAsRead(notification.id);
            });

            set(draft => {
              draft.notifications.forEach(notification => {
                if (notification.status === 'unread') {
                  notification.status = 'read';
                  notification.readAt = new Date();
                }
              });
              draft.unreadCount = 0;
            });

            await get().updateBadgeCount();
          } catch (error) {
            set(draft => {
              draft.error =
                error instanceof Error ? error.message : 'Failed to mark all notifications as read';
            });
          }
        },

        deleteNotification: async (notificationId: string) => {
          try {
            set(draft => {
              const index = draft.notifications.findIndex(n => n.id === notificationId);
              if (index >= 0) {
                const notification = draft.notifications[index];
                if (notification.status === 'unread') {
                  draft.unreadCount = Math.max(0, draft.unreadCount - 1);
                }
                draft.notifications.splice(index, 1);
              }
            });

            await get().updateBadgeCount();
          } catch (error) {
            set(draft => {
              draft.error =
                error instanceof Error ? error.message : 'Failed to delete notification';
            });
          }
        },

        clearAllNotifications: async () => {
          try {
            await notificationService.clearAllNotifications();

            set(draft => {
              draft.notifications = [];
              draft.unreadCount = 0;
              draft.badgeCount = 0;
            });

            await get().updateBadgeCount();
          } catch (error) {
            set(draft => {
              draft.error =
                error instanceof Error ? error.message : 'Failed to clear all notifications';
            });
          }
        },

        // Scheduled notifications
        loadScheduledNotifications: async () => {
          try {
            const scheduled =
              userStorage.getObject<ScheduledNotification[]>('scheduled_notifications') || [];

            set(draft => {
              draft.scheduledNotifications = scheduled.filter(n => n.active);
            });
          } catch (error) {
            set(draft => {
              draft.error =
                error instanceof Error ? error.message : 'Failed to load scheduled notifications';
            });
          }
        },

        cancelScheduledNotification: async (notificationId: string) => {
          try {
            await notificationService.cancelScheduledNotification(notificationId);

            set(draft => {
              const index = draft.scheduledNotifications.findIndex(n => n.id === notificationId);
              if (index >= 0) {
                draft.scheduledNotifications.splice(index, 1);
              }
            });
          } catch (error) {
            set(draft => {
              draft.error =
                error instanceof Error ? error.message : 'Failed to cancel scheduled notification';
            });
          }
        },

        // Settings management
        loadSettings: async () => {
          try {
            const settings = notificationService.getNotificationSettings();

            set(draft => {
              draft.settings = settings;
            });
          } catch (error) {
            set(draft => {
              draft.error =
                error instanceof Error ? error.message : 'Failed to load notification settings';
            });
          }
        },

        updateSettings: async (newSettings: Partial<NotificationSettings>) => {
          try {
            notificationService.updateNotificationSettings(newSettings);

            set(draft => {
              if (draft.settings) {
                Object.assign(draft.settings, newSettings);
              }
            });
          } catch (error) {
            set(draft => {
              draft.error =
                error instanceof Error ? error.message : 'Failed to update notification settings';
            });
          }
        },

        resetSettings: async () => {
          try {
            userStorage.delete('notification_settings');
            await get().loadSettings();
          } catch (error) {
            set(draft => {
              draft.error =
                error instanceof Error ? error.message : 'Failed to reset notification settings';
            });
          }
        },

        // Badge management
        updateBadgeCount: async () => {
          try {
            const count = get().unreadCount;
            await notificationService.setBadgeCount(count);

            set(draft => {
              draft.badgeCount = count;
            });
          } catch (error) {
            console.error('Failed to update badge count:', error);
          }
        },

        setBadgeCount: async (count: number) => {
          try {
            await notificationService.setBadgeCount(count);

            set(draft => {
              draft.badgeCount = count;
            });
          } catch (error) {
            set(draft => {
              draft.error = error instanceof Error ? error.message : 'Failed to set badge count';
            });
          }
        },

        clearBadge: async () => {
          await get().setBadgeCount(0);
        },

        // Statistics
        loadStatistics: async () => {
          try {
            const notifications = get().notifications;
            const now = new Date();
            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

            const statistics: NotificationStatistics = {
              total: notifications.length,
              unread: notifications.filter(n => n.status === 'unread').length,
              byType: {} as Record<NotificationType, number>,
              byPriority: {} as Record<NotificationPriority, number>,
              todayCount: notifications.filter(n => new Date(n.createdAt) >= today).length,
              weekCount: notifications.filter(n => new Date(n.createdAt) >= weekAgo).length,
              monthCount: notifications.filter(n => new Date(n.createdAt) >= monthAgo).length,
            };

            // Calculate type distribution
            notifications.forEach(notification => {
              statistics.byType[notification.type] =
                (statistics.byType[notification.type] || 0) + 1;
              statistics.byPriority[notification.priority] =
                (statistics.byPriority[notification.priority] || 0) + 1;
            });

            set(draft => {
              draft.statistics = statistics;
            });
          } catch (error) {
            set(draft => {
              draft.error = error instanceof Error ? error.message : 'Failed to load statistics';
            });
          }
        },

        // Display notifications
        displayNotification: async (type, title, message, options = {}) => {
          try {
            const notificationId = await notificationService.displayNotification(
              type,
              title,
              message,
              options
            );

            // Refresh notifications to include the new one
            await get().loadNotifications();

            return notificationId;
          } catch (error) {
            set(draft => {
              draft.error =
                error instanceof Error ? error.message : 'Failed to display notification';
            });
            throw error;
          }
        },

        // Schedule notifications
        scheduleNotification: async (type, title, message, scheduledFor, options = {}) => {
          try {
            const notificationId = await notificationService.scheduleNotification(
              type,
              title,
              message,
              scheduledFor,
              options
            );

            // Refresh scheduled notifications
            await get().loadScheduledNotifications();

            return notificationId;
          } catch (error) {
            set(draft => {
              draft.error =
                error instanceof Error ? error.message : 'Failed to schedule notification';
            });
            throw error;
          }
        },

        // Filter and search
        setFilter: (filter: NotificationFilter | null) => {
          set(draft => {
            draft.currentFilter = filter;
          });
          get().loadNotifications(filter || undefined);
        },

        searchNotifications: (query: string) => {
          const filter: NotificationFilter = {
            search: query,
          };
          get().setFilter(filter);
        },

        // UI state management
        clearError: () => {
          set(draft => {
            draft.error = null;
          });
        },

        setLoading: (loading: boolean) => {
          set(draft => {
            draft.isLoading = loading;
          });
        },

        // Helper method for filtering (not part of the store interface)
        matchesFilter: (notification: AppNotification, filter: NotificationFilter): boolean => {
          if (filter.types && !filter.types.includes(notification.type)) return false;
          if (filter.priorities && !filter.priorities.includes(notification.priority)) return false;
          if (filter.status && !filter.status.includes(notification.status)) return false;
          if (filter.userId && notification.userId !== filter.userId) return false;
          if (filter.groupId && notification.groupId !== filter.groupId) return false;

          if (filter.dateRange) {
            const notificationDate = new Date(notification.createdAt);
            if (
              notificationDate < filter.dateRange.start ||
              notificationDate > filter.dateRange.end
            ) {
              return false;
            }
          }

          if (filter.search) {
            const searchLower = filter.search.toLowerCase();
            return (
              notification.title.toLowerCase().includes(searchLower) ||
              notification.message.toLowerCase().includes(searchLower)
            );
          }

          return true;
        },
      })),
      {
        name: 'notification-store',
        storage: createJSONStorage(() => createZustandMMKVStorage(userStorage)),
        partialize: state => ({
          notifications: state.notifications,
          scheduledNotifications: state.scheduledNotifications,
          settings: state.settings,
          permissionState: state.permissionState,
          badgeCount: state.badgeCount,
          unreadCount: state.unreadCount,
          lastFetched: state.lastFetched,
          lastPermissionCheck: state.lastPermissionCheck,
          statistics: state.statistics,
        }),
      }
    ),
    { name: 'NotificationStore' }
  )
);
