import { useActionState, useCallback, useState, useTransition } from 'react';

import { zodResolver } from '@hookform/resolvers/zod';
import { UseFormProps, UseFormReturn, useForm } from 'react-hook-form';
import { z } from 'zod';

/**
 * Enhanced useForm hook with Zod validation integration
 * Provides type-safe forms with validation
 */
export function useZodForm<T extends z.ZodType<any, any, any>>(
  schema: T,
  formProps?: Omit<UseFormProps<z.infer<T>>, 'resolver'>
): UseFormReturn<z.infer<T>> {
  return useForm<z.infer<T>>({
    resolver: zodResolver(schema),
    mode: 'onBlur',
    ...formProps,
  });
}

/**
 * Hook for handling async form submissions with loading states
 */

type AsyncSubmitOptions = {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
};

export function useAsyncFormSubmit<T>(
  onSubmit: (data: T) => Promise<void>,
  options?: AsyncSubmitOptions
) {
  const [isPending, startTransition] = useTransition();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = useCallback(
    async (data: T) => {
      startTransition(() => {
        setIsLoading(true);
        setError(null);
      });

      try {
        await onSubmit(data);
        options?.onSuccess?.();
      } catch (err) {
        const message = err instanceof Error ? err.message : 'Something went wrong';
        setError(message);
        options?.onError?.(err instanceof Error ? err : new Error(message));
      } finally {
        setIsLoading(false);
      }
    },
    [onSubmit, options]
  );

  return {
    isLoading: isLoading || isPending,
    error,
    handleSubmit,
    clearError: () => setError(null),
    reset: () => {
      setIsLoading(false);
      setError(null);
    },
  };
}

type FormState = {
  error: string | null;
  success: boolean;
};

export function useZodFormAction<T extends z.ZodType<any, any>>(
  schema: T,
  action: (data: z.infer<T>) => Promise<void>,
  formProps?: Omit<UseFormProps<z.infer<T>>, 'resolver'>
) {
  const [state, formAction, isPending] = useActionState<FormState, FormData>(
    async (prevState, formData) => {
      try {
        // Convert FormData to object
        const rawData = Object.fromEntries(formData.entries());
        // Validate with Zod
        const data = schema.parse(rawData);
        // Execute action
        await action(data);
        return { error: null, success: true };
      } catch (err) {
        return {
          error: err instanceof Error ? err.message : 'Validation failed',
          success: false,
        };
      }
    },
    { error: null, success: false } as FormState
  );

  const formMethods = useForm<z.infer<T>>({
    resolver: zodResolver(schema),
    ...formProps,
  });

  return {
    ...formMethods,
    state,
    formAction,
    isPending,
  };
}
