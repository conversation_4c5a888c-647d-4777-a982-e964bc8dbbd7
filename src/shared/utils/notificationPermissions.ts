/**
 * Notification permissions utility
 * Handles requesting, checking, and managing notification permissions
 */
import { Al<PERSON>, Linking, Platform } from 'react-native';

import { AuthorizationStatus } from '@notifee/react-native';

import { notification } from '@/src/core/libs/notification';
import { appStorage } from '@/src/core/storage';
import type { NotificationPermissionState } from '@/src/shared/types/notification';

export interface PermissionRequestOptions {
  showRationale?: boolean;
  rationaleTitle?: string;
  rationaleMessage?: string;
  settingsTitle?: string;
  settingsMessage?: string;
  onDenied?: () => void;
  onGranted?: () => void;
}

/**
 * Notification permissions manager
 */
export class NotificationPermissions {
  private static readonly PERMISSION_REQUESTED_KEY = 'notification_permission_requested';
  private static readonly PERMISSION_DENIED_KEY = 'notification_permission_denied';

  /**
   * Check current notification permission status
   */
  static async getPermissionStatus(): Promise<NotificationPermissionState> {
    try {
      const permissions = await notification.getPermissions();

      return {
        granted: permissions.authorizationStatus === AuthorizationStatus.AUTHORIZED,
        provisional: permissions.authorizationStatus === AuthorizationStatus.PROVISIONAL,
        denied: permissions.authorizationStatus === AuthorizationStatus.DENIED,
        notDetermined: permissions.authorizationStatus === AuthorizationStatus.NOT_DETERMINED,
        settings: permissions.settings,
      };
    } catch (error) {
      console.error('Failed to get permission status:', error);
      return {
        granted: false,
        denied: true,
        notDetermined: false,
        settings: {
          alert: false,
          badge: false,
          sound: false,
          carPlay: false,
          criticalAlert: false,
          lockScreen: false,
          notificationCenter: false,
        },
      };
    }
  }

  /**
   * Request notification permissions with optional rationale
   */
  static async requestPermissions(
    options: PermissionRequestOptions = {}
  ): Promise<NotificationPermissionState> {
    try {
      const currentStatus = await this.getPermissionStatus();

      // Already granted
      if (currentStatus.granted) {
        options.onGranted?.();
        return currentStatus;
      }

      // Previously denied and we should show rationale
      if (currentStatus.denied && options.showRationale) {
        const shouldProceed = await this.showPermissionRationale(
          options.rationaleTitle || 'Enable Notifications',
          options.rationaleMessage ||
            'We need permission to send you important updates and notifications. This helps you stay informed about your account and activities.'
        );

        if (!shouldProceed) {
          options.onDenied?.();
          return currentStatus;
        }
      }

      // Mark that we've requested permission
      this.markPermissionRequested();

      // Request the permission
      const permissions = await notification.requestPermission();
      const newStatus: NotificationPermissionState = {
        granted: permissions.authorizationStatus === AuthorizationStatus.AUTHORIZED,
        provisional: permissions.authorizationStatus === AuthorizationStatus.PROVISIONAL,
        denied: permissions.authorizationStatus === AuthorizationStatus.DENIED,
        notDetermined: permissions.authorizationStatus === AuthorizationStatus.NOT_DETERMINED,
        settings: permissions.settings,
      };

      // Handle the result
      if (newStatus.granted) {
        this.clearPermissionDenied();
        options.onGranted?.();
      } else if (newStatus.denied) {
        this.markPermissionDenied();
        options.onDenied?.();

        // Show settings prompt if appropriate
        if (this.hasRequestedBefore()) {
          this.showSettingsPrompt(
            options.settingsTitle || 'Notifications Disabled',
            options.settingsMessage ||
              'Notifications are currently disabled. You can enable them in Settings to receive important updates.'
          );
        }
      }

      return newStatus;
    } catch (error) {
      console.error('Failed to request permissions:', error);
      options.onDenied?.();
      throw error;
    }
  }

  /**
   * Check if notifications are fully enabled (granted + all settings enabled)
   */
  static async areNotificationsFullyEnabled(): Promise<boolean> {
    const status = await this.getPermissionStatus();
    return status.granted && status.settings.alert && status.settings.badge;
  }

  /**
   * Check if we can send critical notifications (iOS)
   */
  static async canSendCriticalNotifications(): Promise<boolean> {
    if (Platform.OS !== 'ios') return true;

    const status = await this.getPermissionStatus();
    return status.granted && status.settings.criticalAlert;
  }

  /**
   * Open notification settings
   */
  static async openNotificationSettings(): Promise<void> {
    try {
      await notification.openSettings();
    } catch (error) {
      console.error('Failed to open notification settings:', error);
      // Fallback to opening app settings
      if (Platform.OS === 'ios') {
        Linking.openURL('app-settings:');
      } else {
        Linking.openSettings();
      }
    }
  }

  /**
   * Check if we should show permission request based on previous denials
   */
  static shouldShowPermissionRequest(): boolean {
    const deniedCount = this.getPermissionDeniedCount();
    const lastDenied = this.getLastPermissionDeniedTime();

    // Don't show if denied more than 3 times
    if (deniedCount >= 3) return false;

    // Don't show if denied in the last 24 hours
    if (lastDenied && Date.now() - lastDenied < 24 * 60 * 60 * 1000) {
      return false;
    }

    return true;
  }

  /**
   * Reset permission tracking (useful for testing or app reset)
   */
  static resetPermissionTracking(): void {
    appStorage.delete(this.PERMISSION_REQUESTED_KEY);
    appStorage.delete(this.PERMISSION_DENIED_KEY);
    appStorage.delete('notification_permission_denied_count');
    appStorage.delete('notification_permission_last_denied');
  }

  /**
   * Get permission summary for debugging
   */
  static async getPermissionSummary(): Promise<{
    status: NotificationPermissionState;
    requestedBefore: boolean;
    deniedCount: number;
    lastDenied?: number;
    shouldShow: boolean;
  }> {
    const status = await this.getPermissionStatus();

    return {
      status,
      requestedBefore: this.hasRequestedBefore(),
      deniedCount: this.getPermissionDeniedCount(),
      lastDenied: this.getLastPermissionDeniedTime(),
      shouldShow: this.shouldShowPermissionRequest(),
    };
  }

  // Private helper methods

  private static async showPermissionRationale(title: string, message: string): Promise<boolean> {
    return new Promise(resolve => {
      Alert.alert(
        title,
        message,
        [
          {
            text: 'Not Now',
            style: 'cancel',
            onPress: () => resolve(false),
          },
          {
            text: 'Enable',
            style: 'default',
            onPress: () => resolve(true),
          },
        ],
        { cancelable: false }
      );
    });
  }

  private static showSettingsPrompt(title: string, message: string): void {
    Alert.alert(
      title,
      message,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Settings',
          style: 'default',
          onPress: () => this.openNotificationSettings(),
        },
      ],
      { cancelable: true }
    );
  }

  private static markPermissionRequested(): void {
    appStorage.setBoolean(this.PERMISSION_REQUESTED_KEY, true);
  }

  private static hasRequestedBefore(): boolean {
    return appStorage.getBoolean(this.PERMISSION_REQUESTED_KEY) || false;
  }

  private static markPermissionDenied(): void {
    const count = this.getPermissionDeniedCount() + 1;
    appStorage.setNumber('notification_permission_denied_count', count);
    appStorage.setNumber('notification_permission_last_denied', Date.now());
  }

  private static clearPermissionDenied(): void {
    appStorage.delete('notification_permission_denied_count');
    appStorage.delete('notification_permission_last_denied');
  }

  private static getPermissionDeniedCount(): number {
    return appStorage.getNumber('notification_permission_denied_count') || 0;
  }

  private static getLastPermissionDeniedTime(): number | undefined {
    return appStorage.getNumber('notification_permission_last_denied');
  }
}

/**
 * Convenience functions for common permission operations
 */

/**
 * Quick permission check - returns true if notifications are enabled
 */
export async function areNotificationsEnabled(): Promise<boolean> {
  const status = await NotificationPermissions.getPermissionStatus();
  return status.granted;
}

/**
 * Request permissions with sensible defaults
 */
export async function requestNotificationPermissions(): Promise<boolean> {
  const status = await NotificationPermissions.requestPermissions({
    showRationale: true,
  });
  return status.granted;
}

/**
 * Smart permission request - only shows if appropriate
 */
export async function smartRequestNotificationPermissions(): Promise<boolean> {
  if (!NotificationPermissions.shouldShowPermissionRequest()) {
    return false;
  }

  const status = await NotificationPermissions.requestPermissions({
    showRationale: true,
    rationaleTitle: 'Stay Updated',
    rationaleMessage:
      'Enable notifications to receive important updates about your account, new features, and social interactions.',
  });

  return status.granted;
}

/**
 * Check and prompt for notification permissions if needed
 */
export async function ensureNotificationPermissions(): Promise<boolean> {
  const status = await NotificationPermissions.getPermissionStatus();

  if (status.granted) {
    return true;
  }

  if (status.notDetermined) {
    return await requestNotificationPermissions();
  }

  // Permission was denied - could show settings prompt
  return false;
}
