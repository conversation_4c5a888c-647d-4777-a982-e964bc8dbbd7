import { i18n } from '@/src/core';
import { GraphQLErrorResponse } from '@/src/core/api/errors';
import { AuthErrorCode } from '@/src/features/auth/types';

interface NetworkError {
  statusCode?: number;
  message?: string;
}

export interface AuthError {
  errors?: GraphQLErrorResponse<AuthErrorCode>[];
  networkError?: NetworkError;
  message?: string;
}

/**
 * Extract user-friendly error message from authentication errors
 */
export function getAuthErrorMessage(error: AuthError | Error | unknown | string): string {
  // Handle string errors
  if (typeof error === 'string') {
    return error;
  }

  // Safely handle any unknown error type
  try {
    // Handle basic Error objects
    if (error instanceof Error && !('graphQLErrors' in error)) {
      return error.message || i18n.t('common.tryAgain');
    }

    // Handle GraphQL errors
    const authError = error as AuthError;

    if (authError.errors && authError.errors.length > 0) {
      const graphQLError = authError.errors[0];
      const { extensions } = graphQLError;

      // Handle specific error codes with user-friendly messages
      switch (extensions?.code) {
        case 'USER_NOT_FOUND':
          return i18n.t('auth.errors.userNotFound');

        case 'INVALID_PASSWORD':
        case 'WRONG_PASSWORD':
        case 'INVALID_CREDENTIALS':
          return i18n.t('auth.errors.invalidPassword');

        case 'EMAIL_ALREADY_EXISTS':
        case 'USER_ALREADY_EXISTS':
          return i18n.t('auth.errors.emailAlreadyExists');

        case 'INVALID_EMAIL':
          return i18n.t('auth.errors.invalidEmail');

        case 'WEAK_PASSWORD':
          return i18n.t('auth.errors.weakPassword');

        case 'TOKEN_EXPIRED':
        case 'INVALID_TOKEN':
          return i18n.t('auth.errors.invalidToken');

        case 'ACCOUNT_DISABLED':
          return i18n.t('auth.errors.accountDisabled');

        case 'TOO_MANY_ATTEMPTS':
          return i18n.t('auth.errors.tooManyAttempts');

        case 'VERIFICATION_REQUIRED':
          return i18n.t('auth.errors.verificationRequired');

        case 'UNAUTHENTICATED':
          return i18n.t('auth.errors.unauthenticated');

        case 'FORBIDDEN':
          return i18n.t('auth.errors.forbidden');

        default:
          // Use the suggestion if available, otherwise the message
          if (extensions?.suggestion) {
            return extensions.suggestion;
          }
          return graphQLError.message || i18n.t('auth.errors.general');
      }
    }

    // Handle network errors
    if (authError.networkError) {
      const { statusCode } = authError.networkError;

      switch (statusCode) {
        case 400:
          return i18n.t('auth.errors.badRequest');
        case 401:
          return i18n.t('auth.errors.unauthorized');
        case 403:
          return i18n.t('auth.errors.forbidden');
        case 404:
          return i18n.t('auth.errors.userNotFound');
        case 409:
          return i18n.t('auth.errors.conflict');
        case 429:
          return i18n.t('auth.errors.tooManyRequests');
        case 500:
          return i18n.t('auth.errors.serverError');
        case 503:
          return i18n.t('auth.errors.serviceUnavailable');
        default:
          return i18n.t('auth.errors.networkError');
      }
    }

    // Fallback to generic error message
    return authError?.message || i18n.t('auth.errors.general');
  } catch (e) {
    // Ultimate fallback for any error that occurs during error processing
    console.error('Error while processing error message:', e);
    return i18n.t('auth.errors.general');
  }
}

/**
 * Get error title based on error type
 */
export function getAuthErrorTitle(error: AuthError | Error | unknown | string): string {
  if (typeof error === 'string') {
    return i18n.t('common.error');
  }

  if (error instanceof Error && !('graphQLErrors' in error)) {
    return i18n.t('common.error');
  }

  const authError = error as AuthError;

  if (authError.errors && authError.errors.length > 0) {
    const { extensions } = authError.errors[0];

    switch (extensions?.code) {
      case 'USER_NOT_FOUND':
        return i18n.t('auth.errors.titles.userNotFound');
      case 'INVALID_PASSWORD':
      case 'WRONG_PASSWORD':
        return i18n.t('auth.errors.titles.invalidPassword');
      case 'EMAIL_ALREADY_EXISTS':
        return i18n.t('auth.errors.titles.emailExists');
      case 'TOO_MANY_ATTEMPTS':
        return i18n.t('auth.errors.titles.tooManyAttempts');
      case 'VERIFICATION_REQUIRED':
        return i18n.t('auth.errors.titles.verificationRequired');
      default:
        return i18n.t('auth.errors.titles.general');
    }
  }

  if (authError.networkError?.statusCode === 500) {
    return i18n.t('auth.errors.titles.serverError');
  }

  try {
    return i18n.t('common.error');
  } catch {
    // Fallback if an error occurs during error title processing
    return i18n.t('common.error');
  }
}

const extractErrorMessage = (response: any, fallbackMessage: string) => {
  if ('errors' in response && response.errors?.length > 0) {
    const errorCode = response.errors[0].extensions?.code || fallbackMessage;
    return getAuthErrorMessage(errorCode);
  }
  return getAuthErrorMessage(fallbackMessage);
};

export { extractErrorMessage };
