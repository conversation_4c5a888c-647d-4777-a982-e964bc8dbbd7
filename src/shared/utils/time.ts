import { isToday, isYesterday } from 'date-fns';

import i18n from '@/src/core/i18n/config';
import {
  formatDateSync as formatDateIntl,
  formatRelativeTimeSync as formatRelativeTime,
  formatTimeSync as formatTimeIntl,
  getUserTimezone,
  toUserTimezone,
} from '@/src/core/i18n/localization';

/**
 * Format time ago with intelligent fallbacks
 * Uses i18n for "Yesterday" translation and locale-aware relative formatting
 */
export function formatTimeAgo(date: string | Date): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;

  if (!dateObj || isNaN(dateObj.getTime())) {
    return '';
  }

  // Convert to user's timezone for accurate day comparison
  const userDate = toUserTimezone(dateObj);

  if (isToday(userDate)) {
    return formatRelativeTime(dateObj);
  }

  if (isYesterday(userDate)) {
    return i18n.t('common:time.yesterday', 'Yesterday');
  }

  // For dates older than yesterday, show the formatted date
  return formatDateIntl(dateObj, 'MMM d, yyyy');
}

/**
 * Format time using locale-aware formatting and user's timezone
 * Respects user's 12h/24h preference
 */
export function formatTime(
  date: string | Date,
  options?: {
    timezone?: string;
    format?: '12h' | '24h';
  }
): string {
  return formatTimeIntl(date, options);
}

/**
 * Format date using locale-aware formatting and user's timezone
 */
export function formatDate(
  date: string | Date,
  formatString?: string,
  options?: {
    timezone?: string;
  }
): string {
  return formatDateIntl(date, formatString, options);
}

/**
 * Format date and time together
 * Uses locale-aware formatting
 */
export function formatDateTime(
  date: string | Date,
  options?: {
    timezone?: string;
    dateFormat?: string;
    timeFormat?: '12h' | '24h';
    separator?: string;
  }
): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;

  if (!dateObj || isNaN(dateObj.getTime())) {
    return '';
  }

  const separator = options?.separator || ' ';
  const dateStr = formatDateIntl(dateObj, options?.dateFormat || 'MMM d, yyyy', {
    timezone: options?.timezone,
  });
  const timeStr = formatTimeIntl(dateObj, {
    timezone: options?.timezone,
    format: options?.timeFormat,
  });

  return `${dateStr}${separator}${timeStr}`;
}

/**
 * Get human-readable time until a future date
 */
export function formatTimeUntil(futureDate: string | Date): string {
  const future = typeof futureDate === 'string' ? new Date(futureDate) : futureDate;
  const now = new Date();

  if (!future || isNaN(future.getTime())) {
    return '';
  }

  if (future <= now) {
    return i18n.t('common:time.expired', 'Expired');
  }

  return formatRelativeTime(future, now);
}

/**
 * Format business hours based on user's timezone
 */
export function formatBusinessHours(
  startHour: number = 9,
  endHour: number = 17,
  timezone?: string
): string {
  const tz = timezone || getUserTimezone();
  const today = new Date();
  const startTime = new Date(today.getFullYear(), today.getMonth(), today.getDate(), startHour);
  const endTime = new Date(today.getFullYear(), today.getMonth(), today.getDate(), endHour);

  const startFormatted = formatTimeIntl(startTime, { timezone: tz });
  const endFormatted = formatTimeIntl(endTime, { timezone: tz });

  return `${startFormatted} - ${endFormatted}`;
}

/**
 * Check if current time is within business hours
 */
export function isBusinessHours(
  startHour: number = 9,
  endHour: number = 17,
  timezone?: string
): boolean {
  const tz = timezone || getUserTimezone();
  const now = toUserTimezone(new Date());
  const currentHour = now.getHours();

  return currentHour >= startHour && currentHour < endHour;
}

/**
 * Format date range
 */
export function formatDateRange(
  startDate: string | Date,
  endDate: string | Date,
  options?: {
    timezone?: string;
    separator?: string;
  }
): string {
  const start = typeof startDate === 'string' ? new Date(startDate) : startDate;
  const end = typeof endDate === 'string' ? new Date(endDate) : endDate;

  if (!start || !end || isNaN(start.getTime()) || isNaN(end.getTime())) {
    return '';
  }

  const separator = options?.separator || ' - ';
  const startFormatted = formatDateIntl(start, 'MMM d', options);

  // If same year, don't repeat it
  const sameYear = start.getFullYear() === end.getFullYear();
  const sameMonth = sameYear && start.getMonth() === end.getMonth();

  let endFormat = 'MMM d, yyyy';
  if (sameYear) {
    endFormat = sameMonth ? 'd, yyyy' : 'MMM d, yyyy';
  }

  const endFormatted = formatDateIntl(end, endFormat, options);

  return `${startFormatted}${separator}${endFormatted}`;
}
