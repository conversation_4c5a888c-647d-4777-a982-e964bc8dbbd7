import {
  formatCurrency as formatCurrencyIntl,
  formatNumber as formatNumberIntl,
  formatPercentage as formatPercentageIntl,
  getNumberFormatOptions,
} from '@/src/core/i18n/localization';

/**
 * Format number with compact notation for social media counts
 * Uses current locale from i18n context
 */
export function formatNumber(num: number, options: Intl.NumberFormatOptions = {}): string {
  const defaultOptions: Intl.NumberFormatOptions = { notation: 'compact' };
  const mergedOptions = { ...defaultOptions, ...options };
  return formatNumberIntl(num, mergedOptions);
}

/**
 * Format distance with proper units based on locale
 * Uses current locale from i18n context
 */
export function formatDistance(distance: number, options: Intl.NumberFormatOptions = {}): string {
  const { locale } = getNumberFormatOptions();

  try {
    if (distance < 1) {
      const meters = Math.round(distance * 1000);
      return new Intl.NumberFormat(locale, {
        style: 'unit',
        unit: 'meter',
        maximumFractionDigits: 0,
        ...options,
      }).format(meters);
    } else {
      return new Intl.NumberFormat(locale, {
        style: 'unit',
        unit: 'kilometer',
        minimumFractionDigits: 1,
        maximumFractionDigits: 1,
        ...options,
      }).format(distance);
    }
  } catch (error) {
    console.warn('Distance formatting error:', error);
    // Fallback to simple format
    if (distance < 1) {
      return `${Math.round(distance * 1000)}m`;
    }
    return `${distance.toFixed(1)}km`;
  }
}

/**
 * Format currency using locale-aware formatting
 * Uses current locale and currency from i18n context
 */
export function formatCurrency(
  amount: number,
  currencyCode?: string,
  options: Intl.NumberFormatOptions = {}
): string {
  return formatCurrencyIntl(amount, currencyCode, options);
}

/**
 * Format percentage using locale-aware formatting
 * Uses current locale from i18n context
 */
export function formatPercentage(
  value: number,
  decimals = 1,
  options: Intl.NumberFormatOptions = {}
): string {
  const defaultOptions: Intl.NumberFormatOptions = {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  };
  const mergedOptions = { ...defaultOptions, ...options };

  // Convert to decimal if value seems to be already in percentage format
  const normalizedValue = value > 1 ? value / 100 : value;
  return formatPercentageIntl(normalizedValue, mergedOptions);
}

/**
 * Format file size with appropriate units
 * Uses current locale from i18n context
 */
export function formatFileSize(bytes: number, decimals = 1): string {
  const { locale } = getNumberFormatOptions();

  if (bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  const value = bytes / Math.pow(k, i);

  try {
    return (
      new Intl.NumberFormat(locale, {
        minimumFractionDigits: i === 0 ? 0 : decimals,
        maximumFractionDigits: i === 0 ? 0 : decimals,
      }).format(value) +
      ' ' +
      sizes[i]
    );
  } catch (error) {
    console.warn('File size formatting error:', error);
    return `${value.toFixed(decimals)} ${sizes[i]}`;
  }
}

/**
 * Format duration in human-readable format
 * Uses current locale from i18n context
 */
export function formatDuration(seconds: number): string {
  const { locale } = getNumberFormatOptions();

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  try {
    const formatter = new Intl.NumberFormat(locale, {
      minimumIntegerDigits: 2,
    });

    if (hours > 0) {
      return `${hours}:${formatter.format(minutes)}:${formatter.format(remainingSeconds)}`;
    } else {
      return `${minutes}:${formatter.format(remainingSeconds)}`;
    }
  } catch (error) {
    console.warn('Duration formatting error:', error);
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
  }
}
