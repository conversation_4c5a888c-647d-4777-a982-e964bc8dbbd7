import { WithSpringConfig } from 'react-native-reanimated';

/**
 * Spring animation configurations for consistent UI interactions
 */
export const SpringPresets = {
  /**
   * Default button press animation with subtle bounce
   * - Light and responsive feel
   * - Quick reaction, minimal oscillation
   */
  BUTTON_PRESS: {
    mass: 0.8, // Lower mass = faster reaction
    damping: 12, // Higher damping = less oscillation
    stiffness: 120, // Higher stiffness = snappier movement
    overshootClamping: false,
    restDisplacementThreshold: 0.01,
    restSpeedThreshold: 0.01,
  } as WithSpringConfig,

  /**
   * Gentle animation for subtle UI elements
   * - Soft, gradual movements
   * - Good for secondary elements
   */
  SUBTLE: {
    mass: 1,
    damping: 18,
    stiffness: 80,
    overshootClamping: false,
    restDisplacementThreshold: 0.01,
    restSpeedThreshold: 0.01,
  } as WithSpringConfig,

  /**
   * Bouncy animation for playful, attention-grabbing elements
   * - Exaggerated bounce effect
   * - Good for celebratory or engaging moments
   */
  BOUNCY: {
    mass: 1,
    damping: 8, // Lower damping = more bounce
    stiffness: 150,
    overshootClamping: false,
    restDisplacementThreshold: 0.01,
    restSpeedThreshold: 0.01,
  } as WithSpringConfig,

  /**
   * Rapid animation for instant feedback
   * - Very quick, minimal bounce
   * - Good for immediate response requirements
   */
  SNAPPY: {
    mass: 0.5, // Very light mass for quick reaction
    damping: 15,
    stiffness: 200, // High stiffness for quick movement
    overshootClamping: true,
    restDisplacementThreshold: 0.01,
    restSpeedThreshold: 0.01,
  } as WithSpringConfig,
};

/**
 * Scale animation values for different UI elements
 */
export const ScaleValues = {
  BUTTON: {
    DEFAULT: 1,
    PRESSED: 0.98,
  },
  FAB: {
    DEFAULT: 1,
    PRESSED: 0.95,
  },
  CARD: {
    DEFAULT: 1,
    PRESSED: 0.97,
    HIGHLIGHTED: 1.02,
  },
};

/**
 * Animation durations in milliseconds
 */
export const Durations = {
  VERY_FAST: 100,
  FAST: 150,
  MEDIUM: 250,
  SLOW: 350,
  VERY_SLOW: 500,
};

/**
 * Creates a customized spring config by extending a preset
 *
 * @param preset Base animation preset to extend
 * @param overrides Custom parameter overrides
 * @returns Combined spring configuration
 */
export function createSpringConfig(
  preset: keyof typeof SpringPresets | WithSpringConfig,
  overrides: Partial<WithSpringConfig> = {}
): WithSpringConfig {
  const baseConfig = typeof preset === 'string' ? SpringPresets[preset] : preset;

  // Use type assertion to ensure the merged config is recognized as a valid WithSpringConfig
  return {
    ...baseConfig,
    ...overrides,
  } as WithSpringConfig;
}

// Type definitions for animation variants
export type AnimationVariant = {
  scale: {
    default: number;
    pressed: number;
  };
  config: WithSpringConfig;
};

export const ButtonVariantAnimations: Record<
  'primary' | 'secondary' | 'outline' | 'ghost',
  AnimationVariant
> = {
  primary: {
    scale: {
      default: 1,
      pressed: 0.99,
    },
    config: SpringPresets.BUTTON_PRESS,
  },
  secondary: {
    scale: {
      default: 1,
      pressed: 0.95,
    },
    config: createSpringConfig('BUTTON_PRESS', { damping: 14 }),
  },
  outline: {
    scale: {
      default: 1,
      pressed: 0.99,
    },
    config: SpringPresets.SUBTLE,
  },
  ghost: {
    scale: {
      default: 1,
      pressed: 0.95, // No scale change
    },
    config: SpringPresets.SNAPPY,
  },
};

export const FabVariantAnimations: Record<string, AnimationVariant> = {};

export const CardVariantAnimations: Record<string, AnimationVariant> = {};
