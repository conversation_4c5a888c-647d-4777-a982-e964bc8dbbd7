import { useMemo } from 'react';

import { useTranslation } from 'react-i18next';

import { getLocalizedDayName, getLocalizedWeekdaysSync } from '@/src/core/i18n/localization';

/**
 * Hook for getting localized day names
 * Provides convenient access to localized weekday functionality
 */
export const useLocalizedDays = () => {
  const { i18n } = useTranslation();

  const weekdays = useMemo(
    () => getLocalizedWeekdaysSync({ locale: i18n.language }),
    [i18n.language]
  );

  const weekdaysShort = useMemo(
    () => getLocalizedWeekdaysSync({ locale: i18n.language, format: 'short' }),
    [i18n.language]
  );

  const weekdaysNarrow = useMemo(
    () => getLocalizedWeekdaysSync({ locale: i18n.language, format: 'narrow' }),
    [i18n.language]
  );

  const getDayName = (day: string | number, format: 'long' | 'short' | 'narrow' = 'long') => {
    return getLocalizedDayName(day, { locale: i18n.language, format });
  };

  return {
    weekdays,
    weekdaysShort,
    weekdaysNarrow,
    getDayName,
  };
};
