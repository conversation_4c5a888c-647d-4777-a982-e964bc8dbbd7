import { useCallback } from 'react';

import { useTranslation } from 'react-i18next';

import {
  formatCurrency as formatCurrencyUtil,
  formatDistance as formatDistanceUtil,
  formatDuration,
  formatFileSize,
  formatNumber as formatNumberUtil,
  formatPercentage as formatPercentageUtil,
} from '../utils/format';
import {
  formatBusinessHours,
  formatDateRange,
  formatDateTime,
  formatDate as formatDateUtil,
  formatTimeAgo as formatTimeAgoUtil,
  formatTimeUntil,
  formatTime as formatTimeUtil,
} from '../utils/time';

/**
 * Hook for localized number and currency formatting
 * Automatically uses current locale from i18n context
 */
export function useLocalizedFormat() {
  const { i18n } = useTranslation();

  const formatNumber = useCallback(
    (num: number, options?: Intl.NumberFormatOptions) => {
      return formatNumberUtil(num, options);
    },
    [i18n.language]
  );

  const formatDistance = useCallback(
    (distance: number, options?: Intl.NumberFormatOptions) => {
      return formatDistanceUtil(distance, options);
    },
    [i18n.language]
  );

  const formatCurrency = useCallback(
    (amount: number, currencyCode?: string, options?: Intl.NumberFormatOptions) => {
      return formatCurrencyUtil(amount, currencyCode, options);
    },
    [i18n.language]
  );

  const formatPercentage = useCallback(
    (value: number, decimals?: number, options?: Intl.NumberFormatOptions) => {
      return formatPercentageUtil(value, decimals, options);
    },
    [i18n.language]
  );

  const formatBytes = useCallback(
    (bytes: number, decimals?: number) => {
      return formatFileSize(bytes, decimals);
    },
    [i18n.language]
  );

  const formatSeconds = useCallback(
    (seconds: number) => {
      return formatDuration(seconds);
    },
    [i18n.language]
  );

  return {
    formatNumber,
    formatDistance,
    formatCurrency,
    formatPercentage,
    formatBytes,
    formatSeconds,
  };
}

/**
 * Hook for localized date and time formatting
 * Automatically uses current locale and timezone from i18n context
 */
export function useLocalizedTime() {
  const { i18n } = useTranslation();

  const formatTimeAgo = useCallback(
    (date: string | Date) => {
      return formatTimeAgoUtil(date);
    },
    [i18n.language]
  );

  const formatTime = useCallback(
    (date: string | Date, options?: { timezone?: string; format?: '12h' | '24h' }) => {
      return formatTimeUtil(date, options);
    },
    [i18n.language]
  );

  const formatDate = useCallback(
    (date: string | Date, formatString?: string, options?: { timezone?: string }) => {
      return formatDateUtil(date, formatString, options);
    },
    [i18n.language]
  );

  const formatFullDateTime = useCallback(
    (
      date: string | Date,
      options?: {
        timezone?: string;
        dateFormat?: string;
        timeFormat?: '12h' | '24h';
        separator?: string;
      }
    ) => {
      return formatDateTime(date, options);
    },
    [i18n.language]
  );

  const formatCountdown = useCallback(
    (futureDate: string | Date) => {
      return formatTimeUntil(futureDate);
    },
    [i18n.language]
  );

  const formatHours = useCallback(
    (startHour?: number, endHour?: number, timezone?: string) => {
      return formatBusinessHours(startHour, endHour, timezone);
    },
    [i18n.language]
  );

  const formatRange = useCallback(
    (
      startDate: string | Date,
      endDate: string | Date,
      options?: { timezone?: string; separator?: string }
    ) => {
      return formatDateRange(startDate, endDate, options);
    },
    [i18n.language]
  );

  return {
    formatTimeAgo,
    formatTime,
    formatDate,
    formatFullDateTime,
    formatCountdown,
    formatHours,
    formatRange,
  };
}

/**
 * Combined hook for all localized formatting functions
 * Convenient single import for components that need multiple formatters
 */
export function useLocalization() {
  const { t, i18n } = useTranslation();
  const formatUtils = useLocalizedFormat();
  const timeUtils = useLocalizedTime();

  return {
    t,
    locale: i18n.language,
    ...formatUtils,
    ...timeUtils,
  };
}
