/**
 * useAlert hook - provides easy access to alert dialogs
 * Integrates with haptics and translations
 */
import { useCallback } from 'react';

import { useTranslation } from 'react-i18next';

import { alert } from '@/src/core/libs/alert';
import type { AlertAction, AlertIntegrationOptions, ConfirmOptions } from '@/src/core/libs/alert';

export interface UseAlertReturn {
  // Basic alert methods
  show: (
    title: string,
    message?: string,
    actions?: AlertAction[],
    options?: AlertIntegrationOptions
  ) => Promise<void>;

  // Convenience methods with translations
  info: (title: string, message?: string, options?: AlertIntegrationOptions) => Promise<void>;
  success: (title: string, message?: string, options?: AlertIntegrationOptions) => Promise<void>;
  warning: (title: string, message?: string, options?: AlertIntegrationOptions) => Promise<void>;
  error: (title: string, message?: string, options?: AlertIntegrationOptions) => Promise<void>;

  // Confirmation dialogs
  confirm: (options: ConfirmOptions) => Promise<boolean>;
  confirmDestructive: (options: ConfirmOptions) => Promise<boolean>;
  yesNo: (
    title: string,
    message?: string,
    onYes?: () => void,
    onNo?: () => void
  ) => Promise<boolean>;

  // Common confirmation patterns
  confirmDelete: (itemName?: string, onConfirm?: () => void) => Promise<boolean>;
  confirmLogout: (onConfirm?: () => void) => Promise<boolean>;
  confirmUnsavedChanges: (onConfirm?: () => void, onCancel?: () => void) => Promise<boolean>;
}

export const useAlert = (): UseAlertReturn => {
  const { t } = useTranslation();

  const show = useCallback(
    (
      title: string,
      message?: string,
      actions?: AlertAction[],
      options?: AlertIntegrationOptions
    ) => {
      return alert.show(title, message, actions, options);
    },
    []
  );

  const info = useCallback((title: string, message?: string, options?: AlertIntegrationOptions) => {
    return alert.info(title, message, options);
  }, []);

  const success = useCallback(
    (title: string, message?: string, options?: AlertIntegrationOptions) => {
      return alert.success(title, message, options);
    },
    []
  );

  const warning = useCallback(
    (title: string, message?: string, options?: AlertIntegrationOptions) => {
      return alert.warning(title, message, options);
    },
    []
  );

  const error = useCallback(
    (title: string, message?: string, options?: AlertIntegrationOptions) => {
      return alert.error(title, message, options);
    },
    []
  );

  const confirm = useCallback((options: ConfirmOptions) => {
    return alert.confirm(options);
  }, []);

  const confirmDestructive = useCallback((options: ConfirmOptions) => {
    return alert.confirmDestructive(options);
  }, []);

  const yesNo = useCallback(
    (title: string, message?: string, onYes?: () => void, onNo?: () => void) => {
      return alert.yesNo(title, message, onYes, onNo);
    },
    []
  );

  // Common confirmation patterns
  const confirmDelete = useCallback(
    (itemName?: string, onConfirm?: () => void) => {
      const item = itemName || t('common.item');
      return alert.confirmDestructive({
        title: t('alerts.confirmDelete.title'),
        message: t('alerts.confirmDelete.message', { item }),
        confirmText: t('common.delete'),
        cancelText: t('common.cancel'),
        onConfirm,
        haptic: 'warning',
      });
    },
    [t]
  );

  const confirmLogout = useCallback(
    (onConfirm?: () => void) => {
      return alert.confirm({
        title: t('alerts.confirmLogout.title'),
        message: t('alerts.confirmLogout.message'),
        confirmText: t('common.logout'),
        cancelText: t('common.cancel'),
        onConfirm,
        haptic: 'warning',
      });
    },
    [t]
  );

  const confirmUnsavedChanges = useCallback(
    (onConfirm?: () => void, onCancel?: () => void) => {
      return alert.confirm({
        title: t('alerts.unsavedChanges.title'),
        message: t('alerts.unsavedChanges.message'),
        confirmText: t('common.discard'),
        cancelText: t('common.cancel'),
        onConfirm,
        onCancel,
        destructive: true,
        haptic: 'warning',
      });
    },
    [t]
  );

  return {
    show,
    info,
    success,
    warning,
    error,
    confirm,
    confirmDestructive,
    yesNo,
    confirmDelete,
    confirmLogout,
    confirmUnsavedChanges,
  };
};
