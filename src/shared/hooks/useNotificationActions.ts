/**
 * useNotificationActions hook - Specialized hook for common notification actions
 * Provides pre-configured notification actions for common app scenarios
 */
import { useCallback } from 'react';

import { useTranslation } from 'react-i18next';

import type { NotificationPriority, NotificationType } from '@/src/shared/types/notification';

import { useNotifications } from './useNotifications';

/**
 * Hook for common notification actions with localization
 */
export function useNotificationActions() {
  const { t } = useTranslation();
  const { displayNotification, scheduleNotification } = useNotifications();

  // Social notifications
  const notifyNewFollower = useCallback(
    async (followerName: string, followerAvatar?: string) => {
      return displayNotification(
        'follow',
        t('notifications.newFollower.title'),
        t('notifications.newFollower.message', { name: followerName }),
        {
          priority: 'normal',
          avatar: followerAvatar,
          data: { type: 'new_follower', followerName },
          actions: [
            { id: 'view_profile', label: t('notifications.actions.viewProfile') },
            { id: 'follow_back', label: t('notifications.actions.followBack') },
          ],
        }
      );
    },
    [displayNotification, t]
  );

  const notifyNewLike = useCallback(
    async (likerName: string, postTitle: string, likerAvatar?: string) => {
      return displayNotification(
        'like',
        t('notifications.newLike.title'),
        t('notifications.newLike.message', { name: likerName, post: postTitle }),
        {
          priority: 'low',
          avatar: likerAvatar,
          data: { type: 'new_like', likerName, postTitle },
          actions: [{ id: 'view_post', label: t('notifications.actions.viewPost') }],
        }
      );
    },
    [displayNotification, t]
  );

  const notifyNewComment = useCallback(
    async (
      commenterName: string,
      postTitle: string,
      commentPreview: string,
      commenterAvatar?: string
    ) => {
      return displayNotification(
        'comment',
        t('notifications.newComment.title'),
        t('notifications.newComment.message', {
          name: commenterName,
          post: postTitle,
          comment: commentPreview,
        }),
        {
          priority: 'normal',
          avatar: commenterAvatar,
          data: { type: 'new_comment', commenterName, postTitle, commentPreview },
          actions: [
            { id: 'view_comment', label: t('notifications.actions.viewComment') },
            { id: 'reply', label: t('notifications.actions.reply') },
          ],
        }
      );
    },
    [displayNotification, t]
  );

  const notifyMention = useCallback(
    async (mentionerName: string, content: string, mentionerAvatar?: string) => {
      return displayNotification(
        'mention',
        t('notifications.mention.title'),
        t('notifications.mention.message', { name: mentionerName, content }),
        {
          priority: 'high',
          avatar: mentionerAvatar,
          data: { type: 'mention', mentionerName, content },
          actions: [
            { id: 'view_mention', label: t('notifications.actions.view') },
            { id: 'reply', label: t('notifications.actions.reply') },
          ],
        }
      );
    },
    [displayNotification, t]
  );

  const notifyNewMessage = useCallback(
    async (senderName: string, messagePreview: string, senderAvatar?: string, chatId?: string) => {
      return displayNotification('chat', senderName, messagePreview, {
        priority: 'high',
        avatar: senderAvatar,
        data: { type: 'new_message', senderName, messagePreview, chatId },
        actions: [
          { id: 'view_chat', label: t('notifications.actions.viewChat') },
          { id: 'reply', label: t('notifications.actions.reply') },
        ],
      });
    },
    [displayNotification, t]
  );

  // System notifications
  const notifyAccountSecurity = useCallback(
    async (action: string, location?: string) => {
      return displayNotification(
        'security',
        t('notifications.security.title'),
        t('notifications.security.message', {
          action,
          location: location || t('notifications.security.unknownLocation'),
        }),
        {
          priority: 'critical',
          data: { type: 'security_alert', action, location },
          actions: [
            { id: 'review_activity', label: t('notifications.actions.reviewActivity') },
            { id: 'secure_account', label: t('notifications.actions.secureAccount') },
          ],
        }
      );
    },
    [displayNotification, t]
  );

  const notifySystemUpdate = useCallback(
    async (version: string, features: string[]) => {
      return displayNotification(
        'system',
        t('notifications.systemUpdate.title'),
        t('notifications.systemUpdate.message', { version, features: features.join(', ') }),
        {
          priority: 'normal',
          data: { type: 'system_update', version, features },
          actions: [
            { id: 'view_changelog', label: t('notifications.actions.viewChangelog') },
            { id: 'update_now', label: t('notifications.actions.updateNow') },
          ],
        }
      );
    },
    [displayNotification, t]
  );

  const notifyAchievement = useCallback(
    async (achievementName: string, description: string, achievementIcon?: string) => {
      return displayNotification(
        'achievement',
        t('notifications.achievement.title'),
        t('notifications.achievement.message', { achievement: achievementName, description }),
        {
          priority: 'normal',
          image: achievementIcon,
          data: { type: 'achievement_unlocked', achievementName, description },
          actions: [
            { id: 'view_achievements', label: t('notifications.actions.viewAchievements') },
            { id: 'share_achievement', label: t('notifications.actions.share') },
          ],
        }
      );
    },
    [displayNotification, t]
  );

  // Trending notifications
  const notifyTrendingPost = useCallback(
    async (postTitle: string, metrics: { likes: number; comments: number }, postImage?: string) => {
      return displayNotification(
        'trending',
        t('notifications.trending.title'),
        t('notifications.trending.message', {
          post: postTitle,
          likes: metrics.likes,
          comments: metrics.comments,
        }),
        {
          priority: 'low',
          image: postImage,
          data: { type: 'trending_post', postTitle, metrics },
          actions: [
            { id: 'view_post', label: t('notifications.actions.viewPost') },
            { id: 'share_post', label: t('notifications.actions.share') },
          ],
        }
      );
    },
    [displayNotification, t]
  );

  // Reminder notifications
  const scheduleReminder = useCallback(
    async (title: string, message: string, scheduledFor: Date, data?: Record<string, any>) => {
      return scheduleNotification('reminder', title, message, scheduledFor, {
        priority: 'normal',
        data: { type: 'reminder', ...data },
      });
    },
    [scheduleNotification]
  );

  const scheduleRecurringReminder = useCallback(
    async (
      title: string,
      message: string,
      scheduledFor: Date,
      frequency: 'daily' | 'weekly' | 'monthly' | 'yearly',
      data?: Record<string, any>
    ) => {
      return scheduleNotification('reminder', title, message, scheduledFor, {
        priority: 'normal',
        repeatFrequency: frequency,
        data: { type: 'recurring_reminder', frequency, ...data },
      });
    },
    [scheduleNotification]
  );

  // Marketing notifications
  const notifyPromotion = useCallback(
    async (
      promotionTitle: string,
      description: string,
      promoImage?: string,
      promoCode?: string
    ) => {
      return displayNotification('marketing', promotionTitle, description, {
        priority: 'low',
        image: promoImage,
        data: { type: 'promotion', promotionTitle, description, promoCode },
        actions: [
          { id: 'view_promotion', label: t('notifications.actions.viewDetails') },
          ...(promoCode ? [{ id: 'copy_code', label: t('notifications.actions.copyCode') }] : []),
        ],
      });
    },
    [displayNotification, t]
  );

  // Quick notification helpers
  const notifySuccess = useCallback(
    async (message: string) => {
      return displayNotification('system', t('notifications.success.title'), message, {
        priority: 'normal',
      });
    },
    [displayNotification, t]
  );

  const notifyError = useCallback(
    async (message: string, retryAction?: () => void) => {
      return displayNotification('system', t('notifications.error.title'), message, {
        priority: 'high',
        actions: retryAction
          ? [
              { id: 'retry', label: t('notifications.actions.retry') },
              { id: 'dismiss', label: t('notifications.actions.dismiss') },
            ]
          : undefined,
      });
    },
    [displayNotification, t]
  );

  const notifyInfo = useCallback(
    async (title: string, message: string, actions?: { id: string; label: string }[]) => {
      return displayNotification('system', title, message, {
        priority: 'normal',
        actions,
      });
    },
    [displayNotification]
  );

  return {
    // Social notifications
    notifyNewFollower,
    notifyNewLike,
    notifyNewComment,
    notifyMention,
    notifyNewMessage,

    // System notifications
    notifyAccountSecurity,
    notifySystemUpdate,
    notifyAchievement,

    // Trending notifications
    notifyTrendingPost,

    // Reminders
    scheduleReminder,
    scheduleRecurringReminder,

    // Marketing
    notifyPromotion,

    // Quick helpers
    notifySuccess,
    notifyError,
    notifyInfo,
  };
}

/**
 * Hook for batch notification actions
 */
export function useBatchNotificationActions() {
  const { displayNotification } = useNotifications();
  const { t } = useTranslation();

  const notifyMultipleActivities = useCallback(
    async (
      activities: {
        type: 'like' | 'comment' | 'follow';
        userName: string;
        userAvatar?: string;
        content?: string;
      }[]
    ) => {
      if (activities.length === 0) return;

      const groupedActivities = activities.reduce(
        (acc, activity) => {
          if (!acc[activity.type]) acc[activity.type] = [];
          acc[activity.type].push(activity);
          return acc;
        },
        {} as Record<string, typeof activities>
      );

      const notifications: Promise<string>[] = [];

      // Handle likes
      if (groupedActivities.like?.length > 0) {
        const likes = groupedActivities.like;
        if (likes.length === 1) {
          notifications.push(
            displayNotification(
              'like',
              t('notifications.newLike.title'),
              t('notifications.newLike.message', { name: likes[0].userName, post: 'your post' }),
              {
                priority: 'low',
                avatar: likes[0].userAvatar,
                data: { type: 'batch_likes', count: 1, users: likes.map(l => l.userName) },
              }
            )
          );
        } else {
          notifications.push(
            displayNotification(
              'like',
              t('notifications.multipleLikes.title'),
              t('notifications.multipleLikes.message', { count: likes.length }),
              {
                priority: 'normal',
                data: {
                  type: 'batch_likes',
                  count: likes.length,
                  users: likes.map(l => l.userName),
                },
                actions: [{ id: 'view_likes', label: t('notifications.actions.viewAll') }],
              }
            )
          );
        }
      }

      // Handle comments
      if (groupedActivities.comment?.length > 0) {
        const comments = groupedActivities.comment;
        if (comments.length === 1) {
          notifications.push(
            displayNotification(
              'comment',
              t('notifications.newComment.title'),
              t('notifications.newComment.message', {
                name: comments[0].userName,
                post: 'your post',
                comment: comments[0].content || '',
              }),
              {
                priority: 'normal',
                avatar: comments[0].userAvatar,
                data: { type: 'batch_comments', count: 1, users: comments.map(c => c.userName) },
              }
            )
          );
        } else {
          notifications.push(
            displayNotification(
              'comment',
              t('notifications.multipleComments.title'),
              t('notifications.multipleComments.message', { count: comments.length }),
              {
                priority: 'normal',
                data: {
                  type: 'batch_comments',
                  count: comments.length,
                  users: comments.map(c => c.userName),
                },
                actions: [{ id: 'view_comments', label: t('notifications.actions.viewAll') }],
              }
            )
          );
        }
      }

      // Handle follows
      if (groupedActivities.follow?.length > 0) {
        const follows = groupedActivities.follow;
        if (follows.length === 1) {
          notifications.push(
            displayNotification(
              'follow',
              t('notifications.newFollower.title'),
              t('notifications.newFollower.message', { name: follows[0].userName }),
              {
                priority: 'normal',
                avatar: follows[0].userAvatar,
                data: { type: 'batch_follows', count: 1, users: follows.map(f => f.userName) },
              }
            )
          );
        } else {
          notifications.push(
            displayNotification(
              'follow',
              t('notifications.multipleFollowers.title'),
              t('notifications.multipleFollowers.message', { count: follows.length }),
              {
                priority: 'normal',
                data: {
                  type: 'batch_follows',
                  count: follows.length,
                  users: follows.map(f => f.userName),
                },
                actions: [{ id: 'view_followers', label: t('notifications.actions.viewAll') }],
              }
            )
          );
        }
      }

      return Promise.all(notifications);
    },
    [displayNotification, t]
  );

  return {
    notifyMultipleActivities,
  };
}
