/**
 * Global performance optimization hook
 * Combines caching, prefetching, and network optimization for worldwide users
 */
import { useCallback, useEffect, useState } from 'react';

import { AppState } from 'react-native';

import NetInfo from '@react-native-community/netinfo';

import { CACHE_CATEGORIES, apiCache, apiClient } from '@/src/core/api';
import { getBusinessHours, getUserTimezone } from '@/src/core/i18n';

/**
 * Network connection state
 */
interface NetworkState {
  isConnected: boolean;
  connectionType: string;
  isExpensive: boolean;
}

/**
 * Performance metrics
 */
interface PerformanceMetrics {
  cacheHitRate: number;
  avgResponseTime: number;
  failedRequests: number;
  totalRequests: number;
}

/**
 * Global performance hook
 */
export function useGlobalPerformance() {
  const [networkState, setNetworkState] = useState<NetworkState>({
    isConnected: true,
    connectionType: 'unknown',
    isExpensive: false,
  });

  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics>({
    cacheHitRate: 0,
    avgResponseTime: 0,
    failedRequests: 0,
    totalRequests: 0,
  });

  const [isOptimizedMode, setIsOptimizedMode] = useState(false);

  /**
   * Monitor network state
   */
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      setNetworkState({
        isConnected: state.isConnected ?? false,
        connectionType: state.type,
        isExpensive: state.details?.isConnectionExpensive ?? false,
      });

      // Enable optimized mode on poor connections
      const shouldOptimize =
        !state.isConnected ||
        state.details?.isConnectionExpensive ||
        ['2g', 'cellular'].includes(state.type);

      setIsOptimizedMode(shouldOptimize);
    });

    return unsubscribe;
  }, []);

  /**
   * Monitor app state for background optimization
   */
  useEffect(() => {
    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'background') {
        // Cleanup expired cache when app goes to background
        cleanupExpiredCache();
      } else if (nextAppState === 'active') {
        // Prefetch critical data when app becomes active
        prefetchCriticalData();
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, []);

  /**
   * Cleanup expired cache entries
   */
  const cleanupExpiredCache = useCallback(async () => {
    try {
      await apiCache.cleanupCategory(CACHE_CATEGORIES.API_RESPONSES);
      await apiCache.cleanupCategory(CACHE_CATEGORIES.SEARCH_RESULTS);
    } catch (error) {
      console.warn('Cache cleanup error:', error);
    }
  }, []);

  /**
   * Prefetch critical data based on user patterns
   */
  const prefetchCriticalData = useCallback(async () => {
    if (!networkState.isConnected || networkState.isExpensive) {
      return;
    }

    try {
      // Only prefetch during business hours to save bandwidth
      const businessHours = getBusinessHours();
      if (!businessHours.isBusinessHours) {
        return;
      }

      // Prefetch user profile if not cached
      const hasCachedProfile = await apiCache.has(CACHE_CATEGORIES.USER_PROFILES, 'current_user');

      if (!hasCachedProfile) {
        // This would be implemented based on your app's needs
        console.log('Prefetching user profile...');
      }
    } catch (error) {
      console.warn('Prefetch error:', error);
    }
  }, [networkState]);

  /**
   * Get optimized request configuration based on network conditions
   */
  const getOptimizedRequestConfig = useCallback(() => {
    const baseConfig = {
      timeout: 30000,
      retries: 3,
      cache: {
        enabled: true,
        category: CACHE_CATEGORIES.API_RESPONSES,
        ttl: 5 * 60 * 1000, // 5 minutes
      },
    };

    if (isOptimizedMode) {
      return {
        ...baseConfig,
        timeout: 15000, // Shorter timeout on slow connections
        retries: 1, // Fewer retries
        cache: {
          ...baseConfig.cache,
          ttl: 30 * 60 * 1000, // Longer cache TTL (30 minutes)
        },
      };
    }

    return baseConfig;
  }, [isOptimizedMode]);

  /**
   * Update performance metrics
   */
  const updateMetrics = useCallback(
    (responseTime: number, wasFromCache: boolean, failed: boolean = false) => {
      setPerformanceMetrics(prev => {
        const totalRequests = prev.totalRequests + 1;
        const cacheHits = wasFromCache ? 1 : 0;
        const newCacheHitRate =
          (prev.cacheHitRate * prev.totalRequests + cacheHits) / totalRequests;

        const newAvgResponseTime =
          (prev.avgResponseTime * prev.totalRequests + responseTime) / totalRequests;

        return {
          cacheHitRate: newCacheHitRate,
          avgResponseTime: newAvgResponseTime,
          failedRequests: prev.failedRequests + (failed ? 1 : 0),
          totalRequests,
        };
      });
    },
    []
  );

  /**
   * Make optimized API request
   */
  const makeOptimizedRequest = useCallback(
    async <T>(endpoint: string, options: any = {}): Promise<T> => {
      const startTime = Date.now();
      const config = getOptimizedRequestConfig();

      try {
        const response = await apiClient.get<T>(endpoint, {
          ...config,
          ...options,
        });

        const responseTime = Date.now() - startTime;
        updateMetrics(responseTime, response.fromCache || false);

        return response.data;
      } catch (error) {
        const responseTime = Date.now() - startTime;
        updateMetrics(responseTime, false, true);
        throw error;
      }
    },
    [getOptimizedRequestConfig, updateMetrics]
  );

  /**
   * Check if app should use data-saving mode
   */
  const shouldUseDataSaving = useCallback(() => {
    return isOptimizedMode || networkState.isExpensive;
  }, [isOptimizedMode, networkState.isExpensive]);

  /**
   * Get cache statistics
   */
  const getCacheStats = useCallback(async () => {
    return await apiCache.getStats();
  }, []);

  /**
   * Preload images for better UX
   */
  const preloadImages = useCallback(
    (imageUrls: string[]) => {
      if (shouldUseDataSaving()) {
        return; // Skip preloading on expensive connections
      }

      imageUrls.forEach(url => {
        const image = new Image();
        image.src = url;
      });
    },
    [shouldUseDataSaving]
  );

  /**
   * Get timezone-aware configuration
   */
  const getTimezoneConfig = useCallback(() => {
    const timezone = getUserTimezone();
    const businessHours = getBusinessHours();

    return {
      timezone,
      isBusinessHours: businessHours.isBusinessHours,
      shouldPrefetch: businessHours.isBusinessHours && networkState.isConnected,
    };
  }, [networkState.isConnected]);

  return {
    // Network state
    networkState,
    isOptimizedMode,

    // Performance metrics
    performanceMetrics,

    // Optimization methods
    makeOptimizedRequest,
    getOptimizedRequestConfig,
    shouldUseDataSaving,
    preloadImages,

    // Cache management
    getCacheStats,
    cleanupExpiredCache,

    // Timezone utilities
    getTimezoneConfig,

    // Prefetching
    prefetchCriticalData,
  };
}
