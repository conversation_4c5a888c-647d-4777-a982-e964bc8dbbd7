/**
 * useToast hook - provides easy access to toast notifications
 * Integrates with haptics for enhanced user feedback
 */
import { useCallback } from 'react';

import { haptics, toast } from '@/src/core/libs';
import type { DetailedToastOptions, ToastOptions } from '@/src/core/libs/toast';

export interface UseToastReturn {
  // Basic toast methods
  success: (message: string, options?: ToastOptions) => Promise<void>;
  error: (message: string, options?: ToastOptions) => Promise<void>;
  warning: (message: string, options?: ToastOptions) => Promise<void>;
  info: (message: string, options?: ToastOptions) => Promise<void>;

  // Advanced toast methods
  show: (options: DetailedToastOptions) => void;
  hide: () => void;

  // Loading states
  loading: (message: string) => void;
  hideLoading: () => void;

  // Combined actions with haptics
  successWithHaptic: (message: string, options?: ToastOptions) => Promise<void>;
  errorWithHaptic: (message: string, options?: ToastOptions) => Promise<void>;
  warningWithHaptic: (message: string, options?: ToastOptions) => Promise<void>;
}

export const useToast = (): UseToastReturn => {
  const success = useCallback(async (message: string, options?: ToastOptions) => {
    await toast.success(message, options);
  }, []);

  const error = useCallback(async (message: string, options?: ToastOptions) => {
    await toast.error(message, options);
  }, []);

  const warning = useCallback(async (message: string, options?: ToastOptions) => {
    await toast.warning(message, options);
  }, []);

  const info = useCallback(async (message: string, options?: ToastOptions) => {
    await toast.info(message, options);
  }, []);

  const show = useCallback((options: DetailedToastOptions) => {
    toast.show(options);
  }, []);

  const hide = useCallback(() => {
    toast.hide();
  }, []);

  const loading = useCallback((message: string) => {
    toast.loading(message);
  }, []);

  const hideLoading = useCallback(() => {
    toast.hideLoading();
  }, []);

  // Combined actions with haptics
  const successWithHaptic = useCallback(async (message: string, options?: ToastOptions) => {
    await Promise.all([
      haptics.success(),
      toast.success(message, { ...options, haptic: 'success' }),
    ]);
  }, []);

  const errorWithHaptic = useCallback(async (message: string, options?: ToastOptions) => {
    await Promise.all([haptics.error(), toast.error(message, { ...options, haptic: 'error' })]);
  }, []);

  const warningWithHaptic = useCallback(async (message: string, options?: ToastOptions) => {
    await Promise.all([
      haptics.warning(),
      toast.warning(message, { ...options, haptic: 'warning' }),
    ]);
  }, []);

  return {
    success,
    error,
    warning,
    info,
    show,
    hide,
    loading,
    hideLoading,
    successWithHaptic,
    errorWithHaptic,
    warningWithHaptic,
  };
};
