/**
 * useNotifications hook - React hook for notification management
 * Provides convenient access to notification functionality
 */
import { useCallback, useEffect, useMemo } from 'react';

import { AppState, AppStateStatus } from 'react-native';

import { useNotificationStore } from '@/src/shared/store/notificationStore';
import type {
  AppNotification,
  NotificationFilter,
  NotificationPriority,
  NotificationSettings,
  NotificationType,
} from '@/src/shared/types/notification';

/**
 * Main notifications hook
 */
export function useNotifications() {
  const store = useNotificationStore();

  // Auto-initialize when hook is first used
  useEffect(() => {
    let mounted = true;

    const initializeNotifications = async () => {
      if (!store.isInitializing && !store.permissionState) {
        await store.initialize();
      }
    };

    initializeNotifications();

    return () => {
      mounted = false;
    };
  }, [store.initialize, store.isInitializing, store.permissionState]);

  // Handle app state changes
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active') {
        // Refresh permissions and notifications when app becomes active
        store.refreshPermissions();
        store.refreshNotifications();
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [store.refreshPermissions, store.refreshNotifications]);

  // Memoized selectors
  const notifications = useMemo(() => store.notifications, [store.notifications]);
  const unreadNotifications = useMemo(
    () => notifications.filter(n => n.status === 'unread'),
    [notifications]
  );
  const settings = useMemo(() => store.settings, [store.settings]);
  const permissionState = useMemo(() => store.permissionState, [store.permissionState]);

  // Action creators with useCallback for performance
  const displayNotification = useCallback(
    (
      type: NotificationType,
      title: string,
      message: string,
      options?: {
        priority?: NotificationPriority;
        data?: Record<string, any>;
        actions?: { id: string; label: string }[];
        image?: string;
        avatar?: string;
        deepLink?: string;
      }
    ) => store.displayNotification(type, title, message, options),
    [store.displayNotification]
  );

  const scheduleNotification = useCallback(
    (
      type: NotificationType,
      title: string,
      message: string,
      scheduledFor: Date,
      options?: {
        repeatFrequency?: 'daily' | 'weekly' | 'monthly' | 'yearly';
        data?: Record<string, any>;
        priority?: NotificationPriority;
      }
    ) => store.scheduleNotification(type, title, message, scheduledFor, options),
    [store.scheduleNotification]
  );

  const markAsRead = useCallback(
    (notificationId: string) => store.markAsRead(notificationId),
    [store.markAsRead]
  );

  const markAllAsRead = useCallback(() => store.markAllAsRead(), [store.markAllAsRead]);

  const deleteNotification = useCallback(
    (notificationId: string) => store.deleteNotification(notificationId),
    [store.deleteNotification]
  );

  const updateSettings = useCallback(
    (newSettings: Partial<NotificationSettings>) => store.updateSettings(newSettings),
    [store.updateSettings]
  );

  const requestPermissions = useCallback(
    () => store.requestPermissions(),
    [store.requestPermissions]
  );

  const setFilter = useCallback(
    (filter: NotificationFilter | null) => store.setFilter(filter),
    [store.setFilter]
  );

  const searchNotifications = useCallback(
    (query: string) => store.searchNotifications(query),
    [store.searchNotifications]
  );

  const loadMore = useCallback(() => store.loadMoreNotifications(), [store.loadMoreNotifications]);

  const refresh = useCallback(() => store.refreshNotifications(), [store.refreshNotifications]);

  return {
    // Data
    notifications,
    unreadNotifications,
    scheduledNotifications: store.scheduledNotifications,
    settings,
    permissionState,
    statistics: store.statistics,

    // Counts
    unreadCount: store.unreadCount,
    badgeCount: store.badgeCount,
    totalCount: notifications.length,

    // Loading states
    isLoading: store.isLoading,
    isInitializing: store.isInitializing,
    isRequestingPermission: store.isRequestingPermission,
    isLoadingMore: store.isLoadingMore,
    hasMoreNotifications: store.hasMoreNotifications,

    // Error handling
    error: store.error,
    clearError: store.clearError,

    // Permission management
    isPermissionGranted: permissionState?.granted || false,
    isPermissionDenied: permissionState?.denied || false,
    requestPermissions,
    refreshPermissions: store.refreshPermissions,

    // Notification actions
    displayNotification,
    scheduleNotification,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    clearAllNotifications: store.clearAllNotifications,

    // Settings management
    updateSettings,
    resetSettings: store.resetSettings,

    // Filter and search
    currentFilter: store.currentFilter,
    setFilter,
    searchNotifications,

    // Pagination
    loadMore,
    refresh,

    // Badge management
    setBadgeCount: store.setBadgeCount,
    clearBadge: store.clearBadge,
    updateBadgeCount: store.updateBadgeCount,
  };
}

/**
 * Hook for notification settings management
 */
export function useNotificationSettings() {
  const { settings, updateSettings, resetSettings, isLoading, error, clearError } =
    useNotifications();

  const toggleNotifications = useCallback(
    (enabled: boolean) => updateSettings({ enabled }),
    [updateSettings]
  );

  const toggleSound = useCallback((sound: boolean) => updateSettings({ sound }), [updateSettings]);

  const toggleVibration = useCallback(
    (vibration: boolean) => updateSettings({ vibration }),
    [updateSettings]
  );

  const toggleBadge = useCallback((badge: boolean) => updateSettings({ badge }), [updateSettings]);

  const updateQuietHours = useCallback(
    (quietHours: NotificationSettings['quietHours']) => updateSettings({ quietHours }),
    [updateSettings]
  );

  const updateCategorySettings = useCallback(
    (
      type: NotificationType,
      categorySettings: NotificationSettings['categories'][NotificationType]
    ) =>
      updateSettings({
        categories: {
          ...settings?.categories,
          [type]: categorySettings,
        },
      }),
    [updateSettings, settings?.categories]
  );

  return {
    settings,
    isLoading,
    error,
    clearError,
    updateSettings,
    resetSettings,
    toggleNotifications,
    toggleSound,
    toggleVibration,
    toggleBadge,
    updateQuietHours,
    updateCategorySettings,
  };
}

/**
 * Hook for notification permissions
 */
export function useNotificationPermissions() {
  const {
    permissionState,
    isRequestingPermission,
    requestPermissions,
    refreshPermissions,
    error,
    clearError,
  } = useNotifications();

  const isGranted = useMemo(() => permissionState?.granted || false, [permissionState?.granted]);
  const isDenied = useMemo(() => permissionState?.denied || false, [permissionState?.denied]);
  const isNotDetermined = useMemo(
    () => permissionState?.notDetermined || false,
    [permissionState?.notDetermined]
  );
  const isProvisional = useMemo(
    () => permissionState?.provisional || false,
    [permissionState?.provisional]
  );

  const canShowNotifications = useMemo(
    () => isGranted && permissionState?.settings.alert,
    [isGranted, permissionState?.settings.alert]
  );

  const canShowBadges = useMemo(
    () => isGranted && permissionState?.settings.badge,
    [isGranted, permissionState?.settings.badge]
  );

  const canPlaySounds = useMemo(
    () => isGranted && permissionState?.settings.sound,
    [isGranted, permissionState?.settings.sound]
  );

  return {
    permissionState,
    isGranted,
    isDenied,
    isNotDetermined,
    isProvisional,
    canShowNotifications,
    canShowBadges,
    canPlaySounds,
    isRequestingPermission,
    requestPermissions,
    refreshPermissions,
    error,
    clearError,
  };
}

/**
 * Hook for filtered notifications
 */
export function useFilteredNotifications(filter?: NotificationFilter) {
  const { notifications, setFilter, currentFilter } = useNotifications();

  // Apply filter when it changes
  useEffect(() => {
    if (filter && JSON.stringify(filter) !== JSON.stringify(currentFilter)) {
      setFilter(filter);
    }
  }, [filter, currentFilter, setFilter]);

  return {
    notifications,
    currentFilter,
    setFilter,
  };
}

/**
 * Hook for notification statistics
 */
export function useNotificationStatistics() {
  const store = useNotificationStore();

  useEffect(() => {
    store.loadStatistics();
  }, [store.loadStatistics, store.notifications.length]);

  return {
    statistics: store.statistics,
    isLoading: store.isLoading,
    error: store.error,
    refresh: store.loadStatistics,
  };
}

/**
 * Hook for scheduled notifications
 */
export function useScheduledNotifications() {
  const { scheduledNotifications, scheduleNotification, isLoading, error, clearError } =
    useNotifications();

  const store = useNotificationStore();

  const cancelScheduledNotification = useCallback(
    (notificationId: string) => store.cancelScheduledNotification(notificationId),
    [store.cancelScheduledNotification]
  );

  const refreshScheduled = useCallback(
    () => store.loadScheduledNotifications(),
    [store.loadScheduledNotifications]
  );

  return {
    scheduledNotifications,
    scheduleNotification,
    cancelScheduledNotification,
    refreshScheduled,
    isLoading,
    error,
    clearError,
  };
}
