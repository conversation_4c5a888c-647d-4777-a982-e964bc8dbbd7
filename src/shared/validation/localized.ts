import { z } from 'zod';

import i18n from '@/src/core/i18n/config';

/**
 * Localized validation messages utility
 * Creates Zod error map that uses i18n translations
 */
export function createLocalizedErrorMap(): z.ZodErrorMap {
  return (issue, ctx) => {
    const { path, code } = issue;
    const fieldName = path.join('.');

    switch (code) {
      case z.ZodIssueCode.invalid_type:
        if (issue.expected === 'string') {
          return { message: i18n.t('validation:required', 'This field is required') };
        }
        break;

      case z.ZodIssueCode.too_small:
        if (issue.type === 'string') {
          return {
            message: i18n.t('validation:minLength', 'Must be at least {{min}} characters', {
              min: issue.minimum,
            }),
          };
        }
        if (issue.type === 'number') {
          return {
            message: i18n.t('validation:number.min', 'Value must be at least {{min}}', {
              min: issue.minimum,
            }),
          };
        }
        if (issue.type === 'array') {
          return {
            message: i18n.t('validation:minItems', 'Must have at least {{min}} items', {
              min: issue.minimum,
            }),
          };
        }
        break;

      case z.ZodIssueCode.too_big:
        if (issue.type === 'string') {
          return {
            message: i18n.t('validation:maxLength', 'Must be less than {{max}} characters', {
              max: issue.maximum,
            }),
          };
        }
        if (issue.type === 'number') {
          return {
            message: i18n.t('validation:number.max', 'Value must be at most {{max}}', {
              max: issue.maximum,
            }),
          };
        }
        if (issue.type === 'array') {
          return {
            message: i18n.t('validation:maxItems', 'Must have at most {{max}} items', {
              max: issue.maximum,
            }),
          };
        }
        break;

      case z.ZodIssueCode.invalid_string:
        if (issue.validation === 'email') {
          return {
            message: i18n.t('validation:email.invalid', 'Please enter a valid email address'),
          };
        }
        if (issue.validation === 'url') {
          return { message: i18n.t('validation:url.invalid', 'Please enter a valid URL') };
        }
        break;

      case z.ZodIssueCode.custom:
        if (issue.params?.i18nKey) {
          return {
            message: i18n.t(
              issue.params.i18nKey,
              issue.params.defaultMessage || 'Invalid value',
              issue.params
            ),
          };
        }
        break;
    }

    // Fallback to default message
    return { message: ctx.defaultError };
  };
}

/**
 * Localized validation helpers
 */
export const localizedValidation = {
  /**
   * Required string field with custom message
   */
  requiredString: (messageKey?: string, defaultMessage?: string) =>
    z.string({
      required_error: i18n.t(
        messageKey || 'validation:required',
        defaultMessage || 'This field is required'
      ),
    }),

  /**
   * Email validation with localized messages
   */
  email: () =>
    z
      .string({
        required_error: i18n.t('validation:email.required', 'Email is required'),
      })
      .email(i18n.t('validation:email.invalid', 'Please enter a valid email address')),

  /**
   * Password validation with localized messages
   */
  password: (minLength = 8) =>
    z
      .string({
        required_error: i18n.t('validation:password.required', 'Password is required'),
      })
      .min(
        minLength,
        i18n.t('validation:password.minLength', 'Password must be at least {{min}} characters', {
          min: minLength,
        })
      ),

  /**
   * Name validation with localized messages
   */
  name: (minLength = 1, maxLength = 100) =>
    z
      .string({
        required_error: i18n.t('validation:name.required', 'Name is required'),
      })
      .min(
        minLength,
        i18n.t('validation:name.minLength', 'Name must be at least {{min}} characters', {
          min: minLength,
        })
      )
      .max(
        maxLength,
        i18n.t('validation:name.maxLength', 'Name must be less than {{max}} characters', {
          max: maxLength,
        })
      ),

  /**
   * Username validation with localized messages
   */
  username: (minLength = 3, maxLength = 30) =>
    z
      .string({
        required_error: i18n.t('validation:username.required', 'Username is required'),
      })
      .min(
        minLength,
        i18n.t('validation:username.minLength', 'Username must be at least {{min}} characters', {
          min: minLength,
        })
      )
      .max(
        maxLength,
        i18n.t('validation:username.maxLength', 'Username must be less than {{max}} characters', {
          max: maxLength,
        })
      )
      .regex(
        /^[a-zA-Z0-9_]+$/,
        i18n.t(
          'validation:username.invalid',
          'Username can only contain letters, numbers, and underscores'
        )
      ),

  /**
   * Phone number validation with localized messages
   */
  phone: () =>
    z
      .string({
        required_error: i18n.t('validation:phone.required', 'Phone number is required'),
      })
      .regex(
        /^\+?[\d\s\-\(\)]+$/,
        i18n.t('validation:phone.invalid', 'Please enter a valid phone number')
      ),

  /**
   * URL validation with localized messages
   */
  url: () => z.string().url(i18n.t('validation:url.invalid', 'Please enter a valid URL')),

  /**
   * Custom validation with i18n key
   */
  custom: <T>(
    schema: z.ZodType<T>,
    validator: (value: T) => boolean,
    i18nKey: string,
    defaultMessage: string,
    params?: Record<string, any>
  ) =>
    schema.refine(validator, {
      message: i18n.t(i18nKey, defaultMessage, params),
      params: { i18nKey, defaultMessage, ...params },
    }),
};

/**
 * Set global Zod error map for consistent localized validation
 */
export function initializeLocalizedValidation() {
  z.setErrorMap(createLocalizedErrorMap());
}

/**
 * Update validation messages when language changes
 */
export function updateValidationLocale() {
  z.setErrorMap(createLocalizedErrorMap());
}
