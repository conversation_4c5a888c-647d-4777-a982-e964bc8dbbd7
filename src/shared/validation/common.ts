import { z } from 'zod';

import i18n from '@/src/core/i18n/config';

import { localizedValidation } from './localized';

/**
 * Common validation schemas used across the application
 * All schemas use localized error messages
 */

// Basic string validations
export const requiredString = localizedValidation.requiredString();
export const optionalString = z.string().optional();

// Email validation
export const emailSchema = localizedValidation.email();

// URL validation
export const urlSchema = localizedValidation.url().optional().or(z.literal(''));

// Username validation
export const usernameSchema = localizedValidation.username(3, 30);

// Password validation
export const passwordSchema = localizedValidation
  .password(8)
  .regex(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
    i18n.t(
      'validation:password.weak',
      'Password must contain at least one uppercase letter, one lowercase letter, and one number'
    )
  );

// Phone number validation
export const phoneSchema = localizedValidation.phone().optional().or(z.literal(''));

// Date validation helpers
export const dateSchema = z.string().refine(date => {
  const parsedDate = new Date(date);
  return !isNaN(parsedDate.getTime());
}, 'Please enter a valid date');

export const birthDateSchema = z.string().refine(date => {
  const parsedDate = new Date(date);
  const today = new Date();
  const age = today.getFullYear() - parsedDate.getFullYear();
  return age >= 13 && age <= 120;
}, 'You must be between 13 and 120 years old');

// File validation helpers
export const imageFileSchema = z.object({
  uri: z.string(),
  type: z.string().regex(/^image\/(jpeg|jpg|png|gif|webp)$/, 'Please select a valid image file'),
  size: z.number().max(10 * 1024 * 1024, 'Image must be less than 10MB'),
});

// Generic ID validation
export const idSchema = z.string().min(1, 'ID is required');

// Text length validations
export const shortTextSchema = (maxLength: number = 100) =>
  z.string().max(maxLength, `Text must be less than ${maxLength} characters`);

export const longTextSchema = (maxLength: number = 1000) =>
  z.string().max(maxLength, `Text must be less than ${maxLength} characters`);

// Numeric validations
export const positiveNumberSchema = z
  .number()
  .positive(i18n.t('validation:number.positive', 'Must be a positive number'));
export const nonNegativeNumberSchema = z
  .number()
  .nonnegative(i18n.t('validation:number.positive', 'Must be zero or positive'));

/**
 * Re-export localized validation utilities
 */
export {
  localizedValidation,
  initializeLocalizedValidation,
  updateValidationLocale,
  createLocalizedErrorMap,
} from './localized';
