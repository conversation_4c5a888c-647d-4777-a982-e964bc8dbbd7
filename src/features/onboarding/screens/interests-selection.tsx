import React, { useState } from 'react';

import { ScrollView } from 'react-native';

import { useNavigation } from '@react-navigation/native';
import {
  Airplane,
  Book,
  Camera,
  Code,
  Dumbbell,
  FilmStrip,
  ForkKnife,
  GameController,
  Heartbeat,
  MusicNote,
  PaintBrush,
  ShoppingCart,
} from 'phosphor-react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';

import { Box, Text, useTheme } from '@/src/core/theme';
import { Button, NavigationTopBar } from '@/src/shared/components';

interface Interest {
  id: string;
  name: string;
  icon: React.ReactNode;
  color: string;
}

const interests: Interest[] = [
  { id: 'music', name: 'Music', icon: <MusicNote size={24} />, color: 'primary' },
  { id: 'gaming', name: 'Gaming', icon: <GameController size={24} />, color: 'secondary' },
  { id: 'photography', name: 'Photography', icon: <Camera size={24} />, color: 'tertiary' },
  { id: 'food', name: 'Food & Drink', icon: <ForkKnife size={24} />, color: 'success' },
  { id: 'travel', name: 'Travel', icon: <Airplane size={24} />, color: 'warning' },
  { id: 'reading', name: 'Reading', icon: <Book size={24} />, color: 'error' },
  { id: 'fitness', name: 'Fitness', icon: <Dumbbell size={24} />, color: 'primary' },
  { id: 'art', name: 'Art & Design', icon: <PaintBrush size={24} />, color: 'secondary' },
  { id: 'movies', name: 'Movies & TV', icon: <FilmStrip size={24} />, color: 'tertiary' },
  { id: 'tech', name: 'Technology', icon: <Code size={24} />, color: 'success' },
  { id: 'health', name: 'Health', icon: <Heartbeat size={24} />, color: 'warning' },
  { id: 'shopping', name: 'Shopping', icon: <ShoppingCart size={24} />, color: 'error' },
];

export default function InterestsSelectionScreen() {
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();
  const theme = useTheme();
  const [selectedInterests, setSelectedInterests] = useState<string[]>([]);

  const toggleInterest = (interestId: string) => {
    setSelectedInterests(prev =>
      prev.includes(interestId) ? prev.filter(id => id !== interestId) : [...prev, interestId]
    );
  };

  const handleContinue = () => {
    // Save interests to user profile
    // For now, just navigate to the next screen
    navigation.navigate('ProfileCompletion' as any);
  };

  const handleSkip = () => {
    // Navigate to main app
    navigation.reset({
      index: 0,
      routes: [{ name: 'Main' as any }],
    });
  };

  const isValid = selectedInterests.length >= 3;

  return (
    <SafeAreaView style={{ flex: 1, paddingTop: theme.spacing.sm_12 }} edges={['top']}>
      <Box flex={1} backgroundColor="background">
        <NavigationTopBar
          title="Your Interests"
          rightAction={{ title: 'Skip', onPress: handleSkip }}
        />

        <Box flex={1} paddingHorizontal="lg_24">
          {/* Header */}
          <Box marginBottom="lg_24">
            <Text variant="h_24SemiBold_section" color="text" marginBottom="xs_8">
              What are you interested in?
            </Text>
            <Text variant="b_14Regular_input" color="textSecondary">
              Select at least 3 interests to personalize your experience
            </Text>
          </Box>

          {/* Interests Grid */}
          <ScrollView
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: 100 }}>
            <Box flexDirection="row" flexWrap="wrap" gap="sm_12" marginBottom="lg_24">
              {interests.map(interest => {
                const isSelected = selectedInterests.includes(interest.id);
                return (
                  <InterestChip
                    key={interest.id}
                    interest={interest}
                    isSelected={isSelected}
                    onPress={() => toggleInterest(interest.id)}
                  />
                );
              })}
            </Box>
          </ScrollView>

          {/* Continue Button */}
          <Box
            position="absolute"
            bottom={0}
            left={0}
            right={0}
            paddingHorizontal="lg_24"
            paddingBottom="lg_24"
            backgroundColor="background">
            <Button
              title={`Continue (${selectedInterests.length} selected)`}
              variant="primary"
              onPress={handleContinue}
              enabled={isValid}
            />
          </Box>
        </Box>
      </Box>
    </SafeAreaView>
  );
}

interface InterestChipProps {
  interest: Interest;
  isSelected: boolean;
  onPress: () => void;
}

function InterestChip({ interest, isSelected, onPress }: InterestChipProps) {
  const theme = useTheme();

  return (
    <Box
      borderRadius="pill_9999"
      paddingVertical="sm_12"
      paddingHorizontal="md_16"
      backgroundColor={isSelected ? 'primary' : 'surfaceBackground'}
      borderWidth={1}
      borderColor={isSelected ? 'primary' : 'borderSubtle'}
      flexDirection="row"
      alignItems="center"
      gap="xs_8"
      onTouchEnd={onPress}>
      {React.cloneElement(interest.icon as React.ReactElement, {
        color: isSelected ? theme.colors.white : theme.colors.iconPrimary,
      })}
      <Text variant="b_14Medium_button" color={isSelected ? 'white' : 'text'}>
        {interest.name}
      </Text>
    </Box>
  );
}
