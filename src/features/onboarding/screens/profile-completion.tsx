import React, { useState } from 'react';

import { Alert, Image, KeyboardAvoidingView, Platform, ScrollView } from 'react-native';

import { useNavigation } from '@react-navigation/native';
import { Camera, Plus } from 'phosphor-react-native';
import { Controller } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import { z } from 'zod';

import { i18n } from '@/src/core';
import { Box, Text, useTheme } from '@/src/core/theme';
import TextInput from '@/src/core/theme/TextInput';
import { useAuth } from '@/src/features/auth/services';
import { Button, NavigationTopBar } from '@/src/shared/components';
import { useZodForm } from '@/src/shared/forms/useZodForm';

const profileSchema = () =>
  z.object({
    displayName: z
      .string()
      .min(2, { message: i18n.t('validation.displayName.minLength') })
      .max(30, { message: i18n.t('validation.displayName.maxLength') }),
    bio: z
      .string()
      .max(160, { message: i18n.t('validation.bio.maxLength') })
      .optional(),
    username: z
      .string()
      .min(3, { message: i18n.t('validation.username.minLength') })
      .max(20, { message: i18n.t('validation.username.maxLength') })
      .regex(/^[a-zA-Z0-9_]+$/, { message: i18n.t('validation.username.invalid') }),
  });

export default function ProfileCompletionScreen() {
  const navigation = useNavigation();
  const { t } = useTranslation();
  const insets = useSafeAreaInsets();
  const theme = useTheme();
  const { user } = useAuth();

  const [profileImage, setProfileImage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const { control, handleSubmit, formState, trigger, watch } = useZodForm(profileSchema(), {
    mode: 'onBlur',
    reValidateMode: 'onChange',
    defaultValues: {
      displayName: user?.name || '',
      bio: '',
      username: '',
    },
  });

  const displayNameValue = watch('displayName');
  const usernameValue = watch('username');

  const isFormValid = formState.isValid && displayNameValue && usernameValue;

  const handleSelectPhoto = () => {
    // TODO: Implement photo selection using expo-image-picker
    Alert.alert('Photo Selection', 'Photo selection not yet implemented');
  };

  const handleCompleteProfile = async (data: {
    displayName: string;
    bio?: string;
    username: string;
  }) => {
    if (isLoading) return;

    try {
      setIsLoading(true);

      // TODO: Call API to update user profile
      console.log('Profile data:', { ...data, profileImage });

      // Navigate to main app
      navigation.reset({
        index: 0,
        routes: [{ name: 'Main' as any }],
      });
    } catch (error) {
      const message = error instanceof Error ? error.message : i18n.t('tryAgain');
      Alert.alert(i18n.t('error'), message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSkip = () => {
    navigation.reset({
      index: 0,
      routes: [{ name: 'Main' as any }],
    });
  };

  return (
    <SafeAreaView style={{ flex: 1, paddingTop: theme.spacing.sm_12 }} edges={['top']}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        <Box flex={1} backgroundColor="background">
          <NavigationTopBar
            title="Complete Profile"
            trailingActions={[{ title: 'Skip', onPress: handleSkip } as any]}
          />

          <ScrollView
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{
              flexGrow: 1,
              paddingHorizontal: 24,
              paddingBottom: Math.max(insets.bottom, 24),
            }}>
            {/* Profile Photo */}
            <Box alignItems="center" marginBottom="xl_32">
              <Box
                width={120}
                height={120}
                borderRadius="circle_9999"
                backgroundColor="surfaceBackground"
                justifyContent="center"
                alignItems="center"
                borderWidth={2}
                borderColor="borderSubtle"
                marginBottom="md_16"
                onTouchEnd={handleSelectPhoto}>
                {profileImage ? (
                  <Image
                    source={{ uri: profileImage }}
                    style={{ width: '100%', height: '100%', borderRadius: 60 }}
                  />
                ) : (
                  <Camera size={40} color={theme.colors.iconSecondary} />
                )}

                {/* Add Photo Badge */}
                <Box
                  position="absolute"
                  bottom={0}
                  right={0}
                  width={36}
                  height={36}
                  borderRadius="circle_9999"
                  backgroundColor="primary"
                  justifyContent="center"
                  alignItems="center"
                  borderWidth={3}
                  borderColor="background">
                  <Plus size={18} color={theme.colors.white} weight="bold" />
                </Box>
              </Box>

              <Button title="Add Photo" variant="ghost" onPress={handleSelectPhoto} />
            </Box>

            {/* Form */}
            <Box gap="lg_24">
              <Controller
                control={control}
                name="displayName"
                render={({ field }) => (
                  <TextInput
                    label={t('profile.displayNameLabel')}
                    placeholder={t('profile.displayNamePlaceholder')}
                    autoCapitalize="words"
                    autoComplete="name"
                    autoCorrect={false}
                    enterKeyHint="next"
                    onChangeText={value => {
                      field.onChange(value);
                      if (formState.errors.displayName?.message) {
                        trigger('displayName');
                      }
                    }}
                    error={formState.errors.displayName?.message}
                    onBlur={field.onBlur}
                    value={field.value}
                  />
                )}
              />

              <Controller
                control={control}
                name="username"
                render={({ field }) => (
                  <TextInput
                    label={t('profile.usernameLabel')}
                    placeholder={t('profile.usernamePlaceholder')}
                    autoCapitalize="none"
                    autoComplete="username"
                    autoCorrect={false}
                    enterKeyHint="next"
                    onChangeText={value => {
                      field.onChange(value);
                      if (formState.errors.username?.message) {
                        trigger('username');
                      }
                    }}
                    error={formState.errors.username?.message}
                    onBlur={field.onBlur}
                    value={field.value}
                    leftIcon="At"
                  />
                )}
              />

              <Controller
                control={control}
                name="bio"
                render={({ field }) => (
                  <TextInput
                    label={t('profile.bioLabel')}
                    placeholder={t('profile.bioPlaceholder')}
                    autoCapitalize="sentences"
                    autoCorrect={true}
                    enterKeyHint="done"
                    multiline
                    numberOfLines={3}
                    maxLength={160}
                    onChangeText={value => {
                      field.onChange(value);
                      if (formState.errors.bio?.message) {
                        trigger('bio');
                      }
                    }}
                    error={formState.errors.bio?.message}
                    onBlur={field.onBlur}
                    value={field.value}
                  />
                )}
              />
            </Box>

            {/* Complete Button */}
            <Box marginTop="xl_32">
              <Button
                title="Complete Profile"
                variant="primary"
                onPress={() => {
                  handleSubmit(handleCompleteProfile)();
                }}
                enabled={isFormValid && !isLoading}
                loading={isLoading}
              />
            </Box>
          </ScrollView>
        </Box>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
