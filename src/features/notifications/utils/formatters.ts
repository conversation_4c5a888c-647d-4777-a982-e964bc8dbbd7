import { differenceInHours, format, formatDistanceToNow, isToday, isYesterday } from 'date-fns';

export const formatNotificationTime = (timestamp: Date): string => {
  const now = new Date();
  const diffInHours = differenceInHours(now, timestamp);

  // If less than 24 hours ago, show relative time
  if (diffInHours < 24) {
    return formatDistanceToNow(timestamp, { addSuffix: true });
  }

  // If yesterday
  if (isYesterday(timestamp)) {
    return 'Yesterday';
  }

  // If today (but more than 24 hours somehow)
  if (isToday(timestamp)) {
    return format(timestamp, 'HH:mm');
  }

  // Otherwise show date and time
  return format(timestamp, 'dd MMM, yyyy');
};

export const formatNotificationDateTime = (timestamp: Date): string => {
  return `${format(timestamp, 'dd MMM, yyyy')} | ${format(timestamp, 'HH:mm')} PM`;
};

export const getNotificationIconColor = (variant: string, isRead: boolean): string => {
  if (isRead) {
    return 'iconMuted';
  }

  switch (variant) {
    case 'account-success':
      return 'successMain';
    case 'booking-success':
      return 'brandMain';
    case 'new-services':
      return 'warningMain';
    default:
      return 'iconPrimary';
  }
};

export const getNotificationBadgeColor = (isNew: boolean): string => {
  return isNew ? 'brandMain' : 'transparent';
};
