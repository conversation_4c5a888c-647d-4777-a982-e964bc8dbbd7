import React, { memo, useCallback } from 'react';

import { CalendarDots, CaretRight, MapPin, ShieldCheck, Star, Ticket } from 'phosphor-react-native';

import { Box, Pressable, Text, useResponsive, useTheme } from '@/src/core/theme';
import { Avatar, Button, Chip, Divider } from '@/src/shared/components';

import { Notification } from '../types/notification.types';
import { formatNotificationDateTime, formatNotificationTime } from '../utils/formatters';

interface NotificationItemProps {
  notification: Notification;
  onPress?: (notification: Notification) => void;
  onAction?: (notificationId: string, actionType: string) => void;
  showDivider?: boolean;
}

const NotificationItemComponent: React.FC<NotificationItemProps> = ({
  notification,
  onPress,
  onAction,
  showDivider = true,
}) => {
  const theme = useTheme();
  const { isTablet } = useResponsive();
  const iconSize = isTablet ? 24 : 20;

  const handlePress = useCallback(() => {
    onPress?.(notification);
  }, [notification, onPress]);

  const handleAction = useCallback(
    (actionType: string) => {
      onAction?.(notification.id, actionType);
    },
    [notification.id, onAction]
  );

  const renderIcon = () => {
    const iconColor = notification.isRead ? theme.colors.iconMuted : theme.colors.iconDefault;

    switch (notification.variant) {
      case 'account-success':
        return (
          <Box
            width={isTablet ? 48 : 40}
            height={isTablet ? 48 : 40}
            borderRadius="circle_9999"
            backgroundColor="successLight"
            justifyContent="center"
            alignItems="center">
            <ShieldCheck size={iconSize} weight="bold" color={theme.colors.successMain} />
          </Box>
        );
      case 'booking-success':
        return (
          <Box
            width={isTablet ? 48 : 40}
            height={isTablet ? 48 : 40}
            borderRadius="circle_9999"
            backgroundColor="brandLight"
            justifyContent="center"
            alignItems="center">
            <CalendarDots size={iconSize} weight="bold" color={theme.colors.brandMain} />
          </Box>
        );
      case 'new-services':
        return (
          <Box
            width={isTablet ? 48 : 40}
            height={isTablet ? 48 : 40}
            borderRadius="circle_9999"
            backgroundColor="warningLight"
            justifyContent="center"
            alignItems="center">
            <Ticket size={iconSize} weight="bold" color={theme.colors.warningMain} />
          </Box>
        );
      case 'party-invite':
      case 'saved-event':
        return <Avatar size="s" source={{ uri: notification.fromUser.avatar }} />;
      default:
        return null;
    }
  };

  const renderTimeInfo = () => {
    const timeText = formatNotificationTime(notification.timestamp);
    const userInfo = 'fromUser' in notification ? `from: ${notification.fromUser.name}` : null;

    if (userInfo) {
      return (
        <Box flexDirection="row" alignItems="center" gap="xxs_4">
          <Text variant="l_12Regular_helperText" color="secondaryText">
            {userInfo}
          </Text>
          <Text variant="l_12Regular_helperText" color="mutedText">
            |
          </Text>
          <Text variant="l_12Regular_helperText" color="secondaryText">
            {timeText}
          </Text>
        </Box>
      );
    }

    return (
      <Text variant="l_12Regular_helperText" color="secondaryText">
        {formatNotificationDateTime(notification.timestamp)}
      </Text>
    );
  };

  const renderLocationInfo = () => {
    if (notification.variant !== 'party-invite' || !notification.location) return null;

    return (
      <Box
        flexDirection="row"
        alignItems="center"
        justifyContent="space-between"
        paddingVertical="sm_12"
        paddingHorizontal="md_16"
        backgroundColor="subtleBackground"
        borderRadius="md_12"
        marginTop="sm_12">
        <Box flexDirection="row" alignItems="center" gap="sm_12" flex={1}>
          <Box flexDirection="row" alignItems="center" gap="xs_8">
            <MapPin size={16} color={theme.colors.iconDefault} />
            <Text variant="b_14Regular_content" color="mutedText">
              {notification.location.name}
            </Text>
          </Box>
          {notification.location.distance && (
            <Chip
              chipVariant="solidSmall"
              leftIcon={<Star size={12} weight="fill" color={theme.colors.warningMain} />}
              label={notification.location.distance}
            />
          )}
        </Box>
        <CaretRight size={16} color={theme.colors.iconMuted} />
      </Box>
    );
  };

  const renderActions = () => {
    if (notification.variant !== 'party-invite' || !notification.actions?.length) return null;

    return (
      <Box flexDirection="row" gap="xs_8" marginTop="sm_12">
        {notification.actions.map(action => (
          <Button
            key={action.type}
            variant={action.variant === 'primary' ? 'primary' : 'outline'}
            onPress={() => handleAction(action.type)}
            title={action.label}
          />
        ))}
      </Box>
    );
  };

  const renderNewBadge = () => {
    if (notification.variant !== 'booking-success' || !notification.isNew) return null;

    return <Button variant="primary" title="New" />;
  };

  const renderContent = () => {
    const hasLocationOrActions =
      notification.variant === 'party-invite' &&
      (notification.location || notification.actions?.length);
    const isCompactVariant = notification.variant === 'saved-event';

    if (hasLocationOrActions) {
      return (
        <Box flex={1}>
          <Box flexDirection="row" alignItems="flex-start" gap="sm_12" flex={1}>
            {renderIcon()}
            <Box flex={1} gap="xxs_4">
              <Text
                variant="b_14SemiBold_listTitle"
                color={notification.isRead ? 'secondaryText' : 'mainText'}
                numberOfLines={2}>
                {notification.title}
              </Text>
              {renderTimeInfo()}
            </Box>
          </Box>
          {renderLocationInfo()}
          {renderActions()}
        </Box>
      );
    }

    return (
      <Box flexDirection="row" alignItems="flex-start" gap="sm_12" flex={1}>
        {renderIcon()}
        <Box flex={1} gap="xxs_4">
          <Box flexDirection="row" justifyContent="space-between" alignItems="flex-start">
            <Box flex={1} gap="xxs_4">
              <Text
                variant="b_14SemiBold_listTitle"
                color={notification.isRead ? 'secondaryText' : 'mainText'}
                numberOfLines={2}>
                {notification.title}
              </Text>
              {renderTimeInfo()}
            </Box>
            {renderNewBadge()}
          </Box>
          {notification.description && !isCompactVariant && (
            <Text
              variant="b_14Regular_content"
              color="mutedText"
              numberOfLines={3}
              marginTop="xs_8">
              {notification.description}
            </Text>
          )}
        </Box>
      </Box>
    );
  };

  return (
    <>
      <Pressable onPress={handlePress} activeOpacity={0}>
        <Box paddingVertical="sm_12" paddingHorizontal="sm_12">
          {renderContent()}
        </Box>
      </Pressable>
      {showDivider && <Divider />}
    </>
  );
};

export const NotificationItem = memo(NotificationItemComponent);
