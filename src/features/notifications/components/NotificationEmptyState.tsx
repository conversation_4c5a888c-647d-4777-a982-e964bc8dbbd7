import React from 'react';

import { ClipboardText } from 'phosphor-react-native';
import { useTranslation } from 'react-i18next';

import { Box, Text, useResponsive, useTheme } from '@/src/core/theme';

export const NotificationEmptyState: React.FC = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const { isTablet } = useResponsive();

  return (
    <Box flex={1} justifyContent="center" alignItems="center" paddingHorizontal="xl_32" gap="lg_24">
      <Box
        width={isTablet ? 64 : 56}
        height={isTablet ? 64 : 56}
        borderRadius="circle_9999"
        backgroundColor="brandMain"
        justifyContent="center"
        alignItems="center">
        <ClipboardText size={isTablet ? 40 : 32} weight="bold" color={theme.colors.white} />
      </Box>

      <Box alignItems="center" gap="sm_12">
        <Text
          variant={isTablet ? 'h_24SemiBold_section' : 'h_20Medium_subsection'}
          color="mainText">
          {t('notifications.empty.title')}
        </Text>
        <Text variant="b_14Regular_content" color="secondaryText" textAlign="center">
          {t('notifications.empty.description')}
        </Text>
      </Box>
    </Box>
  );
};
