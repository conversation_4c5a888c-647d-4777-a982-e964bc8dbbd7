import React from 'react';

import { Box } from '@/src/core/theme';
import { Card } from '@/src/shared/components';

import { NotificationGroup as NotificationGroupType } from '../types/notification.types';
import { NotificationItem } from './NotificationItem';

interface NotificationGroupProps {
  group: NotificationGroupType;
  onNotificationPress?: (notification: any) => void;
  onNotificationAction?: (notificationId: string, actionType: string) => void;
}

export const NotificationGroup: React.FC<NotificationGroupProps> = ({
  group,
  onNotificationPress,
  onNotificationAction,
}) => {
  return (
    <Card showToolbar toolbarTitle={group.title} px="none_0">
      <Box>
        {group.notifications.map((notification, index) => (
          <NotificationItem
            key={notification.id}
            notification={notification}
            onPress={onNotificationPress}
            onAction={onNotificationAction}
            showDivider={index < group.notifications.length - 1}
          />
        ))}
      </Box>
    </Card>
  );
};
