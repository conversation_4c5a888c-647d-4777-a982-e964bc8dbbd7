import {
  AccountSuccessNotification,
  BookingSuccessNotification,
  NewServicesNotification,
  Notification,
  PartyInviteNotification,
  SavedEventNotification,
} from '../types';

// Helper to create timestamps
const createTimestamp = (hoursAgo: number): Date => {
  const date = new Date();
  date.setHours(date.getHours() - hoursAgo);
  return date;
};

const createDateTimestamp = (daysAgo: number, hour: number, minute: number): Date => {
  const date = new Date();
  date.setDate(date.getDate() - daysAgo);
  date.setHours(hour, minute, 0, 0);
  return date;
};

export const mockNotifications: Notification[] = [
  // New category notifications
  {
    id: '1',
    variant: 'party-invite',
    title: 'You were invited to a party',
    timestamp: createTimestamp(1),
    isRead: false,
    category: 'new',
    fromUser: {
      id: 'user-1',
      name: '<PERSON>',
      avatar: 'https://i.pravatar.cc/150?u=isaac',
    },
    location: {
      id: 'loc-1',
      name: 'Skyline lounge',
      distance: '2.3 Km',
    },
    actions: [],
  } as PartyInviteNotification,

  {
    id: '2',
    variant: 'saved-event',
    title: '<PERSON> saved your event',
    timestamp: createTimestamp(5),
    isRead: false,
    category: 'new',
    fromUser: {
      id: 'user-1',
      name: 'Isaac Newton',
      avatar: 'https://i.pravatar.cc/150?u=isaac',
    },
  } as SavedEventNotification,

  {
    id: '3',
    variant: 'account-success',
    title: 'Account Setup Successful!',
    description: 'Your account creation is successful, you can now experience our services.',
    timestamp: createDateTimestamp(14, 20, 49),
    isRead: false,
    category: 'new',
    iconType: 'shield-check',
  } as AccountSuccessNotification,

  // Earlier category notifications
  {
    id: '4',
    variant: 'saved-event',
    title: 'Isaac Newton saved your event',
    timestamp: createTimestamp(5),
    isRead: true,
    category: 'earlier',
    fromUser: {
      id: 'user-1',
      name: 'Isaac Newton',
      avatar: 'https://i.pravatar.cc/150?u=isaac',
    },
  } as SavedEventNotification,

  {
    id: '5',
    variant: 'party-invite',
    title: 'You were invited to a party',
    timestamp: createTimestamp(5),
    isRead: true,
    category: 'earlier',
    fromUser: {
      id: 'user-1',
      name: 'Isaac Newton',
      avatar: 'https://i.pravatar.cc/150?u=isaac',
    },
    location: {
      id: 'loc-1',
      name: 'Skyline lounge',
      distance: '2.3 Km',
    },
    actions: [
      {
        type: 'reject',
        label: 'Reject',
        variant: 'outline',
      },
      {
        type: 'accept',
        label: 'Accept',
        variant: 'primary',
      },
    ],
  } as PartyInviteNotification,

  {
    id: '6',
    variant: 'booking-success',
    title: 'Booking Successful!',
    description:
      "You have successfully booked the Art Workshops. The event will be held on Sunday, December 22, 2022, 13.00 - 14.00 PM. Don't forget to activate your reminder. Enjoy the event!",
    timestamp: createDateTimestamp(14, 20, 49),
    isRead: true,
    category: 'earlier',
    eventDetails: {
      name: 'Art Workshops',
      date: 'Sunday, December 22, 2022',
      time: '13.00 - 14.00 PM',
    },
    iconType: 'calendar',
    isNew: true,
  } as BookingSuccessNotification,

  {
    id: '7',
    variant: 'new-services',
    title: 'New Services Available!',
    description: 'You can now make multiple book events at once. You can also cancel your booking.',
    timestamp: createDateTimestamp(14, 20, 49),
    isRead: true,
    category: 'earlier',
    features: ['Multiple bookings', 'Cancel bookings'],
    iconType: 'ticket',
  } as NewServicesNotification,
];

// Helper function to group notifications by category
export const groupNotificationsByCategory = (notifications: Notification[]) => {
  const grouped = notifications.reduce(
    (acc, notification) => {
      const category = notification.category;
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(notification);
      return acc;
    },
    {} as Record<string, Notification[]>
  );

  return [
    { category: 'new', title: 'New', notifications: grouped.new || [] },
    { category: 'earlier', title: 'Earlier', notifications: grouped.earlier || [] },
  ].filter(group => group.notifications.length > 0);
};
