/**
 * Notification Types
 * Based on Figma design variants
 */

export type NotificationVariant =
  | 'party-invite'
  | 'saved-event'
  | 'account-success'
  | 'booking-success'
  | 'new-services';

export type NotificationCategory = 'new' | 'earlier';

export type NotificationActionType = 'accept' | 'reject' | 'view' | 'new' | 'confirm';

export interface NotificationLocation {
  id: string;
  name: string;
  distance?: string;
  address?: string;
}

export interface NotificationAction {
  type: NotificationActionType;
  label: string;
  variant: 'primary' | 'outline';
}

export interface BaseNotification {
  id: string;
  variant: NotificationVariant;
  title: string;
  description?: string;
  timestamp: Date;
  isRead: boolean;
  category: NotificationCategory;
}

export interface PartyInviteNotification extends BaseNotification {
  variant: 'party-invite';
  fromUser: {
    id: string;
    name: string;
    avatar?: string;
  };
  location: NotificationLocation;
  actions: NotificationAction[];
}

export interface SavedEventNotification extends BaseNotification {
  variant: 'saved-event';
  fromUser: {
    id: string;
    name: string;
    avatar?: string;
  };
}

export interface AccountSuccessNotification extends BaseNotification {
  variant: 'account-success';
  iconType: 'shield-check';
}

export interface BookingSuccessNotification extends BaseNotification {
  variant: 'booking-success';
  eventDetails: {
    name: string;
    date: string;
    time: string;
  };
  iconType: 'calendar';
  isNew: boolean;
}

export interface NewServicesNotification extends BaseNotification {
  variant: 'new-services';
  features: string[];
  iconType: 'ticket';
}

export type Notification =
  | PartyInviteNotification
  | SavedEventNotification
  | AccountSuccessNotification
  | BookingSuccessNotification
  | NewServicesNotification;

export interface NotificationGroup {
  category: NotificationCategory;
  title: string;
  notifications: Notification[];
}

export interface NotificationFilters {
  unreadOnly: boolean;
  categories: NotificationCategory[];
  variants: NotificationVariant[];
}
