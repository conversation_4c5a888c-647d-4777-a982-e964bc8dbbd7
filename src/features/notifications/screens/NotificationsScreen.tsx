import React, { useCallback, useState } from 'react';

import { RefreshControl } from 'react-native';

import { useBottomTabBarHeight } from '@react-navigation/bottom-tabs';
import { FlashList } from '@shopify/flash-list';

import { toast as showToast } from '@/src/core/libs';
import { Box, useTheme } from '@/src/core/theme';

import { NotificationEmptyState, NotificationGroup } from '../components';
import { groupNotificationsByCategory, mockNotifications } from '../data/mockNotifications';

const NotificationsScreen: React.FC = () => {
  const theme = useTheme();
  const bottomTabBarHeight = useBottomTabBarHeight();
  const [refreshing, setRefreshing] = useState(false);
  const [notifications] = useState(mockNotifications);

  const groupedNotifications = groupNotificationsByCategory(notifications);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
    showToast.success('Notifications refreshed');
  }, []);

  const handleNotificationPress = useCallback((notification: Notification) => {
    console.log('Notification pressed:', notification);
    // TODO: Navigate to relevant screen based on notification type
  }, []);

  const handleNotificationAction = useCallback((notificationId: string, actionType: string) => {
    console.log('Notification action:', notificationId, actionType);

    switch (actionType) {
      case 'accept':
        showToast.success('Invitation accepted');
        break;
      case 'reject':
        showToast.info('Invitation rejected');
        break;
      case 'view':
        // Navigate to detail
        break;
      default:
        break;
    }
  }, []);

  if (notifications.length === 0) {
    return (
      <Box flex={1}>
        <NotificationEmptyState />
      </Box>
    );
  }

  return (
    <Box flex={1}>
      <FlashList
        data={groupedNotifications}
        contentContainerStyle={{ paddingBottom: bottomTabBarHeight, paddingHorizontal: 16 }}
        renderItem={({ item }) => (
          <NotificationGroup
            group={item}
            onNotificationPress={handleNotificationPress}
            onNotificationAction={handleNotificationAction}
          />
        )}
        keyExtractor={item => item.category}
        estimatedItemSize={200}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />}
      />
    </Box>
  );
};

export default NotificationsScreen;
