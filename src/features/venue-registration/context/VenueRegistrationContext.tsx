import React, { ReactNode, createContext, useCallback, useContext, useMemo } from 'react';

import { NativeStackNavigationProp } from '@react-navigation/native-stack';

import { toast } from '@/src/core/libs';
import { VenueRegistrationStackParamList } from '@/src/core/navigation/types';

import { useVenueRegistrationStore } from '../store/venueRegistrationStore';

interface VenueRegistrationContextValue {
  // Navigation
  navigateToStep: (
    step: number,
    navigation: NativeStackNavigationProp<VenueRegistrationStackParamList>
  ) => void;
  navigateNext: (navigation: NativeStackNavigationProp<VenueRegistrationStackParamList>) => void;
  navigatePrevious: (
    navigation: NativeStackNavigationProp<VenueRegistrationStackParamList>
  ) => void;
  syncCurrentStep: (screenName: string) => void;

  // Form actions
  canProceedToNext: () => boolean;
  handleStepComplete: (
    navigation: NativeStackNavigationProp<VenueRegistrationStackParamList>
  ) => void;
  handleExitRegistration: (
    navigation: NativeStackNavigationProp<VenueRegistrationStackParamList>
  ) => void;

  // Draft actions
  handleSaveDraft: () => Promise<void>;
  handleDeleteDraft: (draftId: string) => Promise<void>;
}

const VenueRegistrationContext = createContext<VenueRegistrationContextValue | undefined>(
  undefined
);

interface VenueRegistrationProviderProps {
  children: ReactNode;
}

export const VenueRegistrationProvider: React.FC<VenueRegistrationProviderProps> = ({
  children,
}) => {
  const { currentStep, setCurrentStep, validateStep, saveDraft, deleteDraft } =
    useVenueRegistrationStore();

  const stepScreens = useMemo(
    () => ['VenueBasicDetails', 'VenueOpeningTimes', 'VenueFeatures', 'VenueSummary'],
    []
  );

  const navigateToStep = useCallback(
    (step: number, navigation: NativeStackNavigationProp<VenueRegistrationStackParamList>) => {
      if (step >= 0 && step < stepScreens.length) {
        setCurrentStep(step);
        navigation.navigate(stepScreens[step] as any);
      }
    },
    [setCurrentStep, stepScreens]
  );

  const navigateNext = useCallback(
    (navigation: NativeStackNavigationProp<VenueRegistrationStackParamList>) => {
      if (currentStep < stepScreens.length - 1) {
        const nextStepIndex = currentStep + 1;
        setCurrentStep(nextStepIndex);
        navigation.navigate(stepScreens[nextStepIndex] as any);
      }
    },
    [currentStep, stepScreens, setCurrentStep]
  );

  const navigatePrevious = useCallback(
    (navigation: NativeStackNavigationProp<VenueRegistrationStackParamList>) => {
      if (currentStep > 0) {
        const prevStepIndex = currentStep - 1;
        setCurrentStep(prevStepIndex);
      }

      navigation.goBack();
    },
    [currentStep, setCurrentStep]
  );

  const canProceedToNext = useCallback(() => {
    return validateStep(currentStep);
  }, [currentStep, validateStep]);

  const handleStepComplete = useCallback(
    (navigation: NativeStackNavigationProp<VenueRegistrationStackParamList>) => {
      if (canProceedToNext()) {
        navigateNext(navigation);
      } else {
        toast.error('Please complete all required fields');
      }
    },
    [canProceedToNext, navigateNext]
  );

  const handleExitRegistration = useCallback(
    (navigation: NativeStackNavigationProp<VenueRegistrationStackParamList>) => {
      // Show confirmation dialog using magicModal
      // For now, just navigate back
      navigation.goBack();
    },
    []
  );

  const handleSaveDraft = useCallback(async () => {
    try {
      saveDraft();
      toast.success('Draft saved successfully');
    } catch {
      toast.error('Failed to save draft');
    }
  }, [saveDraft]);

  const handleDeleteDraft = useCallback(
    async (draftId: string) => {
      try {
        deleteDraft(draftId);
        toast.success('Draft deleted');
      } catch {
        toast.error('Failed to delete draft');
      }
    },
    [deleteDraft]
  );

  const syncCurrentStep = useCallback(
    (screenName: string) => {
      const stepIndex = stepScreens.findIndex(screen => screen === screenName);
      if (stepIndex !== -1 && stepIndex !== currentStep) {
        setCurrentStep(stepIndex);
      }
    },
    [stepScreens, currentStep, setCurrentStep]
  );

  const value: VenueRegistrationContextValue = {
    navigateToStep,
    navigateNext,
    navigatePrevious,
    syncCurrentStep,
    canProceedToNext,
    handleStepComplete,
    handleExitRegistration,
    handleSaveDraft,
    handleDeleteDraft,
  };

  return (
    <VenueRegistrationContext.Provider value={value}>{children}</VenueRegistrationContext.Provider>
  );
};

export const useVenueRegistration = () => {
  const context = useContext(VenueRegistrationContext);
  if (!context) {
    throw new Error('useVenueRegistration must be used within VenueRegistrationProvider');
  }
  return context;
};
