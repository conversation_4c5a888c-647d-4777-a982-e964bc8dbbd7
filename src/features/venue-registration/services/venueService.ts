import { apolloClient } from '@/src/core/api';
import { GraphQLErrorResponse } from '@/src/core/api/errors';

import { ServiceResponse } from '../../event-registration/services/eventService';
import {
  ACTIVATE_VENUE_MUTATION,
  CREATE_VENUE_MUTATION,
  DELETE_VENUE_MUTATION,
  UPDATE_VENUE_MUTATION,
  UPLOAD_VENUE_IMAGES_MUTATION,
  VERIFY_VENUE_MUTATION,
} from '../graphql/mutations';
import {
  GET_VENUES_NEARBY_QUERY,
  GET_VENUES_QUERY,
  GET_VENUE_IMAGES_QUERY,
  GET_VENUE_QUERY,
  GET_VENUE_TYPES_QUERY,
  SEARCH_VENUES_QUERY,
} from '../graphql/queries';
import { VenueFormData } from '../types';

// API Types based on actual GraphQL schema
export interface Venue {
  id: string;
  name: string;
  description?: string;
  address: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  latitude?: number;
  longitude?: number;
  phone?: string;
  email?: string;
  website?: string;
  capacity?: number;
  rating: number;
  reviewCount: number;
  isActive: boolean;
  venueTypeId?: number;
  venueType?: VenueTypeHierarchy;
  content?: VenueContent;
  distance?: number;
  createdAt: string;
  updatedAt: string;
}

export interface VenueTypeHierarchy {
  id: number;
  name: string;
  description?: string;
  icon?: string;
  parent?: {
    id: number;
    name: string;
  };
  children?: {
    id: number;
    name: string;
    description?: string;
  }[];
}

export interface VenueContent {
  primaryImage?: string;
  images?: string[];
  description?: string;
  tags?: string[];
  amenities?: string[];
}

export interface CreateVenueInput {
  name: string;
  description?: string;
  address: string;
  city: string;
  state: string;
  country: string;
  postalCode?: string;
  latitude: number;
  longitude: number;
  capacity?: number;
  venueType: string; // VenueType enum
  venueTypeId?: number;
  tagIds?: number[];
  amenities?: string[];
  tags?: string[];
}

export interface UpdateVenueInput {
  name?: string;
  description?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  latitude?: number;
  longitude?: number;
  capacity?: number;
  venueType?: string;
  venueTypeId?: number;
  tagIds?: number[];
  amenities?: string[];
  tags?: string[];
}

export interface VenueSearchInput {
  query?: string;
  latitude?: number;
  longitude?: number;
  radius?: number;
  city?: string;
  state?: string;
  country?: string;
  venueTypes?: string[];
  tags?: string[];
  minRating?: number;
  minCapacity?: number;
  maxCapacity?: number;
  limit?: number;
  offset?: number;
}

export interface VenueSearchResult {
  venues: Venue[];
  totalCount: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface VenueImage {
  id: string;
  url: string;
  isPrimary: boolean;
  order: number;
  createdAt: string;
}

// Venue Service Class
class VenueService {
  async createVenue(data: VenueFormData): Promise<ServiceResponse<Venue>> {
    try {
      const input: CreateVenueInput = {
        name: data.name,
        description: data.description,
        address: data.location?.address || '',
        city: data.location?.city || '',
        state: data.location?.state || '',
        country: data.location?.country || '',
        postalCode: data.location?.postalCode,
        latitude: data.location?.latitude || 0,
        longitude: data.location?.longitude || 0,
        capacity: data.capacity,
        venueType: 'NIGHTCLUB', // Default - should be mapped from data
        amenities: data.features,
        tags: data.musicGenres,
      };

      const { data: responseData, errors } = await apolloClient.mutate({
        mutation: CREATE_VENUE_MUTATION,
        variables: { input },
      });

      if (responseData?.createVenue) {
        return {
          success: true,
          data: responseData.createVenue,
        };
      }

      return {
        success: false,
        errors: errors?.map(error => ({
          message: error.message,
          extensions: {
            code: error.extensions?.code || 'UNKNOWN_ERROR',
            statusCode: error.extensions?.statusCode || 500,
          },
        })) as GraphQLErrorResponse<any>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  async updateVenue(
    venueId: string,
    data: Partial<VenueFormData>
  ): Promise<ServiceResponse<Venue>> {
    try {
      const input: UpdateVenueInput = {
        ...(data.name && { name: data.name }),
        ...(data.description && { description: data.description }),
        ...(data.location?.address && { address: data.location.address }),
        ...(data.location?.city && { city: data.location.city }),
        ...(data.location?.state && { state: data.location.state }),
        ...(data.location?.country && { country: data.location.country }),
        ...(data.location?.postalCode && { postalCode: data.location.postalCode }),
        ...(data.location?.latitude !== undefined && { latitude: data.location.latitude }),
        ...(data.location?.longitude !== undefined && { longitude: data.location.longitude }),
        ...(data.capacity !== undefined && { capacity: data.capacity }),
        ...(data.features && { amenities: data.features }),
        ...(data.musicGenres && { tags: data.musicGenres }),
      };

      const { data: responseData, errors } = await apolloClient.mutate({
        mutation: UPDATE_VENUE_MUTATION,
        variables: { id: venueId, input },
      });

      if (responseData?.updateVenue) {
        return {
          success: true,
          data: responseData.updateVenue,
        };
      }

      return {
        success: false,
        errors: errors?.map(error => ({
          message: error.message,
          extensions: {
            code: error.extensions?.code || 'UNKNOWN_ERROR',
            statusCode: error.extensions?.statusCode || 500,
          },
        })) as GraphQLErrorResponse<any>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  async getVenue(venueId: string): Promise<ServiceResponse<Venue>> {
    try {
      const { data: responseData } = await apolloClient.query({
        query: GET_VENUE_QUERY,
        variables: { id: venueId },
        fetchPolicy: 'cache-first',
      });

      if (responseData?.venue) {
        return {
          success: true,
          data: responseData.venue,
        };
      }

      return {
        success: false,
        errors: [
          {
            message: 'Venue not found',
            extensions: {
              code: 'VENUE_NOT_FOUND',
              statusCode: 404,
            },
          },
        ],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  async deleteVenue(venueId: string): Promise<ServiceResponse<boolean>> {
    try {
      const { data: responseData, errors } = await apolloClient.mutate({
        mutation: DELETE_VENUE_MUTATION,
        variables: { id: venueId },
      });

      if (responseData?.deleteVenue) {
        return {
          success: true,
          data: responseData.deleteVenue,
        };
      }

      return {
        success: false,
        errors: errors?.map(error => ({
          message: error.message,
          extensions: {
            code: error.extensions?.code || 'UNKNOWN_ERROR',
            statusCode: error.extensions?.statusCode || 500,
          },
        })) as GraphQLErrorResponse<any>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  async getVenues(limit?: number, offset?: number): Promise<ServiceResponse<Venue[]>> {
    try {
      const { data: responseData } = await apolloClient.query({
        query: GET_VENUES_QUERY,
        variables: { limit, offset },
        fetchPolicy: 'cache-first',
      });

      if (responseData?.venues) {
        return {
          success: true,
          data: responseData.venues,
        };
      }

      return {
        success: false,
        errors: [
          {
            message: 'Failed to fetch venues',
            extensions: {
              code: 'VENUES_FETCH_ERROR',
              statusCode: 500,
            },
          },
        ],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  async searchVenues(input: VenueSearchInput): Promise<ServiceResponse<VenueSearchResult>> {
    try {
      const { data: responseData } = await apolloClient.query({
        query: SEARCH_VENUES_QUERY,
        variables: { input },
        fetchPolicy: 'cache-first',
      });

      if (responseData?.searchVenues) {
        return {
          success: true,
          data: responseData.searchVenues,
        };
      }

      return {
        success: false,
        errors: [
          {
            message: 'Failed to search venues',
            extensions: {
              code: 'VENUES_SEARCH_ERROR',
              statusCode: 500,
            },
          },
        ],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  async getVenuesNearby(
    latitude: number,
    longitude: number,
    radius?: number,
    limit?: number
  ): Promise<ServiceResponse<Venue[]>> {
    try {
      const { data: responseData } = await apolloClient.query({
        query: GET_VENUES_NEARBY_QUERY,
        variables: { latitude, longitude, radius, limit },
        fetchPolicy: 'cache-first',
      });

      if (responseData?.venuesNearby) {
        return {
          success: true,
          data: responseData.venuesNearby,
        };
      }

      return {
        success: false,
        errors: [
          {
            message: 'Failed to fetch nearby venues',
            extensions: {
              code: 'VENUES_NEARBY_ERROR',
              statusCode: 500,
            },
          },
        ],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  async getVenueTypes(): Promise<ServiceResponse<VenueTypeHierarchy[]>> {
    try {
      const { data: responseData } = await apolloClient.query({
        query: GET_VENUE_TYPES_QUERY,
        fetchPolicy: 'cache-first',
      });

      if (responseData?.venueTypes) {
        return {
          success: true,
          data: responseData.venueTypes,
        };
      }

      return {
        success: false,
        errors: [
          {
            message: 'Failed to fetch venue types',
            extensions: {
              code: 'VENUE_TYPES_ERROR',
              statusCode: 500,
            },
          },
        ],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  async uploadVenueImages(venueId: string, images: File[]): Promise<ServiceResponse<string[]>> {
    try {
      const { data: responseData, errors } = await apolloClient.mutate({
        mutation: UPLOAD_VENUE_IMAGES_MUTATION,
        variables: { venueId, images },
      });

      if (responseData?.uploadVenueImages) {
        return {
          success: true,
          data: responseData.uploadVenueImages,
        };
      }

      return {
        success: false,
        errors: errors?.map(error => ({
          message: error.message,
          extensions: {
            code: error.extensions?.code || 'UNKNOWN_ERROR',
            statusCode: error.extensions?.statusCode || 500,
          },
        })) as GraphQLErrorResponse<any>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  async getVenueImages(venueId: string): Promise<ServiceResponse<VenueImage[]>> {
    try {
      const { data: responseData } = await apolloClient.query({
        query: GET_VENUE_IMAGES_QUERY,
        variables: { venueId },
        fetchPolicy: 'cache-first',
      });

      if (responseData?.venueImages) {
        return {
          success: true,
          data: responseData.venueImages,
        };
      }

      return {
        success: false,
        errors: [
          {
            message: 'Failed to fetch venue images',
            extensions: {
              code: 'VENUE_IMAGES_ERROR',
              statusCode: 500,
            },
          },
        ],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  async activateVenue(venueId: string): Promise<ServiceResponse<Venue>> {
    try {
      const { data: responseData, errors } = await apolloClient.mutate({
        mutation: ACTIVATE_VENUE_MUTATION,
        variables: { venueId },
      });

      if (responseData?.activateVenue) {
        return {
          success: true,
          data: responseData.activateVenue,
        };
      }

      return {
        success: false,
        errors: errors?.map(error => ({
          message: error.message,
          extensions: {
            code: error.extensions?.code || 'UNKNOWN_ERROR',
            statusCode: error.extensions?.statusCode || 500,
          },
        })) as GraphQLErrorResponse<any>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  async verifyVenue(venueId: string): Promise<ServiceResponse<Venue>> {
    try {
      const { data: responseData, errors } = await apolloClient.mutate({
        mutation: VERIFY_VENUE_MUTATION,
        variables: { venueId },
      });

      if (responseData?.verifyVenue) {
        return {
          success: true,
          data: responseData.verifyVenue,
        };
      }

      return {
        success: false,
        errors: errors?.map(error => ({
          message: error.message,
          extensions: {
            code: error.extensions?.code || 'UNKNOWN_ERROR',
            statusCode: error.extensions?.statusCode || 500,
          },
        })) as GraphQLErrorResponse<any>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }
}

// Export service instance
export const venueService = new VenueService();

// Export individual functions for backward compatibility
export const createVenue = (data: VenueFormData) => venueService.createVenue(data);
export const updateVenue = (venueId: string, data: Partial<VenueFormData>) =>
  venueService.updateVenue(venueId, data);
export const getVenue = (venueId: string) => venueService.getVenue(venueId);
