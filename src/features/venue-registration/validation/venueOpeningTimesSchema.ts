import { z } from 'zod';

// Time format validation (HH:mm)
const timeFormatRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;

// Opening hours schema
const openingHoursSchema = z
  .object({
    day: z.enum(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']),
    open: z.string().regex(timeFormatRegex, 'Invalid time format (HH:mm)'),
    close: z.string().regex(timeFormatRegex, 'Invalid time format (HH:mm)'),
    isClosed: z.boolean().optional(),
  })
  .refine(
    data => {
      if (data.isClosed) return true;

      const [openHour, openMin] = data.open.split(':').map(Number);
      const [closeHour, closeMin] = data.close.split(':').map(Number);

      const openMinutes = openHour * 60 + openMin;
      const closeMinutes = closeHour * 60 + closeMin;

      // Allow closing time to be next day (e.g., 23:00 - 02:00)
      return true;
    },
    {
      message: 'Invalid opening hours',
    }
  );

// Special hours schema
const specialHoursSchema = z.object({
  date: z.string().refine(date => {
    const d = new Date(date);
    return d instanceof Date && !isNaN(d.getTime());
  }, 'Invalid date'),
  name: z.string().min(1, 'Name is required'),
  open: z.string().regex(timeFormatRegex, 'Invalid time format (HH:mm)'),
  close: z.string().regex(timeFormatRegex, 'Invalid time format (HH:mm)'),
  isClosed: z.boolean().optional(),
});

// Happy hours schema
const happyHoursSchema = z
  .object({
    enabled: z.boolean(),
    days: z.array(
      z.enum(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'])
    ),
    startTime: z.string().regex(timeFormatRegex, 'Invalid time format (HH:mm)'),
    endTime: z.string().regex(timeFormatRegex, 'Invalid time format (HH:mm)'),
  })
  .optional();

// Main opening times schema
export const venueOpeningTimesSchema = z.object({
  regularHours: z
    .array(openingHoursSchema)
    .min(7, 'All days must be configured')
    .max(7, 'All days must be configured')
    .refine(hours => {
      const days = hours.map(h => h.day);
      const uniqueDays = new Set(days);
      return uniqueDays.size === 7;
    }, 'All days of the week must be configured'),

  specialHours: z.array(specialHoursSchema).optional().default([]),

  happyHours: happyHoursSchema,
});

export type VenueOpeningTimesFormData = z.infer<typeof venueOpeningTimesSchema>;
