import { z } from 'zod';

import { DayOfWeek } from '../types';

// Basic venue details schema
export const venueBasicDetailsSchema = z.object({
  name: z
    .string()
    .min(3, 'Venue name must be at least 3 characters')
    .max(100, 'Venue name must be less than 100 characters'),

  description: z
    .string()
    .min(20, 'Description must be at least 20 characters')
    .max(1000, 'Description must be less than 1000 characters'),

  musicGenres: z
    .array(z.string())
    .min(1, 'Select at least one music genre')
    .max(10, 'Maximum 10 music genres allowed'),

  dressCodes: z
    .array(z.string())
    .min(1, 'Select at least one dress code')
    .max(5, 'Maximum 5 dress codes allowed'),

  location: z
    .object({
      address: z.string().min(1, 'Address is required'),
      city: z.string().min(1, 'City is required'),
      state: z.string().min(1, 'State is required'),
      country: z.string().min(1, 'Country is required'),
      postalCode: z.string().min(1, 'Postal code is required'),
      latitude: z.number(),
      longitude: z.number(),
      foursquareId: z.string().optional(),
      formattedAddress: z.string().optional(),
    })
    .nullable()
    .refine(val => val !== null, {
      message: 'Location is required',
    }),

  photos: z
    .array(z.any()) // ImagePickerAsset type
    .min(1, 'Add at least one photo')
    .max(10, 'Maximum 10 photos allowed'),
});

export type VenueBasicDetailsFormData = z.infer<typeof venueBasicDetailsSchema>;
