import { z } from 'zod';

// Venue features schema
export const venueFeaturesSchema = z.object({
  features: z
    .array(z.string())
    .min(1, 'Select at least one feature')
    .max(20, 'Maximum 20 features allowed'),

  capacity: z
    .number()
    .min(10, 'Capacity must be at least 10')
    .max(10000, 'Capacity must be less than 10,000')
    .optional(),

  minimumAge: z
    .number()
    .min(18, 'Minimum age must be at least 18')
    .max(25, 'Minimum age must be less than 25')
    .optional()
    .default(18),

  // Contact information (optional)
  phoneNumber: z
    .string()
    .regex(/^\+?[1-9]\d{1,14}$/, 'Invalid phone number')
    .optional(),

  email: z.string().email('Invalid email address').optional(),

  website: z.string().url('Invalid website URL').optional().or(z.literal('')),

  instagram: z
    .string()
    .regex(/^@?[a-zA-Z0-9_.]+$/, 'Invalid Instagram handle')
    .optional()
    .or(z.literal('')),
});

export type VenueFeaturesFormData = z.infer<typeof venueFeaturesSchema>;
