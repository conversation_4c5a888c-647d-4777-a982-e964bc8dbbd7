import React, { useCallback } from 'react';

import { Platform } from 'react-native';

import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { Controller } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller';
import Animated, { FadeIn } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { VenueRegistrationNavigationProps } from '@/src/core/navigation/types';
import { Box, SafeAreaWrapper, Text, TextInput, useTheme } from '@/src/core/theme';
import { Button, NavigationTopBar } from '@/src/shared/components';
import { useZodForm } from '@/src/shared/forms/useZodForm';

import { VenueFeaturesSelector } from '../components/VenueFeaturesSelector';
import { VenueProgressIndicator } from '../components/VenueProgressIndicator';
import { useVenueRegistration } from '../context/VenueRegistrationContext';
import { useVenueRegistrationStore } from '../store/venueRegistrationStore';
import { VenueFeaturesFormData, venueFeaturesSchema } from '../validation';

export const VenueFeaturesScreen: React.FC = () => {
  const { t } = useTranslation();
  const { formData, updateFeatures } = useVenueRegistrationStore();
  const { navigateNext, navigatePrevious, syncCurrentStep } = useVenueRegistration();
  const navigation = useNavigation<VenueRegistrationNavigationProps>();

  const insets = useSafeAreaInsets();
  const theme = useTheme();

  // Sync current step when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      syncCurrentStep('VenueFeatures');
    }, [syncCurrentStep])
  );

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    watch,
    trigger,
  } = useZodForm(venueFeaturesSchema, {
    mode: 'onBlur', // Errors only show when user leaves field
    reValidateMode: 'onChange', // Clear errors immediately when fixed
    defaultValues: {
      features: formData.features || [],
      capacity: formData.capacity || undefined,
      minimumAge: formData.minimumAge || 18,
      phoneNumber: formData.phoneNumber || '',
      email: formData.email || '',
      website: formData.website || '',
      instagram: formData.instagram || '',
    },
  });

  // Watch form values for real-time validation
  const watchedValues = watch();

  // Use Zod safeParse for button enabling (doesn't affect form error state)
  const isInputValid = React.useMemo(() => {
    const result = venueFeaturesSchema.safeParse({
      features: watchedValues.features || [],
      capacity: watchedValues.capacity,
      minimumAge: watchedValues.minimumAge || 18,
      phoneNumber: watchedValues.phoneNumber || '',
      email: watchedValues.email || '',
      website: watchedValues.website || '',
      instagram: watchedValues.instagram || '',
    });
    return result.success;
  }, [
    watchedValues.features,
    watchedValues.capacity,
    watchedValues.minimumAge,
    watchedValues.phoneNumber,
    watchedValues.email,
    watchedValues.website,
    watchedValues.instagram,
  ]);

  // Enable button when input passes validation OR form is officially valid
  const isFormValid = isValid || isInputValid;

  const onSubmit = useCallback(
    (data: VenueFeaturesFormData) => {
      updateFeatures(data);
      navigateNext(navigation);
    },
    [updateFeatures, navigateNext, navigation]
  );

  const handleBack = useCallback(() => {
    navigatePrevious(navigation);
  }, [navigatePrevious, navigation]);

  return (
    <SafeAreaWrapper
      edges={['top']}
      style={{ paddingBottom: Platform.OS === 'ios' ? insets.bottom : 0 }}>
      <NavigationTopBar title="Venue Registration" onPress={handleBack} />
      <Animated.View
        entering={FadeIn.duration(300)}
        style={{ flex: 1, padding: theme.spacing.md_16 }}>
        <KeyboardAwareScrollView
          contentContainerStyle={{
            flexGrow: 1,
            paddingBottom: insets.bottom + theme.spacing.md_16,
          }}
          bottomOffset={Platform.OS === 'ios' ? 0 : 20}
          keyboardShouldPersistTaps="handled"
          extraKeyboardSpace={-20}
          showsVerticalScrollIndicator={false}>
          <VenueProgressIndicator currentStep={2} totalSteps={4} />
          <Box paddingVertical="md_16">
            <Text variant="h_32SemiBold_Page" marginBottom="lg_24">
              {t('venue.registration.features.title')}
            </Text>

            {/* Features Selection */}
            <Box marginBottom="lg_24">
              <Text variant="h_20Medium_subsection" marginBottom="md_16">
                {t('venue.registration.features.clubFeatures')}
              </Text>
              <Controller
                control={control}
                name="features"
                render={({ field: { onChange, value } }) => (
                  <VenueFeaturesSelector
                    selectedFeatures={value}
                    onFeaturesChange={features => {
                      onChange(features);
                      // Only trigger validation if there's an error (to clear it immediately)
                      if (errors.features?.message) {
                        trigger('features');
                      }
                    }}
                    error={errors.features?.message}
                  />
                )}
              />
            </Box>

            {/* Capacity */}
            <Box marginBottom="md_16">
              <Controller
                control={control}
                name="capacity"
                render={({ field: { onChange, onBlur, value } }) => (
                  <TextInput
                    label={t('venue.registration.features.capacity')}
                    placeholder={t('venue.registration.features.capacityPlaceholder')}
                    value={value?.toString() || ''}
                    onChangeText={text => {
                      onChange(text ? parseInt(text, 10) : undefined);
                      // Only trigger validation if there's an error (to clear it immediately)
                      if (errors.capacity?.message) {
                        trigger('capacity');
                      }
                    }}
                    onBlur={onBlur}
                    keyboardType="numeric"
                    error={errors.capacity?.message}
                  />
                )}
              />
            </Box>
            {/* Phone Number */}
            <Box marginBottom="md_16">
              <Controller
                control={control}
                name="phoneNumber"
                render={({ field: { onChange, onBlur, value } }) => (
                  <TextInput
                    label={t('venue.registration.features.phoneNumber')}
                    placeholder={t('venue.registration.features.phoneNumberPlaceholder')}
                    value={value || ''}
                    onChangeText={text => {
                      onChange(text);
                      // Only trigger validation if there's an error (to clear it immediately)
                      if (errors.phoneNumber?.message) {
                        trigger('phoneNumber');
                      }
                    }}
                    onBlur={onBlur}
                    keyboardType="phone-pad"
                    error={errors.phoneNumber?.message}
                  />
                )}
              />
            </Box>

            {/* Email */}
            <Box marginBottom="md_16">
              <Controller
                control={control}
                name="email"
                render={({ field: { onChange, onBlur, value } }) => (
                  <TextInput
                    label={t('venue.registration.features.email')}
                    placeholder={t('venue.registration.features.emailPlaceholder')}
                    value={value || ''}
                    onChangeText={text => {
                      onChange(text);
                      // Only trigger validation if there's an error (to clear it immediately)
                      if (errors.email?.message) {
                        trigger('email');
                      }
                    }}
                    onBlur={onBlur}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    error={errors.email?.message}
                  />
                )}
              />
            </Box>

            {/* Website */}
            <Box marginBottom="md_16">
              <Controller
                control={control}
                name="website"
                render={({ field: { onChange, onBlur, value } }) => (
                  <TextInput
                    label={t('venue.registration.features.website')}
                    placeholder={t('venue.registration.features.websitePlaceholder')}
                    value={value || ''}
                    onChangeText={text => {
                      onChange(text);
                      // Only trigger validation if there's an error (to clear it immediately)
                      if (errors.website?.message) {
                        trigger('website');
                      }
                    }}
                    onBlur={onBlur}
                    keyboardType="url"
                    autoCapitalize="none"
                    error={errors.website?.message}
                  />
                )}
              />
            </Box>

            {/* Instagram */}
            <Box marginBottom="xxxl_48">
              <Controller
                control={control}
                name="instagram"
                render={({ field: { onChange, onBlur, value } }) => (
                  <TextInput
                    label={t('venue.registration.features.instagram')}
                    placeholder={t('venue.registration.features.instagramPlaceholder')}
                    value={value || ''}
                    onChangeText={text => {
                      onChange(text);
                      // Only trigger validation if there's an error (to clear it immediately)
                      if (errors.instagram?.message) {
                        trigger('instagram');
                      }
                    }}
                    onBlur={onBlur}
                    autoCapitalize="none"
                    error={errors.instagram?.message}
                  />
                )}
              />
            </Box>
          </Box>
        </KeyboardAwareScrollView>
      </Animated.View>

      {/* Floating Action Buttons */}
      <Box
        position="absolute"
        bottom={Platform.OS === 'ios' ? insets.bottom : 0}
        left={0}
        right={0}
        backgroundColor="background"
        paddingHorizontal="ml_20"
        paddingTop="md_16"
        style={{
          borderTopWidth: 1,
          borderTopColor: theme.colors.border,
          paddingBottom: insets.bottom / 2,
        }}>
        <Box flexDirection="row" gap="sm_12">
          <Box flex={1}>
            <Button variant="outline" onPress={handleBack} title={t('common.back')} />
          </Box>
          <Box flex={1}>
            <Button
              variant="primary"
              onPress={() => handleSubmit(onSubmit)()}
              enabled={isFormValid}
              title={t('common.next')}
            />
          </Box>
        </Box>
      </Box>
    </SafeAreaWrapper>
  );
};
