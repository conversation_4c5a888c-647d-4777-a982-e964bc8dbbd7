import React, { useCallback } from 'react';

import { Platform } from 'react-native';

import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { ArrowRight, CheckCircle } from 'phosphor-react-native';
import { useTranslation } from 'react-i18next';
import Animated, { FadeIn } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import {
  VenueRegistrationNavigationProps,
  VenueRegistrationStackParamList,
} from '@/src/core/navigation/types';
import { Box, SafeAreaWrapper, Text, useTheme } from '@/src/core/theme';
import { Button, NavigationTopBar } from '@/src/shared/components';

type SuccessRouteProp = RouteProp<VenueRegistrationStackParamList, 'VenueSuccess'>;

export const VenueSuccessScreen: React.FC = () => {
  const { t } = useTranslation();
  const route = useRoute<SuccessRouteProp>();
  const navigation = useNavigation<VenueRegistrationNavigationProps>();
  const { venueId } = route.params;

  const insets = useSafeAreaInsets();
  const theme = useTheme();

  const handleViewVenue = useCallback(() => {
    // Navigate to venue details
    // navigation.navigate('VenueDetails', { venueId });
  }, [venueId]);

  const handleDone = useCallback(() => {
    // Navigate to home or venues list
    navigation.navigate('Home' as any);
  }, [navigation]);

  const handleBack = useCallback(() => {
    navigation.goBack();
  }, [navigation]);

  return (
    <SafeAreaWrapper
      edges={['top']}
      style={{ paddingBottom: Platform.OS === 'ios' ? insets.bottom : 0 }}>
      <NavigationTopBar title="Venue Registration" onPress={handleBack} />
      <Animated.View
        entering={FadeIn.duration(300)}
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          paddingHorizontal: theme.spacing.ml_20,
          paddingBottom: Math.max(insets.bottom, theme.spacing.md_16),
        }}>
        {/* Success Animation */}
        <Box marginBottom="xl_32">
          {/* You can add a Lottie animation here */}
          <Box
            width={120}
            height={120}
            borderRadius="circle_9999"
            backgroundColor="success"
            justifyContent="center"
            alignItems="center">
            <CheckCircle size={80} color="#ffffff" weight="fill" />
          </Box>
        </Box>

        {/* Success Message */}
        <Text variant="h_32SemiBold_Page" textAlign="center" marginBottom="md_16">
          {t('venue.registration.success.title')}
        </Text>

        <Text
          variant="b_16Regular_input"
          textAlign="center"
          color="textSecondary"
          marginBottom="xl_32"
          paddingHorizontal="lg_24">
          {t('venue.registration.success.message')}
        </Text>

        {/* Action Buttons */}
        <Box width="100%" gap="sm_12">
          <Button
            variant="primary"
            onPress={handleViewVenue}
            title={t('venue.registration.success.viewVenue')}
            rightIcon={<ArrowRight size={20} />}
          />

          <Button variant="outline" onPress={handleDone} title={t('common.done')} />
        </Box>
      </Animated.View>
    </SafeAreaWrapper>
  );
};
