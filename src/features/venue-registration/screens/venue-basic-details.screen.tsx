import React, { useCallback } from 'react';

import { Platform } from 'react-native';

import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { Controller } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { VenueRegistrationNavigationProps } from '@/src/core/navigation/types';
import {
  AnimatedContainer,
  Box,
  SafeAreaWrapper,
  Text,
  TextInput,
  useTheme,
} from '@/src/core/theme';
import { Button, NavigationTopBar } from '@/src/shared/components';
import { useZodForm } from '@/src/shared/forms/useZodForm';

import { MultiSelectInput } from '../components/MultiSelectInput';
import { VenueLocationPicker } from '../components/VenueLocationPicker';
import { VenueMediaUpload } from '../components/VenueMediaUpload';
import { VenueProgressIndicator } from '../components/VenueProgressIndicator';
import { useVenueRegistration } from '../context/VenueRegistrationContext';
import { useVenueRegistrationStore } from '../store/venueRegistrationStore';
import { VenueBasicDetailsFormData, venueBasicDetailsSchema } from '../validation';

// Predefined options
const musicGenreOptions = [
  'House',
  'Techno',
  'Hip Hop',
  'R&B',
  'Reggaeton',
  'Pop',
  'Rock',
  'Electronic',
  'Latin',
  'Trap',
  'Funk',
  'Samba',
  'Pagode',
  'Sertanejo',
  'Forró',
  'MPB',
  'Axé',
];

const dressCodeOptions = [
  'Casual',
  'Smart Casual',
  'Business Casual',
  'Cocktail',
  'Formal',
  'Beach Wear',
  'No Dress Code',
];

export const VenueBasicDetailsScreen: React.FC = () => {
  const { t } = useTranslation();
  const { formData, updateBasicDetails } = useVenueRegistrationStore();
  const { navigatePrevious, navigateNext, syncCurrentStep } = useVenueRegistration();
  const navigation = useNavigation<VenueRegistrationNavigationProps>();

  const insets = useSafeAreaInsets();
  const theme = useTheme();

  // Sync current step when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      syncCurrentStep('VenueBasicDetails');
    }, [syncCurrentStep])
  );

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    watch,
    trigger,
    reset,
  } = useZodForm(venueBasicDetailsSchema, {
    mode: 'onBlur', // Errors only show when user leaves field
    reValidateMode: 'onChange', // Clear errors immediately when fixed
    defaultValues: {
      name: formData.name || '',
      description: formData.description || '',
      musicGenres: formData.musicGenres || [],
      dressCodes: formData.dressCodes || [],
      location: formData.location || undefined,
      photos: formData.photos || [],
    },
  });

  // Watch form values for real-time validation
  const watchedValues = watch();

  // Use Zod safeParse for button enabling (doesn't affect form error state)
  const isInputValid = React.useMemo(() => {
    const result = venueBasicDetailsSchema.safeParse({
      name: watchedValues.name || '',
      description: watchedValues.description || '',
      musicGenres: watchedValues.musicGenres || [],
      dressCodes: watchedValues.dressCodes || [],
      location: watchedValues.location || null,
      photos: watchedValues.photos || [],
    });
    return result.success;
  }, [
    watchedValues.name,
    watchedValues.description,
    watchedValues.musicGenres,
    watchedValues.dressCodes,
    watchedValues.location,
    watchedValues.photos,
  ]);

  // Enable button when input passes validation OR form is officially valid
  const isFormValid = isValid || isInputValid;

  const onSubmit = useCallback(
    (data: VenueBasicDetailsFormData) => {
      updateBasicDetails(data);
      navigateNext(navigation);
    },
    [updateBasicDetails, navigateNext, navigation]
  );

  const handleBack = useCallback(() => {
    navigatePrevious(navigation);
  }, [navigatePrevious, navigation]);

  return (
    <SafeAreaWrapper
      edges={['top']}
      style={{ paddingBottom: Platform.OS === 'ios' ? insets.bottom : 0 }}>
      <NavigationTopBar
        title="Venue Registration"
        onPress={handleBack}
        trailingActions={
          <Box marginRight="md_16">
            <Button variant="ghost" title={'Reset'} onPress={() => reset()} />
          </Box>
        }
      />
      <AnimatedContainer padding="md_16">
        <KeyboardAwareScrollView
          contentContainerStyle={{
            flexGrow: 1,
            paddingBottom: insets.bottom + theme.spacing.md_16,
          }}
          bottomOffset={Platform.OS === 'ios' ? 0 : 20}
          keyboardShouldPersistTaps="handled"
          extraKeyboardSpace={-20}
          showsVerticalScrollIndicator={false}>
          <VenueProgressIndicator currentStep={0} totalSteps={4} />
          <Box paddingVertical="md_16">
            <Text variant="h_32SemiBold_Page" marginBottom="lg_24">
              {t('venue.registration.basicDetails.title')}
            </Text>

            {/* Media Upload */}
            <Box marginBottom="lg_24">
              <Controller
                control={control}
                name="photos"
                render={({ field: { onChange, value } }) => (
                  <VenueMediaUpload
                    photos={value}
                    onPhotosChange={photos => {
                      onChange(photos);
                      // Only trigger validation if there's an error (to clear it immediately)
                      if (errors.photos?.message) {
                        trigger('photos');
                      }
                    }}
                    error={errors.photos?.message}
                  />
                )}
              />
            </Box>

            {/* Venue Name */}
            <Box marginBottom="md_16">
              <Controller
                control={control}
                name="name"
                render={({ field: { onChange, onBlur, value } }) => (
                  <TextInput
                    label={t('venue.registration.basicDetails.venueName')}
                    placeholder={t('venue.registration.basicDetails.venueNamePlaceholder')}
                    value={value}
                    onChangeText={text => {
                      onChange(text);
                      // Only trigger validation if there's an error (to clear it immediately)
                      if (errors.name?.message) {
                        trigger('name');
                      }
                    }}
                    onBlur={onBlur}
                    error={errors.name?.message}
                  />
                )}
              />
            </Box>

            {/* Music Genres */}
            <Box marginBottom="md_16">
              <Controller
                control={control}
                name="musicGenres"
                render={({ field: { onChange, value } }) => (
                  <MultiSelectInput
                    label={t('venue.registration.basicDetails.musicGenres')}
                    placeholder={t('venue.registration.basicDetails.musicGenresPlaceholder')}
                    options={musicGenreOptions}
                    selectedValues={value}
                    onSelectionChange={selection => {
                      onChange(selection);
                      // Only trigger validation if there's an error (to clear it immediately)
                      if (errors.musicGenres?.message) {
                        trigger('musicGenres');
                      }
                    }}
                    error={errors.musicGenres?.message}
                  />
                )}
              />
            </Box>

            {/* Dress Code */}
            <Box marginBottom="md_16">
              <Controller
                control={control}
                name="dressCodes"
                render={({ field: { onChange, value } }) => (
                  <MultiSelectInput
                    label={t('venue.registration.basicDetails.dressCode')}
                    placeholder={t('venue.registration.basicDetails.dressCodePlaceholder')}
                    options={dressCodeOptions}
                    selectedValues={value}
                    onSelectionChange={selection => {
                      onChange(selection);
                      // Only trigger validation if there's an error (to clear it immediately)
                      if (errors.dressCodes?.message) {
                        trigger('dressCodes');
                      }
                    }}
                    error={errors.dressCodes?.message}
                  />
                )}
              />
            </Box>

            {/* Description */}
            <Box marginBottom="md_16">
              <Controller
                control={control}
                name="description"
                render={({ field: { onChange, onBlur, value } }) => (
                  <TextInput
                    label={t('venue.registration.basicDetails.description')}
                    placeholder={t('venue.registration.basicDetails.descriptionPlaceholder')}
                    value={value}
                    onChangeText={text => {
                      onChange(text);
                      // Only trigger validation if there's an error (to clear it immediately)
                      if (errors.description?.message) {
                        trigger('description');
                      }
                    }}
                    onBlur={onBlur}
                    multiline
                    numberOfLines={4}
                    maxLength={1000}
                    error={errors.description?.message}
                  />
                )}
              />
            </Box>

            {/* Location */}
            <Box marginBottom="xxxl_48">
              <Controller
                control={control}
                name="location"
                render={({ field: { onChange, value } }) => (
                  <VenueLocationPicker
                    location={value}
                    onLocationChange={location => {
                      onChange(location);
                      // Only trigger validation if there's an error (to clear it immediately)
                      if (errors.location?.message) {
                        trigger('location');
                      }
                    }}
                    error={errors.location?.message}
                  />
                )}
              />
            </Box>
          </Box>
        </KeyboardAwareScrollView>
      </AnimatedContainer>

      {/* Floating Action Buttons */}
      <Box
        position="absolute"
        bottom={Platform.OS === 'ios' ? insets.bottom : 0}
        left={0}
        right={0}
        backgroundColor="background"
        paddingHorizontal="ml_20"
        paddingTop="md_16"
        style={{
          borderTopWidth: 1,
          borderTopColor: theme.colors.border,
          paddingBottom: insets.bottom / 2,
        }}>
        <Box flexDirection="row" gap="sm_12">
          <Box flex={1}>
            <Button variant="outline" onPress={() => handleBack()} title={t('common.back')} />
          </Box>
          <Box flex={1}>
            <Button
              variant="primary"
              onPress={() => handleSubmit(onSubmit)()}
              enabled={isFormValid}
              title={t('common.next')}
            />
          </Box>
        </Box>
      </Box>
    </SafeAreaWrapper>
  );
};
