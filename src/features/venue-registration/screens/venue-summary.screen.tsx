import React, { useCallback, useState } from 'react';

import { Platform } from 'react-native';

import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { PencilSimple } from 'phosphor-react-native';
import { useTranslation } from 'react-i18next';
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller';
import Animated, { FadeIn } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { VenueRegistrationNavigationProps } from '@/src/core/navigation/types';
import { AnimatedContainer, Box, SafeAreaWrapper, Text, useTheme } from '@/src/core/theme';
import { Button, Chip, Divider, NavigationTopBar } from '@/src/shared/components';
import { useLocalizedDays } from '@/src/shared/hooks/useLocalizedDays';

import { VenuePreviewCard } from '../components/VenuePreviewCard';
import { VenueProgressIndicator } from '../components/VenueProgressIndicator';
import { useVenueRegistration } from '../context/VenueRegistrationContext';
import { useCreateVenue } from '../hooks/useCreateVenue';
import { useVenueRegistrationStore } from '../store/venueRegistrationStore';

export const VenueSummaryScreen: React.FC = () => {
  const { t } = useTranslation();
  const { formData } = useVenueRegistrationStore();
  const { navigatePrevious, syncCurrentStep } = useVenueRegistration();
  const { mutate: createVenue, isPending } = useCreateVenue();
  const [editSection, setEditSection] = useState<string | null>(null);
  const navigation = useNavigation<VenueRegistrationNavigationProps>();
  const { getDayName } = useLocalizedDays();

  const insets = useSafeAreaInsets();
  const theme = useTheme();

  // Sync current step when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      syncCurrentStep('VenueSummary');
    }, [syncCurrentStep])
  );

  const handleSubmit = useCallback(() => {
    console.log('Submitting venue registration:', formData);
    createVenue(formData);
  }, [createVenue, formData]);

  const handleEditSection = useCallback((section: string) => {
    // Navigate to specific section
    // For now, just track which section was clicked
    setEditSection(section);
  }, []);

  const handleBack = useCallback(() => {
    navigatePrevious(navigation);
  }, [navigatePrevious, navigation]);

  return (
    <SafeAreaWrapper
      edges={['top']}
      style={{
        paddingBottom: Platform.OS === 'ios' ? insets.bottom : 0,
      }}>
      <NavigationTopBar title="Venue Registration" onPress={handleBack} />
      <AnimatedContainer padding="md_16">
        <KeyboardAwareScrollView
          contentContainerStyle={{
            flexGrow: 1,
            paddingBottom: insets.bottom + theme.spacing.huge_64,
          }}
          bottomOffset={Platform.OS === 'ios' ? 0 : 20}
          keyboardShouldPersistTaps="handled"
          extraKeyboardSpace={-20}
          showsVerticalScrollIndicator={false}>
          <VenueProgressIndicator currentStep={3} totalSteps={4} />
          <Box paddingVertical="md_16">
            <Text variant="h_32SemiBold_Page" marginBottom="lg_24">
              {t('venue.registration.summary.title')}
            </Text>

            {/* Venue Preview Card */}
            <Box marginBottom="lg_24">
              <VenuePreviewCard venue={formData} />
            </Box>

            <Divider marginBottom="lg_24" />

            {/* Basic Details Section */}
            <Box marginBottom="lg_24">
              <Box
                flexDirection="row"
                justifyContent="space-between"
                alignItems="center"
                marginBottom="md_16">
                <Text variant="h_20Medium_subsection">
                  {t('venue.registration.summary.basicDetails')}
                </Text>
                <Button
                  variant="ghost"
                  onPress={() => handleEditSection('basicDetails')}
                  rightIcon={<PencilSimple size={20} />}
                />
              </Box>

              <Box gap="sm_12">
                <Box>
                  <Text variant="l_12Regular_helperText" color="textSecondary">
                    {t('venue.registration.summary.venueName')}
                  </Text>
                  <Text variant="b_16Regular_input">{formData.name}</Text>
                </Box>

                <Box>
                  <Text variant="l_12Regular_helperText" color="textSecondary">
                    {t('venue.registration.summary.musicGenres')}
                  </Text>
                  <Box flexDirection="row" flexWrap="wrap" gap="xs_8" marginTop="xs_8">
                    {formData.musicGenres?.map(genre => (
                      <Chip key={genre} label={genre} />
                    ))}
                  </Box>
                </Box>

                <Box>
                  <Text variant="l_12Regular_helperText" color="textSecondary">
                    {t('venue.registration.summary.dressCodes')}
                  </Text>
                  <Box flexDirection="row" flexWrap="wrap" gap="xs_8" marginTop="xs_8">
                    {formData.dressCodes?.map(code => (
                      <Chip key={code} label={code} />
                    ))}
                  </Box>
                </Box>

                <Box>
                  <Text variant="l_12Regular_helperText" color="textSecondary">
                    {t('venue.registration.summary.location')}
                  </Text>
                  <Text variant="b_16Regular_input">{formData.location?.formattedAddress}</Text>
                </Box>
              </Box>
            </Box>

            <Divider marginBottom="lg_24" />

            {/* Opening Times Section */}
            <Box marginBottom="lg_24">
              <Box
                flexDirection="row"
                justifyContent="space-between"
                alignItems="center"
                marginBottom="md_16">
                <Text variant="h_20Medium_subsection">
                  {t('venue.registration.summary.openingTimes')}
                </Text>
                <Button
                  variant="ghost"
                  onPress={() => handleEditSection('openingTimes')}
                  rightIcon={<PencilSimple size={20} />}
                />
              </Box>

              <Box gap="xs_8">
                {formData.regularHours?.map(hours => (
                  <Box key={hours.day} flexDirection="row" justifyContent="space-between">
                    <Text variant="b_14Regular_content" textTransform="capitalize">
                      {getDayName(hours.day)}
                    </Text>
                    <Text variant="b_14Regular_content">
                      {hours.isClosed ? t('common.closed') : `${hours.open} - ${hours.close}`}
                    </Text>
                  </Box>
                ))}
              </Box>
            </Box>

            <Divider marginBottom="lg_24" />

            {/* Features Section */}
            <Box marginBottom="xl_32">
              <Box
                flexDirection="row"
                justifyContent="space-between"
                alignItems="center"
                marginBottom="md_16">
                <Text variant="h_20Medium_subsection">
                  {t('venue.registration.summary.features')}
                </Text>
                <Button
                  variant="ghost"
                  onPress={() => handleEditSection('features')}
                  rightIcon={<PencilSimple size={20} />}
                />
              </Box>

              <Box flexDirection="row" flexWrap="wrap" gap="xs_8">
                {formData.features?.map(feature => (
                  <Chip key={feature} label={feature} chipVariant="solidMedium" />
                ))}
              </Box>

              {formData.capacity && (
                <Box marginTop="md_16">
                  <Text variant="l_12Regular_helperText" color="textSecondary">
                    {t('venue.registration.summary.capacity')}
                  </Text>
                  <Text variant="b_16Regular_input">
                    {formData.capacity} {t('common.people')}
                  </Text>
                </Box>
              )}
            </Box>
          </Box>
        </KeyboardAwareScrollView>
      </AnimatedContainer>

      {/* Floating Action Buttons */}
      <Box
        position="absolute"
        bottom={Platform.OS === 'ios' ? insets.bottom : 0}
        left={0}
        right={0}
        backgroundColor="background"
        paddingHorizontal="ml_20"
        paddingTop="md_16"
        style={{
          borderTopWidth: 1,
          borderTopColor: theme.colors.border,
          paddingBottom: insets.bottom / 2,
        }}>
        <Box flexDirection="row" gap="sm_12">
          <Box flex={1}>
            <Button variant="outline" onPress={handleBack} title={t('common.back')} />
          </Box>
          <Box flex={1}>
            <Button
              variant="primary"
              onPress={handleSubmit}
              loading={isPending}
              title={t('venue.registration.summary.submit')}
            />
          </Box>
        </Box>
      </Box>
    </SafeAreaWrapper>
  );
};
