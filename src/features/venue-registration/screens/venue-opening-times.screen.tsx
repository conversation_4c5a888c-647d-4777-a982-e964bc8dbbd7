import React, { useCallback } from 'react';

import { Platform } from 'react-native';

import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { Controller } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { VenueRegistrationNavigationProps } from '@/src/core/navigation/types';
import { AnimatedContainer, Box, SafeAreaWrapper, Text, useTheme } from '@/src/core/theme';
import { Button, NavigationTopBar } from '@/src/shared/components';
import { useZodForm } from '@/src/shared/forms/useZodForm';

import { VenueHappyHours } from '../components/VenueHappyHours';
import { VenueOpeningTimePicker } from '../components/VenueOpeningTimePicker';
import { VenueProgressIndicator } from '../components/VenueProgressIndicator';
import { VenueSpecialHours } from '../components/VenueSpecialHours';
import { useVenueRegistration } from '../context/VenueRegistrationContext';
import { useVenueRegistrationStore } from '../store/venueRegistrationStore';
import { VenueOpeningTimesFormData, venueOpeningTimesSchema } from '../validation';

export const VenueOpeningTimesScreen: React.FC = () => {
  const { t } = useTranslation();
  const { formData, updateOpeningTimes } = useVenueRegistrationStore();
  const { navigateNext, navigatePrevious, syncCurrentStep } = useVenueRegistration();
  const navigation = useNavigation<VenueRegistrationNavigationProps>();

  const insets = useSafeAreaInsets();
  const theme = useTheme();

  // Sync current step when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      syncCurrentStep('VenueOpeningTimes');
    }, [syncCurrentStep])
  );

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    watch,
    trigger,
  } = useZodForm(venueOpeningTimesSchema, {
    mode: 'onBlur', // Errors only show when user leaves field
    reValidateMode: 'onChange', // Clear errors immediately when fixed
    defaultValues: {
      regularHours: formData.regularHours || [],
      specialHours: formData.specialHours || [],
      happyHours: formData.happyHours || {
        enabled: false,
        days: [],
        startTime: '',
        endTime: '',
      },
    },
  });

  // Watch form values for real-time validation
  const watchedValues = watch();

  // Use Zod safeParse for button enabling (doesn't affect form error state)
  const isInputValid = React.useMemo(() => {
    const result = venueOpeningTimesSchema.safeParse({
      regularHours: watchedValues.regularHours || [],
      specialHours: watchedValues.specialHours || [],
      happyHours: watchedValues.happyHours || {
        enabled: false,
        days: [],
        startTime: '',
        endTime: '',
      },
    });
    return result.success;
  }, [watchedValues.regularHours, watchedValues.specialHours, watchedValues.happyHours]);

  // Enable button when input passes validation OR form is officially valid
  const isFormValid = isValid || isInputValid;

  const onSubmit = useCallback(
    (data: VenueOpeningTimesFormData) => {
      updateOpeningTimes(data);
      navigateNext(navigation);
    },
    [updateOpeningTimes, navigateNext, navigation]
  );

  const handleBack = useCallback(() => {
    navigatePrevious(navigation);
  }, [navigatePrevious, navigation]);

  return (
    <SafeAreaWrapper
      edges={['top']}
      style={{ paddingBottom: Platform.OS === 'ios' ? insets.bottom : 0 }}>
      <NavigationTopBar title="Venue Registration" onPress={handleBack} />
      <AnimatedContainer padding="md_16">
        <KeyboardAwareScrollView
          contentContainerStyle={{
            flexGrow: 1,
            paddingBottom: insets.bottom + theme.spacing.md_16,
          }}
          bottomOffset={Platform.OS === 'ios' ? 0 : 20}
          keyboardShouldPersistTaps="handled"
          extraKeyboardSpace={-20}
          showsVerticalScrollIndicator={false}>
          <VenueProgressIndicator currentStep={1} totalSteps={4} />
          <Box paddingVertical="md_16">
            <Text variant="h_32SemiBold_Page" marginBottom="lg_24">
              {t('venue.registration.openingTimes.title')}
            </Text>

            {/* Regular Hours */}
            <Box marginBottom="lg_24">
              <Text variant="h_20Medium_subsection" marginBottom="md_16">
                {t('venue.registration.openingTimes.regularHours')}
              </Text>
              <Controller
                control={control}
                name="regularHours"
                render={({ field: { onChange, value } }) => (
                  <VenueOpeningTimePicker
                    hours={value}
                    onHoursChange={hours => {
                      onChange(hours);
                      // Only trigger validation if there's an error (to clear it immediately)
                      if (errors.regularHours?.message) {
                        trigger('regularHours');
                      }
                    }}
                    error={errors.regularHours?.message}
                  />
                )}
              />
            </Box>

            {/* Special Hours */}
            <Box marginBottom="lg_24">
              <Text variant="h_20Medium_subsection" marginBottom="md_16">
                {t('venue.registration.openingTimes.specialHours')}
              </Text>
              <Controller
                control={control}
                name="specialHours"
                render={({ field: { onChange, value } }) => (
                  <VenueSpecialHours
                    specialHours={value || []}
                    onSpecialHoursChange={specialHours => {
                      onChange(specialHours);
                      // Only trigger validation if there's an error (to clear it immediately)
                      if (errors.specialHours?.message) {
                        trigger('specialHours');
                      }
                    }}
                    error={errors.specialHours?.message}
                  />
                )}
              />
            </Box>

            {/* Happy Hours */}
            <Box marginBottom="xxxl_48">
              <Text variant="h_20Medium_subsection" marginBottom="md_16">
                {t('venue.registration.openingTimes.happyHours')}
              </Text>
              <Controller
                control={control}
                name="happyHours"
                render={({ field: { onChange, value } }) => (
                  <VenueHappyHours
                    happyHours={value}
                    onHappyHoursChange={happyHours => {
                      onChange(happyHours);
                      // Only trigger validation if there's an error (to clear it immediately)
                      if (errors.happyHours?.message) {
                        trigger('happyHours');
                      }
                    }}
                    error={errors.happyHours?.message}
                  />
                )}
              />
            </Box>
          </Box>
        </KeyboardAwareScrollView>
      </AnimatedContainer>
      {/* Floating Action Buttons */}
      <Box
        position="absolute"
        bottom={Platform.OS === 'ios' ? insets.bottom : 0}
        left={0}
        right={0}
        backgroundColor="background"
        paddingHorizontal="ml_20"
        paddingTop="md_16"
        style={{
          borderTopWidth: 1,
          borderTopColor: theme.colors.border,
          paddingBottom: insets.bottom / 2,
        }}>
        <Box flexDirection="row" gap="sm_12">
          <Box flex={1}>
            <Button variant="outline" onPress={handleBack} title={t('common.back')} />
          </Box>
          <Box flex={1}>
            <Button
              variant="primary"
              onPress={() => handleSubmit(onSubmit)()}
              enabled={isFormValid}
              title={t('common.next')}
            />
          </Box>
        </Box>
      </Box>
    </SafeAreaWrapper>
  );
};
