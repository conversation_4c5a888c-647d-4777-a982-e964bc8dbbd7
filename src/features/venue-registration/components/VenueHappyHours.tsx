import React, { useMemo, useRef, useState } from 'react';

import { useTranslation } from 'react-i18next';

import { Box, Pressable, Text } from '@/src/core/theme';
import { DatePicker, DatePickerHandle, Switch } from '@/src/shared/components';

import { DayOfWeek } from '../types';
import { DaySelector } from './DaySelector';

interface HappyHoursData {
  enabled: boolean;
  days: DayOfWeek[];
  startTime: string;
  endTime: string;
}

interface VenueHappyHoursProps {
  happyHours?: HappyHoursData;
  onHappyHoursChange: (hours?: HappyHoursData) => void;
  error?: string;
}

export const VenueHappyHours: React.FC<VenueHappyHoursProps> = ({
  happyHours,
  onHappyHoursChange,
  error,
}) => {
  const { t } = useTranslation();
  const datePickerRef = useRef<DatePickerHandle>(null);
  const [editingTime, setEditingTime] = useState<'startTime' | 'endTime' | null>(null);

  const handleToggle = (enabled: boolean) => {
    if (enabled) {
      onHappyHoursChange({
        enabled: true,
        days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
        startTime: '17:00',
        endTime: '20:00',
      });
    } else {
      // Keep data but set enabled to false
      onHappyHoursChange(happyHours ? { ...happyHours, enabled: false } : undefined);
    }
  };

  const handleTimePress = (type: 'startTime' | 'endTime') => {
    setEditingTime(type);
    datePickerRef.current?.showPicker();
  };

  const onPickerChange = (date: Date | null) => {
    if (date && editingTime && happyHours) {
      const hoursStr = date.getHours().toString().padStart(2, '0');
      const minutesStr = date.getMinutes().toString().padStart(2, '0');
      onHappyHoursChange({ ...happyHours, [editingTime]: `${hoursStr}:${minutesStr}` });
    }
    setEditingTime(null);
  };

  const handleDaySelectionChange = (days: DayOfWeek[]) => {
    if (happyHours) {
      onHappyHoursChange({ ...happyHours, days });
    }
  };

  const currentPickerDate = useMemo(() => {
    if (!editingTime || !happyHours) {
      return new Date();
    }
    const timeString = happyHours[editingTime];
    const [h, m] = timeString.split(':');
    const d = new Date();
    d.setHours(parseInt(h, 10), parseInt(m, 10));
    return d;
  }, [editingTime, happyHours]);

  return (
    <Box>
      <Box
        flexDirection="row"
        justifyContent="space-between"
        alignItems="center"
        marginBottom="sm_12">
        <Text variant="b_16Regular_input">
          {t('venue.registration.openingTimes.enableHappyHours')}
        </Text>
        <Switch isChecked={happyHours?.enabled || false} onToggle={handleToggle} />
      </Box>

      {happyHours?.enabled && (
        <Box backgroundColor="elevatedBackground" padding="md_16" borderRadius="md_12" gap="md_16">
          <DaySelector
            selectedDays={happyHours.days}
            onSelectionChange={handleDaySelectionChange}
          />
          <Box flexDirection="row" justifyContent="space-between" alignItems="center">
            <Text variant="b_16Regular_input">{t('common.time')}</Text>
            <Box flexDirection="row" alignItems="center" gap="xs_8">
              <Pressable onPress={() => handleTimePress('startTime')}>
                <Box
                  flexDirection="row"
                  alignItems="center"
                  backgroundColor="background"
                  paddingHorizontal="sm_12"
                  paddingVertical="xs_8"
                  borderRadius="sm_8">
                  <Text variant="b_14Medium_button">{happyHours.startTime}</Text>
                </Box>
              </Pressable>

              <Text variant="b_14Regular_content" color="secondaryText">
                -
              </Text>

              <Pressable onPress={() => handleTimePress('endTime')}>
                <Box
                  flexDirection="row"
                  alignItems="center"
                  backgroundColor="background"
                  paddingHorizontal="sm_12"
                  paddingVertical="xs_8"
                  borderRadius="sm_8">
                  <Text variant="b_14Medium_button">{happyHours.endTime}</Text>
                </Box>
              </Pressable>
            </Box>
          </Box>
        </Box>
      )}

      {error && (
        <Text variant="l_12Medium_message" color="errorMain" marginTop="xs_8">
          {error}
        </Text>
      )}

      <DatePicker
        ref={datePickerRef}
        type="time"
        value={currentPickerDate}
        onChange={onPickerChange}
        is24Hour
      />
    </Box>
  );
};
