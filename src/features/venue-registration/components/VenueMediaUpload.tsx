import React, { useCallback } from 'react';

import { FlatList, Image, TouchableOpacity } from 'react-native';

import { ImagePickerAsset, launchImageLibraryAsync } from 'expo-image-picker';
import { Camera, X } from 'phosphor-react-native';
import { useTranslation } from 'react-i18next';

import { Box, Text } from '@/src/core/theme';

interface VenueMediaUploadProps {
  photos: ImagePickerAsset[];
  onPhotosChange: (photos: ImagePickerAsset[]) => void;
  error?: string;
  maxPhotos?: number;
}

export const VenueMediaUpload: React.FC<VenueMediaUploadProps> = ({
  photos,
  onPhotosChange,
  error,
  maxPhotos = 10,
}) => {
  const { t } = useTranslation();

  const handleAddPhoto = useCallback(async () => {
    if (photos.length >= maxPhotos) return;

    const result = await launchImageLibraryAsync({
      mediaTypes: ['images'],
      allowsEditing: true,
      aspect: [16, 9],
      quality: 1,
    });

    if (!result.canceled && result.assets) {
      onPhotosChange([...photos, ...result.assets]);
    }
  }, [photos, onPhotosChange, maxPhotos]);

  const handleRemovePhoto = useCallback(
    (index: number) => {
      onPhotosChange(photos.filter((_, i) => i !== index));
    },
    [photos, onPhotosChange]
  );

  const renderPhoto = ({ item, index }: { item: ImagePickerAsset; index: number }) => (
    <Box position="relative" marginRight="sm_12">
      <Image
        source={{ uri: item.uri }}
        style={{
          width: 100,
          height: 100,
          borderRadius: 12,
        }}
      />
      <TouchableOpacity
        style={{
          position: 'absolute',
          top: 4,
          right: 4,
          backgroundColor: 'rgba(0,0,0,0.5)',
          borderRadius: 12,
          padding: 4,
        }}
        onPress={() => handleRemovePhoto(index)}>
        <X size={16} color="white" />
      </TouchableOpacity>
      {index === 0 && (
        <Box
          position="absolute"
          bottom={4}
          left={4}
          backgroundColor="primary"
          paddingHorizontal="xs_8"
          paddingVertical="xxs_4"
          borderRadius="xs_4">
          <Text variant="l_10SemiBold_chip" color="white">
            {t('common.mainPhoto')}
          </Text>
        </Box>
      )}
    </Box>
  );

  return (
    <Box>
      <Text variant="h_16Medium_formLabel" marginBottom="sm_12">
        {t('venue.registration.basicDetails.photos')}
      </Text>

      <FlatList
        horizontal
        data={[...photos, { isAddButton: true } as any]}
        renderItem={({ item, index }) => {
          if (item.isAddButton && photos.length < maxPhotos) {
            return (
              <TouchableOpacity onPress={handleAddPhoto}>
                <Box
                  width={100}
                  height={100}
                  borderRadius="md_12"
                  borderWidth={2}
                  borderColor="mainBorder"
                  borderStyle="dashed"
                  justifyContent="center"
                  alignItems="center"
                  backgroundColor="surfaceBackground">
                  <Camera size={32} color="textSecondary" />
                  <Text variant="l_12Regular_helperText" color="secondaryText" marginTop="xs_8">
                    {t('common.addPhoto')}
                  </Text>
                </Box>
              </TouchableOpacity>
            );
          }
          return renderPhoto({ item, index });
        }}
        keyExtractor={(item, index) => index.toString()}
        showsHorizontalScrollIndicator={false}
      />

      <Text variant="l_12Regular_helperText" color="secondaryText" marginTop="xs_8">
        {t('venue.registration.basicDetails.photosHelper', {
          current: photos.length,
          max: maxPhotos,
        })}
      </Text>

      {error && (
        <Text variant="l_12Medium_message" color="errorMain" marginTop="xs_8">
          {error}
        </Text>
      )}
    </Box>
  );
};
