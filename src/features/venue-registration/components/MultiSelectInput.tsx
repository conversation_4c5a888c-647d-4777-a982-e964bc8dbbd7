import React, { useCallback } from 'react';

import { Plus } from 'phosphor-react-native';
import { MagicModalHideReason, magicModal } from 'react-native-magic-modal';

import { Box, Pressable, Text, useTheme } from '@/src/core/theme';
import { Chip } from '@/src/shared/components';

import { MultiSelectModal } from './MultiSelectModal';

interface MultiSelectInputProps {
  label: string;
  placeholder: string;
  options: string[];
  selectedValues: string[];
  onSelectionChange: (values: string[]) => void;
  error?: string;
  maxSelection?: number;
}

type Result = {
  result: string[];
};

export const MultiSelectInput: React.FC<MultiSelectInputProps> = ({
  label,
  placeholder,
  options,
  selectedValues,
  onSelectionChange,
  error,
  maxSelection,
}) => {
  const theme = useTheme();

  const handleOpenSelector = useCallback(async () => {
    const result = await magicModal.show<Result>(
      () => (
        <MultiSelectModal
          title={label}
          options={options}
          selectedValues={selectedValues}
          maxSelection={maxSelection}
        />
      ),
      { swipeDirection: undefined }
    ).promise;

    if (result.reason !== MagicModalHideReason.INTENTIONAL_HIDE) {
      // User cancelled the flow
      return;
    }

    onSelectionChange(result.data?.result || []);
  }, [label, options, selectedValues, maxSelection, onSelectionChange]);

  const handleRemoveValue = useCallback(
    (value: string) => {
      onSelectionChange(selectedValues.filter(v => v !== value));
    },
    [selectedValues, onSelectionChange]
  );

  return (
    <Box>
      <Text variant="h_16Medium_formLabel" marginBottom="sm_12">
        {label}
      </Text>

      <Pressable onPress={handleOpenSelector}>
        <Box
          borderWidth={1}
          borderColor={error ? 'errorMain' : 'mainBorder'}
          borderRadius="lg_16"
          padding="md_16"
          flexDirection="row"
          alignItems="center"
          justifyContent="space-between"
          backgroundColor="inputBackground"
          minHeight={56}>
          {selectedValues.length > 0 ? (
            <Box flexDirection="row" flexWrap="wrap" gap="xs_8" flex={1} marginRight="sm_12">
              {selectedValues.map(value => (
                <Chip
                  key={value}
                  label={value}
                  chipVariant="solidSmall"
                  onPress={() => handleRemoveValue(value)}
                />
              ))}
            </Box>
          ) : (
            <Text variant="b_16Regular_input" color="placeholderText" flex={1}>
              {placeholder}
            </Text>
          )}

          <Plus size={24} color={theme.colors.iconMuted} />
        </Box>
      </Pressable>

      {error && (
        <Text variant="l_12Medium_message" color="errorMain" marginTop="xs_8">
          {error}
        </Text>
      )}
    </Box>
  );
};
