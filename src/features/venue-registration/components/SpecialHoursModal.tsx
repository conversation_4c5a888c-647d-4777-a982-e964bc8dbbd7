import React, { useRef, useState } from 'react';

import { ScrollView } from 'react-native';

import { useTranslation } from 'react-i18next';
import { useMagicModal } from 'react-native-magic-modal';

import { modal } from '@/src/core/libs/magicModal';
import { Box, Pressable, Text, TextInput } from '@/src/core/theme';
import { Button, DatePicker, DatePickerHandle, Switch } from '@/src/shared/components';

import { SpecialHours } from '../types';

interface SpecialHoursModalProps {
  onSave: (specialHour: SpecialHours) => void;
  onDelete?: () => void;
  initialData?: SpecialHours | null;
}

type PickerMode = 'startDate' | 'endDate' | 'openTime' | 'closeTime' | null;

export const SpecialHoursModal: React.FC<SpecialHoursModalProps> = ({
  onSave,
  onDelete,
  initialData,
}) => {
  const { t } = useTranslation();
  const { hide } = useMagicModal();
  const datePickerRef = useRef<DatePickerHandle>(null);

  const [name, setName] = useState(initialData?.name || '');
  const [startDate, setStartDate] = useState(
    initialData ? new Date(initialData.startDate) : new Date()
  );
  const [endDate, setEndDate] = useState(initialData ? new Date(initialData.endDate) : new Date());
  const [isClosed, setIsClosed] = useState(initialData?.isClosed || false);
  const [openTime, setOpenTime] = useState(() => {
    if (initialData?.open) {
      const [hours, minutes] = initialData.open.split(':');
      const time = new Date();
      time.setHours(parseInt(hours, 10), parseInt(minutes, 10), 0, 0);
      return time;
    }
    const time = new Date();
    time.setHours(18, 0, 0, 0);
    return time;
  });
  const [closeTime, setCloseTime] = useState(() => {
    if (initialData?.close) {
      const [hours, minutes] = initialData.close.split(':');
      const time = new Date();
      time.setHours(parseInt(hours, 10), parseInt(minutes, 10), 0, 0);
      return time;
    }
    const time = new Date();
    time.setHours(2, 0, 0, 0);
    return time;
  });

  const [currentPickerMode, setCurrentPickerMode] = useState<PickerMode>(null);

  const handleSave = () => {
    if (name && startDate && endDate) {
      const formatTime = (date: Date) => {
        return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
      };

      onSave({
        id: initialData?.id || Date.now().toString(),
        name,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        isClosed,
        open: formatTime(openTime),
        close: formatTime(closeTime),
      });
      hide();
    }
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete();
    }
    hide();
  };

  const handleCancel = () => {
    hide();
  };

  const showPickerForMode = (mode: PickerMode) => {
    setCurrentPickerMode(mode);
    modal.disableFullWindowOverlay();
    // Small delay to ensure component is mounted before showing picker
    datePickerRef.current?.showPicker();
  };

  // Handle picker value change
  const handlePickerChange = (date: Date | null) => {
    if (date && currentPickerMode) {
      switch (currentPickerMode) {
        case 'startDate':
          setStartDate(date);
          break;
        case 'endDate':
          setEndDate(date);
          break;
        case 'openTime':
          setOpenTime(date);
          break;
        case 'closeTime':
          setCloseTime(date);
          break;
      }
    }

    // Reset picker mode
    setCurrentPickerMode(null);
  };

  // Format date for display
  const formatDateForDisplay = (date: Date) => {
    return date.toLocaleDateString();
  };

  // Format time for display
  const formatTimeForDisplay = (date: Date) => {
    return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
  };

  return (
    <Box
      backgroundColor="background"
      borderRadius="lg_16"
      marginHorizontal="ml_20"
      maxHeight="80%"
      width="80%"
      style={{
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.15,
        shadowRadius: 12,
        elevation: 8,
      }}>
      <Box
        paddingHorizontal="lg_24"
        paddingTop="lg_24"
        paddingBottom="md_16"
        borderBottomWidth={1}
        borderBottomColor="border">
        <Text variant="h_20Medium_subsection">
          {initialData
            ? t('venue.registration.openingTimes.editSpecialHours')
            : t('venue.registration.openingTimes.addSpecialHours')}
        </Text>
      </Box>

      <ScrollView
        style={{ maxHeight: '60%' }}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          paddingHorizontal: 24,
          paddingVertical: 24,
        }}>
        <Box gap="lg_24">
          <TextInput
            label={t('common.name')}
            placeholder={
              t('venue.registration.openingTimes.specialHoursNamePlaceholder') ||
              "e.g., New Year's Eve"
            }
            value={name}
            onChangeText={setName}
          />

          <Box gap="md_16">
            <Box>
              <Text variant="b_14Medium_button" marginBottom="xs_8">
                {t('common.startDate')}
              </Text>
              <Pressable onPress={() => showPickerForMode('startDate')}>
                <Box
                  borderWidth={1}
                  borderColor="border"
                  borderRadius="md_12"
                  backgroundColor="background"
                  paddingHorizontal="md_16"
                  paddingVertical="sm_12"
                  flexDirection="row"
                  justifyContent="space-between"
                  alignItems="center">
                  <Text variant="b_16Regular_input">{formatDateForDisplay(startDate)}</Text>
                  <Text variant="b_14Regular_content" color="textSecondary">
                    📅
                  </Text>
                </Box>
              </Pressable>
            </Box>

            <Box>
              <Text variant="b_14Medium_button" marginBottom="xs_8">
                {t('common.endDate')}
              </Text>
              <Pressable onPress={() => showPickerForMode('endDate')}>
                <Box
                  borderWidth={1}
                  borderColor="border"
                  borderRadius="md_12"
                  backgroundColor="background"
                  paddingHorizontal="md_16"
                  paddingVertical="sm_12"
                  flexDirection="row"
                  justifyContent="space-between"
                  alignItems="center">
                  <Text variant="b_16Regular_input">{formatDateForDisplay(endDate)}</Text>
                  <Text variant="b_14Regular_content" color="textSecondary">
                    📅
                  </Text>
                </Box>
              </Pressable>
            </Box>
          </Box>

          <Box
            flexDirection="row"
            justifyContent="space-between"
            alignItems="center"
            paddingVertical="sm_12"
            paddingHorizontal="md_16"
            backgroundColor="surface"
            borderRadius="md_12">
            <Text variant="b_16Regular_input">{t('common.closed')}</Text>
            <Switch isChecked={isClosed} onToggle={setIsClosed} />
          </Box>

          {!isClosed && (
            <Box gap="md_16">
              <Box>
                <Text variant="b_14Medium_button" marginBottom="xs_8">
                  {t('common.openTime')}
                </Text>
                <Pressable onPress={() => showPickerForMode('openTime')}>
                  <Box
                    borderWidth={1}
                    borderColor="border"
                    borderRadius="md_12"
                    backgroundColor="background"
                    paddingHorizontal="md_16"
                    paddingVertical="sm_12"
                    flexDirection="row"
                    justifyContent="space-between"
                    alignItems="center">
                    <Text variant="b_16Regular_input">{formatTimeForDisplay(openTime)}</Text>
                    <Text variant="b_14Regular_content" color="textSecondary">
                      🕒
                    </Text>
                  </Box>
                </Pressable>
              </Box>

              <Box>
                <Text variant="b_14Medium_button" marginBottom="xs_8">
                  {t('common.closeTime')}
                </Text>
                <Pressable onPress={() => showPickerForMode('closeTime')}>
                  <Box
                    borderWidth={1}
                    borderColor="border"
                    borderRadius="md_12"
                    backgroundColor="background"
                    paddingHorizontal="md_16"
                    paddingVertical="sm_12"
                    flexDirection="row"
                    justifyContent="space-between"
                    alignItems="center">
                    <Text variant="b_16Regular_input">{formatTimeForDisplay(closeTime)}</Text>
                    <Text variant="b_14Regular_content" color="textSecondary">
                      🕒
                    </Text>
                  </Box>
                </Pressable>
              </Box>
            </Box>
          )}
        </Box>
      </ScrollView>

      <Box
        paddingHorizontal="lg_24"
        paddingBottom="lg_24"
        gap="sm_12"
        borderTopWidth={1}
        borderTopColor="border"
        paddingTop="md_16">
        <Box flexDirection="row" gap="sm_12">
          <Box flex={1}>
            <Button variant="outline" title={t('common.cancel')} onPress={handleCancel} />
          </Box>
          <Box flex={1}>
            <Button
              variant="primary"
              title={t('common.save')}
              onPress={handleSave}
              enabled={!!name && !!startDate && !!endDate}
            />
          </Box>
        </Box>

        {initialData && onDelete && (
          <Button variant="ghost" title={t('common.delete')} onPress={handleDelete} />
        )}
      </Box>

      {/* DatePicker component - only render when needed */}
      <DatePicker
        ref={datePickerRef}
        type={
          currentPickerMode === 'openTime' || currentPickerMode === 'closeTime' ? 'time' : 'date'
        }
        value={
          currentPickerMode === 'startDate'
            ? startDate
            : currentPickerMode === 'endDate'
              ? endDate
              : currentPickerMode === 'openTime'
                ? openTime
                : closeTime
        }
        onChange={handlePickerChange}
        is24Hour
        inline={false}
      />
    </Box>
  );
};
