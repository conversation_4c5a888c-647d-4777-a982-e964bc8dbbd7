import React, { useCallback, useState } from 'react';

import { FlatList, TouchableOpacity } from 'react-native';

import { Check } from 'phosphor-react-native';
import { useTranslation } from 'react-i18next';
import { magicModal, useMagicModal } from 'react-native-magic-modal';

import { Box, SafeAreaWrapper, Text, useTheme } from '@/src/core/theme';
import { Button } from '@/src/shared/components';

interface MultiSelectModalProps {
  title: string;
  options: string[];
  selectedValues: string[];
  maxSelection?: number;
}

export const MultiSelectModal: React.FC<MultiSelectModalProps> = ({
  title,
  options,
  selectedValues: initialValues,
  maxSelection,
}) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const { hide } = useMagicModal();
  const [selectedValues, setSelectedValues] = useState<string[]>(initialValues);

  const toggleOption = useCallback(
    (option: string) => {
      setSelectedValues(prev => {
        if (prev.includes(option)) {
          return prev.filter(v => v !== option);
        }
        if (maxSelection && prev.length >= maxSelection) {
          return prev;
        }
        return [...prev, option];
      });
    },
    [maxSelection]
  );

  const handleConfirm = () => {
    magicModal.hide({ result: selectedValues });
  };

  const handleCancel = () => {
    hide();
  };

  const renderOption = ({ item }: { item: string }) => {
    const isSelected = selectedValues.includes(item);

    return (
      <TouchableOpacity onPress={() => toggleOption(item)}>
        <Box
          flexDirection="row"
          alignItems="center"
          justifyContent="space-between"
          paddingVertical="md_16"
          paddingHorizontal="ml_20"
          borderBottomWidth={1}
          borderBottomColor="mainBorder">
          <Text variant="b_16Regular_input">{item}</Text>
          {isSelected && <Check size={20} color={theme.colors.iconActiveInput} weight="bold" />}
        </Box>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaWrapper>
      <Box flex={1} backgroundColor="mainBackground">
        <Box
          paddingHorizontal="ml_20"
          paddingVertical="md_16"
          borderBottomWidth={1}
          borderBottomColor="mainBorder">
          <Text variant="h_20Medium_subsection">{title}</Text>
          {maxSelection && (
            <Text variant="l_12Regular_helperText" color="secondaryText" marginTop="xs_8">
              {t('common.selectUpTo', { count: maxSelection })}
            </Text>
          )}
        </Box>

        <FlatList data={options} renderItem={renderOption} keyExtractor={item => item} />

        <Box
          flexDirection="row"
          gap="sm_12"
          paddingHorizontal="ml_20"
          paddingVertical="md_16"
          borderTopWidth={1}
          borderTopColor="mainBorder">
          <Box flex={1}>
            <Button variant="outline" onPress={handleCancel} title={t('common.cancel')} />
          </Box>
          <Box flex={1}>
            <Button
              variant="primary"
              onPress={handleConfirm}
              title={t('common.confirm')}
              enabled={selectedValues.length > 0}
            />
          </Box>
        </Box>
      </Box>
    </SafeAreaWrapper>
  );
};
