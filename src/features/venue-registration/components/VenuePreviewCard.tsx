import React from 'react';

import { Image } from 'react-native';

import { Car, Crown, MapPin, MusicNotes, WifiHigh } from 'phosphor-react-native';
import { useTranslation } from 'react-i18next';

import { Box, Text } from '@/src/core/theme';

import { VenueFormData } from '../types';

interface VenuePreviewCardProps {
  venue: VenueFormData;
}

export const VenuePreviewCard: React.FC<VenuePreviewCardProps> = ({ venue }) => {
  const { t } = useTranslation();
  const mainPhoto = venue.photos[0];

  return (
    <Box
      backgroundColor="cardBackground"
      borderRadius="lg_16"
      overflow="hidden"
      style={{
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 3,
      }}>
      {/* Main Photo */}
      {mainPhoto && (
        <Image
          source={{ uri: mainPhoto.uri }}
          style={{
            width: '100%',
            height: 200,
          }}
          resizeMode="cover"
        />
      )}

      {/* Venue Info */}
      <Box padding="md_16">
        <Text variant="h_24SemiBold_section" marginBottom="xs_8">
          {venue.name}
        </Text>

        {/* Location */}
        {venue.location && (
          <Box flexDirection="row" alignItems="center" marginBottom="sm_12">
            <MapPin size={16} color="textSecondary" />
            <Text
              variant="b_14Regular_content"
              color="secondaryText"
              marginLeft="xs_8"
              numberOfLines={1}>
              {venue.location.address}, {venue.location.city}
            </Text>
          </Box>
        )}

        {/* Music Genres */}
        <Box flexDirection="row" alignItems="center" marginBottom="sm_12">
          <MusicNotes size={16} color="textSecondary" />
          <Text
            variant="b_14Regular_content"
            color="secondaryText"
            marginLeft="xs_8"
            numberOfLines={1}>
            {venue.musicGenres.slice(0, 3).join(' • ')}
            {venue.musicGenres.length > 3 && ` +${venue.musicGenres.length - 3}`}
          </Text>
        </Box>

        {/* Features Preview */}
        <Box flexDirection="row" alignItems="center" gap="md_16">
          {venue.features.includes('parking') && (
            <Box flexDirection="row" alignItems="center">
              <Car size={16} color="textSecondary" />
              <Text variant="l_12Regular_helperText" color="secondaryText" marginLeft="xxs_4">
                {t('venue.features.parking')}
              </Text>
            </Box>
          )}

          {venue.features.includes('wifi') && (
            <Box flexDirection="row" alignItems="center">
              <WifiHigh size={16} color="textSecondary" />
              <Text variant="l_12Regular_helperText" color="secondaryText" marginLeft="xxs_4">
                {t('venue.features.wifi')}
              </Text>
            </Box>
          )}

          {venue.features.includes('vip_area') && (
            <Box flexDirection="row" alignItems="center">
              <Crown size={16} color="textSecondary" />
              <Text variant="l_12Regular_helperText" color="secondaryText" marginLeft="xxs_4">
                {t('venue.features.vip')}
              </Text>
            </Box>
          )}
        </Box>
      </Box>
    </Box>
  );
};
