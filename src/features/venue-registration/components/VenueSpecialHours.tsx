import React, { useState } from 'react';

import { Pencil, Plus, Trash } from 'phosphor-react-native';
import { useTranslation } from 'react-i18next';

import { modal } from '@/src/core/libs/magicModal';
import { Box, Pressable, Text, useTheme } from '@/src/core/theme';
import { Button } from '@/src/shared/components';

import { SpecialHours } from '../types';
import { SpecialHoursModal } from './SpecialHoursModal';

interface VenueSpecialHoursProps {
  specialHours: SpecialHours[];
  onSpecialHoursChange: (hours: SpecialHours[]) => void;
  error?: string;
}

export const VenueSpecialHours: React.FC<VenueSpecialHoursProps> = ({
  specialHours,
  onSpecialHoursChange,
  error,
}) => {
  const { t } = useTranslation();
  const theme = useTheme();

  const handleSave = (specialHour: SpecialHours) => {
    const index = specialHours.findIndex(sh => sh.id === specialHour.id);
    if (index !== -1) {
      const newSpecialHours = [...specialHours];
      newSpecialHours[index] = specialHour;
      onSpecialHoursChange(newSpecialHours);
    } else {
      onSpecialHoursChange([...specialHours, specialHour]);
    }
  };

  const handleDelete = (id: string) => {
    onSpecialHoursChange(specialHours.filter(sh => sh.id !== id));
  };

  const handleAddPress = () => {
    modal.show(() => <SpecialHoursModal onSave={handleSave} initialData={null} />, {
      position: 'center',
      animation: { type: 'scale', duration: 300 },
      background: { opacity: 0.5, closeOnPress: false },
      gestures: { swipeToClose: false },
    });
  };

  const handleEditPress = (specialHour: SpecialHours) => {
    modal.show(
      () => (
        <SpecialHoursModal
          onSave={handleSave}
          onDelete={() => handleDelete(specialHour.id)}
          initialData={specialHour}
        />
      ),
      {
        position: 'center',

        animation: { type: 'scale', duration: 300 },
        background: { opacity: 0.5, closeOnPress: false },
        gestures: { swipeToClose: false },
      }
    );
  };

  return (
    <Box>
      <Text variant="b_14Regular_content" color="textSecondary" marginBottom="md_16">
        {t('venue.registration.openingTimes.specialHoursDescription')}
      </Text>

      {specialHours.length === 0 ? (
        <Button
          variant="outline"
          onPress={handleAddPress}
          title={t('venue.registration.openingTimes.addSpecialHours')}
          rightIcon={<Plus size={20} color={theme.colors.primary} />}
        />
      ) : (
        <Box gap="sm_12">
          {specialHours.map(sh => (
            <Pressable key={sh.id} onPress={() => handleEditPress(sh)}>
              <Box
                padding="md_16"
                borderRadius="md_12"
                backgroundColor="surface"
                borderWidth={1}
                borderColor="border"
                flexDirection="row"
                justifyContent="space-between"
                alignItems="center">
                <Box>
                  <Text variant="b_16Medium_button" marginBottom="xs_8">
                    {sh.name}
                  </Text>
                  <Text variant="b_14Regular_content" color="textSecondary">
                    {new Date(sh.startDate).toLocaleDateString()} -{' '}
                    {new Date(sh.endDate).toLocaleDateString()}
                  </Text>
                  {sh.isClosed ? (
                    <Text variant="l_12Medium_message" color="error" marginTop="xxs_4">
                      {t('common.closed')}
                    </Text>
                  ) : (
                    <Text variant="l_12Regular_helperText" color="textSecondary" marginTop="xxs_4">
                      {sh.open} - {sh.close}
                    </Text>
                  )}
                </Box>
                <Box flexDirection="row" gap="md_16" alignItems="center">
                  <Pencil size={20} color={theme.colors.textSecondary} />
                  <Pressable onPress={() => handleDelete(sh.id)}>
                    <Trash size={20} color={theme.colors.error} />
                  </Pressable>
                </Box>
              </Box>
            </Pressable>
          ))}
          <Button
            variant="ghost"
            onPress={handleAddPress}
            title={t('common.addMore')}
            rightIcon={<Plus size={16} color={theme.colors.primary} />}
          />
        </Box>
      )}

      {error && (
        <Text variant="l_12Medium_message" color="error" marginTop="xs_8">
          {error}
        </Text>
      )}
    </Box>
  );
};
