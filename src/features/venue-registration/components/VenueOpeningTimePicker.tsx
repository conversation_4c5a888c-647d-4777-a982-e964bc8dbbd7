import React, { useMemo, useRef, useState } from 'react';

import { useTranslation } from 'react-i18next';

import { Box, Pressable, Text } from '@/src/core/theme';
import { DatePicker, DatePickerHandle, Switch } from '@/src/shared/components';
import { useLocalizedDays } from '@/src/shared/hooks/useLocalizedDays';

import { DayOfWeek, OpeningHours } from '../types';

interface VenueOpeningTimePickerProps {
  hours: OpeningHours[];
  onHoursChange: (hours: OpeningHours[]) => void;
  error?: string;
}

const DAYS_OF_WEEK: DayOfWeek[] = [
  'monday',
  'tuesday',
  'wednesday',
  'thursday',
  'friday',
  'saturday',
  'sunday',
];

export const VenueOpeningTimePicker: React.FC<VenueOpeningTimePickerProps> = ({
  hours,
  onHoursChange,
  error,
}) => {
  const { t } = useTranslation();
  const { getDayName } = useLocalizedDays();
  const datePickerRef = useRef<DatePickerHandle>(null);
  const [editingTime, setEditingTime] = useState<{ day: DayOfWeek; type: 'open' | 'close' } | null>(
    null
  );

  const updateDayHours = (day: DayOfWeek, updates: Partial<OpeningHours>) => {
    const dayIndex = hours.findIndex(h => h.day === day);
    if (dayIndex !== -1) {
      const updatedHours = [...hours];
      updatedHours[dayIndex] = { ...updatedHours[dayIndex], ...updates };
      onHoursChange(updatedHours);
    } else {
      // If the day doesn't exist, create it. This can happen if the initial data is sparse.
      const newDayHours: OpeningHours = {
        day,
        open: '18:00',
        close: '02:00',
        isClosed: false,
        ...updates,
      };
      onHoursChange([...hours, newDayHours]);
    }
  };

  const handleTimePress = (day: DayOfWeek, type: 'open' | 'close') => {
    setEditingTime({ day, type });
    datePickerRef.current?.showPicker();
  };

  const onPickerChange = (date: Date | null) => {
    if (date && editingTime) {
      const hoursStr = date.getHours().toString().padStart(2, '0');
      const minutesStr = date.getMinutes().toString().padStart(2, '0');
      updateDayHours(editingTime.day, { [editingTime.type]: `${hoursStr}:${minutesStr}` });
    }
    setEditingTime(null);
  };

  const currentPickerDate = useMemo(() => {
    if (!editingTime) {
      return new Date();
    }
    const dayHours = hours.find(h => h.day === editingTime.day);
    if (!dayHours) {
      return new Date();
    }
    const timeString = dayHours[editingTime.type];
    const [h, m] = timeString.split(':');
    const d = new Date();
    d.setHours(parseInt(h, 10), parseInt(m, 10));
    return d;
  }, [editingTime, hours]);

  return (
    <Box>
      {DAYS_OF_WEEK.map(day => {
        const dayHours = hours.find(h => h.day === day) || {
          day,
          open: '18:00',
          close: '02:00',
          isClosed: true, // Default to closed if not specified
        };

        return (
          <Box
            key={day}
            paddingVertical="sm_12"
            borderBottomWidth={1}
            borderBottomColor="mainBorder">
            <Box flexDirection="row" justifyContent="space-between" alignItems="center">
              <Text variant="b_16Regular_input" flex={1} textTransform="capitalize">
                {getDayName(day)}
              </Text>

              {dayHours.isClosed ? (
                <Text variant="b_14Regular_content" color="secondaryText">
                  {t('common.closed')}
                </Text>
              ) : (
                <Box flexDirection="row" alignItems="center" gap="xs_8">
                  <Pressable onPress={() => handleTimePress(day, 'open')}>
                    <Box
                      flexDirection="row"
                      alignItems="center"
                      backgroundColor="elevatedBackground"
                      paddingHorizontal="sm_12"
                      paddingVertical="xs_8"
                      borderRadius="sm_8">
                      <Text variant="b_14Medium_button">{dayHours.open}</Text>
                    </Box>
                  </Pressable>

                  <Text variant="b_14Regular_content" color="secondaryText">
                    -
                  </Text>

                  <Pressable onPress={() => handleTimePress(day, 'close')}>
                    <Box
                      flexDirection="row"
                      alignItems="center"
                      backgroundColor="elevatedBackground"
                      paddingHorizontal="sm_12"
                      paddingVertical="xs_8"
                      borderRadius="sm_8">
                      <Text variant="b_14Medium_button">{dayHours.close}</Text>
                    </Box>
                  </Pressable>
                </Box>
              )}

              <Box width={50} alignItems="flex-end">
                <Switch
                  isChecked={!dayHours.isClosed}
                  onToggle={isToggled => {
                    updateDayHours(day, { isClosed: !isToggled });
                  }}
                />
              </Box>
            </Box>
          </Box>
        );
      })}

      {error && (
        <Text variant="l_12Medium_message" color="errorMain" marginTop="xs_8">
          {error}
        </Text>
      )}

      <DatePicker
        ref={datePickerRef}
        type="time"
        value={currentPickerDate}
        onChange={onPickerChange}
        is24Hour
      />
    </Box>
  );
};
