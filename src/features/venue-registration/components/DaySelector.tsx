import React from 'react';

import { useTranslation } from 'react-i18next';

import { Box, Pressable, Text, useTheme } from '@/src/core/theme';
import { useLocalizedDays } from '@/src/shared/hooks/useLocalizedDays';

import { DayOfWeek } from '../types';

interface DaySelectorProps {
  selectedDays: DayOfWeek[];
  onSelectionChange: (days: DayOfWeek[]) => void;
}

const DAYS_OF_WEEK: DayOfWeek[] = [
  'monday',
  'tuesday',
  'wednesday',
  'thursday',
  'friday',
  'saturday',
  'sunday',
];

export const DaySelector: React.FC<DaySelectorProps> = ({ selectedDays, onSelectionChange }) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const { getDayName } = useLocalizedDays();

  const toggleDay = (day: DayOfWeek) => {
    const newSelection = selectedDays.includes(day)
      ? selectedDays.filter(d => d !== day)
      : [...selectedDays, day];
    onSelectionChange(newSelection);
  };

  return (
    <Box flexDirection="row" justifyContent="space-around">
      {DAYS_OF_WEEK.map(day => (
        <Pressable key={day} onPress={() => toggleDay(day)}>
          <Box
            width={40}
            height={40}
            borderRadius="circle_9999"
            backgroundColor={selectedDays.includes(day) ? 'primary' : 'elevatedBackground'}
            justifyContent="center"
            alignItems="center">
            <Text variant="b_14Medium_button" color={selectedDays.includes(day) ? 'white' : 'text'}>
              {getDayName(day, 'narrow')}
            </Text>
          </Box>
        </Pressable>
      ))}
    </Box>
  );
};
