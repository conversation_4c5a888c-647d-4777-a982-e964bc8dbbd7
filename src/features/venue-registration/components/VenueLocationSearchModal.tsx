import React, { useCallback, useEffect, useMemo, useState } from 'react';

import { FlatList, Keyboard, Platform, Pressable } from 'react-native';

import { MaterialIcons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import { MagnifyingGlass, MapPin, X } from 'phosphor-react-native';
import { useTranslation } from 'react-i18next';
import { magicModal, useMagicModal } from 'react-native-magic-modal';
import Animated, { FadeInDown } from 'react-native-reanimated';

import { FOURSQUARE_CATEGORIES, FoursquarePlace } from '@/src/core';
import { Box, SafeAreaWrapper, Text, TextInput, useTheme } from '@/src/core/theme';
import { useResponsive } from '@/src/core/theme/useResponsive';
import { useFoursquareLocationSearch } from '@/src/features/event-registration/hooks/useFoursquareLocationSearch';
import { Divider } from '@/src/shared/components';

import { VenueLocation } from '../types';

interface VenueLocationSearchModalProps {
  currentLocation: VenueLocation | null;
}

// Memoized location item component to prevent unnecessary re-renders
const VenueLocationItem = React.memo(
  ({
    item,
    onPress,
    iconSize,
    iconColor,
  }: {
    item: FoursquarePlace;
    onPress: (item: FoursquarePlace) => void;
    iconSize: number;
    iconColor: string;
  }) => {
    return (
      <Pressable
        onPress={() => onPress(item)}
        style={({ pressed }) => ({
          opacity: pressed ? 0.7 : 1,
        })}>
        <Box
          flexDirection="row"
          alignItems="center"
          paddingHorizontal="md_16"
          paddingVertical="md_16"
          backgroundColor="background">
          <Box marginRight="sm_12">
            <MapPin size={iconSize} color={iconColor} />
          </Box>
          <Box flex={1}>
            <Text variant="b_16SemiBold_button" color="text" numberOfLines={1}>
              {item.name}
            </Text>
            <Text variant="b_14Regular_content" color="textSecondary" numberOfLines={2}>
              {item.location?.formatted_address || 'Address not available'}
            </Text>
            {item.categories && item.categories.length > 0 && (
              <Text variant="l_12Regular_helperText" color="textTertiary" numberOfLines={1}>
                {item.categories[0].name}
              </Text>
            )}
          </Box>
          {item.distance && (
            <Text variant="l_12Regular_helperText" color="textSecondary">
              {Math.round(item.distance)}m
            </Text>
          )}
        </Box>
      </Pressable>
    );
  }
);

VenueLocationItem.displayName = 'VenueLocationItem';

export const VenueLocationSearchModal: React.FC<VenueLocationSearchModalProps> = ({
  currentLocation,
}) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const { select } = useResponsive();
  const { hide } = useMagicModal<VenueLocation | null>();

  // Initialize Foursquare location search with optimized hook
  const { searchQuery, debouncedQuery, updateSearch, updateLocation, places, isSearching } =
    useFoursquareLocationSearch({
      radius: 10000, // 10km radius for venues
      limit: 20,
      // categories: [FOURSQUARE_CATEGORIES.BAR, FOURSQUARE_CATEGORIES.NIGHTCLUB],
      sort: 'popularity',
      debounceDelay: 300,
    });

  // Get user location on mount
  useEffect(() => {
    const getCurrentLocation = async () => {
      try {
        if (Platform.OS === 'ios') {
          const { status } = await Location.requestForegroundPermissionsAsync();
          if (status !== 'granted') {
            console.warn('Location permission denied');
            return;
          }
        }

        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.Balanced,
        });

        updateLocation(location.coords.latitude, location.coords.longitude);
      } catch (error) {
        console.warn('Failed to get current location:', error);
        // Fallback to NYC coordinates if available in currentLocation
        if (currentLocation?.latitude && currentLocation?.longitude) {
          updateLocation(currentLocation.latitude, currentLocation.longitude);
        } else {
          updateLocation(40.7128, -74.006); // NYC as fallback
        }
      }
    };

    getCurrentLocation();
  }, [updateLocation, currentLocation]);

  // Handle venue location selection
  const handleSelectPlace = useCallback(
    (place: FoursquarePlace) => {
      Keyboard.dismiss();

      console.log('Selected place:', place); // Debug log

      const location: VenueLocation = {
        address: place.location?.address || place.location?.formatted_address || '',
        city: place.location?.locality || place.location?.region || '',
        state: place.location?.region || place.location?.admin_region || '',
        country: place.location?.country || '',
        postalCode: place.location?.postcode || '',
        latitude: place.geocodes?.main?.latitude || place.latitude || 0,
        longitude: place.geocodes?.main?.longitude || place.longitude || 0,
        foursquareId: place.fsq_place_id,
        formattedAddress: place.location?.formatted_address || place.name || '',
      };

      console.log('Mapped location:', location); // Debug log
      hide(location);
    },
    [hide]
  );

  const handleCancel = useCallback(() => {
    hide(null);
  }, [hide]);

  // Memoized values for icon props
  const iconSize = useMemo(() => select({ phone: 20, tablet: 24 }), [select]);
  const iconColor = theme.colors.textSecondary;

  // Render venue location item with memoized component
  const renderLocationItem = useCallback(
    ({ item }: { item: FoursquarePlace }) => (
      <VenueLocationItem
        item={item}
        onPress={handleSelectPlace}
        iconSize={iconSize}
        iconColor={iconColor}
      />
    ),
    [handleSelectPlace, iconSize, iconColor]
  );

  // Memoized search results
  const searchResults = useMemo(() => {
    if (debouncedQuery.length < 2) return [];
    return places || [];
  }, [places, debouncedQuery]);

  // Memoized keyExtractor
  const keyExtractor = useCallback((item: FoursquarePlace) => item.fsq_place_id, []);

  // Memoized empty states
  const EmptyState = useMemo(() => {
    if (searchQuery.length >= 2 && !isSearching && searchResults.length === 0) {
      return (
        <Animated.View
          entering={FadeInDown.duration(300)}
          style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
          <Box padding="xl_32" alignItems="center">
            <MaterialIcons name="location-off" size={48} color={theme.colors.textTertiary} />
            <Text variant="b_16Regular_input" color="textSecondary" marginTop="md_16">
              {t('venue.registration.locationSearch.noResults', 'No venues found')}
            </Text>
            <Text
              variant="b_14Regular_content"
              color="textTertiary"
              textAlign="center"
              marginTop="xs_8">
              {t(
                'venue.registration.locationSearch.noResultsHint',
                'Try searching for a different venue or address'
              )}
            </Text>
          </Box>
        </Animated.View>
      );
    }

    if (searchQuery.length < 2) {
      return (
        <Box flex={1} justifyContent="center" alignItems="center">
          <Box padding="xl_32" alignItems="center">
            <MagnifyingGlass size={48} color={theme.colors.textTertiary} />
            <Text variant="b_16Regular_input" color="textSecondary" marginTop="md_16">
              {t('venue.registration.locationSearch.searchPrompt', 'Search for a venue')}
            </Text>
            <Text
              variant="b_14Regular_content"
              color="textTertiary"
              textAlign="center"
              marginTop="xs_8">
              {t(
                'venue.registration.locationSearch.searchHint',
                'Enter a venue name or address to get started'
              )}
            </Text>
          </Box>
        </Box>
      );
    }

    return null;
  }, [searchQuery.length, isSearching, searchResults.length, theme.colors.textTertiary, t]);

  return (
    <SafeAreaWrapper style={{ flex: 1 }}>
      <Box
        flex={1}
        backgroundColor="background"
        style={{
          overflow: 'hidden', // Prevent modal container from scrolling
          maxHeight: '100%', // Constrain modal height
        }}>
        {/* Fixed Header - Non-scrollable */}
        <Box
          backgroundColor="background"
          paddingHorizontal="md_16"
          paddingTop="md_16"
          paddingBottom="sm_12"
          style={{
            elevation: 2,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: 0.1,
            shadowRadius: 2,
            zIndex: 1,
          }}>
          <Box flexDirection="row" alignItems="center" marginBottom="md_16">
            <Pressable
              onPress={handleCancel}
              style={({ pressed }) => ({ opacity: pressed ? 0.7 : 1 })}>
              <Box padding="xs_8">
                <X size={select({ phone: 24, tablet: 28 })} color={theme.colors.text} />
              </Box>
            </Pressable>
            <Text variant="h_18SemiBold_cardTitle" color="text" marginLeft="sm_12">
              {t('venue.registration.locationSearch.title', 'Search Venue Location')}
            </Text>
          </Box>

          {/* Search Input */}
          <TextInput
            placeholder={t(
              'venue.registration.locationSearch.placeholder',
              'Search for venues, addresses...'
            )}
            value={searchQuery}
            label={t('venue.registration.locationSearch.inputLabel', 'Search location')}
            onChangeText={updateSearch}
            leading={
              <MagnifyingGlass
                size={select({ phone: 20, tablet: 24 })}
                color={theme.colors.textSecondary}
                weight="regular"
              />
            }
            showClearButton
            autoCorrect={false}
            autoCapitalize="none"
            returnKeyType="search"
            autoFocus
          />
        </Box>

        {/* Results List - Only this should scroll */}
        <Box flex={1}>
          <FlatList
            data={searchResults}
            renderItem={renderLocationItem}
            keyExtractor={keyExtractor}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={true}
            ItemSeparatorComponent={() => <Divider />}
            contentContainerStyle={{
              paddingBottom: 40,
              flexGrow: searchResults.length === 0 ? 1 : undefined,
            }}
            style={{ flex: 1 }}
            nestedScrollEnabled={false}
            removeClippedSubviews={false}
            maxToRenderPerBatch={15}
            windowSize={10}
            initialNumToRender={15}
            keyboardDismissMode="on-drag"
            ListEmptyComponent={EmptyState}
            bounces={true}
            scrollEventThrottle={16}
            directionalLockEnabled={true}
            disableIntervalMomentum={true}
          />
        </Box>
      </Box>
    </SafeAreaWrapper>
  );
};
