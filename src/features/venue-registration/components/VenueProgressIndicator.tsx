import React from 'react';

import Animated, { interpolate, useAnimatedStyle, withTiming } from 'react-native-reanimated';

import { Box, useTheme } from '@/src/core/theme';

interface VenueProgressIndicatorProps {
  currentStep: number;
  totalSteps: number;
}

export const VenueProgressIndicator: React.FC<VenueProgressIndicatorProps> = ({
  currentStep,
  totalSteps,
}) => {
  const progress = (currentStep + 1) / totalSteps;
  const { colors, borderRadii } = useTheme();

  const animatedStyle = useAnimatedStyle(() => {
    return {
      width: withTiming(`${interpolate(progress, [0, 1], [0, 100])}%`, {
        duration: 300,
      }),
    };
  });

  return (
    <Box paddingVertical="xxs_4">
      <Box height={4} backgroundColor="elevatedBackground" borderRadius="xxs_2" overflow="hidden">
        <Animated.View
          style={[
            {
              height: '100%',
              backgroundColor: colors.primary,
              borderRadius: borderRadii.xxs_2,
            },
            animatedStyle,
          ]}
        />
      </Box>
    </Box>
  );
};
