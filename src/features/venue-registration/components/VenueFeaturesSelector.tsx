import React from 'react';

import {
  Bank,
  Car,
  Cigarette,
  CoatHanger,
  Couch,
  CreditCard,
  Crown,
  Disc,
  Door,
  IconProps,
  Microphone,
  Money,
  MusicNotes,
  PersonSimpleRun,
  PixLogo,
  Sun,
  Wheelchair,
  WifiHigh,
} from 'phosphor-react-native';
import { useTranslation } from 'react-i18next';

import { Box, Text, useTheme } from '@/src/core/theme';
import { Chip } from '@/src/shared/components';

import { VenueFeature } from '../types';

// Predefined features
const VENUE_FEATURES: VenueFeature[] = [
  // Entertainment
  { id: 'live_music', category: 'entertainment', label: 'Live Music', icon: <MusicNotes /> },
  { id: 'dj_booth', category: 'entertainment', label: 'DJ Booth', icon: <Disc /> },
  { id: 'dance_floor', category: 'entertainment', label: 'Dance Floor', icon: <PersonSimpleRun /> },
  { id: 'karaoke', category: 'entertainment', label: 'Karaoke', icon: <Microphone /> },

  // Areas
  { id: 'vip_area', category: 'areas', label: 'VIP Area', icon: <Crown /> },
  { id: 'lounge', category: 'areas', label: 'Lounge', icon: <Couch /> },
  { id: 'private_event_area', category: 'areas', label: 'Private Event Area', icon: <Door /> },
  { id: 'outdoor_area', category: 'areas', label: 'Outdoor Area', icon: <Sun /> },
  { id: 'smoking_area', category: 'areas', label: 'Smoking Area', icon: <Cigarette /> },

  // Amenities
  { id: 'parking', category: 'amenities', label: 'Parking', icon: <Car /> },
  { id: 'wifi', category: 'amenities', label: 'WiFi', icon: <WifiHigh /> },
  { id: 'accessibility', category: 'amenities', label: 'Accessibility', icon: <Wheelchair /> },
  { id: 'coat_check', category: 'amenities', label: 'Coat Check', icon: <CoatHanger /> },
  { id: 'atm', category: 'amenities', label: 'ATM', icon: <Money /> },

  // Payment
  { id: 'cash', category: 'payment', label: 'Cash', icon: <Money /> },
  { id: 'credit_card', category: 'payment', label: 'Credit Card', icon: <CreditCard /> },
  { id: 'debit_card', category: 'payment', label: 'Debit Card', icon: <Bank /> },
  { id: 'pix', category: 'payment', label: 'PIX', icon: <PixLogo /> },
];

interface VenueFeaturesSelectorProps {
  selectedFeatures: string[];
  onFeaturesChange: (features: string[]) => void;
  error?: string;
}

export const VenueFeaturesSelector: React.FC<VenueFeaturesSelectorProps> = ({
  selectedFeatures,
  onFeaturesChange,
  error,
}) => {
  const { t } = useTranslation();
  const theme = useTheme();

  const toggleFeature = (featureId: string) => {
    if (selectedFeatures.includes(featureId)) {
      onFeaturesChange(selectedFeatures.filter(id => id !== featureId));
    } else {
      onFeaturesChange([...selectedFeatures, featureId]);
    }
  };

  const renderFeaturesByCategory = (category: VenueFeature['category']) => {
    const categoryFeatures = VENUE_FEATURES.filter(f => f.category === category);

    return (
      <Box marginBottom="lg_24">
        <Text variant="h_16Medium_formLabel" marginBottom="sm_12">
          {t(`venue.registration.features.categories.${category}`)}
        </Text>

        <Box flexDirection="row" flexWrap="wrap" gap="xs_8">
          {categoryFeatures.map(feature => {
            const isSelected = selectedFeatures.includes(feature.id);

            return (
              <Chip
                key={feature.id}
                label={feature.label}
                chipVariant={isSelected ? 'solidMedium' : 'outlineMedium'}
                onPress={() => toggleFeature(feature.id)}
                rightIcon={React.cloneElement(feature.icon as React.ReactElement<IconProps>, {
                  size: 16,
                  color: isSelected
                    ? theme.colors.buttonTintedText
                    : theme.colors.buttonOutlineText,
                  weight: isSelected ? 'bold' : 'regular',
                })}
              />
            );
          })}
        </Box>
      </Box>
    );
  };

  return (
    <Box>
      {renderFeaturesByCategory('entertainment')}
      {renderFeaturesByCategory('areas')}
      {renderFeaturesByCategory('amenities')}
      {renderFeaturesByCategory('payment')}

      {error && (
        <Text variant="l_12Medium_message" color="errorMain" marginTop="xs_8">
          {error}
        </Text>
      )}
    </Box>
  );
};
