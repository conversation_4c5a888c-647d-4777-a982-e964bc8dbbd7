import React, { useCallback } from 'react';

import { CaretRight, MapPin } from 'phosphor-react-native';
import { useTranslation } from 'react-i18next';
import { MagicModalHideReason, magicModal } from 'react-native-magic-modal';

import { Box, Pressable, Text } from '@/src/core/theme';

import { VenueLocation } from '../types';
import { VenueLocationSearchModal } from './VenueLocationSearchModal';

interface VenueLocationPickerProps {
  location: VenueLocation | null;
  onLocationChange: (location: VenueLocation) => void;
  error?: string;
}

export const VenueLocationPicker: React.FC<VenueLocationPickerProps> = ({
  location,
  onLocationChange,
  error,
}) => {
  const { t } = useTranslation();

  const handleOpenLocationSearch = useCallback(async () => {
    const result = await magicModal.show<VenueLocation>(
      () => <VenueLocationSearchModal currentLocation={location} />,
      { swipeDirection: undefined }
    ).promise;

    if (result.reason !== MagicModalHideReason.INTENTIONAL_HIDE) {
      return;
    }

    // Handle both old and new result formats
    const selectedLocation = result.data;

    if (selectedLocation) {
      onLocationChange(selectedLocation);
    }
  }, [location, onLocationChange]);

  return (
    <Box>
      <Text variant="h_16Medium_formLabel" marginBottom="sm_12">
        {t('venue.registration.basicDetails.location')}
      </Text>

      <Pressable onPress={handleOpenLocationSearch}>
        <Box
          borderWidth={1}
          borderColor={error ? 'errorMain' : 'mainBorder'}
          borderRadius="lg_16"
          padding="md_16"
          flexDirection="row"
          alignItems="center"
          justifyContent="space-between"
          backgroundColor="inputBackground">
          <Box flexDirection="row" alignItems="center" flex={1}>
            <MapPin size={24} color={location ? 'text' : 'textMuted'} style={{ marginRight: 12 }} />
            <Box flex={1}>
              {location ? (
                <Box>
                  <Text variant="b_16Regular_input" numberOfLines={1}>
                    {location.formattedAddress || location.address || 'Address not available'}
                  </Text>
                  {(location.city || location.state) && (
                    <Text variant="l_12Regular_helperText" color="secondaryText" numberOfLines={1}>
                      {[location.city, location.state].filter(Boolean).join(', ')}
                    </Text>
                  )}
                </Box>
              ) : (
                <Text variant="b_16Regular_input" color="placeholderText">
                  {t('venue.registration.basicDetails.locationPlaceholder')}
                </Text>
              )}
            </Box>
          </Box>

          <CaretRight size={20} color="textMuted" />
        </Box>
      </Pressable>

      {error && (
        <Text variant="l_12Medium_message" color="errorMain" marginTop="xs_8">
          {error}
        </Text>
      )}
    </Box>
  );
};
