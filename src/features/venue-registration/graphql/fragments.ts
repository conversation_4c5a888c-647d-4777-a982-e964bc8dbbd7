import { gql } from '@apollo/client';

export const VENUE_TYPE_FRAGMENT = gql`
  fragment VenueTypeFields on VenueTypeHierarchy {
    id
    name
    description
  }
`;

export const VENUE_CONTENT_FRAGMENT = gql`
  fragment VenueContentFields on VenueContent {
    primaryImage
    images
    description
    tags
    amenities
  }
`;

export const VENUE_BASIC_FRAGMENT = gql`
  fragment VenueBasicFields on Venue {
    id
    name
    description
    address
    city
    state
    country
    postalCode
    latitude
    longitude
    phone
    email
    website
    capacity
    rating
    reviewCount
    isActive
    createdAt
    updatedAt
  }
`;

export const VENUE_FULL_FRAGMENT = gql`
  ${VENUE_BASIC_FRAGMENT}
  ${VENUE_TYPE_FRAGMENT}
  ${VENUE_CONTENT_FRAGMENT}

  fragment VenueFullFields on Venue {
    ...VenueBasicFields
    venueType {
      ...VenueTypeFields
    }
    content {
      ...VenueContentFields
    }
    distance
    venueTypeId
  }
`;
