import { gql } from '@apollo/client';

import { VENUE_FULL_FRAGMENT } from './fragments';

export const CREATE_VENUE_MUTATION = gql`
  ${VENUE_FULL_FRAGMENT}

  mutation CreateVenue($input: CreateVenueInput!) {
    createVenue(input: $input) {
      ...VenueFullFields
    }
  }
`;

export const UPDATE_VENUE_MUTATION = gql`
  ${VENUE_FULL_FRAGMENT}

  mutation UpdateVenue($id: UUID!, $input: UpdateVenueInput!) {
    updateVenue(id: $id, input: $input) {
      ...VenueFullFields
    }
  }
`;

export const DELETE_VENUE_MUTATION = gql`
  mutation DeleteVenue($id: UUID!) {
    deleteVenue(id: $id)
  }
`;

export const UPLOAD_VENUE_IMAGE_MUTATION = gql`
  mutation UploadVenueImage($venueId: UUID!, $image: Upload!) {
    uploadVenueImage(venueId: $venueId, image: $image)
  }
`;

export const UPLOAD_VENUE_IMAGES_MUTATION = gql`
  mutation UploadVenueImages($venueId: UUID!, $images: [Upload!]!) {
    uploadVenueImages(venueId: $venueId, images: $images)
  }
`;

export const DELETE_VENUE_IMAGE_MUTATION = gql`
  mutation DeleteVenueImage($venueId: UUID!, $imageUrl: String!) {
    deleteVenueImage(venueId: $venueId, imageUrl: $imageUrl)
  }
`;

export const SET_PRIMARY_VENUE_IMAGE_MUTATION = gql`
  mutation SetPrimaryVenueImage($venueId: UUID!, $imageUrl: String!) {
    setPrimaryVenueImage(venueId: $venueId, imageUrl: $imageUrl)
  }
`;

export const ACTIVATE_VENUE_MUTATION = gql`
  ${VENUE_FULL_FRAGMENT}

  mutation ActivateVenue($venueId: UUID!) {
    activateVenue(venueId: $venueId) {
      ...VenueFullFields
    }
  }
`;

export const VERIFY_VENUE_MUTATION = gql`
  ${VENUE_FULL_FRAGMENT}

  mutation VerifyVenue($venueId: UUID!) {
    verifyVenue(venueId: $venueId) {
      ...VenueFullFields
    }
  }
`;
