import { gql } from '@apollo/client';

import { VENUE_BASIC_FRAGMENT, VENUE_FULL_FRAGMENT } from './fragments';

export const GET_VENUE_QUERY = gql`
  ${VENUE_FULL_FRAGMENT}

  query GetVenue($id: UUID!) {
    venue(id: $id) {
      ...VenueFullFields
    }
  }
`;

export const GET_VENUES_QUERY = gql`
  ${VENUE_BASIC_FRAGMENT}

  query GetVenues($limit: Int, $offset: Int) {
    venues(limit: $limit, offset: $offset) {
      ...VenueBasicFields
    }
  }
`;

export const SEARCH_VENUES_QUERY = gql`
  ${VENUE_BASIC_FRAGMENT}

  query SearchVenues($input: VenueSearchInput!) {
    searchVenues(input: $input) {
      venues {
        ...VenueBasicFields
      }
      totalCount
      hasNextPage
      hasPreviousPage
    }
  }
`;

export const GET_VENUES_NEARBY_QUERY = gql`
  ${VENUE_BASIC_FRAGMENT}

  query GetVenuesNearby($latitude: Float!, $longitude: Float!, $radius: Float, $limit: Int) {
    venuesNearby(latitude: $latitude, longitude: $longitude, radius: $radius, limit: $limit) {
      ...VenueBasicFields
      distance
    }
  }
`;

export const GET_VENUE_TYPES_QUERY = gql`
  query GetVenueTypes {
    venueTypes {
      id
      name
      description
      icon
      parent {
        id
        name
      }
      children {
        id
        name
        description
      }
    }
  }
`;

export const GET_VENUE_TYPE_QUERY = gql`
  query GetVenueType($id: Int!) {
    venueType(id: $id) {
      id
      name
      description
      icon
      parent {
        id
        name
      }
      children {
        id
        name
        description
      }
    }
  }
`;

export const SEARCH_VENUE_TYPES_QUERY = gql`
  query SearchVenueTypes($query: String!) {
    searchVenueTypes(query: $query) {
      id
      name
      description
      icon
    }
  }
`;

export const GET_VENUE_IMAGES_QUERY = gql`
  query GetVenueImages($venueId: UUID!) {
    venueImages(venueId: $venueId) {
      id
      url
      isPrimary
      order
      createdAt
    }
  }
`;

export const GET_PRIMARY_VENUE_IMAGE_QUERY = gql`
  query GetPrimaryVenueImage($venueId: UUID!) {
    primaryVenueImage(venueId: $venueId) {
      id
      url
      isPrimary
      order
      createdAt
    }
  }
`;
