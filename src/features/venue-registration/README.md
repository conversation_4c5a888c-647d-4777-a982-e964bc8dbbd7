# Venue Registration Feature

## Overview
The venue registration feature allows club owners and venue managers to register their establishments on Movuca. It follows a multi-step flow ensuring all necessary information is collected for a comprehensive venue profile.

## Flow Architecture

### Step 1: Basic Details
- Venue name
- Music genres/types (multiple selection)
- Dress code requirements (multiple selection)
- Description
- Location (integrated with Foursquare Places API)
- Venue photos/media upload

### Step 2: Opening Times
- Weekly schedule (Monday - Sunday)
- Special hours for holidays/events
- Closed days configuration
- Happy hour times

### Step 3: Club Features
- Amenities selection (parking, wifi, accessibility)
- Special areas (VIP, lounge, private event spaces)
- Entertainment options (live music, DJ booth)
- Payment methods accepted
- Capacity information

### Step 4: Summary & Submit
- Review all information
- Edit any section
- Submit for approval
- Success confirmation

## State Management
- Uses Zustand store for persistent form state across steps
- Draft management for interrupted flows
- Validation state tracking per step

## Key Components
- `VenueMediaUpload` - Handle venue photos with compression
- `VenueOpeningTimePicker` - Complex time scheduling UI
- `VenueLocationPicker` - Foursquare integration for accurate location
- `VenueFeaturesSelector` - Multi-select features with categories
- `VenuePreviewCard` - Summary view of venue information

## Navigation
- Stack navigator with transition animations
- Bottom action bar with Back/Next/Submit buttons
- Progress indicator showing current step
- Exit confirmation for unsaved changes
