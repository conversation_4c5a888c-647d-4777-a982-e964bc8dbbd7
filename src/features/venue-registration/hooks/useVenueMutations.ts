import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useMutation, useQueryClient } from '@tanstack/react-query';

import { toast } from '@/src/core/libs';
import { VenueRegistrationStackParamList } from '@/src/core/navigation/types';
import { extractErrorMessage } from '@/src/shared/utils/errors';

import { type Venue, venueService } from '../services/venueService';
import { useVenueRegistrationStore } from '../store/venueRegistrationStore';
import { VenueFormData } from '../types';

type NavigationProp = NativeStackNavigationProp<VenueRegistrationStackParamList>;

export const useCreateVenue = () => {
  const navigation = useNavigation<NavigationProp>();
  const queryClient = useQueryClient();
  const { resetForm, clearCurrentDraft } = useVenueRegistrationStore();

  return useMutation({
    mutationFn: (data: VenueFormData) => venueService.createVenue(data),
    onSuccess: response => {
      if (response.success && response.data) {
        resetForm();
        clearCurrentDraft();

        toast.success('Venue registered successfully!');

        // Invalidate related queries
        queryClient.invalidateQueries({ queryKey: ['myVenues'] });

        navigation.navigate('VenueSuccess', { venueId: response.data.id });
      } else if (response.success === false) {
        const errorMessage = extractErrorMessage(response.errors, 'Failed to register venue');
        console.log('Create venue error:', response.errors);

        toast.error(errorMessage);
      }
    },
    onError: error => {
      console.error('Create venue error:', error);
      const errorMessage = extractErrorMessage(error, 'An unexpected error occurred');
      toast.error(errorMessage);
    },
  });
};

export const useUpdateVenue = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ venueId, data }: { venueId: string; data: Partial<VenueFormData> }) =>
      venueService.updateVenue(venueId, data),
    onSuccess: (response, variables) => {
      if (response.success) {
        toast.success('Venue updated successfully!');

        // Invalidate specific venue and list queries
        queryClient.invalidateQueries({ queryKey: ['venue', variables.venueId] });
        queryClient.invalidateQueries({ queryKey: ['myVenues'] });
      } else {
        const errorMessage = extractErrorMessage(response.errors, 'Failed to update venue');
        toast.error(errorMessage);
      }
    },
    onError: error => {
      console.error('Update venue error:', error);
      const errorMessage = extractErrorMessage(error, 'An unexpected error occurred');
      toast.error(errorMessage);
    },
  });
};

export const useDeleteVenue = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (venueId: string) => venueService.deleteVenue(venueId),
    onSuccess: response => {
      if (response.success) {
        toast.success('Venue deleted successfully!');

        // Invalidate venue list queries
        queryClient.invalidateQueries({ queryKey: ['myVenues'] });
      } else {
        const errorMessage = extractErrorMessage(response.errors, 'Failed to delete venue');
        toast.error(errorMessage);
      }
    },
    onError: error => {
      console.error('Delete venue error:', error);
      const errorMessage = extractErrorMessage(error, 'An unexpected error occurred');
      toast.error(errorMessage);
    },
  });
};

export const useUploadVenueMedia = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ venueId, images }: { venueId: string; images: File[] }) =>
      venueService.uploadVenueImages(venueId, images),
    onSuccess: (response, variables) => {
      if (response.success) {
        toast.success('Photos uploaded successfully!');

        // Invalidate venue query to refresh photos
        queryClient.invalidateQueries({ queryKey: ['venue', variables.venueId] });
      } else {
        const errorMessage = extractErrorMessage(response.errors, 'Failed to upload photos');
        toast.error(errorMessage);
      }
    },
    onError: error => {
      console.error('Upload venue media error:', error);
      const errorMessage = extractErrorMessage(error, 'An unexpected error occurred');
      toast.error(errorMessage);
    },
  });
};

export const useActivateVenue = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (venueId: string) => venueService.activateVenue(venueId),
    onSuccess: (response, venueId) => {
      if (response.success) {
        toast.success('Venue activated successfully!');

        // Invalidate venue queries to refresh status
        queryClient.invalidateQueries({ queryKey: ['venue', venueId] });
        queryClient.invalidateQueries({ queryKey: ['venues'] });
      } else {
        const errorMessage = extractErrorMessage(response.errors, 'Failed to activate venue');
        toast.error(errorMessage);
      }
    },
    onError: error => {
      console.error('Activate venue error:', error);
      const errorMessage = extractErrorMessage(error, 'An unexpected error occurred');
      toast.error(errorMessage);
    },
  });
};

export const useVerifyVenue = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (venueId: string) => venueService.verifyVenue(venueId),
    onSuccess: (response, venueId) => {
      if (response.success) {
        toast.success('Venue verified successfully!');

        // Invalidate venue queries to refresh status
        queryClient.invalidateQueries({ queryKey: ['venue', venueId] });
        queryClient.invalidateQueries({ queryKey: ['venues'] });
      } else {
        const errorMessage = extractErrorMessage(response.errors, 'Failed to verify venue');
        toast.error(errorMessage);
      }
    },
    onError: error => {
      console.error('Verify venue error:', error);
      const errorMessage = extractErrorMessage(error, 'An unexpected error occurred');
      toast.error(errorMessage);
    },
  });
};
