import { useInfiniteQuery, useQuery } from '@tanstack/react-query';

import {
  type Venue,
  type VenueImage,
  type VenueSearchInput,
  type VenueTypeHierarchy,
  venueService,
} from '../services/venueService';

export const useVenue = (venueId: string, enabled = true) => {
  return useQuery({
    queryKey: ['venue', venueId],
    queryFn: () => venueService.getVenue(venueId),
    enabled: enabled && !!venueId,
    select: response => {
      if (response.success) {
        return response.data;
      }
      throw new Error(response.errors?.[0]?.message || 'Failed to fetch venue');
    },
  });
};

export const useVenues = (limit?: number, offset?: number) => {
  return useQuery({
    queryKey: ['venues', limit, offset],
    queryFn: () => venueService.getVenues(limit, offset),
    select: response => {
      if (response.success) {
        return response.data;
      }
      throw new Error(response.errors?.[0]?.message || 'Failed to fetch venues');
    },
  });
};

export const useSearchVenues = (input: VenueSearchInput, enabled = true) => {
  return useQuery({
    queryKey: ['searchVenues', input],
    queryFn: () => venueService.searchVenues(input),
    enabled: enabled && (!!input.query || !!input.latitude || !!input.city),
    select: response => {
      if (response.success) {
        return response.data;
      }
      throw new Error(response.errors?.[0]?.message || 'Failed to search venues');
    },
  });
};

export const useVenuesNearby = (
  latitude: number,
  longitude: number,
  radius?: number,
  limit?: number,
  enabled = true
) => {
  return useQuery({
    queryKey: ['venuesNearby', latitude, longitude, radius, limit],
    queryFn: () => venueService.getVenuesNearby(latitude, longitude, radius, limit),
    enabled: enabled && !!latitude && !!longitude,
    select: response => {
      if (response.success) {
        return response.data;
      }
      throw new Error(response.errors?.[0]?.message || 'Failed to fetch nearby venues');
    },
  });
};

export const useVenueTypes = () => {
  return useQuery({
    queryKey: ['venueTypes'],
    queryFn: () => venueService.getVenueTypes(),
    staleTime: 1000 * 60 * 60, // 1 hour - venue types don't change often
    select: response => {
      if (response.success) {
        return response.data;
      }
      throw new Error(response.errors?.[0]?.message || 'Failed to fetch venue types');
    },
  });
};

export const useVenueImages = (venueId: string, enabled = true) => {
  return useQuery({
    queryKey: ['venueImages', venueId],
    queryFn: () => venueService.getVenueImages(venueId),
    enabled: enabled && !!venueId,
    select: response => {
      if (response.success) {
        return response.data;
      }
      throw new Error(response.errors?.[0]?.message || 'Failed to fetch venue images');
    },
  });
};
