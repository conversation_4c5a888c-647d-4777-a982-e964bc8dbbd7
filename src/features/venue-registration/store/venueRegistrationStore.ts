import { ImagePickerAsset } from 'expo-image-picker';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

import { createZustandMMKVStorage, userStorage } from '@/src/core/storage';

import {
  DayOfWeek,
  OpeningHours,
  SpecialHours,
  VenueDraft,
  VenueFormData,
  VenueLocation,
  VenueRegistrationStepValidation,
} from '../types';

interface VenueRegistrationStore {
  // Draft management
  currentDraft: VenueDraft | null;
  drafts: VenueDraft[];

  // Form data
  formData: VenueFormData;

  // Validation state
  stepValidation: VenueRegistrationStepValidation;

  // Navigation
  currentStep: number;

  // Actions - Draft management
  createDraft: () => void;
  loadDraft: (draftId: string) => void;
  saveDraft: () => void;
  deleteDraft: (draftId: string) => void;
  clearCurrentDraft: () => void;

  // Actions - Form data updates
  updateBasicDetails: (
    data: Partial<
      Pick<
        VenueFormData,
        'name' | 'description' | 'musicGenres' | 'dressCodes' | 'location' | 'photos'
      >
    >
  ) => void;
  updateOpeningTimes: (
    data: Partial<Pick<VenueFormData, 'regularHours' | 'specialHours' | 'happyHours'>>
  ) => void;
  updateFeatures: (
    data: Partial<
      Pick<
        VenueFormData,
        'features' | 'capacity' | 'minimumAge' | 'phoneNumber' | 'email' | 'website' | 'instagram'
      >
    >
  ) => void;

  // Actions - Photos
  addPhotos: (photos: ImagePickerAsset[]) => void;
  removePhoto: (index: number) => void;
  reorderPhotos: (fromIndex: number, toIndex: number) => void;

  // Actions - Navigation
  setCurrentStep: (step: number) => void;
  nextStep: () => void;
  previousStep: () => void;

  // Actions - Validation
  validateStep: (step: number) => boolean;
  setStepValidation: (step: keyof VenueRegistrationStepValidation, isValid: boolean) => void;

  // Actions - Reset
  resetForm: () => void;
  resetStore: () => void;
}

// Initial form data
const initialFormData: VenueFormData = {
  name: '',
  description: '',
  musicGenres: [],
  dressCodes: [],
  location: null,
  photos: [],
  regularHours: [
    { day: 'monday' as DayOfWeek, open: '18:00', close: '02:00', isClosed: false },
    { day: 'tuesday' as DayOfWeek, open: '18:00', close: '02:00', isClosed: false },
    { day: 'wednesday' as DayOfWeek, open: '18:00', close: '02:00', isClosed: false },
    { day: 'thursday' as DayOfWeek, open: '18:00', close: '02:00', isClosed: false },
    { day: 'friday' as DayOfWeek, open: '18:00', close: '04:00', isClosed: false },
    { day: 'saturday' as DayOfWeek, open: '18:00', close: '04:00', isClosed: false },
    { day: 'sunday' as DayOfWeek, open: '18:00', close: '02:00', isClosed: false },
  ],
  specialHours: [],
  happyHours: {
    enabled: false,
    days: [],
    startTime: '17:00',
    endTime: '20:00',
  },
  features: [],
  capacity: undefined,
  minimumAge: 18,
  phoneNumber: '',
  email: '',
  website: '',
  instagram: '',
};

// Initial validation state
const initialStepValidation: VenueRegistrationStepValidation = {
  basicDetails: false,
  openingTimes: false,
  features: false,
};

export const useVenueRegistrationStore = create<VenueRegistrationStore>()(
  persist(
    (set, get) => ({
      // Initial state
      currentDraft: null,
      drafts: [],
      formData: initialFormData,
      stepValidation: initialStepValidation,
      currentStep: 0,

      // Draft management
      createDraft: () => {
        const draft: VenueDraft = {
          id: `draft_${Date.now()}`,
          ...get().formData,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          currentStep: get().currentStep,
          isCompleted: false,
        };

        set(state => ({
          currentDraft: draft,
          drafts: [...state.drafts, draft],
        }));
      },

      loadDraft: (draftId: string) => {
        const draft = get().drafts.find(d => d.id === draftId);
        if (draft) {
          const { id, createdAt, updatedAt, currentStep, isCompleted, ...formData } = draft;
          set({
            currentDraft: draft,
            formData: formData as VenueFormData,
            currentStep,
          });
        }
      },

      saveDraft: () => {
        const { currentDraft, formData, currentStep } = get();
        if (currentDraft) {
          const updatedDraft: VenueDraft = {
            ...currentDraft,
            ...formData,
            updatedAt: new Date().toISOString(),
            currentStep,
          };

          set(state => ({
            currentDraft: updatedDraft,
            drafts: state.drafts.map(d => (d.id === currentDraft.id ? updatedDraft : d)),
          }));
        }
      },

      deleteDraft: (draftId: string) => {
        set(state => ({
          drafts: state.drafts.filter(d => d.id !== draftId),
          currentDraft: state.currentDraft?.id === draftId ? null : state.currentDraft,
        }));
      },

      clearCurrentDraft: () => {
        set({ currentDraft: null });
      },

      // Form data updates
      updateBasicDetails: data => {
        set(state => ({
          formData: { ...state.formData, ...data },
        }));
      },

      updateOpeningTimes: data => {
        set(state => ({
          formData: { ...state.formData, ...data },
        }));
      },

      updateFeatures: data => {
        set(state => ({
          formData: { ...state.formData, ...data },
        }));
      },

      // Photo management
      addPhotos: photos => {
        set(state => ({
          formData: {
            ...state.formData,
            photos: [...state.formData.photos, ...photos].slice(0, 10), // Max 10 photos
          },
        }));
      },

      removePhoto: index => {
        set(state => ({
          formData: {
            ...state.formData,
            photos: state.formData.photos.filter((_, i) => i !== index),
          },
        }));
      },

      reorderPhotos: (fromIndex, toIndex) => {
        set(state => {
          const photos = [...state.formData.photos];
          const [removed] = photos.splice(fromIndex, 1);
          photos.splice(toIndex, 0, removed);

          return {
            formData: { ...state.formData, photos },
          };
        });
      },

      // Navigation
      setCurrentStep: step => {
        set({ currentStep: step });
      },

      nextStep: () => {
        set(state => ({ currentStep: Math.min(state.currentStep + 1, 3) }));
      },

      previousStep: () => {
        set(state => ({ currentStep: Math.max(state.currentStep - 1, 0) }));
      },

      // Validation
      validateStep: step => {
        const { formData } = get();

        switch (step) {
          case 0: // Basic Details
            return !!(
              formData.name &&
              formData.description &&
              formData.musicGenres.length > 0 &&
              formData.dressCodes.length > 0 &&
              formData.location &&
              formData.photos.length > 0
            );

          case 1: // Opening Times
            return formData.regularHours.length === 7;

          case 2: // Features
            return formData.features.length > 0;

          default:
            return false;
        }
      },

      setStepValidation: (step, isValid) => {
        set(state => ({
          stepValidation: { ...state.stepValidation, [step]: isValid },
        }));
      },

      // Reset actions
      resetForm: () => {
        set({
          formData: initialFormData,
          stepValidation: initialStepValidation,
          currentStep: 0,
        });
      },

      resetStore: () => {
        set({
          currentDraft: null,
          drafts: [],
          formData: initialFormData,
          stepValidation: initialStepValidation,
          currentStep: 0,
        });
      },
    }),
    {
      name: 'venue-registration-storage',
      storage: createJSONStorage(() => createZustandMMKVStorage(userStorage)),
      partialize: state => ({
        drafts: state.drafts,
        currentDraft: state.currentDraft,
      }),
    }
  )
);
