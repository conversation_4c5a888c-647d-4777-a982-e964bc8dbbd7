import React from 'react';

import { ImagePickerAsset } from 'expo-image-picker';

export type VenueStatus =
  | 'draft'
  | 'pending_approval'
  | 'approved'
  | 'rejected'
  | 'active'
  | 'suspended';

export type DayOfWeek =
  | 'monday'
  | 'tuesday'
  | 'wednesday'
  | 'thursday'
  | 'friday'
  | 'saturday'
  | 'sunday';

export interface OpeningHours {
  day: DayOfWeek;
  open: string; // HH:mm format
  close: string; // HH:mm format
  isClosed?: boolean;
}

export interface SpecialHours {
  id: string;
  name: string; // e.g., "New Year's Eve"
  startDate: string; // ISO date
  endDate: string; // ISO date
  open: string;
  close: string;
  isClosed?: boolean;
}

export interface VenueFeature {
  id: string;
  category: 'amenities' | 'entertainment' | 'areas' | 'payment';
  label: string;
  icon?: React.ReactNode | React.ReactElement;
}

export interface VenueLocation {
  address: string;
  city: string;
  state: string;
  country: string;
  postalCode: string;
  latitude: number;
  longitude: number;
  foursquareId?: string;
  formattedAddress?: string;
}

export interface VenueFormData {
  // Basic Details
  name: string;
  description: string;
  musicGenres: string[];
  dressCodes: string[];
  location: VenueLocation | null;
  photos: ImagePickerAsset[];

  // Opening Times
  regularHours: OpeningHours[];
  specialHours: SpecialHours[];
  happyHours?: {
    enabled: boolean;
    days: DayOfWeek[];
    startTime: string;
    endTime: string;
  };

  // Features
  features: string[]; // Feature IDs
  capacity?: number;
  minimumAge?: number;

  // Contact
  phoneNumber?: string;
  email?: string;
  website?: string;
  instagram?: string;
}

export interface VenueDraft extends VenueFormData {
  id: string;
  createdAt: string;
  updatedAt: string;
  currentStep: number;
  isCompleted: boolean;
}

export interface Venue extends VenueFormData {
  id: string;
  ownerId: string;
  status: VenueStatus;
  createdAt: string;
  updatedAt: string;
  approvedAt?: string;
  rejectionReason?: string;
  averageRating?: number;
  totalReviews?: number;
  isVerified?: boolean;
}

// Form step validation states
export interface VenueRegistrationStepValidation {
  basicDetails: boolean;
  openingTimes: boolean;
  features: boolean;
}
