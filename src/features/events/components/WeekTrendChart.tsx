import { Box, Text } from '@/src/core/theme';
import { Card } from '@/src/shared/components';

type WeekTrendChartProps = {
  data: number[];
  maxValue?: number;
  width?: number;
  height?: number;
};

const daysOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

const WeekTrendChart = ({ data, maxValue, width = 320, height = 150 }: WeekTrendChartProps) => {
  const max = maxValue ?? Math.max(...data, 1);

  const barWidth = width / data.length - 12;

  return (
    <Card
      padding="md_16"
      flexDirection="row"
      justifyContent="space-between"
      paddingHorizontal="sm_12">
      {data.map((value, index) => {
        const fillHeight = (value / max) * height;

        return (
          <Box key={index} alignItems="center" width={barWidth}>
            <Box
              backgroundColor="primaryExtraLight"
              width={barWidth}
              height={height}
              borderRadius="md_12"
              position="relative"
              overflow="hidden">
              <Box
                backgroundColor="primary"
                width={barWidth}
                height={fillHeight}
                borderRadius="md_12"
                position="absolute"
                bottom={0}
                left={0}
              />
            </Box>

            <Text variant="b_12Medium_CardSubtitle" marginTop="xs_8">
              {daysOfWeek[index].slice(0, 3)}
            </Text>
          </Box>
        );
      })}
    </Card>
  );
};

export default WeekTrendChart;
