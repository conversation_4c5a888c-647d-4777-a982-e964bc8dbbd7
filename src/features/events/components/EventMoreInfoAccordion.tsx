import { Box, Text } from '@/src/core/theme';
import { Divider } from '@/src/shared/components';

import Accordion from './Accordion';

type AccordionSection = {
  title: string;
  content: string[];
};

type EventMoreInfoAccordionProps = {
  sections: AccordionSection[];
};

const EventMoreInfoAccordion = ({ sections }: EventMoreInfoAccordionProps) => (
  <Box gap="xs_8" borderRadius="lg_16" borderWidth={1} borderColor="border">
    {sections.map((section, index) => (
      <Box key={section.title}>
        <Accordion title={section.title}>
          {section.content.map((paragraph, idx) => (
            <Text key={idx}>{paragraph}</Text>
          ))}
        </Accordion>
        {index < sections.length - 1 && <Divider />}
      </Box>
    ))}
  </Box>
);

export default EventMoreInfoAccordion;
