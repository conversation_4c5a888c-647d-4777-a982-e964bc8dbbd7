import { MapPin } from 'phosphor-react-native';

import { Box, Text, theme } from '@/src/core/theme';

type EventLocationComponentProps = {
  address: string;
};

const EventLocation = ({ address }: EventLocationComponentProps) => (
  <Box flexDirection="row" gap="xxs_4" alignItems="center">
    <MapPin size={24} color={theme.colors.iconPrimary} weight="fill" />
    <Text numberOfLines={2} variant="b_12Medium_CardSubtitle">
      {address}
    </Text>
  </Box>
);

export default EventLocation;
