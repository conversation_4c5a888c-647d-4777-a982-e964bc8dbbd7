import { Dimensions } from 'react-native';

import { Info, Star, StarHalf } from 'phosphor-react-native';

import { Box, Text, theme } from '@/src/core/theme';
import { Avatar, Card } from '@/src/shared/components';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

const CARD_WIDTH = SCREEN_WIDTH * (321 / 375);
const CARD_HEIGHT = SCREEN_WIDTH * (124 / 375);

type ReviewCardProps = {
  avatarUrl: string;
  name: string;
  rating: number;
  date: string;
  comment: string;
};

const getStarIcons = (rating: number) => {
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 >= 0.25 && rating % 1 < 0.75;
  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

  const stars = [];

  for (let i = 0; i < fullStars; i++) {
    stars.push(<Star key={`full-${i}`} size={18} color={theme.colors.warning} weight="fill" />);
  }

  if (hasHalfStar) {
    stars.push(<StarHalf key="half" size={18} color={theme.colors.warning} weight="fill" />);
  }

  for (let i = 0; i < emptyStars; i++) {
    stars.push(
      <Star key={`empty-${i}`} size={18} color={theme.colors.iconDisabled} weight="regular" />
    );
  }

  return stars;
};

const ReviewCard = ({ avatarUrl, name, rating, date, comment }: ReviewCardProps) => {
  return (
    <Card
      gap="xs_8"
      padding="md_16"
      flexDirection="row"
      width={CARD_WIDTH}
      height={CARD_HEIGHT}
      maxWidth={400}
      maxHeight={160}>
      <Box width={60} alignItems="center">
        <Avatar size="l" source={{ uri: avatarUrl }} />
      </Box>

      <Box flex={1}>
        <Text variant="b_16Large_Subtitle">{name}</Text>
        <Box flexDirection="row" gap="xxs_4">
          {getStarIcons(rating)}
        </Box>
        <Text variant="b_14Regular_content" numberOfLines={3}>
          {comment}
        </Text>
      </Box>

      <Box alignItems="flex-end" justifyContent="space-between">
        <Text variant="b_12Medium_CardSubtitle">{date}</Text>
        <Info size={24} />
      </Box>
    </Card>
  );
};

export default ReviewCard;
