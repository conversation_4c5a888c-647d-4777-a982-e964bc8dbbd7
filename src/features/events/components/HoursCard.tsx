import { Text } from '@/src/core/theme';
import { Card } from '@/src/shared/components';

type HourItem = {
  label: string;
  time: string;
};

type HoursCardProps = {
  hours: HourItem[];
};

const HoursCard = ({ hours }: HoursCardProps) => (
  <Card padding="md_16">
    {hours.map(({ label, time }, index) => (
      <Text key={index}>
        <Text variant="b_14SemiBold_listTitle">{label}:</Text> {time}
      </Text>
    ))}
  </Card>
);

export default HoursCard;
