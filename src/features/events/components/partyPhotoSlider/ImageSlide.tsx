// components/PartyPhotoSlider/ImageSlide.tsx
import { Dimensions, ImageBackground } from 'react-native';

import { Box, Text } from '@/src/core/theme';
import { Tag } from '@/src/shared/components/Tag';

const { width } = Dimensions.get('window');
const SLIDER_HEIGHT = width * 0.6;
const ITEM_WIDTH = width - 32;

type TagVariant = 'success' | 'error';

type ImageSlideProps = {
  item: {
    uri: string;
    title: string;
    date?: string;
    time?: string;
    tag?: {
      text: string;
      variant: TagVariant;
    };
  };
};

const ImageSlide = ({ item }: ImageSlideProps) => (
  <ImageBackground
    source={{ uri: item.uri }}
    style={{
      width: ITEM_WIDTH,
      height: SLIDER_HEIGHT,
      borderRadius: 32,
      overflow: 'hidden',
      justifyContent: 'flex-end',
      padding: 12,
    }}
    resizeMode="cover">
    <Box gap="xxs_4" marginBottom="sm_12">
      <Text variant="b_24Bold_CTA" color="white" numberOfLines={1}>
        {item.title}
      </Text>

      {item.tag ? (
        <Tag text={item.tag.text} variant={item.tag.variant} />
      ) : (
        <>
          {item.date && (
            <Text variant="b_16Large_Subtitle" color="white" numberOfLines={1}>
              {item.date}
            </Text>
          )}
          {item.time && (
            <Text variant="b_12Medium_CardSubtitle" color="disabled" numberOfLines={1}>
              {item.time}
            </Text>
          )}
        </>
      )}
    </Box>
  </ImageBackground>
);

export default ImageSlide;
