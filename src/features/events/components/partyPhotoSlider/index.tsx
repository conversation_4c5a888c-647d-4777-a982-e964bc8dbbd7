// components/PartyPhotoSlider/index.tsx
import { useRef } from 'react';

import { Animated, Dimensions } from 'react-native';

import { Box } from '@/src/core/theme';

import ImageSlide from './ImageSlide';
import PaginationDots from './PaginationDots';

const { width } = Dimensions.get('window');
const ITEM_WIDTH = width - 32;
const ITEM_SPACING = 12;

const images = [
  {
    uri: 'https://t3.ftcdn.net/jpg/02/87/35/70/360_F_287357045_Ib0oYOxhotdjOEHi0vkggpZTQCsz0r19.jpg',
    title: 'Barbecue party with friends',
    date: 'Monday, December 24, 2022',
    time: '18.00 - 23.00 PM (GMT +07:00)',
  },
  {
    uri: 'https://t3.ftcdn.net/jpg/02/87/35/70/360_F_287357045_Ib0oYOxhotdjOEHi0vkggpZTQCsz0r19.jpg',
    title: 'Neon Night Vibes',
    date: 'Friday, May 5, 2023',
    time: '20.00 - 03.00 AM',
  },
  {
    uri: 'https://t3.ftcdn.net/jpg/02/87/35/70/360_F_287357045_Ib0oYOxhotdjOEHi0vkggpZTQCsz0r19.jpg',
    title: 'Festival ao ar livre',
    date: 'Saturday, August 19, 2023',
    time: '12.00 - 23.00 PM',
  },
  {
    uri: 'https://t3.ftcdn.net/jpg/02/87/35/70/360_F_287357045_Ib0oYOxhotdjOEHi0vkggpZTQCsz0r19.jpg',
    title: 'Barbecue party with friends',
    date: 'Monday, December 24, 2022',
    time: '18.00 - 23.00 PM (GMT +07:00)',
  },
];

const PartyPhotoSlider = () => {
  const scrollX = useRef(new Animated.Value(0)).current;

  return (
    <Box>
      <Animated.FlatList
        data={images}
        keyExtractor={(_, index) => String(index)}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={Animated.event([{ nativeEvent: { contentOffset: { x: scrollX } } }], {
          useNativeDriver: false,
        })}
        renderItem={({ item }) => <ImageSlide item={item} />}
        snapToInterval={ITEM_WIDTH + ITEM_SPACING}
        contentContainerStyle={{
          gap: ITEM_SPACING,
        }}
      />

      <PaginationDots scrollX={scrollX} itemCount={images.length} />
    </Box>
  );
};

export default PartyPhotoSlider;
