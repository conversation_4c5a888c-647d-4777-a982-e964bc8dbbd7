// components/PartyPhotoSlider/PaginationDots.tsx
import { Animated, Dimensions } from 'react-native';

import { Box } from '@/src/core/theme';

const { width } = Dimensions.get('window');

const DOT_SIZE = 8;
const ACTIVE_DOT_WIDTH = 32;

type PaginationDotsProps = {
  scrollX: Animated.Value;
  itemCount: number;
};

const PaginationDots = ({ scrollX, itemCount }: PaginationDotsProps) => (
  <Box bottom={16} flexDirection="row" justifyContent="center" alignItems="center" gap="xs_8">
    {Array.from({ length: itemCount }).map((_, index) => {
      const inputRange = [(index - 1) * width, index * width, (index + 1) * width];

      const dotWidth = scrollX.interpolate({
        inputRange,
        outputRange: [DOT_SIZE, ACTIVE_DOT_WIDTH, DOT_SIZE],
        extrapolate: 'clamp',
      });

      const dotOpacity = scrollX.interpolate({
        inputRange,
        outputRange: [0.4, 1, 0.4],
        extrapolate: 'clamp',
      });

      return (
        <Animated.View
          key={index}
          style={{
            width: dotWidth,
            height: DOT_SIZE,
            borderRadius: 9999,
            backgroundColor: '#FFF',
            opacity: dotOpacity,
          }}
        />
      );
    })}
  </Box>
);

export default PaginationDots;
