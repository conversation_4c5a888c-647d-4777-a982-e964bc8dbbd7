import { CaretRight } from 'phosphor-react-native';

import { Box, Text } from '@/src/core/theme';
import { Avatar, Card } from '@/src/shared/components';
import { Divider } from '@/src/shared/components/Divider';

type EventPerson = {
  role: string;
  name: string;
  description: string;
  imageUrl: string;
};

type EventPersonCardProps = {
  person: EventPerson;
};

const EventPersonCard = ({ person }: EventPersonCardProps) => {
  return (
    <Card width={200} padding="md_16" alignItems="center" gap="sm_12">
      <Box alignItems="center" flexDirection="row" width="100%">
        <Text variant="b_10Bold_CardTitle">{person.role}</Text>
        <CaretRight size={12} weight="bold" />
      </Box>

      <Box alignItems="center" width="100%" padding="xs_8" gap="xs_8">
        <Box alignItems="center">
          <Avatar
            borderColor="alertInfoBackground"
            borderWidth={2}
            size="3xl"
            source={{ uri: person.imageUrl }} // Replace with actual image URL
          />

          <Text marginTop="xxs_4" variant="b_14Bold_CardTitle" numberOfLines={2}>
            {person.name}
          </Text>
        </Box>

        <Divider />

        <Box gap="xxs_4">
          <Text variant="l_12Regular_helperText">{person.description}</Text>
        </Box>
      </Box>
    </Card>
  );
};

export default EventPersonCard;
