import React from 'react';

import { Image, ImageBackground, Pressable } from 'react-native';

import {
  BlurMask,
  Canvas,
  Group,
  Rect,
  RoundedRect,
  LinearGradient as SkiaGradient,
  vec,
} from '@shopify/react-native-skia';
import { CaretRight, CurrencyDollar, TrendUp } from 'phosphor-react-native';
import Animated, {
  FadeInUp,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from 'react-native-reanimated';

import { Box, Text, useTheme } from '@/src/core/theme';

const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

interface EventTemplateCardProps {
  title: string;
  subtitle: string;
  icon?: React.ReactNode;
  image?: string; // New prop for image display
  backgroundImage?: string;
  gradientColors?: [string, string];
  onPress: () => void;
  revenueCapable?: boolean;
  isPopular?: boolean;
  isNew?: boolean;
  delay?: number;
  aspectRatio?: number;
  compact?: boolean;
  tags?: string[]; // New prop for tags
}

export const EventTemplateCard: React.FC<EventTemplateCardProps> = ({
  title,
  subtitle,
  icon,
  image,
  backgroundImage,
  gradientColors = ['#6366f1', '#8b5cf6'],
  onPress,
  revenueCapable = false,
  isPopular = false,
  isNew = false,
  delay = 0,
  aspectRatio = 1.4,
  compact = false,
  tags = [],
}) => {
  const theme = useTheme();
  const scale = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const handlePressIn = React.useCallback(() => {
    'worklet';
    scale.value = withSpring(0.95);
  }, [scale]);

  const handlePressOut = React.useCallback(() => {
    'worklet';
    scale.value = withSpring(1);
  }, [scale]);

  const renderContent = () => (
    <Box flex={1} justifyContent="space-between">
      {/* Dark overlay for better text contrast */}
      <Box position="absolute" left={0} right={0} bottom={0} height="100%">
        <Canvas style={{ flex: 1 }}>
          <Rect x={0} y={0} width={1000} height={1000}>
            <SkiaGradient
              start={vec(0, 0)}
              end={vec(0, 1000)}
              colors={['rgba(0,0,0,0.1)', 'rgba(0,0,0,0.6)']}
            />
          </Rect>
        </Canvas>
      </Box>

      {/* Outlined Tags/Chips */}
      {(isPopular || isNew || tags.length > 0) && (
        <Box
          position="absolute"
          top={compact ? 8 : 12}
          right={compact ? 8 : 12}
          flexDirection="row"
          gap="xs_8"
          zIndex="overlay"
          flexWrap="wrap">
          {isNew && (
            <Box
              backgroundColor="transparent"
              borderWidth={1}
              borderColor="white"
              paddingHorizontal="sm_12"
              paddingVertical="xxs_4"
              borderRadius="circle_9999">
              <Text variant="l_12Medium_message" color="white" style={{ fontWeight: '600' }}>
                Novo
              </Text>
            </Box>
          )}
          {isPopular && (
            <Box
              backgroundColor="transparent"
              borderWidth={1}
              borderColor="white"
              paddingHorizontal="sm_12"
              paddingVertical="xxs_4"
              borderRadius="circle_9999"
              flexDirection="row"
              alignItems="center"
              gap="xxs_4">
              <TrendUp size={12} color="white" weight="bold" />
              <Text variant="l_12Medium_message" color="white" style={{ fontWeight: '600' }}>
                Popular
              </Text>
            </Box>
          )}
          {tags.slice(0, 2).map((tag, index) => (
            <Box
              key={`tag-${index}`}
              backgroundColor="transparent"
              borderWidth={1}
              borderColor="white"
              paddingHorizontal="sm_12"
              paddingVertical="xxs_4"
              borderRadius="circle_9999">
              <Text variant="l_12Medium_message" color="white" style={{ fontWeight: '600' }}>
                {tag}
              </Text>
            </Box>
          ))}
        </Box>
      )}

      {/* Content */}
      <Box flex={1} padding={compact ? 'sm_12' : 'md_16'} justifyContent="flex-end">
        {/* Icon or Image with Shadow/Blur */}
        {(icon || image) && (
          <Box marginBottom={compact ? 'xs_8' : 'sm_12'}>
            {image ? (
              <Box position="relative">
                {/* Shadow/Blur effect behind image */}
                <Box
                  position="absolute"
                  top={4}
                  left={4}
                  width={compact ? 48 : 64}
                  height={compact ? 48 : 64}
                  borderRadius="md_12"
                  overflow="hidden">
                  <Canvas style={{ flex: 1 }}>
                    <Group>
                      <RoundedRect
                        x={0}
                        y={0}
                        width={compact ? 48 : 64}
                        height={compact ? 48 : 64}
                        r={12}
                        color={gradientColors[0]}
                        opacity={0.3}>
                        <BlurMask blur={8} style="normal" />
                      </RoundedRect>
                    </Group>
                  </Canvas>
                </Box>
                {/* Main image */}
                <Image
                  source={{ uri: image }}
                  style={{
                    width: compact ? 48 : 64,
                    height: compact ? 48 : 64,
                    borderRadius: 12,
                  }}
                  resizeMode="cover"
                />
              </Box>
            ) : (
              <Box
                backgroundColor="white"
                width={compact ? 48 : 56}
                height={compact ? 48 : 56}
                borderRadius="md_12"
                justifyContent="center"
                alignItems="center"
                shadowOffset={{ width: 0, height: 4 }}
                shadowOpacity={0.15}
                shadowRadius={8}
                elevation={5}>
                {icon}
              </Box>
            )}
          </Box>
        )}

        {/* Text with Improved Typography Hierarchy */}
        <Box marginBottom={compact ? 'xs_8' : 'sm_12'}>
          <Text
            variant={compact ? 'h_20Medium_subsection' : 'h_24SemiBold_section'}
            color="white"
            numberOfLines={1}
            marginBottom="xxs_4"
            style={{ letterSpacing: -0.5 }}>
            {title}
          </Text>
          <Text
            variant={compact ? 'b_14Regular_content' : 'b_16Regular_input'}
            color="white"
            opacity={0.85}
            numberOfLines={compact ? 1 : 2}
            style={{ lineHeight: compact ? 18 : 22 }}>
            {subtitle}
          </Text>
        </Box>

        {/* Footer */}
        <Box flexDirection="row" justifyContent="space-between" alignItems="center">
          <Box flexDirection="row" alignItems="center" gap="xs_8">
            {revenueCapable && (
              <Box
                backgroundColor="transparent"
                borderWidth={1}
                borderColor="white"
                paddingHorizontal="sm_12"
                paddingVertical="xxs_4"
                borderRadius="circle_9999"
                flexDirection="row"
                alignItems="center"
                gap="xxs_4">
                <CurrencyDollar size={14} color="white" weight="bold" />
                <Text variant="l_12Medium_message" color="white" style={{ fontWeight: '600' }}>
                  Monetize
                </Text>
              </Box>
            )}
          </Box>

          <Box
            backgroundColor="white"
            borderRadius="circle_9999"
            width={28}
            height={28}
            justifyContent="center"
            alignItems="center"
            opacity={0.9}>
            <CaretRight size={16} color={theme.colors.text} weight="bold" />
          </Box>
        </Box>
      </Box>
    </Box>
  );

  return (
    <AnimatedPressable
      onPress={onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      style={animatedStyle}>
      <Animated.View entering={FadeInUp.delay(delay).springify()}>
        <Box
          borderRadius="lg_16"
          overflow="hidden"
          borderWidth={1}
          borderColor="border"
          shadowOffset={{ width: 0, height: 4 }}
          shadowOpacity={0.08}
          shadowRadius={12}
          elevation={8}
          aspectRatio={aspectRatio}>
          {backgroundImage ? (
            <ImageBackground
              source={{ uri: backgroundImage }}
              style={{ flex: 1 }}
              resizeMode="cover">
              {renderContent()}
            </ImageBackground>
          ) : (
            <Box flex={1}>
              {/* Background Gradient */}
              <Box position="absolute" top={0} left={0} right={0} bottom={0}>
                <Canvas style={{ flex: 1 }}>
                  <Rect x={0} y={0} width={1000} height={1000}>
                    <SkiaGradient start={vec(0, 0)} end={vec(300, 300)} colors={gradientColors} />
                  </Rect>
                </Canvas>
              </Box>
              {/* Content on top */}
              {renderContent()}
            </Box>
          )}
        </Box>
      </Animated.View>
    </AnimatedPressable>
  );
};
