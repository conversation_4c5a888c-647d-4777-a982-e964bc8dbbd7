import React, { useCallback, useEffect, useRef, useState } from 'react';

import { LayoutChangeEvent } from 'react-native';

import { CaretDown, CaretUp } from 'phosphor-react-native';
import Animated, {
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';

import { Box, Pressable, Text } from '@/src/core/theme';

type AccordionProps = {
  title: string;
  children: React.ReactNode;
};

const Accordion = ({ title, children }: AccordionProps) => {
  const [expanded, setExpanded] = useState(false);
  const [contentHeight, setContentHeight] = useState(0);
  const maxHeight = useSharedValue(0);

  const hasMeasured = useRef(false);

  useEffect(() => {
    maxHeight.value = withTiming(expanded ? contentHeight : 0, {
      duration: 300,
      easing: Easing.out(Easing.cubic),
    });
  }, [expanded, contentHeight]);

  const onMeasure = useCallback((event: LayoutChangeEvent) => {
    if (!hasMeasured.current) {
      const height = event.nativeEvent.layout.height;
      if (height > 0) {
        setContentHeight(height);
        hasMeasured.current = true;
      }
    }
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    maxHeight: maxHeight.value,
    opacity: maxHeight.value > 0 ? 1 : 0,
    overflow: 'hidden',
  }));

  return (
    <Box borderRadius="md_12">
      <Pressable onPress={() => setExpanded(prev => !prev)}>
        <Box flexDirection="row" justifyContent="space-between" alignItems="center" padding="md_16">
          <Text variant="b_16Large_Subtitle">{title}</Text>
          {expanded ? <CaretUp size={20} /> : <CaretDown size={20} />}
        </Box>
      </Pressable>

      <Animated.View style={animatedStyle}>
        <Box paddingHorizontal="md_16">{children}</Box>
      </Animated.View>

      {!hasMeasured.current && (
        <Box
          position="absolute"
          top={-9999}
          left={0}
          right={0}
          opacity={0}
          zIndex="base"
          width="100%"
          onLayout={onMeasure}>
          <Box paddingHorizontal="md_16">{children}</Box>
        </Box>
      )}
    </Box>
  );
};

export default Accordion;
