import { Pressable } from 'react-native';

import { Box, Text } from '@/src/core/theme';

type SectionHeaderComponentProps = {
  title: string;
  actionLabel?: string;
  onPress?: () => void;
};

const SectionHeader = ({ title, actionLabel, onPress }: SectionHeaderComponentProps) => {
  return (
    <Box flexDirection="row" alignItems="center" justifyContent="space-between">
      <Text variant="h_18Bold_formTitle">{title}</Text>

      {actionLabel && onPress && (
        <Pressable onPress={onPress}>
          <Text color="primary" variant="b_14SemiBold_listTitle" textDecorationLine="underline">
            {actionLabel}
          </Text>
        </Pressable>
      )}
    </Box>
  );
};

export default SectionHeader;
