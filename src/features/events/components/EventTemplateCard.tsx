import React from 'react';

import { Pressable } from 'react-native';

import { Box, Text } from '@/src/core/theme';
import { Card } from '@/src/shared/components';

interface EventTemplateCardProps {
  icon: React.ReactNode;
  title: string;
  subtitle?: string;
  iconBgColor?: string;
  onPress: () => void;
  expiresIn?: string;
  isHot?: boolean;
  revenueCapable?: boolean;
}

export const EventTemplateCard = ({
  icon,
  title,
  subtitle,
  iconBgColor = 'primaryLight',
  onPress,
  expiresIn,
  isHot,
  revenueCapable,
}: EventTemplateCardProps) => {
  return (
    <Pressable onPress={onPress}>
      <Card
        variant="elevated"
        padding="md_16"
        style={{
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 0.05,
          shadowRadius: 2,
          elevation: 1,
        }}>
        {/* Hot Badge */}
        {isHot && (
          <Box
            position="absolute"
            top={-8}
            right={8}
            backgroundColor="error"
            paddingHorizontal="xs_8"
            paddingVertical="xxs_4"
            borderRadius="sm_8"
            zIndex={1}>
            <Text variant="l_10SemiBold_tag" color="white">
              HOT
            </Text>
          </Box>
        )}

        {/* Revenue Indicator */}
        {revenueCapable && (
          <Box
            position="absolute"
            top={8}
            right={8}
            backgroundColor="successLight"
            borderRadius="circle_9999"
            paddingHorizontal="xs_8"
            paddingVertical="xxs_4">
            <Text variant="l_10SemiBold_tag" color="success">
              💰
            </Text>
          </Box>
        )}

        <Box gap="sm_12" alignItems="center">
          {/* Icon Container */}
          <Box
            width={56}
            height={56}
            borderRadius="circle_9999"
            backgroundColor={iconBgColor as any}
            alignItems="center"
            justifyContent="center">
            {icon}
          </Box>

          {/* Content */}
          <Box alignItems="center" gap="xxs_4">
            <Text variant="b_16SemiBold_button" color="text" textAlign="center" numberOfLines={2}>
              {title}
            </Text>

            {subtitle && (
              <Text
                variant="l_12Regular_helperText"
                color="textSecondary"
                textAlign="center"
                numberOfLines={1}>
                {subtitle}
              </Text>
            )}

            {expiresIn && (
              <Box
                backgroundColor="warningLight"
                paddingHorizontal="xs_8"
                paddingVertical="xxs_4"
                borderRadius="sm_8"
                marginTop="xxs_4">
                <Text variant="l_10SemiBold_tag" color="warningDark">
                  Expires in {expiresIn}
                </Text>
              </Box>
            )}
          </Box>
        </Box>
      </Card>
    </Pressable>
  );
};
