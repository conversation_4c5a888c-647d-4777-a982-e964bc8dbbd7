import React from 'react';

import { Pressable, ScrollView } from 'react-native';

import { Clock, Crown, Fire, Gift, MusicNote } from 'phosphor-react-native';

import { Box, Text, useTheme } from '@/src/core/theme';

export type FilterType = 'all' | 'specials' | 'music' | 'vip' | 'urgent' | 'trending';

interface Filter {
  id: FilterType;
  label: string;
  icon?: React.ReactNode;
}

interface EventFilterPillsProps {
  selectedFilter: FilterType;
  onFilterChange: (filter: FilterType) => void;
}

export const EventFilterPills = ({ selectedFilter, onFilterChange }: EventFilterPillsProps) => {
  const theme = useTheme();

  const filters: Filter[] = [
    { id: 'all', label: 'All' },
    { id: 'specials', label: 'Specials', icon: <Gift size={16} color={theme.colors.text} /> },
    { id: 'music', label: 'Music', icon: <MusicNote size={16} color={theme.colors.text} /> },
    { id: 'vip', label: 'VIP', icon: <Crown size={16} color={theme.colors.text} /> },
    { id: 'urgent', label: 'Urgent', icon: <Clock size={16} color={theme.colors.text} /> },
    { id: 'trending', label: 'Trending', icon: <Fire size={16} color={theme.colors.text} /> },
  ];

  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={{ paddingHorizontal: theme.spacing.md_16 }}>
      <Box flexDirection="row" gap="xs_8">
        {filters.map(filter => {
          const isSelected = selectedFilter === filter.id;

          return (
            <Pressable key={filter.id} onPress={() => onFilterChange(filter.id)}>
              <Box
                flexDirection="row"
                alignItems="center"
                gap="xxs_4"
                backgroundColor={isSelected ? 'primary' : 'surfaceBackground'}
                borderWidth={isSelected ? 0 : 1}
                borderColor="border"
                paddingVertical="xs_8"
                paddingHorizontal="sm_12"
                borderRadius="circle_9999">
                {filter.icon && (
                  <Box opacity={isSelected ? 1 : 0.7}>
                    {React.cloneElement(filter.icon as React.ReactElement, {
                      color: isSelected ? theme.colors.white : theme.colors.textSecondary,
                    })}
                  </Box>
                )}
                <Text variant="b_14Medium_button" color={isSelected ? 'white' : 'textSecondary'}>
                  {filter.label}
                </Text>
              </Box>
            </Pressable>
          );
        })}
      </Box>
    </ScrollView>
  );
};
