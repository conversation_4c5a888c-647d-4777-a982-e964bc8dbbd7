import React from 'react';

import SegmentedControl from '@/src/shared/components/SegmentedControl/SegmentedControl';

export type EventType = 'personal' | 'venue' | 'flash';

interface EventTypeSelectorProps {
  selectedType: EventType;
  onTypeChange: (type: EventType) => void;
}

const eventTypeOptions: EventType[] = ['personal', 'venue', 'flash'];

const eventTypeLabels: Record<EventType, string> = {
  personal: 'Personal Party',
  venue: 'Venue Event',
  flash: 'Flash Invite',
};

export const EventTypeSelector = ({ selectedType, onTypeChange }: EventTypeSelectorProps) => {
  const segmentItems = eventTypeOptions.map(type => ({
    title: eventTypeLabels[type],
  }));

  const selectedIndex = eventTypeOptions.indexOf(selectedType);

  const handleValueChange = (index: number) => {
    onTypeChange(eventTypeOptions[index]);
  };

  return (
    <SegmentedControl
      items={segmentItems}
      selected={selectedIndex}
      onValueChange={handleValueChange}
    />
  );
};
