import { Box, Text } from '@/src/core/theme';
import { Avatar } from '@/src/shared/components';

type AvatarGroupProps = {
  avatars: string[];
  size?: 'xs' | 's' | 'm'; // optional size
  overlap?: number; // overlap distance
};

const AvatarGroup = ({ avatars, size = 's', overlap = 12 }: AvatarGroupProps) => {
  return (
    <Box flexDirection="row" alignItems="center">
      {avatars.map((uri, index) => (
        <Avatar
          size={size}
          key={index}
          borderWidth={2}
          borderColor="white"
          style={[
            {
              marginLeft: index === 0 ? 0 : -overlap,
            },
          ]}
          source={{ uri }}
        />
      ))}
      <Text marginLeft="xs_8">+25 going</Text>
    </Box>
  );
};

export default AvatarGroup;
