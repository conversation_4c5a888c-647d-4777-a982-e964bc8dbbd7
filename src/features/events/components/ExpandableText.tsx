import { useState } from 'react';

import { LayoutAnimation, Platform, UIManager } from 'react-native';

import { Pressable, Text } from '@/src/core/theme';

type ExpandableTextProps = {
  text: string;
  maxLength?: number;
};

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

const ExpandableText = ({ text, maxLength = 120 }: ExpandableTextProps) => {
  const [expanded, setExpanded] = useState(false);

  const shouldTruncate = text.length > maxLength;
  const truncatedText = text.slice(0, maxLength);

  const handleToggle = () => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setExpanded(prev => !prev);
  };

  return (
    <Pressable onPress={handleToggle}>
      <Text variant="b_16Medium_button">
        {shouldTruncate && !expanded ? truncatedText : text}
        {shouldTruncate && (
          <Text color="primary" variant="b_16Medium_button" textDecorationLine="underline">
            {expanded ? ' Read less' : ' Read more'}
          </Text>
        )}
      </Text>
    </Pressable>
  );
};

export default ExpandableText;
