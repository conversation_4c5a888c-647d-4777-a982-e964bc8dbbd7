import React, { memo } from 'react';

import { Pressable } from 'react-native';

import Image from '@d11/react-native-fast-image';
import { Feather, MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@shopify/restyle';
import { format } from 'date-fns';
import Animated, { FadeIn } from 'react-native-reanimated';

import { Box, Text, Theme } from '@/src/core/theme';
import { useResponsive } from '@/src/core/theme/useResponsive';
import { Avatar, Card } from '@/src/shared/components';

import type { Event } from '../types/event.types';

interface EventCardProps {
  event: Event;
  variant?: 'default' | 'compact' | 'featured';
  onPress: () => void;
  onRSVP?: () => void;
  onShare?: () => void;
}

// Create animated Card component - now properly supported with forwardRef
const AnimatedCard = Animated.createAnimatedComponent(Card);

const EventCard: React.FC<EventCardProps> = ({
  event,
  variant = 'default',
  onPress,
  onRSVP,
  onShare,
}) => {
  const theme = useTheme<Theme>();
  const { select } = useResponsive();

  // Format date and time
  const eventDate = format(event.startDate, 'MMM d');
  const eventTime = format(event.startDate, 'h:mm a');
  const isToday = format(event.startDate, 'yyyy-MM-dd') === format(new Date(), 'yyyy-MM-dd');

  // RSVP button color
  const rsvpColors = {
    going: theme.colors.success,
    interested: theme.colors.brandMain,
    not_going: theme.colors.textSecondary,
  };

  // Compact variant
  if (variant === 'compact') {
    return (
      <Pressable onPress={onPress}>
        <AnimatedCard entering={FadeIn} variant="elevated" padding="sm_12" marginBottom="xs_8">
          <Box flexDirection="row" alignItems="center">
            {event.coverImage && (
              <Image
                source={{ uri: event.coverImage }}
                style={{
                  width: select({ phone: 60, tablet: 80 }),
                  height: select({ phone: 60, tablet: 80 }),
                  borderRadius: theme.borderRadii.md_12,
                  marginRight: theme.spacing.sm_12,
                }}
              />
            )}
            <Box flex={1}>
              <Text variant="b_14SemiBold_listTitle" color="mainText" numberOfLines={1}>
                {event.title}
              </Text>
              <Box flexDirection="row" alignItems="center" marginTop="xxs_4">
                <MaterialIcons name="location-on" size={14} color={theme.colors.textSecondary} />
                <Text
                  variant="b_14Regular_content"
                  color="textSecondary"
                  marginLeft="xxs_4"
                  numberOfLines={1}
                  flex={1}>
                  {event.location.name}
                </Text>
              </Box>
              <Box flexDirection="row" alignItems="center" marginTop="xxs_4">
                <Feather name="calendar" size={14} color={theme.colors.textSecondary} />
                <Text variant="l_12Regular_helperText" color="textSecondary" marginLeft="xxs_4">
                  {isToday ? `Today, ${eventTime}` : `${eventDate}, ${eventTime}`}
                </Text>
              </Box>
            </Box>
            <Box alignItems="flex-end">
              <Text variant="l_12Medium_message" color="brandMain">
                {event.attendeeCount} going
              </Text>
              {event.entryFee && event.entryFee > 0 ? (
                <Text variant="l_14Medium_info" color="mainText" marginTop="xxs_4">
                  {event.currency} {event.entryFee}
                </Text>
              ) : (
                <Text variant="l_12Medium_message" color="success" marginTop="xxs_4">
                  FREE
                </Text>
              )}
            </Box>
          </Box>
        </AnimatedCard>
      </Pressable>
    );
  }

  // Default and featured variants
  return (
    <Pressable onPress={onPress}>
      <AnimatedCard
        paddingLeft="none_0"
        paddingRight="none_0"
        entering={FadeIn}
        variant={variant === 'featured' ? 'outlined' : 'elevated'}
        overflow="hidden"
        marginBottom="md_16">
        {/* Cover Image */}
        {event.coverImage && (
          <Box position="relative">
            <Image
              source={{ uri: event.coverImage }}
              style={{
                width: '100%',
                height: select({ phone: 180, tablet: 220 }),
              }}
            />
            {/* Date badge */}
            <Box
              position="absolute"
              top={theme.spacing.sm_12}
              left={theme.spacing.sm_12}
              backgroundColor="background"
              paddingHorizontal="xs_8"
              paddingVertical="xxs_4"
              borderRadius="md_12"
              style={{
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.1,
                shadowRadius: 4,
                elevation: 3,
              }}>
              <Text variant="l_12Medium_message" color="mainText" textAlign="center">
                {format(event.startDate, 'MMM')}
              </Text>
              <Text variant="l_12Medium_message" color="mainText" textAlign="center">
                {format(event.startDate, 'd')}
              </Text>
            </Box>
            {/* Share button */}
            {onShare && (
              <Pressable
                onPress={onShare}
                style={{
                  position: 'absolute',
                  top: theme.spacing.sm_12,
                  right: theme.spacing.sm_12,
                }}>
                <Box
                  backgroundColor="background"
                  padding="xs_8"
                  borderRadius="circle_9999"
                  style={{
                    shadowColor: '#000',
                    shadowOffset: { width: 0, height: 2 },
                    shadowOpacity: 0.1,
                    shadowRadius: 4,
                    elevation: 3,
                  }}>
                  <Feather
                    name="share"
                    size={select({ phone: 20, tablet: 24 })}
                    color={theme.colors.mainText}
                  />
                </Box>
              </Pressable>
            )}
          </Box>
        )}

        {/* Content */}
        <Box padding="md_16">
          {/* Title and location */}
          <Text
            variant={variant === 'featured' ? 'h_24SemiBold_section' : 'b_16Medium_button'}
            color="mainText">
            {event.title}
          </Text>
          <Box flexDirection="row" alignItems="center" marginTop="xs_8">
            <MaterialIcons
              name="location-on"
              size={select({ phone: 16, tablet: 20 })}
              color={theme.colors.textSecondary}
            />
            <Text
              variant={variant === 'featured' ? 'b_16Medium_button' : 'b_14Regular_content'}
              color="textSecondary"
              marginLeft="xxs_4"
              numberOfLines={1}
              flex={1}>
              {event.location.name}
            </Text>
          </Box>

          {/* Time */}
          <Box flexDirection="row" alignItems="center" marginTop="xs_8">
            <Feather
              name="clock"
              size={select({ phone: 16, tablet: 20 })}
              color={theme.colors.textSecondary}
            />
            <Text
              variant={variant === 'featured' ? 'b_16Medium_button' : 'b_14Regular_content'}
              color="textSecondary"
              marginLeft="xxs_4">
              {isToday ? `Today at ${eventTime}` : `${eventDate} at ${eventTime}`}
            </Text>
          </Box>

          {/* Host and attendees */}
          <Box
            flexDirection="row"
            alignItems="center"
            justifyContent="space-between"
            marginTop="sm_12">
            <Box flexDirection="row" alignItems="center" flex={1}>
              <Avatar source={event.hostAvatar} name={event.hostName} size="s" />
              <Box marginLeft="xs_8" flex={1}>
                <Text variant="l_12Regular_helperText" color="textSecondary">
                  Hosted by
                </Text>
                <Text variant="b_14Medium_button" color="mainText" numberOfLines={1}>
                  {event.hostName}
                </Text>
              </Box>
            </Box>
            <Box alignItems="flex-end">
              <Text variant="b_14Medium_button" color="brandMain">
                {event.attendeeCount} going
              </Text>
              <Text variant="l_12Regular_helperText" color="textSecondary">
                {event.interestedCount} interested
              </Text>
            </Box>
          </Box>

          {/* Action buttons */}
          <Box flexDirection="row" alignItems="center" marginTop="md_16" gap="xs_8">
            {/* RSVP button */}
            {onRSVP && (
              <Pressable onPress={onRSVP} style={{ flex: 1 }}>
                <Box
                  backgroundColor={event.userRsvp === 'going' ? 'brandMain' : 'elevatedBackground'}
                  paddingVertical="sm_12"
                  paddingHorizontal="md_16"
                  borderRadius="md_12"
                  alignItems="center">
                  <Text
                    variant="b_14Medium_button"
                    color={event.userRsvp === 'going' ? 'white' : 'mainText'}>
                    {event.userRsvp === 'going'
                      ? 'Going ✓'
                      : event.userRsvp === 'interested'
                        ? 'Interested'
                        : 'RSVP'}
                  </Text>
                </Box>
              </Pressable>
            )}

            {/* Price/Free badge */}
            <Box
              backgroundColor={
                event.entryFee && event.entryFee > 0 ? 'elevatedBackground' : 'success'
              }
              paddingVertical="sm_12"
              paddingHorizontal="md_16"
              borderRadius="md_12"
              opacity={0.9}>
              <Text
                variant="b_14Medium_button"
                color={event.entryFee && event.entryFee > 0 ? 'mainText' : 'white'}>
                {event.entryFee && event.entryFee > 0
                  ? `${event.currency} ${event.entryFee}`
                  : 'FREE'}
              </Text>
            </Box>
          </Box>

          {/* Tags */}
          {event.tags.length > 0 && (
            <Box flexDirection="row" flexWrap="wrap" marginTop="sm_12" gap="xs_8">
              {event.tags.slice(0, 4).map(tag => (
                <Box
                  key={tag}
                  backgroundColor="elevatedBackground"
                  paddingHorizontal="xs_8"
                  paddingVertical="xxs_4"
                  borderRadius="md_12">
                  <Text variant="b_14Medium_button" color="textSecondary">
                    #{tag}
                  </Text>
                </Box>
              ))}
            </Box>
          )}
        </Box>
      </AnimatedCard>
    </Pressable>
  );
};

export default memo(EventCard);
