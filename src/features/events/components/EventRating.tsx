import { Star, StarHalf } from 'phosphor-react-native';

import { Box, Pressable, Text, theme } from '@/src/core/theme';

type EventRatingProps = {
  rating: number;
  totalReviews: number;
  onPressReview?: () => void;
};

const getStarIcons = (rating: number) => {
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 >= 0.25 && rating % 1 < 0.75;
  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

  const stars = [];

  for (let i = 0; i < fullStars; i++) {
    stars.push(<Star key={`full-${i}`} size={18} color={theme.colors.warning} weight="fill" />);
  }

  if (hasHalfStar) {
    stars.push(<StarHalf key="half" size={18} color={theme.colors.warning} weight="fill" />);
  }

  for (let i = 0; i < emptyStars; i++) {
    stars.push(
      <Star key={`empty-${i}`} size={18} color={theme.colors.iconDisabled} weight="regular" />
    );
  }

  return stars;
};

const EventRating = ({ rating, totalReviews, onPressReview }: EventRatingProps) => {
  return (
    <Box flexDirection="row" alignItems="center" justifyContent="space-between">
      <Box flexDirection="row" alignItems="center" gap="xxs_4">
        <Text color="placeholderText">{rating.toFixed(1)}</Text>

        <Box flexDirection="row" gap="xxs_4">
          {getStarIcons(rating)}
        </Box>

        <Text color="placeholderText">({Intl.NumberFormat('en-US').format(totalReviews)})</Text>
      </Box>

      {onPressReview && (
        <Pressable
          backgroundColor="brandMain"
          borderRadius="sm_8"
          paddingHorizontal="sm_12"
          paddingVertical="xs_8"
          onPress={onPressReview}>
          <Text variant="l_14SemiBold_action" color="white">
            Review
          </Text>
        </Pressable>
      )}
    </Box>
  );
};

export default EventRating;
