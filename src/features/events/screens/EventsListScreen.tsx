/**
 * Events List Screen
 * Browse and discover events
 */
import React, { useState } from 'react';

import { RefreshControl } from 'react-native';

import { Feather } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { FlashList } from '@shopify/flash-list';
import { useTheme } from '@shopify/restyle';
import { SafeAreaView } from 'react-native-safe-area-context';

import { EventsStackScreenProps } from '@/src/core/navigation/types';
import { Box, Pressable, Text, Theme } from '@/src/core/theme';
import { useResponsive } from '@/src/core/theme/useResponsive';
import { CategoryPills, FilterSheet } from '@/src/features/discovery';
import { useFilters } from '@/src/features/discovery/hooks/useFilters';
import { NavigationTopBar } from '@/src/shared/components';

import EventCard from '../components/EventCard';
import { useEventRSVP, useEventShare } from '../hooks/useEventActions';
import { useEvents, useTodayEvents, useTrendingEvents } from '../hooks/useEvents';
import type { Event } from '../types/event.types';

const EventItem = ({ item, onPress }: { item: Event; onPress: (event: Event) => void }) => {
  const { mutate: rsvp } = useEventRSVP(item.id);
  const { mutate: share } = useEventShare(item.id);

  return (
    <Box paddingHorizontal="md_16">
      <EventCard
        event={item}
        onPress={() => onPress(item)}
        onRSVP={() => rsvp(item.userRsvp === 'going' ? 'not_going' : 'going')}
        onShare={() => share()}
      />
    </Box>
  );
};

const EventsListScreen: React.FC = () => {
  const theme = useTheme<Theme>();
  const { select } = useResponsive();
  const navigation = useNavigation<EventsStackScreenProps<'EventsList'>['navigation']>();
  const [showFilters, setShowFilters] = useState(false);
  const [selectedTab, setSelectedTab] = useState<'all' | 'today' | 'trending'>('all');
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);

  const { filters, updateFilters } = useFilters();

  // Convert discovery filters to event filters
  const convertToEventFilters = React.useCallback((discoveryFilters: any) => {
    // Create a new object to avoid mutating the original
    const eventFilters = { ...discoveryFilters };

    // Handle incompatible sortBy values
    if (eventFilters.sortBy) {
      // Map incompatible values or remove them
      switch (eventFilters.sortBy) {
        case 'date':
        case 'popularity':
        case 'distance':
          // These values are compatible, keep as is
          break;
        case 'price':
        case 'rating':
          // Map to a default compatible value or remove
          eventFilters.sortBy = 'date'; // Or choose another compatible default
          break;
        default:
          // For any other unexpected values
          delete eventFilters.sortBy;
      }
    }

    return eventFilters;
  }, []);

  // Get events based on selected tab
  const allEventsQuery = useEvents(selectedTab === 'all' ? convertToEventFilters(filters) : {});
  const todayEventsQuery = useTodayEvents();
  const trendingEventsQuery = useTrendingEvents();

  // Select active query
  const activeQuery =
    selectedTab === 'today'
      ? todayEventsQuery
      : selectedTab === 'trending'
        ? trendingEventsQuery
        : allEventsQuery;

  // Handle both plain EventListResponse and InfiniteData<EventListResponse> types
  const events = React.useMemo(() => {
    if (!activeQuery.data) return [];

    // Check if it's InfiniteData (has pages property)
    if ('pages' in activeQuery.data) {
      // It's an infinite query, extract events from the first page or all pages as needed
      return activeQuery.data.pages.flatMap(page => page.events || []);
    } else {
      // It's a regular query
      return activeQuery.data.events || [];
    }
  }, [activeQuery.data]);

  const isLoading = activeQuery.isLoading;
  const isRefreshing = activeQuery.isFetching && !isLoading;

  // Handle navigation
  const handleEventPress = (event: Event) => {
    navigation.navigate('EventDetails', { eventId: event.id, location: null });
  };

  // const handleCreateEvent = () => {
  //   navigation.navigate('CreateEvent', { selectedLocation: null });
  // };

  const handleSearch = () => {
    navigation.navigate('SearchEvents');
  };

  // Handle refresh
  const handleRefresh = () => {
    activeQuery.refetch();
  };

  // Handle category selection
  const handleCategorySelect = (categoryId: string) => {
    setSelectedCategories(prev =>
      prev.includes(categoryId) ? prev.filter(id => id !== categoryId) : [...prev, categoryId]
    );
    // TODO: Apply category filter
  };

  // Handle filter apply
  const handleApplyFilters = (newFilters: any) => {
    updateFilters(newFilters);
    setShowFilters(false);
  };

  // Render event item

  // Render header
  const renderHeader = () => (
    <Box>
      {/* Tabs */}
      <Box flexDirection="row" paddingHorizontal="md_16" paddingVertical="sm_12" gap="xs_8">
        <Pressable onPress={() => setSelectedTab('all')} style={{ flex: 1 }}>
          <Box
            paddingVertical="xs_8"
            borderRadius="md_12"
            backgroundColor={selectedTab === 'all' ? 'brandMain' : 'elevatedBackground'}
            alignItems="center">
            <Text variant="b_14Medium_button" color={selectedTab === 'all' ? 'white' : 'mainText'}>
              All Events
            </Text>
          </Box>
        </Pressable>
        <Pressable onPress={() => setSelectedTab('today')} style={{ flex: 1 }}>
          <Box
            paddingVertical="xs_8"
            borderRadius="md_12"
            backgroundColor={selectedTab === 'today' ? 'brandMain' : 'elevatedBackground'}
            alignItems="center">
            <Text
              variant="b_14Medium_button"
              color={selectedTab === 'today' ? 'white' : 'mainText'}>
              Today
            </Text>
          </Box>
        </Pressable>
        <Pressable onPress={() => setSelectedTab('trending')} style={{ flex: 1 }}>
          <Box
            paddingVertical="xs_8"
            borderRadius="md_12"
            backgroundColor={selectedTab === 'trending' ? 'brandMain' : 'elevatedBackground'}
            alignItems="center">
            <Text
              variant="b_14Medium_button"
              color={selectedTab === 'trending' ? 'white' : 'mainText'}>
              Trending 🔥
            </Text>
          </Box>
        </Pressable>
      </Box>

      {/* Categories */}
      <CategoryPills selected={selectedCategories} onSelect={handleCategorySelect} scrollable />
    </Box>
  );

  // Render empty state
  const renderEmpty = () => {
    if (isLoading) return null;

    return (
      <Box flex={1} alignItems="center" justifyContent="center" paddingHorizontal="xl_32">
        <Feather
          name="calendar"
          size={select({ phone: 48, tablet: 64 })}
          color={theme.colors.textSecondary}
          style={{ marginBottom: theme.spacing.md_16 }}
        />
        <Text
          variant="h_24SemiBold_section"
          color="mainText"
          textAlign="center"
          marginBottom="xs_8">
          {selectedTab === 'today'
            ? 'No Events Today'
            : selectedTab === 'trending'
              ? 'No Trending Events'
              : 'No Events Found'}
        </Text>
        <Text variant="b_14Regular_content" color="textSecondary" textAlign="center">
          {selectedTab === 'today'
            ? 'Check back later or browse all events'
            : 'Be the first to create an event!'}
        </Text>
      </Box>
    );
  };

  const renderEventItem = ({ item }: { item: Event }) => (
    <EventItem item={item} onPress={handleEventPress} />
  );

  return (
    <SafeAreaView style={{ flex: 1 }} edges={['top']}>
      <Box backgroundColor="background" flex={1}>
        <NavigationTopBar
          title="Events"
          trailingActions={
            <Box flexDirection="row" gap="xs_8">
              <Pressable onPress={handleSearch}>
                <Feather
                  name="search"
                  size={select({ phone: 24, tablet: 28 })}
                  color={theme.colors.mainText}
                />
              </Pressable>
              <Pressable onPress={() => setShowFilters(true)}>
                <Feather
                  name="filter"
                  size={select({ phone: 24, tablet: 28 })}
                  color={theme.colors.mainText}
                />
              </Pressable>
            </Box>
          }
        />

        <FlashList
          data={events}
          renderItem={renderEventItem}
          keyExtractor={item => item.id}
          ListHeaderComponent={renderHeader}
          ListEmptyComponent={renderEmpty}
          estimatedItemSize={300}
          contentContainerStyle={{
            paddingBottom: theme.spacing.xl_32 + 80,
          }}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              tintColor={theme.colors.brandMain}
            />
          }
        />

        {/* Create event FAB */}
        {/* <FAB icon="plus" onPress={handleCreateEvent} position="bottom-right" label="Create Event" /> */}

        {/* Filter sheet */}
        <FilterSheet
          visible={showFilters}
          onClose={() => setShowFilters(false)}
          filters={filters}
          onApply={handleApplyFilters}
        />
      </Box>
    </SafeAreaView>
  );
};

export default EventsListScreen;
