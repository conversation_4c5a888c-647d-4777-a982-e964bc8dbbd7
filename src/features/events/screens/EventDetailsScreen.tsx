import { FlatList, Platform } from 'react-native';

import { useBottomTabBarHeight } from '@react-navigation/bottom-tabs';
import { Calendar, MapTrifold } from 'phosphor-react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { Box, ScrollableContainer, useTheme } from '@/src/core/theme';
import { Card, Chip, Divider, Tag } from '@/src/shared/components';

import EventLocation from '../components/EventLocation';
import EventMoreInfoAccordion from '../components/EventMoreInfoAccordion';
import EventRating from '../components/EventRating';
import ExpandableText from '../components/ExpandableText';
import HoursCard from '../components/HoursCard';
import ReviewCard from '../components/ReviewCard';
import SectionHeader from '../components/SectionHeader';
import ThermometerChart from '../components/WeekTrendChart';
import PartyPhotoSlider from '../components/partyPhotoSlider';

const EventDetailsScreen = () => {
  const { colors } = useTheme();
  const tabBarHeight = useBottomTabBarHeight();

  return (
    <SafeAreaView
      edges={['left', 'right', 'bottom']}
      style={{
        flex: 1,
      }}>
      <ScrollableContainer
        backgroundColor="mainBackground"
        padding="md_16"
        gap="sm_12"
        contentContainerStyle={{
          paddingBottom: Platform.OS === 'ios' ? tabBarHeight : 0,
        }}>
        <PartyPhotoSlider />

        <FlatList
          horizontal
          showsHorizontalScrollIndicator={false}
          data={Array.from({ length: 4 }, (_, i) => `Item ${i + 1}`)}
          keyExtractor={item => item}
          renderItem={({ item }) => <Tag variant="outlineInfo" text="House party" />}
          contentContainerStyle={{
            gap: 8,
          }}
        />

        <ExpandableText text="Join us for a fun barbecue in our garden. Enjoy grilled treats and great music. Bring your friends and make memories with us in an unforgettable night filled with joy and laughter under the stars." />

        <Box flexDirection="row" gap="xs_8">
          <Chip label="Book now" leftIcon={<Calendar size={16} color={colors.iconPrimary} />} />
          <Chip label="Directions" leftIcon={<MapTrifold size={16} color={colors.iconPrimary} />} />
          <Chip label="..." />
        </Box>

        <EventLocation address="Grand City St. 100, New York, United States." />

        <Divider />

        <SectionHeader title="Hours" actionLabel="view all" onPress={() => {}} />

        <HoursCard
          hours={[
            { label: 'Today', time: '10 AM - 2 AM' },
            { label: 'Tomorrow', time: '10 AM - 2 AM' },
          ]}
        />

        <SectionHeader title="Popular Times" />

        <ThermometerChart
          data={[15, 22, 18, 25, 20, 10, 16]}
          maxValue={30}
          width={320}
          height={150}
        />

        <SectionHeader title="Highlights" />

        <Card padding="md_16">
          <FlatList
            horizontal
            showsHorizontalScrollIndicator={false}
            data={Array.from({ length: 6 }, (_, i) => `Item ${i + 1}`)}
            keyExtractor={item => item}
            renderItem={({ item }) => <Tag variant="highlight" text="House party" />}
            contentContainerStyle={{
              gap: 8,
            }}
          />
        </Card>

        <SectionHeader title="Reviews" actionLabel="view all" onPress={() => {}} />

        <FlatList
          horizontal
          showsHorizontalScrollIndicator={false}
          data={Array.from({ length: 6 }, (_, i) => `Item ${i + 1}`)}
          keyExtractor={item => item}
          renderItem={({ item }) => (
            <ReviewCard
              avatarUrl="https://github.com/hmarques98.png"
              name="Angelina Marques"
              rating={4.5}
              date="21 Aug"
              comment="Nice host with a nice atmosphere with nice friends and much more"
            />
          )}
          contentContainerStyle={{
            gap: 8,
          }}
        />

        <EventRating
          rating={4.5}
          totalReviews={2300}
          onPressReview={() => console.log('Open review modal')}
        />

        <SectionHeader title="More infos" />

        <EventMoreInfoAccordion
          sections={[
            {
              title: 'Entry details',
              content: ['Free entry until 10 PM.', 'Bring ID and QR code.'],
            },
            {
              title: 'Music & Entertainment',
              content: ['Live DJ from 8 PM.', 'Open mic at midnight.'],
            },
            {
              title: 'Special Offers and Happy Hours',
              content: ['2x1 drinks from 6–8 PM.', 'Free shots for early birds.'],
            },
            {
              title: 'Payment Options',
              content: ['Credit, debit, and Pix accepted.'],
            },
            {
              title: 'Amenities',
              content: [
                'Outdoor seating askdks mkasdka asdkaskd asdkask',
                'VIP lounge',
                'Free parking',
              ],
            },
            {
              title: 'Offerings',
              content: ['Cocktails', 'Craft beer', 'BBQ buffet'],
            },
          ]}
        />
      </ScrollableContainer>
    </SafeAreaView>
  );
};

export default EventDetailsScreen;
