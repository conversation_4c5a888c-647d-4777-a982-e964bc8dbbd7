/**
 * Create Event Screen
 * Multi-step form for creating events
 */
import React, { useEffect, useRef, useState } from 'react';

import { Platform, ScrollView } from 'react-native';

import { zodResolver } from '@hookform/resolvers/zod';
import { useBottomTabBarHeight } from '@react-navigation/bottom-tabs';
import { addHours, setHours, setMinutes } from 'date-fns';
import { Globe, Lock, Users } from 'phosphor-react-native';
import { Controller, useForm } from 'react-hook-form';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import { z } from 'zod';

import { Box, Pressable, Text, TextInput, useTheme } from '@/src/core/theme';
import {
  But<PERSON>,
  DatePickerField,
  LocationPicker,
  NavigationTopBar,
  Switch,
} from '@/src/shared/components';
import { useToast } from '@/src/shared/hooks/useToast';

import { useCreateEvent, useSuggestedTags } from '../hooks/useEventCreation';
import { useTemplateHandler } from '../hooks/useTemplateHandler';
import { useEventStore } from '../store/eventStore';
import type { CreateEventData, EventPrivacy } from '../types/event.types';

// Form schema
const eventSchema = z
  .object({
    title: z.string().min(3, 'Title must be at least 3 characters').max(100),
    description: z.string().min(10, 'Description must be at least 10 characters').max(1000),
    location: z.object({
      name: z.string(),
      address: z.string(),
      latitude: z.number(),
      longitude: z.number(),
    }),
    startDate: z.date().refine(date => date > new Date(), {
      message: 'Start date must be in the future',
    }),
    endDate: z.date(),
    privacy: z.enum(['public', 'private', 'friends']),
    capacity: z.number().optional(),
    ageRestriction: z.number().min(0).max(100).optional(),
    entryFee: z.number().min(0).optional(),
    preferences: z.object({
      notifyRadarUsers: z.boolean(),
      drinksAvailable: z.boolean(),
      privateListing: z.boolean(),
      allowGroups: z.boolean(),
    }),
    tags: z.array(z.string()),
  })
  .refine(data => data.endDate > data.startDate, {
    message: 'End date must be after start date',
    path: ['endDate'],
  });

type FormData = z.infer<typeof eventSchema>;

const CreateEventScreen: React.FC = () => {
  const theme = useTheme();
  const tabBarHeight = useBottomTabBarHeight();
  const { info: showInfo } = useToast();
  const [currentStep, setCurrentStep] = useState(0);
  const [isFormInitialized, setIsFormInitialized] = useState(false);
  const { eventDraft, updateEventDraft, clearEventDraft } = useEventStore();
  const { mutate: createEvent, isPending: isLoading } = useCreateEvent();
  const suggestedTags = useSuggestedTags();

  const eventDraftRef = useRef(eventDraft);

  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    reset,
  } = useForm<FormData>({
    resolver: zodResolver(eventSchema),
    mode: 'onBlur',
    defaultValues: {
      title: '',
      description: '',
      startDate: setMinutes(setHours(new Date(), 20), 0), // Default to 8 PM
      endDate: setMinutes(setHours(addHours(new Date(), 4), 0), 0), // 4 hours later
      privacy: 'public',
      preferences: {
        notifyRadarUsers: true,
        drinksAvailable: false,
        privateListing: false,
        allowGroups: true,
      },
      tags: [],
    },
  });

  // Watch form values - ensure we get a fresh copy each time
  const formValues = watch();

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsFormInitialized(true);

      // Capture the initial eventDraft to avoid infinite loops
      const initialDraft = eventDraftRef.current;
      if (initialDraft) {
        // Create a deep copy to ensure all nested objects are mutable
        const draftData = {
          title: initialDraft.title || '',
          description: initialDraft.description || '',
          location: initialDraft.location || undefined,
          startDate:
            initialDraft.startDate instanceof Date
              ? initialDraft.startDate
              : setMinutes(setHours(new Date(), 20), 0),
          endDate:
            initialDraft.endDate instanceof Date
              ? initialDraft.endDate
              : setMinutes(setHours(addHours(new Date(), 4), 0), 0),
          privacy: initialDraft.privacy || 'public',
          capacity: initialDraft.capacity || undefined,
          ageRestriction: initialDraft.ageRestriction || undefined,
          entryFee: initialDraft.entryFee || undefined,
          tags: initialDraft.tags ? [...initialDraft.tags] : [],
          // Create a completely new preferences object to avoid read-only errors
          preferences: {
            notifyRadarUsers: initialDraft.preferences?.notifyRadarUsers ?? true,
            drinksAvailable: initialDraft.preferences?.drinksAvailable ?? false,
            privateListing: initialDraft.preferences?.privateListing ?? false,
            allowGroups: initialDraft.preferences?.allowGroups ?? true,
          },
        };
        reset(draftData);
        showInfo('Draft loaded');
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [reset, showInfo]);

  // Save draft on change - only after form is initialized
  useEffect(() => {
    if (!isFormInitialized) return;

    const subscription = watch(data => {
      updateEventDraft(data as Partial<CreateEventData>);
    });
    return () => subscription.unsubscribe();
  }, [watch, updateEventDraft, isFormInitialized]);

  // Use the template handler hook
  // Use the template handler hook with only the methods it needs
  const { templates, appliedTemplate, handleTemplateSelect } = useTemplateHandler(
    {
      // Only pass the methods we actually use in the hook
      setValue,
      watch,
    },
    isFormInitialized
  );

  // Handle tag selection
  const handleTagToggle = (tag: string) => {
    const currentTags = formValues.tags || [];
    if (currentTags.includes(tag)) {
      setValue(
        'tags',
        currentTags.filter(t => t !== tag)
      );
    } else {
      setValue('tags', [...currentTags, tag]);
    }
  };

  // Handle form submission
  const onSubmit = (data: FormData) => {
    // Add timezone
    const eventData: CreateEventData = {
      ...data,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      currency: 'BRL', // Default currency
    };

    createEvent(eventData, {
      onSuccess: () => {
        clearEventDraft();
      },
    });
  };

  // Navigate between steps
  const nextStep = () => {
    if (currentStep < 2) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Render step indicator
  const renderStepIndicator = () => (
    <Box flexDirection="row" justifyContent="center" paddingVertical="md_16" gap="xs_8">
      {[0, 1, 2].map(step => (
        <Box
          key={step}
          width={step === currentStep ? 32 : 8}
          height={8}
          borderRadius="circle_9999"
          backgroundColor={step === currentStep ? 'brandMain' : 'border'}
        />
      ))}
    </Box>
  );

  // Render step content
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return renderBasicDetails();
      case 1:
        return renderEventSettings();
      case 2:
        return renderPreferences();
      default:
        return null;
    }
  };

  // Step 1: Basic Details
  const renderBasicDetails = () => (
    <Box paddingHorizontal="md_16" flex={1}>
      {/* Templates */}
      <Text variant="h_20Medium_subsection" color="mainText" marginBottom="sm_12">
        Quick Start Templates
      </Text>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: theme.spacing.md_16 }}>
        {templates.map(template => {
          const isSelected = appliedTemplate === template.id;
          return (
            <Pressable
              key={template.id}
              onPress={() => handleTemplateSelect(template)}
              enabled={isFormInitialized}>
              <Box
                backgroundColor={isSelected ? 'primary' : 'surfaceBackground'}
                padding="sm_12"
                borderRadius="md_12"
                marginRight="xs_8"
                alignItems="center"
                minWidth={100}
                borderWidth={isSelected ? 2 : 0}
                borderColor={isSelected ? 'primaryDark' : 'transparent'}>
                <Text variant="h_20Medium_subsection" marginBottom="xxs_4">
                  {template.icon}
                </Text>
                <Text variant="l_12Medium_message" color={isSelected ? 'surface' : 'text'}>
                  {template.name}
                </Text>
                {isSelected && (
                  <Text variant="l_10SemiBold_tag" color="surface" marginTop="xxs_4">
                    Applied ✓
                  </Text>
                )}
              </Box>
            </Pressable>
          );
        })}
      </ScrollView>

      {/* Title */}
      <Controller
        control={control}
        name="title"
        render={({ field: { onChange, onBlur, value } }) => (
          <TextInput
            label="Event Title"
            placeholder="Friday Night Party"
            value={value}
            onChangeText={onChange}
            onBlur={onBlur}
            error={errors.title?.message}
            maxLength={100}
          />
        )}
      />

      {/* Description */}
      <Box marginTop="md_16">
        <Controller
          control={control}
          name="description"
          render={({ field: { onChange, onBlur, value } }) => (
            <TextInput
              label="Description"
              placeholder="Tell people what to expect..."
              value={value}
              onChangeText={onChange}
              onBlur={onBlur}
              error={errors.description?.message}
              multiline
              numberOfLines={4}
              maxLength={1000}
            />
          )}
        />
      </Box>

      {/* Location */}
      <Box marginTop="md_16">
        <Controller
          control={control}
          name="location"
          render={({ field: { onChange, value } }) => (
            <LocationPicker
              label="Location"
              value={value}
              onSelect={onChange}
              error={errors.location?.message}
            />
          )}
        />
      </Box>

      {/* Date & Time */}
      <Box marginTop="md_16">
        <Text variant="b_14Medium_button" color="mainText" marginBottom="xs_8">
          Date & Time
        </Text>

        {/* Start Date */}
        {isFormInitialized && (
          <DatePickerField
            name="startDate"
            control={control}
            type="datetime"
            label="Start Date & Time"
            placeholder="Select start date and time"
            error={errors.startDate?.message}
            min={new Date()}
            formatDate={date => {
              if (!date || Array.isArray(date)) return 'Select start date and time';
              return date.toLocaleString('en-US', {
                weekday: 'short',
                month: 'short',
                day: 'numeric',
                hour: 'numeric',
                minute: '2-digit',
                hour12: true,
              });
            }}
          />
        )}

        {/* End Date */}
        {isFormInitialized && (
          <Box marginTop="md_16">
            <DatePickerField
              name="endDate"
              control={control}
              type="datetime"
              label="End Date & Time"
              placeholder="Select end date and time"
              error={errors.endDate?.message}
              min={formValues.startDate || new Date()}
              formatDate={date => {
                if (!date || Array.isArray(date)) return 'Select end date and time';
                return date.toLocaleString('en-US', {
                  weekday: 'short',
                  month: 'short',
                  day: 'numeric',
                  hour: 'numeric',
                  minute: '2-digit',
                  hour12: true,
                });
              }}
            />
          </Box>
        )}
      </Box>
    </Box>
  );

  const IconToDisplay = (privacy: EventPrivacy) => {
    switch (privacy) {
      case 'public':
        return <Globe />;
      case 'friends':
        return <Users />;
      case 'private':
        return <Lock />;
    }
  };

  // Step 2: Event Settings
  const renderEventSettings = () => (
    <Box paddingHorizontal="md_16">
      {/* Privacy */}
      <Text variant="h_20Medium_subsection" color="mainText" marginBottom="md_16">
        Event Settings
      </Text>

      <Text variant="b_14Medium_button" color="mainText" marginBottom="sm_12">
        Who can see this event?
      </Text>
      <Controller
        control={control}
        name="privacy"
        render={({ field: { onChange, value } }) => (
          <Box gap="xs_8" marginBottom="lg_24">
            {(['public', 'friends', 'private'] as EventPrivacy[]).map(privacy => (
              <Pressable key={privacy} onPress={() => onChange(privacy)}>
                <Box
                  flexDirection="row"
                  alignItems="center"
                  padding="sm_12"
                  borderRadius="md_12"
                  backgroundColor={value === privacy ? 'brandMain' : 'elevatedBackground'}>
                  {React.cloneElement(IconToDisplay(privacy), {
                    size: 20,
                    color: value === privacy ? theme.colors.white : theme.colors.mainText,
                  })}
                  <Box marginLeft="sm_12" flex={1}>
                    <Text
                      variant="b_14Medium_button"
                      color={value === privacy ? 'white' : 'mainText'}>
                      {privacy.charAt(0).toUpperCase() + privacy.slice(1)}
                    </Text>
                    <Text
                      variant="l_12Regular_helperText"
                      color={value === privacy ? 'white' : 'textSecondary'}>
                      {privacy === 'public' && 'Anyone can find and join'}
                      {privacy === 'friends' && 'Only friends can see'}
                      {privacy === 'private' && 'Invite only'}
                    </Text>
                  </Box>
                </Box>
              </Pressable>
            ))}
          </Box>
        )}
      />

      {/* Capacity */}
      <Controller
        control={control}
        name="capacity"
        render={({ field: { onChange, onBlur, value } }) => (
          <TextInput
            label="Guest Limit (optional)"
            placeholder="Leave empty for unlimited"
            value={value?.toString() || ''}
            onChangeText={text => onChange(text ? parseInt(text, 10) : undefined)}
            onBlur={onBlur}
            keyboardType="numeric"
            error={errors.capacity?.message}
          />
        )}
      />

      {/* Age Restriction */}
      <Box marginTop="md_16">
        <Controller
          control={control}
          name="ageRestriction"
          render={({ field: { onChange, onBlur, value } }) => (
            <TextInput
              label="Minimum Age (optional)"
              placeholder="18"
              value={value?.toString() || ''}
              onChangeText={text => onChange(text ? parseInt(text, 10) : undefined)}
              onBlur={onBlur}
              keyboardType="numeric"
              error={errors.ageRestriction?.message}
            />
          )}
        />
      </Box>

      {/* Entry Fee */}
      <Box marginTop="md_16">
        <Controller
          control={control}
          name="entryFee"
          render={({ field: { onChange, onBlur, value } }) => (
            <TextInput
              label="Entry Fee (optional)"
              placeholder="0"
              value={value?.toString() || ''}
              onChangeText={text => onChange(text ? parseFloat(text) : undefined)}
              onBlur={onBlur}
              keyboardType="decimal-pad"
              error={errors.entryFee?.message}
              leading={<Text variant="b_14Medium_button">R$</Text>}
            />
          )}
        />
      </Box>
    </Box>
  );

  // Step 3: Preferences
  const renderPreferences = () => (
    <Box paddingHorizontal="md_16">
      <Text variant="h_20Medium_subsection" color="mainText" marginBottom="md_16">
        Event Preferences
      </Text>

      {/* Preferences switches */}
      <Controller
        control={control}
        name="preferences.notifyRadarUsers"
        render={({ field: { onChange, value } }) => (
          <Box
            flexDirection="row"
            alignItems="center"
            justifyContent="space-between"
            paddingVertical="sm_12">
            <Box flex={1} marginRight="md_16">
              <Text variant="b_14Medium_button" color="mainText">
                Notify Nearby Users
              </Text>
              <Text variant="l_12Regular_helperText" color="textSecondary">
                Alert users on radar about your event
              </Text>
            </Box>
            <Switch isChecked={value} onToggle={onChange} />
          </Box>
        )}
      />

      <Controller
        control={control}
        name="preferences.drinksAvailable"
        render={({ field: { onChange, value } }) => (
          <Box
            flexDirection="row"
            alignItems="center"
            justifyContent="space-between"
            paddingVertical="sm_12">
            <Box flex={1} marginRight="md_16">
              <Text variant="b_14Medium_button" color="mainText">
                🍺 Drinks Available
              </Text>
              <Text variant="l_12Regular_helperText" color="textSecondary">
                Let guests know drinks will be served
              </Text>
            </Box>
            <Switch isChecked={value} onToggle={onChange} />
          </Box>
        )}
      />

      <Controller
        control={control}
        name="preferences.allowGroups"
        render={({ field: { onChange, value } }) => (
          <Box
            flexDirection="row"
            alignItems="center"
            justifyContent="space-between"
            paddingVertical="sm_12">
            <Box flex={1} marginRight="md_16">
              <Text variant="b_14Medium_button" color="mainText">
                Allow Group RSVPs
              </Text>
              <Text variant="l_12Regular_helperText" color="textSecondary">
                Guests can RSVP for their friends
              </Text>
            </Box>
            <Switch isChecked={value} onToggle={onChange} />
          </Box>
        )}
      />

      {/* Tags */}
      <Box marginTop="lg_24">
        <Text variant="b_14Medium_button" color="mainText" marginBottom="sm_12">
          Add Tags
        </Text>
        <Box flexDirection="row" flexWrap="wrap" gap="xs_8">
          {suggestedTags.map(tag => (
            <Pressable key={tag} onPress={() => handleTagToggle(tag)}>
              <Box
                paddingHorizontal="sm_12"
                paddingVertical="xs_8"
                borderRadius="circle_9999"
                backgroundColor={
                  formValues.tags?.includes(tag) ? 'brandMain' : 'surfaceBackground'
                }>
                <Text
                  variant="l_12Medium_message"
                  color={formValues.tags?.includes(tag) ? 'white' : 'mainText'}>
                  #{tag}
                </Text>
              </Box>
            </Pressable>
          ))}
        </Box>
      </Box>
    </Box>
  );

  return (
    <Box backgroundColor="background" flex={1} style={{ paddingBottom: tabBarHeight }}>
      <NavigationTopBar
        title="Create Event"
        trailingActions={
          currentStep > 0 && (
            <Pressable onPress={prevStep}>
              <Text variant="b_14Medium_button" color="brandMain">
                Back
              </Text>
            </Pressable>
          )
        }
      />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}>
        <ScrollView
          contentContainerStyle={{
            paddingBottom: theme.spacing.xl_32 + 80,
          }}
          showsVerticalScrollIndicator={false}>
          {renderStepIndicator()}
          {renderStepContent()}
        </ScrollView>

        {/* Bottom actions */}
        <Box
          paddingHorizontal="md_16"
          paddingTop="md_16"
          backgroundColor="background"
          style={{
            shadowColor: '#000',
            shadowOffset: { width: 0, height: -2 },
            shadowOpacity: 0.1,
            shadowRadius: 4,
            elevation: 5,
          }}>
          {currentStep < 2 ? (
            <Button onPress={nextStep} title="Next" variant="primary" />
          ) : (
            <Button
              onPress={() => handleSubmit(onSubmit)}
              title="Create Event"
              variant="primary"
              loading={isLoading}
            />
          )}
        </Box>
      </KeyboardAvoidingView>
    </Box>
  );
};

export default CreateEventScreen;
