import React, { useState } from 'react';

import { FlatList, KeyboardAvoidingView, Platform, Pressable, TextInput } from 'react-native';

import {
  BeerBottle,
  Camera,
  Lightning,
  MagnifyingGlass,
  Martini,
  Microphone,
  Pizza,
  Plus,
  Star,
  Ticket,
  Users,
  Wine,
} from 'phosphor-react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { Box, Text, useTheme } from '@/src/core/theme';
import { NavigationTopBar } from '@/src/shared/components';

import { EventFilterPills, FilterType } from '../components/EventFilterPills';
import { EventTemplateCard } from '../components/EventTemplateCard';
import { EventType, EventTypeSelector } from '../components/EventTypeSelector';

interface EventTemplate {
  id: string;
  title: string;
  subtitle?: string;
  icon: React.ReactNode;
  iconBgColor: string;
  category: EventType;
  filters: FilterType[];
  expiresIn?: string;
  isHot?: boolean;
  revenueCapable?: boolean;
}

export const EventCreationScreen = () => {
  const theme = useTheme();
  const [selectedType, setSelectedType] = useState<EventType>('personal');
  const [selectedFilter, setSelectedFilter] = useState<FilterType>('all');
  const [searchText, setSearchText] = useState('');

  const templates: EventTemplate[] = [
    // Personal Party Templates
    {
      id: '1',
      title: '2-for-1 Drinks',
      subtitle: 'Perfect for happy hour',
      icon: <Martini size={28} color={theme.colors.primary} weight="duotone" />,
      iconBgColor: 'primaryLight',
      category: 'personal',
      filters: ['specials'],
      revenueCapable: true,
    },
    {
      id: '2',
      title: 'Free Apps',
      subtitle: 'Complimentary appetizers',
      icon: <Pizza size={28} color={theme.colors.success} weight="duotone" />,
      iconBgColor: 'successLight',
      category: 'personal',
      filters: ['specials'],
      revenueCapable: true,
    },
    {
      id: '3',
      title: 'Trivia Night',
      subtitle: 'Test your knowledge',
      icon: <Star size={28} color={theme.colors.warning} weight="duotone" />,
      iconBgColor: 'warningLight',
      category: 'personal',
      filters: ['music'],
    },
    {
      id: '4',
      title: 'Live Music',
      subtitle: 'Local artists performing',
      icon: <Microphone size={28} color={theme.colors.error} weight="duotone" />,
      iconBgColor: 'errorLight',
      category: 'personal',
      filters: ['music'],
    },

    // Venue Event Templates
    {
      id: '5',
      title: 'Skip Line VIP',
      subtitle: 'Priority access',
      icon: <Lightning size={28} color={theme.colors.primary} weight="duotone" />,
      iconBgColor: 'primaryLight',
      category: 'venue',
      filters: ['vip'],
      revenueCapable: true,
    },
    {
      id: '6',
      title: 'Bottle Service',
      subtitle: 'Premium table service',
      icon: <BeerBottle size={28} color={theme.colors.secondary} weight="duotone" />,
      iconBgColor: 'secondaryLight',
      category: 'venue',
      filters: ['vip'],
      revenueCapable: true,
    },
    {
      id: '7',
      title: 'Guest List +3',
      subtitle: 'Bring your friends',
      icon: <Users size={28} color={theme.colors.primary} weight="duotone" />,
      iconBgColor: 'primaryLight',
      category: 'venue',
      filters: ['vip'],
    },
    {
      id: '8',
      title: 'Artist Meet',
      subtitle: 'Meet & greet opportunity',
      icon: <Star size={28} color={theme.colors.warning} weight="duotone" />,
      iconBgColor: 'warningLight',
      category: 'venue',
      filters: ['vip', 'music'],
    },

    // Flash Invite Templates
    {
      id: '9',
      title: 'Last Minute Hangout',
      subtitle: 'Quick spontaneous gathering',
      icon: <Lightning size={28} color={theme.colors.error} weight="duotone" />,
      iconBgColor: 'errorLight',
      category: 'flash',
      filters: ['urgent'],
      expiresIn: '1 hour',
      isHot: true,
    },
    {
      id: '10',
      title: 'Flash Deal',
      subtitle: 'Limited time offer',
      icon: <Ticket size={28} color={theme.colors.success} weight="duotone" />,
      iconBgColor: 'successLight',
      category: 'flash',
      filters: ['urgent', 'specials'],
      expiresIn: '2 hours',
    },
    {
      id: '11',
      title: 'Pop-Up Event',
      subtitle: 'Secret location party',
      icon: <Camera size={28} color={theme.colors.primary} weight="duotone" />,
      iconBgColor: 'primaryLight',
      category: 'flash',
      filters: ['trending'],
      expiresIn: '3 hours',
    },
    {
      id: '12',
      title: 'Emergency Fun',
      subtitle: 'Bored right now?',
      icon: <Star size={28} color={theme.colors.warning} weight="duotone" />,
      iconBgColor: 'warningLight',
      category: 'flash',
      filters: ['urgent', 'trending'],
      expiresIn: '30 minutes',
      isHot: true,
    },
  ];

  const filteredTemplates = templates.filter(template => {
    const matchesType = template.category === selectedType;
    const matchesFilter = selectedFilter === 'all' || template.filters.includes(selectedFilter);
    const matchesSearch =
      searchText === '' ||
      template.title.toLowerCase().includes(searchText.toLowerCase()) ||
      (template.subtitle && template.subtitle.toLowerCase().includes(searchText.toLowerCase()));

    return matchesType && matchesFilter && matchesSearch;
  });

  const handleTemplatePress = (template: EventTemplate) => {
    // Navigate to event details/form with template
    console.log('Selected template:', template);
  };

  const handleCreateFromScratch = () => {
    // Navigate to blank event creation
    console.log('Create from scratch');
  };

  const handleQuickHappyHour = () => {
    console.log('Quick Happy Hour');
    // Navigate to event creation with Happy Hour template
  };

  const handleQuickLiveMusic = () => {
    console.log('Quick Live Music');
    // Navigate to event creation with Live Music template
  };

  const handleQuickFlashParty = () => {
    console.log('Quick Flash Party');
    // Navigate to event creation with Flash Party template
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: theme.colors.background }}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}>
        {/* Header */}
        <NavigationTopBar
          title="Create Invite"
          rightActionLabel="Preview"
          onRightAction={() => console.log('Preview')}
        />

        {/* Event Type Selector */}
        <Box paddingHorizontal="md_16" marginBottom="md_16">
          <EventTypeSelector selectedType={selectedType} onTypeChange={setSelectedType} />
        </Box>

        {/* Search Bar */}
        <Box paddingHorizontal="md_16" marginBottom="sm_12">
          <Box
            flexDirection="row"
            alignItems="center"
            backgroundColor="surfaceBackground"
            borderRadius="md_12"
            paddingHorizontal="md_16"
            paddingVertical="sm_12"
            gap="sm_12">
            <MagnifyingGlass size={20} color={theme.colors.textSecondary} />
            <TextInput
              value={searchText}
              onChangeText={setSearchText}
              placeholder="Type naturally or tap a template below"
              placeholderTextColor={theme.colors.textTertiary}
              style={{
                flex: 1,
                fontSize: 16,
                color: theme.colors.text,
                fontFamily: theme.textVariants.defaults.fontFamily,
              }}
            />
          </Box>
        </Box>

        {/* Quick Actions */}
        <Box paddingHorizontal="md_16" marginBottom="md_16">
          <Text variant="b_14Medium_button" color="textSecondary" marginBottom="sm_12">
            Quick Actions
          </Text>

          <Box flexDirection="row" gap="xs_8">
            {/* Happy Hour */}
            <Box flex={1}>
              <Pressable onPress={handleQuickHappyHour}>
                <Box
                  backgroundColor="warningDark"
                  borderRadius="md_12"
                  padding="sm_12"
                  flexDirection="row"
                  alignItems="center"
                  gap="xs_8">
                  <Box
                    width={32}
                    height={32}
                    borderRadius="circle_9999"
                    backgroundColor="warning"
                    alignItems="center"
                    justifyContent="center">
                    <Wine size={18} color={theme.colors.white} weight="bold" />
                  </Box>
                  <Box flex={1}>
                    <Text variant="b_14Medium_button" color="white" numberOfLines={1}>
                      Happy Hour
                    </Text>
                    <Text variant="l_10SemiBold_tag" color="white" opacity={0.8} numberOfLines={1}>
                      Promos Alert
                    </Text>
                  </Box>
                </Box>
              </Pressable>
            </Box>

            {/* Live Music */}
            <Box flex={1}>
              <Pressable onPress={handleQuickLiveMusic}>
                <Box
                  backgroundColor="primaryDark"
                  borderRadius="md_12"
                  padding="sm_12"
                  flexDirection="row"
                  alignItems="center"
                  gap="xs_8">
                  <Box
                    width={32}
                    height={32}
                    borderRadius="circle_9999"
                    backgroundColor="primary"
                    alignItems="center"
                    justifyContent="center">
                    <Microphone size={18} color={theme.colors.white} weight="bold" />
                  </Box>
                  <Box flex={1}>
                    <Text variant="b_14Medium_button" color="white" numberOfLines={1}>
                      Live Music
                    </Text>
                    <Text variant="l_10SemiBold_tag" color="white" opacity={0.8} numberOfLines={1}>
                      Show Alert
                    </Text>
                  </Box>
                </Box>
              </Pressable>
            </Box>

            {/* Flash Party */}
            <Box flex={1}>
              <Pressable onPress={handleQuickFlashParty}>
                <Box
                  backgroundColor="secondary"
                  borderRadius="md_12"
                  padding="sm_12"
                  flexDirection="row"
                  alignItems="center"
                  gap="xs_8">
                  <Box
                    width={32}
                    height={32}
                    borderRadius="circle_9999"
                    backgroundColor="secondaryLight"
                    alignItems="center"
                    justifyContent="center">
                    <Lightning size={18} color={theme.colors.white} weight="bold" />
                  </Box>
                  <Box flex={1}>
                    <Text variant="b_14Medium_button" color="white" numberOfLines={1}>
                      Flash Party
                    </Text>
                    <Text variant="l_10SemiBold_tag" color="white" opacity={0.8} numberOfLines={1}>
                      2hr Event
                    </Text>
                  </Box>
                </Box>
              </Pressable>
            </Box>
          </Box>
        </Box>

        {/* Filter Pills */}
        <Box marginBottom="md_16">
          <EventFilterPills selectedFilter={selectedFilter} onFilterChange={setSelectedFilter} />
        </Box>

        {/* Templates Grid */}
        <FlatList
          data={filteredTemplates}
          keyExtractor={item => item.id}
          numColumns={2}
          contentContainerStyle={{
            paddingHorizontal: theme.spacing.md_16,
            paddingBottom: 100,
          }}
          columnWrapperStyle={{
            gap: theme.spacing.sm_12,
          }}
          ItemSeparatorComponent={() => <Box height={theme.spacing.sm_12} />}
          renderItem={({ item }) => (
            <Box flex={1}>
              <EventTemplateCard
                icon={item.icon}
                title={item.title}
                subtitle={item.subtitle}
                iconBgColor={item.iconBgColor}
                onPress={() => handleTemplatePress(item)}
                expiresIn={item.expiresIn}
                isHot={item.isHot}
                revenueCapable={item.revenueCapable}
              />
            </Box>
          )}
          ListEmptyComponent={
            <Box alignItems="center" paddingVertical="xxxl_48">
              <Text variant="b_16Regular_input" color="textSecondary">
                No templates found. Try a different filter.
              </Text>
            </Box>
          }
        />

        {/* Floating Action Button */}
        <Box
          position="absolute"
          bottom={20}
          right={20}
          width={56}
          height={56}
          borderRadius="circle_9999"
          backgroundColor="primary"
          alignItems="center"
          justifyContent="center"
          style={{
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.25,
            shadowRadius: 4,
            elevation: 5,
          }}>
          <Plus size={24} color={theme.colors.white} weight="bold" />
        </Box>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};
