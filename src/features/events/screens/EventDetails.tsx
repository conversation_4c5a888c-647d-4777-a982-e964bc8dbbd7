import { FlatList, Platform } from 'react-native';

import { useBottomTabBarHeight } from '@react-navigation/bottom-tabs';
import { MapTrifold } from 'phosphor-react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { Box, ScrollableContainer, Text, useTheme } from '@/src/core/theme';
import { Alert, Button, Card, Tag } from '@/src/shared/components';

import AvatarGroup from '../components/AvatarGroup';
import EventPersonCard from '../components/EventPersonCard';
import PartyPhotoSlider from '../components/partyPhotoSlider';

const EventDetails = () => {
  const { colors } = useTheme();
  const tabBarHeight = useBottomTabBarHeight();

  return (
    <SafeAreaView
      edges={['left', 'right', 'bottom']}
      style={{
        flex: 1,
      }}>
      <ScrollableContainer
        backgroundColor="mainBackground"
        padding="md_16"
        gap="sm_12"
        contentContainerStyle={{
          paddingBottom: Platform.OS === 'ios' ? tabBarHeight : 0,
        }}>
        <PartyPhotoSlider />

        <Alert type="success" text="You were invited for this party" />

        <Card padding="md_16" gap="sm_12">
          <FlatList
            horizontal
            showsHorizontalScrollIndicator={false}
            data={Array.from({ length: 4 }, (_, i) => `Item ${i + 1}`)}
            keyExtractor={item => item}
            renderItem={({ item }) => <Tag variant="outlineInfo" text="House party" />}
            contentContainerStyle={{
              gap: 8,
            }}
          />

          <Text variant="b_16Medium_button">
            Join us for a fun barbecue in our garden Enjoy grilled treats and great music. Bring
            your friends and make memories! Read more...
          </Text>

          <AvatarGroup
            avatars={[
              'https://github.com/mathzsl.png',
              'https://github.com/hmarques98.png',
              'https://github.com/maykbrito.png',
              'https://github.com/hmarques98.png',
              'https://github.com/hmarques98.png',
            ]}
          />
        </Card>

        <Card padding="md_16">
          <Box flexDirection="row" justifyContent="space-between" alignItems="center" gap="xs_8">
            <Box flex={1} gap="xs_8">
              <Text variant="h_18SemiBold_cardTitle">Grand Park, New York City, US</Text>
              <Text variant="b_14Regular_input">Grand City St. 100, New York, United States.</Text>
            </Box>
            <MapTrifold size={32} color={colors['iconPrimary']} />
          </Box>

          <Button title="Leavy party" variant="secondary" />
        </Card>

        <FlatList
          horizontal
          showsHorizontalScrollIndicator={false}
          data={Array.from({ length: 6 }, (_, i) => `Item ${i + 1}`)}
          keyExtractor={item => item}
          renderItem={({ item }) => (
            <EventPersonCard
              person={{
                role: 'DJ',
                name: 'Henrique',
                description:
                  'Join DJ Blaze for an electrifying night of high-energy beats and live music!',
                imageUrl: 'https://github.com/hmarques98.png',
              }}
            />
          )}
          contentContainerStyle={{
            gap: 8,
          }}
        />
      </ScrollableContainer>
    </SafeAreaView>
  );
};

export default EventDetails;
