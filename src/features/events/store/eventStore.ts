/**
 * Event Store
 * Zustand store for event-related state management
 */
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

import { appStorage, createZustandMMKVStorage } from '@/src/core/storage';

import type { CreateEventData, EventFilters } from '../types/event.types';

interface EventStore {
  // Draft management
  eventDraft: Partial<CreateEventData> | null;
  setEventDraft: (draft: Partial<CreateEventData> | null) => void;
  updateEventDraft: (updates: Partial<CreateEventData>) => void;
  clearEventDraft: () => void;

  // Filters
  eventFilters: EventFilters;
  updateEventFilters: (filters: Partial<EventFilters>) => void;
  resetEventFilters: () => void;

  // Recent searches
  recentEventSearches: string[];
  addRecentEventSearch: (search: string) => void;
  clearRecentEventSearches: () => void;

  // Saved events (local cache)
  savedEventIds: string[];
  toggleSavedEvent: (eventId: string) => void;
  isSavedEvent: (eventId: string) => boolean;

  // User preferences
  eventPreferences: {
    defaultPrivacy: 'public' | 'private' | 'friends';
    notifyForNearbyEvents: boolean;
    preferredRadius: number;
  };
  updateEventPreferences: (prefs: Partial<EventStore['eventPreferences']>) => void;
}

const DEFAULT_FILTERS: EventFilters = {
  radius: 5000,
  sortBy: 'date',
  sortOrder: 'asc',
};

const DEFAULT_PREFERENCES = {
  defaultPrivacy: 'public' as const,
  notifyForNearbyEvents: true,
  preferredRadius: 5000,
};

export const useEventStore = create<EventStore>()(
  persist(
    immer((set, get) => ({
      // Draft management
      eventDraft: null,
      setEventDraft: draft =>
        set(state => {
          state.eventDraft = draft;
        }),
      updateEventDraft: updates =>
        set(state => {
          if (!state.eventDraft) {
            state.eventDraft = updates;
          } else {
            // Handle nested preferences object specially to ensure deep merge
            if (updates.preferences && state.eventDraft.preferences) {
              state.eventDraft = {
                ...state.eventDraft,
                ...updates,
                preferences: {
                  ...state.eventDraft.preferences,
                  ...updates.preferences,
                },
              };
            } else {
              state.eventDraft = { ...state.eventDraft, ...updates };
            }
          }
        }),
      clearEventDraft: () =>
        set(state => {
          state.eventDraft = null;
        }),

      // Filters
      eventFilters: DEFAULT_FILTERS,
      updateEventFilters: filters =>
        set(state => {
          state.eventFilters = { ...state.eventFilters, ...filters };
        }),
      resetEventFilters: () =>
        set(state => {
          state.eventFilters = DEFAULT_FILTERS;
        }),

      // Recent searches
      recentEventSearches: [],
      addRecentEventSearch: search =>
        set(state => {
          // Remove if already exists
          const filtered = state.recentEventSearches.filter(s => s !== search);
          // Add to beginning
          state.recentEventSearches = [search, ...filtered].slice(0, 10);
        }),
      clearRecentEventSearches: () =>
        set(state => {
          state.recentEventSearches = [];
        }),

      // Saved events
      savedEventIds: [],
      toggleSavedEvent: eventId =>
        set(state => {
          const index = state.savedEventIds.indexOf(eventId);
          if (index > -1) {
            state.savedEventIds.splice(index, 1);
          } else {
            state.savedEventIds.push(eventId);
          }
        }),
      isSavedEvent: eventId => {
        return get().savedEventIds.includes(eventId);
      },

      // User preferences
      eventPreferences: DEFAULT_PREFERENCES,
      updateEventPreferences: prefs =>
        set(state => {
          state.eventPreferences = { ...state.eventPreferences, ...prefs };
        }),
    })),
    {
      name: 'event-store',
      storage: createJSONStorage(() => createZustandMMKVStorage(appStorage)),
      partialize: state => ({
        eventDraft: state.eventDraft,
        eventFilters: state.eventFilters,
        recentEventSearches: state.recentEventSearches,
        savedEventIds: state.savedEventIds,
        eventPreferences: state.eventPreferences,
      }),
    }
  )
);
