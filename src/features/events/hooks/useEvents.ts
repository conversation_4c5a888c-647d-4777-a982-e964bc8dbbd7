/**
 * Events Hook
 * Hook for fetching and managing events list
 */
import { useInfiniteQuery, useQuery } from '@tanstack/react-query';

import { useLocation } from '@/src/shared/hooks';

import { eventService } from '../services/eventService';
import type { Event, EventFilters } from '../types/event.types';

/**
 * Hook for fetching events with infinite scroll
 */
export const useEvents = (filters?: EventFilters) => {
  const { location } = useLocation();

  return useInfiniteQuery({
    queryKey: ['events', filters, location?.coords],
    queryFn: async ({ pageParam }: { pageParam: string | undefined }) => {
      // Add location to filters if available
      const enhancedFilters: EventFilters = {
        ...filters,
        cursor: pageParam,
        location: location?.coords
          ? {
              latitude: location.coords.latitude,
              longitude: location.coords.longitude,
            }
          : undefined,
      };

      const response = await eventService.getEvents(enhancedFilters);

      if (!response.success) {
        throw new Error(response.errors?.[0]?.message || 'Failed to fetch events');
      }

      return response.data;
    },
    getNextPageParam: lastPage => lastPage.nextCursor,
    initialPageParam: undefined as string | undefined,
  });
};

/**
 * Hook for fetching user's events
 */
export const useUserEvents = (userId: string, type: 'hosting' | 'attending' | 'interested') => {
  return useQuery({
    queryKey: ['user-events', userId, type],
    queryFn: async () => {
      const response = await eventService.getUserEvents(userId, type);

      if (!response.success) {
        throw new Error(response.errors?.[0]?.message || 'Failed to fetch user events');
      }

      return response.data;
    },
    enabled: !!userId,
  });
};

/**
 * Hook for searching events
 */
export const useSearchEvents = (query: string, filters?: EventFilters) => {
  const { location } = useLocation();

  return useQuery({
    queryKey: ['events', 'search', query, filters, location?.coords],
    queryFn: async () => {
      // TODO: Implement search endpoint
      const response = await eventService.getEvents({
        ...filters,
        // Add search query to filters when API supports it
      });

      if (!response.success) {
        throw new Error(response.errors?.[0]?.message || 'Failed to search events');
      }

      // Filter results locally for now
      const searchResults = response.data.events.filter(
        (event: Event) =>
          event.title.toLowerCase().includes(query.toLowerCase()) ||
          event.description.toLowerCase().includes(query.toLowerCase()) ||
          event.tags.some((tag: string) => tag.toLowerCase().includes(query.toLowerCase()))
      );

      return {
        ...response.data,
        events: searchResults,
        totalCount: searchResults.length,
      };
    },
    enabled: query.length > 0,
  });
};

/**
 * Hook for getting nearby events
 */
export const useNearbyEvents = (radius: number = 5000) => {
  const { location } = useLocation();

  return useQuery({
    queryKey: ['events', 'nearby', location?.coords, radius],
    queryFn: async () => {
      if (!location) {
        return { events: [], totalCount: 0, hasMore: false };
      }

      const response = await eventService.getEvents({
        location: {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        },
        radius,
        sortBy: 'distance',
      });

      if (!response.success) {
        throw new Error(response.errors?.[0]?.message || 'Failed to fetch nearby events');
      }

      return response.data;
    },
    enabled: !!location,
  });
};

/**
 * Hook for getting trending events
 */
export const useTrendingEvents = () => {
  return useQuery({
    queryKey: ['events', 'trending'],
    queryFn: async () => {
      const response = await eventService.getEvents({
        sortBy: 'popularity',
        sortOrder: 'desc',
      });

      if (!response.success) {
        throw new Error(response.errors?.[0]?.message || 'Failed to fetch trending events');
      }

      return response.data;
    },
  });
};

/**
 * Hook for getting today's events
 */
export const useTodayEvents = () => {
  return useQuery({
    queryKey: ['events', 'today'],
    queryFn: async () => {
      const response = await eventService.getEvents({
        onlyToday: true,
        sortBy: 'date',
        sortOrder: 'asc',
      });

      if (!response.success) {
        throw new Error(response.errors?.[0]?.message || "Failed to fetch today's events");
      }

      return response.data;
    },
  });
};

/**
 * Hook for getting upcoming events
 */
export const useUpcomingEvents = () => {
  const today = new Date();
  const nextWeek = new Date();
  nextWeek.setDate(today.getDate() + 7);

  return useQuery({
    queryKey: ['events', 'upcoming'],
    queryFn: async () => {
      const response = await eventService.getEvents({
        dateRange: {
          start: today,
          end: nextWeek,
        },
        sortBy: 'date',
        sortOrder: 'asc',
      });

      if (!response.success) {
        throw new Error(response.errors?.[0]?.message || 'Failed to fetch upcoming events');
      }

      return response.data;
    },
  });
};
