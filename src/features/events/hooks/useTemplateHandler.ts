import { useState } from 'react';

import { Path, PathValue, UseFormReturn } from 'react-hook-form';

import { useToast } from '@/src/shared/hooks/useToast';

import { useEventTemplates } from './useEventCreation';

type FormValues = {
  privacy?: 'public' | 'private' | 'friends';
  ageRestriction?: number;
  preferences?: {
    notifyRadarUsers?: boolean;
    drinksAvailable?: boolean;
    privateListing?: boolean;
    allowGroups?: boolean;
  };
  tags?: string[];
  [key: string]: unknown;
};

/**
 * Hook for handling event templates
 * Encapsulates template selection and application logic
 */
export const useTemplateHandler = <T extends FormValues>(
  // Accept only the form methods we actually need instead of full UseFormReturn
  form: {
    setValue: UseFormReturn<T>['setValue'];
    watch: UseFormReturn<T>['watch'];
  },
  isFormInitialized: boolean
) => {
  const { info: showInfo } = useToast();
  const templates = useEventTemplates();
  const [appliedTemplate, setAppliedTemplate] = useState<string | null>(null);

  /**
   * Apply a template to the form values
   * Creates fresh, mutable objects to avoid read-only property errors
   */
  const handleTemplateSelect = (template: (typeof templates)[number]) => {
    if (!isFormInitialized) {
      console.warn('Form not initialized yet, template selection ignored');
      return;
    }

    try {
      const { setValue, watch } = form as UseFormReturn<T>;
      const formValues = watch();

      // Set privacy
      if (template.data.privacy) {
        setValue('privacy' as Path<T>, template.data.privacy as PathValue<T, Path<T>>, {
          shouldValidate: true,
          shouldDirty: true,
          shouldTouch: true,
        });
      }

      // Set age restriction
      if (template.data.ageRestriction) {
        setValue(
          'ageRestriction' as Path<T>,
          template.data.ageRestriction as PathValue<T, Path<T>>,
          {
            shouldDirty: true,
            shouldValidate: true,
            shouldTouch: true,
          }
        );
      }

      // Set preferences (nested object)
      if (template.data.preferences) {
        // Get current preferences safely
        const currentPrefs = formValues.preferences || {};

        // Extract primitive values explicitly to avoid references to frozen objects
        // This ensures we're not referencing any potentially read-only properties
        const notifyRadarUsersValue =
          typeof template.data.preferences.notifyRadarUsers === 'boolean'
            ? Boolean(template.data.preferences.notifyRadarUsers)
            : typeof currentPrefs.notifyRadarUsers === 'boolean'
              ? Boolean(currentPrefs.notifyRadarUsers)
              : true;

        const drinksAvailableValue =
          typeof template.data.preferences.drinksAvailable === 'boolean'
            ? Boolean(template.data.preferences.drinksAvailable)
            : typeof currentPrefs.drinksAvailable === 'boolean'
              ? Boolean(currentPrefs.drinksAvailable)
              : false;

        const privateListingValue =
          typeof template.data.preferences.privateListing === 'boolean'
            ? Boolean(template.data.preferences.privateListing)
            : typeof currentPrefs.privateListing === 'boolean'
              ? Boolean(currentPrefs.privateListing)
              : false;

        const allowGroupsValue =
          typeof template.data.preferences.allowGroups === 'boolean'
            ? Boolean(template.data.preferences.allowGroups)
            : typeof currentPrefs.allowGroups === 'boolean'
              ? Boolean(currentPrefs.allowGroups)
              : true;

        // Create a fresh object with primitive boolean values
        const newPreferences = {
          notifyRadarUsers: notifyRadarUsersValue,
          drinksAvailable: drinksAvailableValue,
          privateListing: privateListingValue,
          allowGroups: allowGroupsValue,
        };

        // Set the value with completely new object
        setValue('preferences' as Path<T>, newPreferences as PathValue<T, Path<T>>, {
          shouldDirty: true,
          shouldValidate: true,
          shouldTouch: true,
        });
      }

      // Set tags - create new array
      if (template.data.tags) {
        setValue('tags' as Path<T>, [...template.data.tags] as PathValue<T, Path<T>>, {
          shouldDirty: true,
          shouldValidate: true,
          shouldTouch: true,
        });
      }

      // Create a summary of what was applied
      const appliedChanges = [];
      if (template.data.privacy) {
        appliedChanges.push(`Privacy: ${template.data.privacy}`);
      }
      if (template.data.ageRestriction) {
        appliedChanges.push(`Age: ${template.data.ageRestriction}+`);
      }
      if (template.data.preferences) {
        const prefSummary = [];
        if (template.data.preferences.drinksAvailable) prefSummary.push('drinks');
        if (template.data.preferences.notifyRadarUsers) prefSummary.push('notifications');
        if (template.data.preferences.privateListing) prefSummary.push('private listing');
        if (prefSummary.length > 0) appliedChanges.push(`Features: ${prefSummary.join(', ')}`);
      }
      if (template.data.tags) {
        appliedChanges.push(`Tags: ${template.data.tags.join(', ')}`);
      }

      // Track which template was applied
      setAppliedTemplate(template.id);

      const summary = appliedChanges.length > 0 ? `\n${appliedChanges.join('\n')}` : '';
      showInfo(`${template.name} template applied!${summary}`);
    } catch (error) {
      console.error('Error applying template:', error);
      showInfo('Failed to apply template');
    }
  };

  return {
    templates,
    appliedTemplate,
    handleTemplateSelect,
  };
};
