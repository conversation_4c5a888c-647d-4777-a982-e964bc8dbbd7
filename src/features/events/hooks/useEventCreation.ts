/**
 * Event Creation Hook
 * Hook for creating and editing events
 */
import { useNavigation } from '@react-navigation/native';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { z } from 'zod';

import { useToast } from '@/src/shared/hooks';

import { eventService } from '../services/eventService';
import type { CreateEventData, Event } from '../types/event.types';

// Helper to extract error message from event service response
const extractErrorMessage = (response: any, fallbackMessage: string) => {
  if (response && !response.success && response.errors?.length > 0) {
    return response.errors[0].message || fallbackMessage;
  }
  return fallbackMessage;
};

/**
 * Event creation schema
 */
const createEventSchema = z
  .object({
    title: z.string().min(3, 'Title must be at least 3 characters').max(100),
    description: z.string().min(10, 'Description must be at least 10 characters').max(1000),
    location: z.object({
      name: z.string(),
      address: z.string(),
      latitude: z.number(),
      longitude: z.number(),
    }),
    startDate: z.date().refine(date => date > new Date(), 'Start date must be in the future'),
    endDate: z.date(),
    timezone: z.string(),
    privacy: z.enum(['public', 'private', 'friends']),
    capacity: z.number().optional(),
    ageRestriction: z.number().min(0).max(100).optional(),
    entryFee: z.number().min(0).optional(),
    currency: z.string().optional(),
    preferences: z.object({
      notifyRadarUsers: z.boolean(),
      drinksAvailable: z.boolean(),
      privateListing: z.boolean(),
      allowGroups: z.boolean(),
    }),
    tags: z.array(z.string()).min(1, 'Add at least one tag'),
  })
  .refine(data => data.endDate > data.startDate, {
    message: 'End date must be after start date',
    path: ['endDate'],
  });

/**
 * Hook for creating event
 */
export const useCreateEvent = () => {
  const queryClient = useQueryClient();
  const navigation = useNavigation();
  const toast = useToast();

  return useMutation({
    mutationFn: async (data: CreateEventData) => {
      // Validate data
      const validatedData = createEventSchema.parse(data);
      return eventService.createEvent(validatedData);
    },
    onSuccess: response => {
      if (response.success) {
        // Invalidate events list
        queryClient.invalidateQueries({ queryKey: ['events'] });
        queryClient.invalidateQueries({ queryKey: ['user-events'] });

        // Show success message
        toast.successWithHaptic('Event created successfully! 🎉');

        // Navigate to event details
        navigation.navigate('EventDetails' as never, { eventId: response.data.id } as never);
      }
    },
    onError: error => {
      if (error instanceof z.ZodError) {
        toast.errorWithHaptic(error.errors[0].message);
      } else {
        const message = extractErrorMessage(error, 'Failed to create event');
        toast.errorWithHaptic(message);
      }
    },
  });
};

/**
 * Hook for updating event
 */
export const useUpdateEvent = (eventId: string) => {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: async (data: Partial<CreateEventData>) => {
      // Validate only provided fields
      const validatedData = createEventSchema.parse(data);
      return eventService.updateEvent(eventId, validatedData);
    },
    onSuccess: response => {
      if (response.success) {
        // Update cache
        queryClient.setQueryData<Event>(['event', eventId], response.data);
        queryClient.invalidateQueries({ queryKey: ['events'] });

        toast.successWithHaptic('Event updated successfully');
      }
    },
    onError: error => {
      if (error instanceof z.ZodError) {
        toast.errorWithHaptic(error.errors[0].message);
      } else {
        const message = extractErrorMessage(error, 'Failed to update event');
        toast.errorWithHaptic(message);
      }
    },
  });
};

/**
 * Hook for event draft management
 */
export const useEventDraft = () => {
  // TODO: Implement draft storage using MMKV
  const saveDraft = (data: Partial<CreateEventData>) => {
    // Save to local storage
  };

  const loadDraft = (): Partial<CreateEventData> | null => {
    // Load from local storage
    return null;
  };

  const clearDraft = () => {
    // Clear from local storage
  };

  return {
    saveDraft,
    loadDraft,
    clearDraft,
  };
};

/**
 * Hook for validating event data
 */
export const useEventValidation = () => {
  const validateField = (field: keyof CreateEventData, value: any) => {
    try {
      const fieldSchema = createEventSchema;
      fieldSchema.parse(value);
      return { valid: true, error: null };
    } catch (error) {
      if (error instanceof z.ZodError) {
        return { valid: false, error: error.errors[0].message };
      }
      return { valid: false, error: 'Invalid value' };
    }
  };

  const validateForm = (data: CreateEventData) => {
    try {
      createEventSchema.parse(data);
      return { valid: true, errors: {} };
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors: Record<string, string> = {};
        error.errors.forEach(err => {
          const path = err.path.join('.');
          errors[path] = err.message;
        });
        return { valid: false, errors };
      }
      return { valid: false, errors: { general: 'Invalid data' } };
    }
  };

  return {
    validateField,
    validateForm,
  };
};

/**
 * Hook for suggested tags
 */
export const useSuggestedTags = () => {
  // Popular tags for nightlife events
  const suggestedTags = [
    'nightlife',
    'party',
    'dj',
    'livemusic',
    'drinks',
    'rooftop',
    'club',
    'bar',
    'happyhour',
    'cocktails',
    'dancing',
    'hiphop',
    'edm',
    'techno',
    'rock',
    'karaoke',
    'openbar',
    'birthday',
    'celebration',
    'networking',
  ];

  return suggestedTags;
};

/**
 * Hook for event templates
 */
export const useEventTemplates = () => {
  const templates = [
    {
      id: 'house-party',
      name: 'House Party',
      icon: '🏠',
      data: {
        privacy: 'private' as const,
        preferences: {
          notifyRadarUsers: false,
          drinksAvailable: true,
          privateListing: true,
          allowGroups: true,
        },
        tags: ['party', 'drinks', 'friends'],
      },
    },
    {
      id: 'club-night',
      name: 'Club Night',
      icon: '🎵',
      data: {
        privacy: 'public' as const,
        ageRestriction: 18,
        preferences: {
          notifyRadarUsers: true,
          drinksAvailable: true,
          privateListing: false,
          allowGroups: true,
        },
        tags: ['nightlife', 'club', 'dj', 'dancing'],
      },
    },
    {
      id: 'bar-meetup',
      name: 'Bar Meetup',
      icon: '🍺',
      data: {
        privacy: 'public' as const,
        preferences: {
          notifyRadarUsers: true,
          drinksAvailable: true,
          privateListing: false,
          allowGroups: true,
        },
        tags: ['bar', 'drinks', 'meetup', 'social'],
      },
    },
    {
      id: 'birthday-party',
      name: 'Birthday Party',
      icon: '🎂',
      data: {
        privacy: 'friends' as const,
        preferences: {
          notifyRadarUsers: false,
          drinksAvailable: true,
          privateListing: false,
          allowGroups: true,
        },
        tags: ['birthday', 'party', 'celebration'],
      },
    },
  ];

  return templates;
};
