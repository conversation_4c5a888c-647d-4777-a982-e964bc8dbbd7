/**
 * Event Details Hook
 * Hook for fetching and managing single event details
 */
import { useQuery } from '@tanstack/react-query';

import { eventService } from '../services/eventService';
import type { Event } from '../types/event.types';

/**
 * Hook for fetching event details
 */
export const useEventDetails = (eventId: string) => {
  return useQuery({
    queryKey: ['event', eventId],
    queryFn: async () => {
      const response = await eventService.getEventDetails(eventId);

      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to fetch event details');
      }

      return response.data;
    },
    enabled: !!eventId,
  });
};

/**
 * Hook for fetching event attendees
 */
export const useEventAttendees = (eventId: string, rsvpStatus?: 'going' | 'interested') => {
  return useQuery({
    queryKey: ['event', eventId, 'attendees', rsvpStatus],
    queryFn: async () => {
      const response = await eventService.getEventAttendees(eventId, rsvpStatus);

      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to fetch attendees');
      }

      return response.data;
    },
    enabled: !!eventId,
  });
};

/**
 * Hook for fetching event comments
 */
export const useEventComments = (eventId: string) => {
  return useQuery({
    queryKey: ['event', eventId, 'comments'],
    queryFn: async () => {
      const response = await eventService.getEventComments(eventId);

      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to fetch comments');
      }

      return response.data;
    },
    enabled: !!eventId,
  });
};

/**
 * Hook for fetching event updates
 */
export const useEventUpdates = (eventId: string) => {
  return useQuery({
    queryKey: ['event', eventId, 'updates'],
    queryFn: async () => {
      const response = await eventService.getEventUpdates(eventId);

      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to fetch updates');
      }

      return response.data;
    },
    enabled: !!eventId,
  });
};

/**
 * Hook for fetching event analytics (for hosts)
 */
export const useEventAnalytics = (eventId: string, isHost: boolean) => {
  return useQuery({
    queryKey: ['event', eventId, 'analytics'],
    queryFn: async () => {
      const response = await eventService.getEventAnalytics(eventId);

      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to fetch analytics');
      }

      return response.data;
    },
    enabled: !!eventId && isHost,
  });
};

/**
 * Hook for checking if user can edit event
 */
export const useCanEditEvent = (event?: Event) => {
  // TODO: Get current user from auth context
  const currentUserId = 'current_user';

  if (!event) return false;

  // User can edit if they are the host or a co-host
  return event.hostId === currentUserId || event.coHosts?.includes(currentUserId) || false;
};

/**
 * Hook for related events
 */
export const useRelatedEvents = (eventId: string, tags: string[]) => {
  return useQuery({
    queryKey: ['events', 'related', eventId, tags],
    queryFn: async () => {
      // Find events with similar tags
      const response = await eventService.getEvents({
        // TODO: Add tags filter when API supports it
        sortBy: 'popularity',
      });

      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to fetch related events');
      }

      // Filter by tags locally for now
      const relatedEvents = response.data.events
        .filter(
          (event: Event) =>
            event.id !== eventId && event.tags.some((tag: string) => tags.includes(tag))
        )
        .slice(0, 5);

      return relatedEvents;
    },
    enabled: !!eventId && tags.length > 0,
  });
};

/**
 * Hook to combine all event details data
 */
export const useFullEventDetails = (eventId: string) => {
  const eventQuery = useEventDetails(eventId);
  const attendeesQuery = useEventAttendees(eventId);
  const commentsQuery = useEventComments(eventId);
  const updatesQuery = useEventUpdates(eventId);

  return {
    event: eventQuery.data,
    attendees: attendeesQuery.data || [],
    comments: commentsQuery.data || [],
    updates: updatesQuery.data || [],
    isLoading:
      eventQuery.isLoading ||
      attendeesQuery.isLoading ||
      commentsQuery.isLoading ||
      updatesQuery.isLoading,
    error: eventQuery.error || attendeesQuery.error || commentsQuery.error || updatesQuery.error,
  };
};
