/**
 * Event Actions Hook
 * Hook for event mutations (RSVP, save, share, etc.)
 */
import { useMutation, useQueryClient } from '@tanstack/react-query';

import { sharing } from '@/src/core/libs';
import { useToast } from '@/src/shared/hooks';

import { eventService } from '../services/eventService';
import type {
  Event,
  EventComment,
  EventReport,
  EventUpdate,
  RSVPStatus,
} from '../types/event.types';

// Helper to extract error message from event service response
const extractErrorMessage = (response: any, fallbackMessage: string) => {
  if (response && !response.success && response.errors?.length > 0) {
    return response.errors[0].message || fallbackMessage;
  }
  return fallbackMessage;
};

/**
 * Hook for RSVP actions
 */
export const useEventRSVP = (eventId: string) => {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: (status: RSVPStatus) => eventService.rsvpEvent(eventId, status),
    onSuccess: (response, status) => {
      if (response.success) {
        // Update event details cache
        queryClient.setQueryData<Event>(['event', eventId], old => {
          if (!old) return old;
          return {
            ...old,
            userRsvp: status,
            attendeeCount:
              status === 'going'
                ? (old.attendeeCount || 0) + 1
                : Math.max(0, (old.attendeeCount || 0) - 1),
            interestedCount:
              status === 'interested'
                ? (old.interestedCount || 0) + 1
                : Math.max(0, (old.interestedCount || 0) - 1),
          };
        });

        // Invalidate related queries
        queryClient.invalidateQueries({ queryKey: ['event', eventId, 'attendees'] });
        queryClient.invalidateQueries({ queryKey: ['events'] });

        const messages = {
          going: "You're going! 🎉",
          interested: 'Marked as interested 👀',
          not_going: 'RSVP removed',
        };
        toast.successWithHaptic(messages[status]);
      }
    },
    onError: error => {
      const message = extractErrorMessage(error, 'Failed to update RSVP');
      toast.errorWithHaptic(message);
    },
  });
};

/**
 * Hook for save/unsave event
 */
export const useEventSave = (eventId: string) => {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: (saved: boolean) => eventService.toggleSaveEvent(eventId, saved),
    onSuccess: (response, saved) => {
      if (response.success) {
        // Update event details cache
        queryClient.setQueryData<Event>(['event', eventId], old => {
          if (!old) return old;
          return { ...old, isSaved: saved };
        });

        toast.successWithHaptic(saved ? 'Event saved' : 'Event unsaved');
      }
    },
    onError: error => {
      const message = extractErrorMessage(error, 'Failed to save event');
      toast.errorWithHaptic(message);
    },
  });
};

/**
 * Hook for sharing event
 */
export const useEventShare = (eventId: string) => {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: async () => {
      const response = await eventService.shareEvent(eventId);
      if (!response.success) {
        throw response;
      }
      return response.data;
    },
    onSuccess: async data => {
      try {
        await sharing.shareAppContent({
          url: data.shareUrl,
          title: 'Check out this event!',
        });

        // Update share count
        queryClient.setQueryData<Event>(['event', eventId], old => {
          if (!old) return old;
          return { ...old, shareCount: (old.shareCount || 0) + 1 };
        });

        toast.successWithHaptic('Event shared');
      } catch {
        // User cancelled sharing
      }
    },
    onError: error => {
      const message = extractErrorMessage(error, 'Failed to share event');
      toast.errorWithHaptic(message);
    },
  });
};

/**
 * Hook for adding comment
 */
export const useAddComment = (eventId: string) => {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: (content: string) => eventService.addEventComment(eventId, content),
    onSuccess: response => {
      if (response.success) {
        // Add comment to cache
        queryClient.setQueryData<EventComment[]>(['event', eventId, 'comments'], old => {
          if (!old) return [response.data];
          return [response.data, ...old];
        });

        toast.successWithHaptic('Comment added');
      }
    },
    onError: error => {
      const message = extractErrorMessage(error, 'Failed to add comment');
      toast.errorWithHaptic(message);
    },
  });
};

/**
 * Hook for posting event update (for hosts)
 */
export const usePostUpdate = (eventId: string) => {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: (update: { title: string; content: string; isImportant?: boolean }) =>
      eventService.postEventUpdate(eventId, update),
    onSuccess: response => {
      if (response.success) {
        // Add update to cache
        queryClient.setQueryData<EventUpdate[]>(['event', eventId, 'updates'], old => {
          if (!old) return [response.data];
          return [response.data, ...old];
        });

        toast.successWithHaptic('Update posted');
      }
    },
    onError: error => {
      const message = extractErrorMessage(error, 'Failed to post update');
      toast.errorWithHaptic(message);
    },
  });
};

/**
 * Hook for reporting event
 */
export const useReportEvent = () => {
  const toast = useToast();

  return useMutation({
    mutationFn: (report: EventReport) => eventService.reportEvent(report),
    onSuccess: response => {
      if (response.success) {
        toast.successWithHaptic("Event reported. We'll review it soon.");
      }
    },
    onError: error => {
      const message = extractErrorMessage(error, 'Failed to report event');
      toast.errorWithHaptic(message);
    },
  });
};

/**
 * Hook for deleting/cancelling event
 */
export const useDeleteEvent = () => {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: (eventId: string) => eventService.deleteEvent(eventId),
    onSuccess: (response, eventId) => {
      if (response.success) {
        // Remove from cache
        queryClient.removeQueries({ queryKey: ['event', eventId] });
        queryClient.invalidateQueries({ queryKey: ['events'] });

        toast.successWithHaptic('Event cancelled');
      }
    },
    onError: error => {
      const message = extractErrorMessage(error, 'Failed to cancel event');
      toast.errorWithHaptic(message);
    },
  });
};

/**
 * Hook for liking a comment
 */
export const useLikeComment = (eventId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ commentId, liked }: { commentId: string; liked: boolean }) => {
      // TODO: Implement like comment API
      return { success: true, data: { liked } };
    },
    onSuccess: (response, variables) => {
      if (response.success) {
        // Update comment in cache
        queryClient.setQueryData<EventComment[]>(['event', eventId, 'comments'], old => {
          if (!old) return old;
          return old.map(comment =>
            comment.id === variables.commentId
              ? {
                  ...comment,
                  isLiked: variables.liked,
                  likes: variables.liked
                    ? (comment.likes || 0) + 1
                    : Math.max(0, (comment.likes || 0) - 1),
                }
              : comment
          );
        });
      }
    },
  });
};
