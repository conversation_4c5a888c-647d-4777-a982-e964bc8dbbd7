/**
 * Event Feature Types
 * Types for user-created events and parties
 */
import type { LocationData } from '@/src/shared/components/LocationPicker';

/**
 * Event status
 */
export type EventStatus = 'draft' | 'published' | 'cancelled' | 'ended';

/**
 * RSVP status
 */
export type RSVPStatus = 'going' | 'interested' | 'not_going';

/**
 * Event privacy settings
 */
export type EventPrivacy = 'public' | 'private' | 'friends';

/**
 * Event preferences
 */
export interface EventPreferences {
  notifyRadarUsers: boolean;
  drinksAvailable: boolean;
  privateListing: boolean;
  allowGroups: boolean;
}

/**
 * Event media
 */
export interface EventMedia {
  id: string;
  url: string;
  thumbnailUrl?: string;
  type: 'image' | 'video';
  uploadedAt: Date;
  uploadedBy: string;
}

/**
 * Event attendee
 */
export interface EventAttendee {
  id: string;
  userId: string;
  name: string;
  avatar?: string;
  rsvpStatus: RSVPStatus;
  rsvpAt: Date;
  isHost?: boolean;
  groupSize?: number;
}

/**
 * Event comment
 */
export interface EventComment {
  id: string;
  eventId: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  content: string;
  createdAt: Date;
  updatedAt?: Date;
  likes: number;
  isLiked?: boolean;
  replies?: EventComment[];
}

/**
 * Event update/announcement
 */
export interface EventUpdate {
  id: string;
  eventId: string;
  title: string;
  content: string;
  createdAt: Date;
  isImportant: boolean;
}

/**
 * Main Event interface
 */
export interface Event {
  id: string;
  title: string;
  description: string;

  // Location
  location: LocationData;

  // Time
  startDate: Date;
  endDate: Date;
  timezone: string;

  // Host
  hostId: string;
  hostName: string;
  hostAvatar?: string;
  coHosts?: string[];

  // Media
  coverImage?: string;
  media: EventMedia[];

  // Settings
  status: EventStatus;
  privacy: EventPrivacy;
  capacity?: number;
  ageRestriction?: number;
  entryFee?: number;
  currency?: string;

  // Preferences
  preferences: EventPreferences;

  // Stats
  attendeeCount: number;
  interestedCount: number;
  viewCount: number;
  shareCount: number;

  // User state
  userRsvp?: RSVPStatus;
  isSaved?: boolean;
  isHost?: boolean;

  // Metadata
  tags: string[];
  createdAt: Date;
  updatedAt: Date;

  // Related
  comments?: EventComment[];
  updates?: EventUpdate[];
  attendees?: EventAttendee[];
}

/**
 * Event creation form data
 */
export interface CreateEventData {
  title: string;
  description: string;
  location: LocationData;
  startDate: Date;
  endDate: Date;
  timezone: string;
  coverImage?: string;
  privacy: EventPrivacy;
  capacity?: number;
  ageRestriction?: number;
  entryFee?: number;
  currency?: string;
  preferences: EventPreferences;
  tags: string[];
  coHosts?: string[];
}

/**
 * Event filters
 */
export interface EventFilters {
  // Time filters
  dateRange?: {
    start: Date;
    end: Date;
  };
  onlyToday?: boolean;
  onlyThisWeek?: boolean;
  onlyThisMonth?: boolean;

  // Location filters
  location?: {
    latitude: number;
    longitude: number;
  };
  radius?: number; // meters

  // Type filters
  privacy?: EventPrivacy[];
  hasEntryFee?: boolean;
  ageRestriction?: number;

  // Status filters
  includeEnded?: boolean;
  includeCancelled?: boolean;

  // User filters
  onlyFollowing?: boolean;
  onlyRsvp?: boolean;

  // Sort
  sortBy?: 'date' | 'popularity' | 'distance' | 'created';
  sortOrder?: 'asc' | 'desc';

  // Pagination
  cursor?: string;
}

/**
 * Event list response
 */
export interface EventListResponse {
  events: Event[];
  totalCount: number;
  hasMore: boolean;
  nextCursor?: string;
}

/**
 * Event analytics
 */
export interface EventAnalytics {
  eventId: string;
  views: {
    total: number;
    unique: number;
    byDay: { date: string; count: number }[];
  };
  engagement: {
    rsvps: number;
    interested: number;
    comments: number;
    shares: number;
  };
  demographics: {
    ageGroups: { range: string; count: number }[];
    topLocations: { location: string; count: number }[];
  };
}

/**
 * Event invite
 */
export interface EventInvite {
  id: string;
  eventId: string;
  invitedBy: string;
  invitedAt: Date;
  status: 'pending' | 'accepted' | 'declined';
  message?: string;
}

/**
 * Event report reasons
 */
export type EventReportReason =
  | 'inappropriate_content'
  | 'spam'
  | 'harassment'
  | 'violence'
  | 'false_information'
  | 'other';

/**
 * Event report
 */
export interface EventReport {
  eventId: string;
  reason: EventReportReason;
  details?: string;
  reportedAt: Date;
}
