import { useCallback, useEffect, useRef, useState } from 'react';

import { useQuery } from '@tanstack/react-query';

import { foursquare } from '@/src/core';

interface UseLocationSearchOptions {
  radius?: number;
  categories?: string[];
  limit?: number;
  sort?: 'relevance' | 'distance' | 'rating' | 'popularity';
  debounceDelay?: number;
}

/**
 * Optimized hook for searching locations using Foursquare API
 * Uses proper debouncing without causing UI lag
 */
export const useFoursquareLocationSearch = (options?: UseLocationSearchOptions) => {
  // Separate state for input value (updates immediately) and search term (debounced)
  const [inputValue, setInputValue] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [userLocation, setUserLocation] = useState<{ lat: number; lng: number } | null>(null);

  // Use ref to track debounce timer
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Debounce search term updates
  useEffect(() => {
    // Clear existing timer
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    // Set new timer
    debounceTimerRef.current = setTimeout(() => {
      setSearchTerm(inputValue);
    }, options?.debounceDelay || 300); // Reduced from 500ms to 300ms

    // Cleanup
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [inputValue, options?.debounceDelay]);

  // Single query for search results (removed autocomplete to reduce API calls)
  const searchQuery = useQuery({
    queryKey: ['foursquare-search', searchTerm, userLocation, options],
    queryFn: async () => {
      if (!searchTerm.trim() || searchTerm.length < 2 || !userLocation) {
        return { results: [] };
      }

      try {
        const response = await foursquare.searchPlaces({
          query: searchTerm,
          ll: `${userLocation.lat},${userLocation.lng}`,
          radius: options?.radius || 50000,
          limit: options?.limit || 20,
          categories: options?.categories || [],
          sort: options?.sort || 'distance',
        });

        return { results: response.results };
      } catch (error) {
        console.error('Search error:', error);
        throw error;
      }
    },
    enabled: searchTerm.length >= 2 && !!userLocation,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes (formerly cacheTime)
    retry: 1,
  });

  // Update input value (no transition needed - updates immediately)
  const updateSearch = useCallback((text: string) => {
    setInputValue(text);
  }, []);

  // Set user location
  const updateLocation = useCallback((lat: number, lng: number) => {
    setUserLocation({ lat, lng });
  }, []);

  // Select a specific place and get its details
  const selectPlace = useCallback(async (fsq_place_id: string) => {
    try {
      const details = await foursquare.getPlaceDetails({
        fsq_place_id,
        fields: ['location', 'geocodes', 'hours', 'rating', 'website', 'tel'],
      });
      return details;
    } catch (error) {
      console.error('Failed to get place details:', error);
      throw error;
    }
  }, []);

  // Get nearby places without search query
  const getNearbyPlaces = useCallback(async () => {
    if (!userLocation) return { results: [] };

    return foursquare.searchNearby(userLocation.lat, userLocation.lng, {
      radius: options?.radius || 1000,
      categories: options?.categories,
      limit: options?.limit || 10,
    });
  }, [userLocation, options]);

  return {
    // Search state
    searchQuery: inputValue,
    debouncedQuery: searchTerm,
    updateSearch,

    // Location state
    userLocation,
    updateLocation,

    // Search results
    places: searchQuery.data?.results || [],
    isSearching: searchQuery.isLoading,
    isTyping: inputValue !== searchTerm && inputValue.length > 0,

    // Actions
    selectPlace,
    getNearbyPlaces,

    // Error states
    searchError: searchQuery.error,

    // Refetch functions
    refetchSearch: searchQuery.refetch,
  };
};
