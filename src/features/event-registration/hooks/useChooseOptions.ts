import { useCallback, useMemo, useState } from 'react';

import { useNavigation } from '@react-navigation/native';

import {
  EventRegistrationStackParamList,
  EventRegistrationStackScreenProps,
} from '@/src/core/navigation/types';

import { useEventRegistrationContext } from '../context/EventRegistrationContext';
import { EventCategory, EventTemplate, EventTemplateCategory } from '../types/event.types';

// Event templates data
const eventTemplates: EventTemplate[] = [
  // Events Category
  {
    id: 'house-party',
    name: 'Casa/Apartamento',
    icon: 'home',
    emoji: '🏠',
    description: 'Festa em casa, ambiente privado',
    category: 'events',
    revenueCapable: false,
    defaults: {
      capacity: 15,
      vipEnabled: false,
      suggestedTimes: ['20:00', '21:00', '22:00'],
      suggestedMusic: ['Funk', 'Eletrônica', 'Variado'],
      duration: '4h',
    },
  },
  {
    id: 'bar-club',
    name: 'Bar/Balada',
    icon: 'beer',
    emoji: '🍺',
    description: 'Evento em estabelecimento',
    category: 'events',
    revenueCapable: true,
    defaults: {
      capacity: 50,
      vipEnabled: true,
      suggestedTimes: ['22:00', '23:00', '00:00'],
      suggestedMusic: ['Eletrônica', 'Funk', 'Sertanejo'],
      duration: '5h',
      suggestedPricing: [
        { tier: 'Antecipado', price: 30, limit: 50 },
        { tier: 'Regular', price: 50, limit: 100 },
        { tier: 'VIP', price: 80, perks: ['Open bar', 'Área reservada'] },
      ],
    },
  },
  {
    id: 'flash-party',
    name: 'Flash Party',
    icon: 'zap',
    emoji: '⚡',
    description: 'Evento rápido, 2-3 horas',
    category: 'events',
    revenueCapable: false,
    defaults: {
      capacity: 25,
      vipEnabled: false,
      suggestedTimes: ['19:00', '20:00', '21:00'],
      suggestedMusic: ['Variado'],
      duration: '3h',
    },
  },

  // Sharing Category
  {
    id: 'promo-share',
    name: 'Cupom/Desconto',
    icon: 'tag',
    emoji: '🎟️',
    description: 'Compartilhar promo ou desconto',
    category: 'sharing',
    revenueCapable: false,
    defaults: {
      suggestedDiscounts: ['10%', '15%', '20%', '2x1', 'R$ 10 off'],
      suggestedDuration: ['1h', '2h', 'Hoje', 'Final de semana'],
      requiresProof: true,
    },
  },

  // Alerts Category
  {
    id: 'happy-hour-alert',
    name: 'Happy Hour',
    icon: 'clock',
    emoji: '🍻',
    description: 'Alertar happy hour rolando',
    category: 'alerts',
    revenueCapable: false,
    defaults: {
      suggestedOffers: ['50% bebidas', '2x1 drinks', 'Cerveja R$ 5', 'Caipirinha R$ 8'],
      suggestedTimes: ['17:00-19:00', '18:00-20:00', '19:00-21:00'],
      requiresPhoto: true,
    },
  },
  {
    id: 'live-show-alert',
    name: 'Show Acontecendo',
    icon: 'music',
    emoji: '🎤',
    description: 'Show ou apresentação ao vivo',
    category: 'alerts',
    revenueCapable: false,
    defaults: {
      suggestedGenres: ['Rock', 'MPB', 'Sertanejo', 'Funk', 'Eletrônica', 'Jazz'],
      requiresArtistName: true,
      suggestedVenues: ['Bar', 'Casa de Show', 'Praça', 'Rua'],
    },
  },

  // Marketplace Category
  {
    id: 'ticket-swap',
    name: 'Trocar/Doar Ingresso',
    icon: 'repeat',
    emoji: '🔄',
    description: 'Vender, trocar ou doar ingresso',
    category: 'marketplace',
    revenueCapable: true,
    defaults: {
      swapTypes: ['Vender', 'Trocar', 'Doar', 'Último preço'],
      paymentMethods: ['PIX', 'Dinheiro', 'Cartão'],
      safetyTips: true,
    },
  },

  // Social Category
  {
    id: 'group-meetup',
    name: 'Grupo/Encontro',
    icon: 'users',
    emoji: '👥',
    description: 'Formar grupo para sair junto',
    category: 'social',
    revenueCapable: false,
    defaults: {
      capacity: 8,
      suggestedActivities: ['Bar crawl', 'Balada', 'Show', 'Jantar'],
      splitCosts: true,
      safetyFeatures: true,
    },
  },

  // Business Category
  {
    id: 'venue-promo',
    name: 'Promover Local',
    icon: 'map-pin',
    emoji: '📍',
    description: 'Divulgar estabelecimento',
    category: 'business',
    revenueCapable: true,
    defaults: {
      venueTypes: ['Bar', 'Restaurant', 'Balada', 'Casa de Show'],
      promoTypes: ['Happy hour', 'Evento especial', 'Nova abertura', 'Oferta'],
      targetAudience: ['Jovens', 'Executivos', 'Famílias', 'Turistas'],
    },
  },
];

const categoryLabels: Record<EventTemplateCategory, string> = {
  events: 'Criar Eventos',
  sharing: 'Compartilhar',
  alerts: 'Alertas da Noite',
  marketplace: 'Marketplace',
  social: 'Social',
  business: 'Negócios',
};

export const useChooseOptions = () => {
  const navigation =
    useNavigation<EventRegistrationStackScreenProps<'ChooseOptions'>['navigation']>();
  const { setSelectedTemplate } = useEventRegistrationContext();
  const [searchQuery, setSearchQuery] = useState('');

  // Group templates by category
  const categorizedTemplates = useMemo(() => {
    const categories: EventCategory[] = [];

    // Get unique categories
    const uniqueCategories = Array.from(new Set(eventTemplates.map(template => template.category)));

    // Create category objects with their templates
    uniqueCategories.forEach(categoryId => {
      const templatesInCategory = eventTemplates.filter(
        template => template.category === categoryId
      );

      if (templatesInCategory.length > 0) {
        categories.push({
          id: categoryId,
          label: categoryLabels[categoryId],
          templates: templatesInCategory,
        });
      }
    });

    return categories;
  }, []);

  // Parse magic input to detect template type
  const parseMagicInput = useCallback((input: string): EventTemplate | null => {
    const lower = input.toLowerCase();

    // Nightlife-specific routing
    if (
      lower.includes('desconto') ||
      lower.includes('promo') ||
      lower.includes('cupom') ||
      lower.includes('%')
    ) {
      return eventTemplates.find(t => t.id === 'promo-share') || null;
    }
    if (lower.includes('happy hour') || lower.includes('promoção') || lower.includes('2x1')) {
      return eventTemplates.find(t => t.id === 'happy-hour-alert') || null;
    }
    if (
      lower.includes('show') ||
      lower.includes('banda') ||
      lower.includes('música ao vivo') ||
      lower.includes('apresentação')
    ) {
      return eventTemplates.find(t => t.id === 'live-show-alert') || null;
    }
    if (
      lower.includes('ingresso') ||
      lower.includes('vender') ||
      lower.includes('trocar') ||
      lower.includes('doar')
    ) {
      return eventTemplates.find(t => t.id === 'ticket-swap') || null;
    }
    if (
      lower.includes('grupo') ||
      lower.includes('galera') ||
      lower.includes('turma') ||
      lower.includes('juntos')
    ) {
      return eventTemplates.find(t => t.id === 'group-meetup') || null;
    }
    if (
      lower.includes('bar') ||
      lower.includes('restaurante') ||
      lower.includes('estabelecimento')
    ) {
      return eventTemplates.find(t => t.id === 'venue-promo') || null;
    }

    // Original event routing
    if (lower.includes('casa') || lower.includes('churras') || lower.includes('apartamento')) {
      return eventTemplates.find(t => t.id === 'house-party') || null;
    }
    if (lower.includes('flash') || lower.includes('rápido') || lower.includes('agora')) {
      return eventTemplates.find(t => t.id === 'flash-party') || null;
    }
    if (lower.includes('balada') || lower.includes('club')) {
      return eventTemplates.find(t => t.id === 'bar-club') || null;
    }

    return null;
  }, []);

  // Handle template selection and navigation
  const handleTemplateSelect = useCallback(
    (template: EventTemplate) => {
      // Store template in context
      setSelectedTemplate(template);

      switch (template.category) {
        case 'sharing':
        case 'alerts':
          // These go to a quick details flow
          navigation.navigate('QuickDetails');
          break;

        case 'marketplace':
          // Goes to marketplace for ticket swap
          navigation.navigate('Marketplace');
          break;

        case 'business':
          // Goes to venue registration
          navigation.navigate('VenueRegistration');
          break;

        case 'events':
          // Check specific event types
          if (template.id === 'flash-party') {
            navigation.navigate('FlashParty');
          } else {
            // Standard event creation flow
            navigation.navigate('CreateEventDetails', {
              selectedLocation: undefined,
            });
          }
          break;

        case 'social':
          // Check specific social types
          if (template.id === 'group-meetup') {
            navigation.navigate('GroupMeetup');
          } else {
            // Standard event creation flow
            navigation.navigate('CreateEventDetails', {
              selectedLocation: undefined,
            });
          }
          break;

        default:
          // Standard event creation flow
          navigation.navigate('CreateEventDetails', {
            selectedLocation: undefined,
          });
          break;
      }
    },
    [navigation, setSelectedTemplate]
  );

  // Handle magic input
  const handleMagicInput = useCallback(() => {
    if (!searchQuery.trim()) return;

    const detectedTemplate = parseMagicInput(searchQuery);
    if (detectedTemplate) {
      handleTemplateSelect(detectedTemplate);
    }
  }, [searchQuery, parseMagicInput, handleTemplateSelect]);

  // Quick action handlers
  const handleQuickHappyHour = useCallback(() => {
    const template = eventTemplates.find(t => t.id === 'happy-hour-alert');
    if (template) handleTemplateSelect(template);
  }, [handleTemplateSelect]);

  const handleQuickLiveShow = useCallback(() => {
    const template = eventTemplates.find(t => t.id === 'live-show-alert');
    if (template) handleTemplateSelect(template);
  }, [handleTemplateSelect]);

  const handleQuickFlashParty = useCallback(() => {
    const template = eventTemplates.find(t => t.id === 'flash-party');
    if (template) handleTemplateSelect(template);
  }, [handleTemplateSelect]);

  return {
    categories: categorizedTemplates,
    searchQuery,
    setSearchQuery,
    handleTemplateSelect,
    handleMagicInput,
    handleQuickHappyHour,
    handleQuickLiveShow,
    handleQuickFlashParty,
    eventTemplates,
  };
};
