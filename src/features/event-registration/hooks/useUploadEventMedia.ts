import { useMutation } from '@tanstack/react-query';
import * as ImageManipulator from 'expo-image-manipulator';

import { useToast } from '@/src/shared/hooks/useToast';

import { eventService } from '../services/eventService';

interface UploadMediaOptions {
  compress?: boolean;
  maxWidth?: number;
  quality?: number;
}

export const useUploadEventMedia = () => {
  const toast = useToast();

  const compressImage = async (uri: string, options?: UploadMediaOptions) => {
    if (!options?.compress) {
      return { uri };
    }

    const manipResult = await ImageManipulator.manipulateAsync(
      uri,
      [{ resize: { width: options.maxWidth || 1080 } }],
      {
        compress: options.quality || 0.8,
        format: ImageManipulator.SaveFormat.JPEG,
      }
    );

    return {
      uri: manipResult.uri,
      width: manipResult.width,
      height: manipResult.height,
    };
  };

  return useMutation({
    mutationFn: async ({
      file,
      options = { compress: true, maxWidth: 1080, quality: 0.8 },
    }: {
      file: any;
      options?: UploadMediaOptions;
    }) => {
      const compressed = await compressImage(file.uri, options);
      return eventService.uploadMedia({
        ...file,
        uri: compressed.uri,
      });
    },

    onSuccess: response => {
      if (response.success) {
        toast.success('Media uploaded');
      } else {
        toast.error(response.errors[0]?.message || 'Please try again');
      }
    },

    onError: (error: Error) => {
      toast.error(error.message || 'Failed to upload media');
    },
  });
};
