import { useCallback, useDeferredValue, useEffect } from 'react';

import { useEventRegistrationStore } from '../store/eventRegistrationStore';
import { CreateEventInput } from '../types/event.types';

export const useEventDraft = () => {
  const {
    eventDraft,
    currentStep,
    updateEventDraft,
    updatePreferences,
    setCurrentStep,
    clearDraft,
    isStepValid,
    canProceedToNext,
    getCompleteDraft,
  } = useEventRegistrationStore();

  // Auto-save draft with React 19's deferred value
  const deferredDraft = useDeferredValue(eventDraft);

  useEffect(() => {
    // Auto-save logic could be implemented here if needed
    // For now, Zustand persistence handles it automatically
    // The deferredDraft will update less frequently during rapid changes
  }, [deferredDraft]);

  const updateField = useCallback(
    (field: keyof CreateEventInput, value: any) => {
      updateEventDraft({ [field]: value });
    },
    [updateEventDraft]
  );

  const goToStep = useCallback(
    (step: 1 | 2 | 3) => {
      setCurrentStep(step);
    },
    [setCurrentStep]
  );

  const goToNextStep = useCallback(() => {
    if (canProceedToNext() && currentStep < 3) {
      setCurrentStep((currentStep + 1) as 1 | 2 | 3);
      return true;
    }
    return false;
  }, [currentStep, canProceedToNext, setCurrentStep]);

  const goToPreviousStep = useCallback(() => {
    if (currentStep > 1) {
      setCurrentStep((currentStep - 1) as 1 | 2 | 3);
      return true;
    }
    return false;
  }, [currentStep, setCurrentStep]);

  return {
    draft: eventDraft,
    currentStep,
    updateField,
    updateDraft: updateEventDraft,
    updatePreferences,
    clearDraft,
    isStepValid,
    canProceedToNext,
    getCompleteDraft,
    goToStep,
    goToNextStep,
    goToPreviousStep,
  };
};
