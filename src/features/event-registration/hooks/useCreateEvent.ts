import { useNavigation } from '@react-navigation/native';
import { useMutation } from '@tanstack/react-query';

import { EventRegistrationStackScreenProps } from '@/src/core/navigation/types';
import { useToast } from '@/src/shared/hooks/useToast';

import { eventService } from '../services/eventService';
import { useEventRegistrationStore } from '../store/eventRegistrationStore';
import { CreateEventInput } from '../types/event.types';

export const useCreateEvent = () => {
  const { clearDraft } = useEventRegistrationStore();
  const toast = useToast();
  const navigation =
    useNavigation<EventRegistrationStackScreenProps<'EventSuccess'>['navigation']>();

  return useMutation({
    mutationFn: (input: CreateEventInput) => eventService.createEvent(input),

    onSuccess: response => {
      if (response.success) {
        clearDraft();
        toast.success('Event created successfully!');
        navigation.reset({
          index: 0,
          routes: [
            { name: 'MainTabs' },
            {
              name: 'EventSuccess',
              params: {
                eventId: response.data.id,
                eventName: response.data.name,
              },
            },
          ],
        });
      } else {
        toast.error(response.errors[0].message);
      }
    },

    onError: (error: Error) => {
      toast.error(error.message || 'Please check your connection and try again');
    },
  });
};
