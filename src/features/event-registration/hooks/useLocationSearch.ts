import { useCallback, useDeferredValue, useState, useTransition } from 'react';

import { useQuery } from '@tanstack/react-query';

import { eventService } from '../services/eventService';

/**
 * Hook for searching locations with React 19's concurrent features
 * Uses useDeferredValue for natural debouncing and useTransition for smooth UI updates
 */
export const useLocationSearch = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isPending, startTransition] = useTransition();
  const deferredQuery = useDeferredValue(searchQuery);

  const query = useQuery({
    queryKey: ['location-search', deferredQuery],
    queryFn: () => eventService.searchLocations(deferredQuery),
    enabled: deferredQuery.length > 2,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const search = useCallback((text: string) => {
    startTransition(() => {
      setSearchQuery(text);
    });
  }, []);

  return {
    search,
    searchQuery,
    locations: query.data?.success ? query.data.data : [],
    isLoading: query.isLoading || isPending,
    error: query.error,
    deferredQuery,
  };
};
