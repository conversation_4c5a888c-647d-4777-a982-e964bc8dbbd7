import { gql } from '@apollo/client';

import { apiClient } from '@/src/core';
import { apolloClient } from '@/src/core/api';
import { GraphQLErrorResponse } from '@/src/core/api/errors';

import { CreateEventInput, Event } from '../types/event.types';

export type ServiceResponse<T> =
  | { success: true; data: T; meta?: any }
  | { success: false; errors: GraphQLErrorResponse<any>[]; networkError?: any };

const CREATE_EVENT_MUTATION = gql`
  mutation CreateEvent($input: CreateEventInput!) {
    createEvent(input: $input) {
      success
      event {
        id
        name
        description
        startDate
        endDate
        location
        locationName
        coordinates {
          latitude
          longitude
        }
        mediaUrl
        coHosts
        preferences {
          notifyRadarUsers
          drinksAvailable
          drinkNotes
          privateListing
          allowGroups
        }
        organizerId
        organizerName
        status
        attendeesCount {
          interested
          going
        }
        tags
        createdAt
        updatedAt
      }
      errors {
        message
        extensions {
          code
          statusCode
        }
      }
    }
  }
`;

class EventService {
  private client = apolloClient;

  async createEvent(input: CreateEventInput): Promise<ServiceResponse<Event>> {
    try {
      const { data } = await this.client.mutate({
        mutation: CREATE_EVENT_MUTATION,
        variables: { input },
      });

      if (data?.createEvent?.success) {
        return {
          success: true,
          data: data.createEvent.event,
        };
      }

      return {
        success: false,
        errors: data?.createEvent?.errors || [
          {
            message: 'Failed to create event',
            extensions: {
              code: 'UNKNOWN_ERROR',
              statusCode: 500,
            },
          },
        ],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  async uploadMedia(file: any): Promise<ServiceResponse<{ url: string }>> {
    try {
      const formData = new FormData();
      formData.append('media', {
        uri: file.uri,
        type: file.type || 'image/jpeg',
        name: file.name || 'event-media.jpg',
      } as any);

      const response = await apiClient.post('/events/media', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        timeout: 30000, // 30 seconds for upload
      });

      if (response.data.success) {
        return {
          success: true,
          data: { url: response.data.url },
        };
      }

      return {
        success: false,
        errors: [
          {
            message: 'Upload failed',
            extensions: {
              code: 'UPLOAD_ERROR',
              statusCode: 500,
            },
          },
        ],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'Upload failed',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.response?.status || 500,
            },
          },
        ],
      };
    }
  }

  async searchLocations(query: string): Promise<ServiceResponse<any[]>> {
    try {
      const response = await apiClient.get('/locations/search', {
        params: { q: query },
      });

      return {
        success: true,
        data: response.data.results,
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: 'Failed to search locations',
            extensions: {
              code: 'LOCATION_SEARCH_ERROR',
              statusCode: error.response?.status || 500,
            },
          },
        ],
      };
    }
  }
}

export const eventService = new EventService();
