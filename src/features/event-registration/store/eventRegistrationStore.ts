import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

import { createZustandMMKVStorage, userStorage } from '@/src/core/storage';

import { CreateEventInput, EventPreferences } from '../types/event.types';
import { eventDetailsSchema } from '../validation/eventDetailsSchema';
import { eventPreferencesSchema } from '../validation/eventPreferencesSchema';

interface EventRegistrationStore {
  // Form data persisted across steps
  eventDraft: Partial<CreateEventInput>;
  currentStep: 1 | 2 | 3;

  // Actions
  updateEventDraft: (data: Partial<CreateEventInput>) => void;
  updatePreferences: (preferences: Partial<EventPreferences>) => void;
  setCurrentStep: (step: 1 | 2 | 3) => void;
  clearDraft: () => void;

  // Computed
  isStepValid: (step: number) => boolean;
  canProceedToNext: () => boolean;
  getCompleteDraft: () => CreateEventInput | null;
}

const defaultPreferences: EventPreferences = {
  notifyRadarUsers: true,
  drinksAvailable: false,
  privateListing: true,
  allowGroups: true,
};

export const useEventRegistrationStore = create<EventRegistrationStore>()(
  persist(
    (set, get) => ({
      eventDraft: {},
      currentStep: 1,

      updateEventDraft: data =>
        set(state => ({
          eventDraft: { ...state.eventDraft, ...data },
        })),

      updatePreferences: preferences =>
        set(state => ({
          eventDraft: {
            ...state.eventDraft,
            preferences: {
              ...defaultPreferences,
              ...state.eventDraft.preferences,
              ...preferences,
            },
          },
        })),

      setCurrentStep: step => set({ currentStep: step }),

      clearDraft: () => set({ eventDraft: {}, currentStep: 1 }),

      isStepValid: step => {
        const draft = get().eventDraft;
        switch (step) {
          case 1:
            const detailsResult = eventDetailsSchema.safeParse(draft);
            return detailsResult.success;
          case 2:
            // Summary is view-only
            return true;
          case 3:
            const preferencesResult = eventPreferencesSchema.safeParse(
              draft.preferences || defaultPreferences
            );
            return preferencesResult.success;
          default:
            return false;
        }
      },

      canProceedToNext: () => {
        const currentStep = get().currentStep;
        return get().isStepValid(currentStep);
      },

      getCompleteDraft: () => {
        const draft = get().eventDraft;

        // Ensure all required fields are present
        if (!draft.name || !draft.description || !draft.startDate || !draft.location) {
          return null;
        }

        return {
          ...draft,
          preferences: {
            ...defaultPreferences,
            ...draft.preferences,
          },
        } as CreateEventInput;
      },
    }),
    {
      name: 'event-registration-storage',
      storage: createJSONStorage(() => createZustandMMKVStorage(userStorage)),
      partialize: state => ({
        eventDraft: state.eventDraft,
        currentStep: state.currentStep,
      }),
    }
  )
);
