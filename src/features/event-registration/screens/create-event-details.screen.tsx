import React, { useCallback, useEffect, useMemo } from 'react';

import { KeyboardAvoidingView, Platform, ScrollView, TouchableOpacity } from 'react-native';

import { useNavigation, useRoute } from '@react-navigation/native';
import {
  ClockClockwise,
  CurrencyCircleDollar,
  Globe,
  MusicNote,
  Users,
} from 'phosphor-react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';

import { EventRegistrationStackScreenProps } from '@/src/core/navigation/types';
import { Box, Text, TextInput, useTheme } from '@/src/core/theme';
import { Button, Card, Chip, DateTimePicker, Input } from '@/src/shared/components';
import { useZodForm } from '@/src/shared/forms/useZodForm';

import { EventDateTimePicker } from '../components/EventDateTimePicker';
import { EventLocationPicker } from '../components/EventLocationPicker';
import { EventMediaUpload } from '../components/EventMediaUpload';
import { EventProgressIndicator } from '../components/EventProgressIndicator';
import { useEventRegistrationContext } from '../context/EventRegistrationContext';
import { useEventDraft } from '../hooks/useEventDraft';
import { eventDetailsSchema } from '../validation/eventDetailsSchema';

export const CreateEventDetailsScreen = () => {
  const navigation =
    useNavigation<EventRegistrationStackScreenProps<'CreateEventDetails'>['navigation']>();
  const route = useRoute<EventRegistrationStackScreenProps<'CreateEventDetails'>['route']>();
  const insets = useSafeAreaInsets();
  const theme = useTheme();
  const { selectedLocation, selectedTemplate } = useEventRegistrationContext();

  const { draft, updateDraft, currentStep, goToNextStep } = useEventDraft();

  const { control, handleSubmit, formState, watch, setValue } = useZodForm(eventDetailsSchema, {
    mode: 'onBlur',
    reValidateMode: 'onChange',
    defaultValues: {
      name: draft.name || '',
      description: draft.description || '',
      startDate: draft.startDate || new Date(),
      endDate: draft.endDate,
      location: draft.location || '',
      locationName: draft.locationName || '',
      capacity: selectedTemplate?.defaults?.capacity?.toString() || '',
      musicGenres: [],
      isPaid: false,
      ticketPrice: '',
      splitCosts: false,
      ...draft,
    },
  });

  // Real-time validation for button enabling
  const watchedValues = watch();
  const isInputValid = useMemo(() => {
    return eventDetailsSchema.safeParse(watchedValues).success;
  }, [watchedValues]);

  const onNext = handleSubmit(data => {
    updateDraft(data);
    goToNextStep();
    navigation.navigate('EventSummary');
  });

  const handleAddEndTime = () => {
    const startDate = watchedValues.startDate || new Date();
    const endDate = new Date(startDate);
    endDate.setHours(endDate.getHours() + 3); // Default 3 hours later
    updateDraft({ endDate });
  };

  const handleUpdateLocation = useCallback(() => {
    setValue('location', selectedLocation?.address || '');
    setValue('locationName', selectedLocation?.name || '');
    setValue('coordinates.latitude', selectedLocation?.latitude || 0);
    setValue('coordinates.longitude', selectedLocation?.longitude || 0);
  }, [selectedLocation, setValue]);

  useEffect(() => {
    handleUpdateLocation();
  }, [handleUpdateLocation]);

  return (
    <SafeAreaView style={{ flex: 1 }} edges={['left', 'right', 'bottom']}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        <Box flex={1} backgroundColor="background">
          <EventProgressIndicator currentStep={currentStep} />

          <ScrollView showsVerticalScrollIndicator={false}>
            <Box px="md_16">
              <Card marginBottom="sm_12" padding="md_16">
                <Box
                  flexDirection="row"
                  justifyContent="space-between"
                  alignItems="center"
                  marginBottom="sm_12">
                  <Text variant="l_10SemiBold_tag" color="textSecondary">
                    DETAILS
                  </Text>
                  <Text variant="l_10SemiBold_tag" color="textSecondary">
                    STEP {currentStep}/3
                  </Text>
                </Box>

                <EventMediaUpload
                  onUpload={url => updateDraft({ mediaUrl: url })}
                  currentMedia={draft.mediaUrl}
                />

                <Box marginTop="md_16">
                  <Input
                    control={control}
                    name="name"
                    label="Give it a name"
                    placeholder="Birthday party, Team dinner..."
                    error={formState.errors.name?.message}
                  />
                </Box>

                <Box marginTop="md_16">
                  <Input
                    control={control}
                    name="description"
                    label="Description*"
                    placeholder="Tell people what to expect..."
                    maxLength={500}
                    error={formState.errors.description?.message}
                    multiline
                    numberOfLines={4}
                  />
                </Box>

                <Box marginTop="md_16">
                  <EventDateTimePicker
                    control={control}
                    name="startDate"
                    label="Starts at *"
                    showTimezone
                    error={formState.errors.startDate?.message}
                  />

                  <Box flexDirection="row" gap="sm_12" marginTop="sm_12">
                    {!draft.endDate && (
                      <Chip
                        label="Add end time"
                        leftIcon={<ClockClockwise size={20} color={theme.colors.textSecondary} />}
                        onPress={handleAddEndTime}
                      />
                    )}

                    <Chip
                      label="UTC +3"
                      leftIcon={<Globe size={20} color={theme.colors.iconDefaultInput} />}
                    />
                  </Box>

                  {draft.endDate && (
                    <Box marginTop="md_16">
                      <DateTimePicker
                        control={control}
                        name="endDate"
                        label="Ends at"
                        minDate={watchedValues.startDate}
                        error={formState.errors.endDate?.message}
                      />
                    </Box>
                  )}
                </Box>

                <Box marginTop="md_16">
                  <EventLocationPicker
                    control={control}
                    name="location"
                    label="Location address*"
                    error={formState.errors.location?.message}
                    onSelect={location => {
                      updateDraft({
                        location: location.address,
                        locationName: location.name,
                        coordinates: location.coordinates,
                      });
                    }}
                  />
                </Box>

                <Box marginTop="md_16">
                  <Input
                    control={control}
                    name="coHosts"
                    label="Add other hosts"
                    placeholder="Search for users..."
                  />
                </Box>

                {/* Template-specific fields */}
                {selectedTemplate && (
                  <>
                    {/* Capacity field for events with limited space */}
                    {(selectedTemplate.id === 'house-party' ||
                      selectedTemplate.id === 'bar-club' ||
                      selectedTemplate.id === 'flash-party' ||
                      selectedTemplate.id === 'group-meetup') && (
                      <Box marginTop="md_16">
                        <Text variant="b_14Medium_button" color="textSecondary" marginBottom="xs_8">
                          <Users size={16} color={theme.colors.textSecondary} /> Capacidade
                        </Text>
                        <Box flexDirection="row" gap="xs_8">
                          {[15, 25, 50, 100, 200].map(capacity => (
                            <TouchableOpacity
                              key={capacity}
                              onPress={() => setValue('capacity', capacity.toString())}>
                              <Box
                                paddingHorizontal="md_16"
                                paddingVertical="xs_8"
                                borderRadius="lg_16"
                                backgroundColor={
                                  watchedValues.capacity === capacity.toString()
                                    ? 'primary'
                                    : 'surfaceBackground'
                                }
                                borderWidth={1}
                                borderColor={
                                  watchedValues.capacity === capacity.toString()
                                    ? 'primary'
                                    : 'border'
                                }>
                                <Text
                                  variant="b_14Medium_button"
                                  color={
                                    watchedValues.capacity === capacity.toString()
                                      ? 'white'
                                      : 'text'
                                  }>
                                  {capacity}
                                </Text>
                              </Box>
                            </TouchableOpacity>
                          ))}
                        </Box>
                      </Box>
                    )}

                    {/* Music preferences for party events */}
                    {(selectedTemplate.id === 'house-party' ||
                      selectedTemplate.id === 'bar-club' ||
                      selectedTemplate.id === 'flash-party') && (
                      <Box marginTop="md_16">
                        <Text variant="b_14Medium_button" color="textSecondary" marginBottom="xs_8">
                          <MusicNote size={16} color={theme.colors.textSecondary} /> Estilo musical
                        </Text>
                        <Box flexDirection="row" flexWrap="wrap" gap="xs_8">
                          {selectedTemplate.defaults?.suggestedMusic?.map((genre: string) => (
                            <TouchableOpacity
                              key={genre}
                              onPress={() => {
                                const currentGenres = watchedValues.musicGenres || [];
                                const newGenres = currentGenres.includes(genre)
                                  ? currentGenres.filter((g: string) => g !== genre)
                                  : [...currentGenres, genre];
                                setValue('musicGenres', newGenres);
                              }}>
                              <Box
                                paddingHorizontal="md_16"
                                paddingVertical="xs_8"
                                borderRadius="lg_16"
                                backgroundColor={
                                  watchedValues.musicGenres?.includes(genre)
                                    ? 'secondary'
                                    : 'surfaceBackground'
                                }
                                borderWidth={1}
                                borderColor={
                                  watchedValues.musicGenres?.includes(genre)
                                    ? 'secondary'
                                    : 'border'
                                }>
                                <Text
                                  variant="b_14Medium_button"
                                  color={
                                    watchedValues.musicGenres?.includes(genre) ? 'white' : 'text'
                                  }>
                                  {genre}
                                </Text>
                              </Box>
                            </TouchableOpacity>
                          ))}
                        </Box>
                      </Box>
                    )}

                    {/* Revenue options for capable events */}
                    {selectedTemplate.revenueCapable && selectedTemplate.id === 'bar-club' && (
                      <Box marginTop="md_16">
                        <Text variant="b_14Medium_button" color="textSecondary" marginBottom="xs_8">
                          <CurrencyCircleDollar size={16} color={theme.colors.success} /> Ingressos
                        </Text>
                        <Box gap="sm_12">
                          <Box
                            backgroundColor="surfaceBackground"
                            borderRadius="md_12"
                            padding="md_16"
                            borderWidth={1}
                            borderColor="border">
                            <Box
                              flexDirection="row"
                              justifyContent="space-between"
                              alignItems="center">
                              <Box>
                                <Text variant="b_14Medium_button">Evento pago</Text>
                                <Text variant="l_12Regular_helperText" color="textSecondary">
                                  Cobra entrada dos convidados
                                </Text>
                              </Box>
                              <TouchableOpacity
                                onPress={() => setValue('isPaid', !watchedValues.isPaid)}>
                                <Box
                                  width={48}
                                  height={28}
                                  borderRadius="lg_16"
                                  backgroundColor={watchedValues.isPaid ? 'success' : 'border'}
                                  justifyContent="center"
                                  padding="xxs_4">
                                  <Box
                                    width={20}
                                    height={20}
                                    borderRadius="circle_9999"
                                    backgroundColor="white"
                                    style={{
                                      alignSelf: watchedValues.isPaid ? 'flex-end' : 'flex-start',
                                    }}
                                  />
                                </Box>
                              </TouchableOpacity>
                            </Box>
                          </Box>

                          {watchedValues.isPaid && (
                            <Box gap="xs_8">
                              <Input
                                control={control}
                                name="ticketPrice"
                                label="Valor do ingresso"
                                placeholder="R$ 0,00"
                                keyboardType="numeric"
                                leftIcon={
                                  <CurrencyCircleDollar
                                    size={20}
                                    color={theme.colors.textSecondary}
                                  />
                                }
                              />
                              <Text variant="l_12Regular_helperText" color="textSecondary">
                                Você pode configurar lotes e VIP depois
                              </Text>
                            </Box>
                          )}
                        </Box>
                      </Box>
                    )}

                    {/* Group meetup specific */}
                    {selectedTemplate.id === 'group-meetup' && (
                      <Box marginTop="md_16">
                        <Text variant="b_14Medium_button" color="textSecondary" marginBottom="xs_8">
                          Configurações do grupo
                        </Text>
                        <Box gap="sm_12">
                          <Box
                            backgroundColor="surfaceBackground"
                            borderRadius="md_12"
                            padding="md_16"
                            borderWidth={1}
                            borderColor="border">
                            <Box
                              flexDirection="row"
                              justifyContent="space-between"
                              alignItems="center">
                              <Box>
                                <Text variant="b_14Medium_button">Dividir custos</Text>
                                <Text variant="l_12Regular_helperText" color="textSecondary">
                                  Rachar a conta entre todos
                                </Text>
                              </Box>
                              <TouchableOpacity
                                onPress={() => setValue('splitCosts', !watchedValues.splitCosts)}>
                                <Box
                                  width={48}
                                  height={28}
                                  borderRadius="lg_16"
                                  backgroundColor={watchedValues.splitCosts ? 'primary' : 'border'}
                                  justifyContent="center"
                                  padding="xxs_4">
                                  <Box
                                    width={20}
                                    height={20}
                                    borderRadius="circle_9999"
                                    backgroundColor="white"
                                    style={{
                                      alignSelf: watchedValues.splitCosts
                                        ? 'flex-end'
                                        : 'flex-start',
                                    }}
                                  />
                                </Box>
                              </TouchableOpacity>
                            </Box>
                          </Box>
                        </Box>
                      </Box>
                    )}
                  </>
                )}
              </Card>
            </Box>
          </ScrollView>

          {/* Bottom Actions */}
          <Box
            backgroundColor="surface"
            borderTopWidth={1}
            borderTopColor="border"
            paddingHorizontal="md_16"
            paddingVertical="md_16"
            flexDirection="row"
            style={{ paddingBottom: insets.bottom + theme.spacing.md_16 }}
            gap="sm_12">
            <Button
              variant="secondary"
              title="Cancel"
              onPress={() => navigation.goBack()}
              flex={1}
            />

            <Button
              variant="primary"
              title="Next"
              onPress={() => onNext()}
              enabled={isInputValid}
              flex={1}
            />
          </Box>
        </Box>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};
