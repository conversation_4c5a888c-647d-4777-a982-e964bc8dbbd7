import React from 'react';

import { useNavigation, useRoute } from '@react-navigation/native';
import { CheckCircle, Copy, Share, WhatsappLogo } from 'phosphor-react-native';

import { Box, Text, useTheme } from '@/src/core/theme';
import { Button } from '@/src/shared/components';

interface EventSuccessScreenParams {
  eventId: string;
  eventName: string;
}

export const EventSuccessScreen = () => {
  const theme = useTheme();
  const route = useRoute();
  const navigation = useNavigation<any>();
  const params = route.params as EventSuccessScreenParams;

  const handleViewEvent = () => {
    navigation.reset({
      index: 1,
      routes: [{ name: 'MainTabs' }, { name: 'EventDetails', params: { eventId: params.eventId } }],
    });
  };

  const handleCreateAnother = () => {
    navigation.reset({
      index: 1,
      routes: [{ name: 'MainTabs' }, { name: 'EventDetails' }],
    });
  };

  const handleGoHome = () => {
    navigation.reset({
      index: 0,
      routes: [{ name: 'MainTabs' }],
    });
  };

  return (
    <Box
      flex={1}
      backgroundColor="background"
      justifyContent="center"
      alignItems="center"
      padding="xl_32">
      {/* Success Animation */}
      <Box
        width={200}
        height={200}
        marginBottom="xl_32"
        backgroundColor="primaryLight"
        borderRadius="circle_9999"
        justifyContent="center"
        alignItems="center">
        <CheckCircle size={100} color={theme.colors.primary} />
      </Box>

      {/* Success Message */}
      <Text variant="h_32SemiBold_Page" color="text" textAlign="center" marginBottom="sm_12">
        Event Created!
      </Text>

      <Text
        variant="b_16Regular_input"
        color="textSecondary"
        textAlign="center"
        marginBottom="xl_32">
        Your event {params.eventName} has been created successfully and is now live.
      </Text>

      {/* Action Buttons */}
      <Box width="100%" gap="sm_12">
        <Button variant="primary" onPress={handleViewEvent} title="View Event" />

        <Button variant="secondary" onPress={handleCreateAnother} title="Create Another Event" />

        <Button variant="ghost" onPress={handleGoHome} title="Go to Home" />
      </Box>

      {/* Share Section */}
      <Box
        marginTop="xxl_40"
        paddingTop="lg_24"
        borderTopWidth={1}
        borderTopColor="border"
        width="100%"
        alignItems="center">
        <Text variant="b_14Medium_button" color="textSecondary" marginBottom="sm_12">
          Share your event
        </Text>
        <Box flexDirection="row" gap="md_16">
          <Share size={20} color={theme.colors.textSecondary} />
          <Copy size={20} color={theme.colors.textSecondary} />
          <WhatsappLogo size={20} color={theme.colors.textSecondary} />
        </Box>
      </Box>
    </Box>
  );
};
