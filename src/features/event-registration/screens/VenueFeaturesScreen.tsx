import { useState } from 'react';

import { ScrollView, TouchableOpacity } from 'react-native';

import { useNavigation } from '@react-navigation/native';
import {
  Baby,
  Car,
  Cigarette,
  Couch,
  CreditCard,
  ForkKnife,
  MusicNote,
  PawPrint,
  Shield,
  Toilet,
  Wheelchair,
  WifiHigh,
} from 'phosphor-react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { VenueRegistrationStackScreenProps } from '@/src/core/navigation/types';
import { Box, Text, useTheme } from '@/src/core/theme';
import { Button, Card } from '@/src/shared/components';

type Feature = {
  id: string;
  label: string;
  icon: React.ReactNode;
  category: 'amenities' | 'services' | 'policies';
};

const VenueFeaturesScreen = () => {
  const theme = useTheme();
  const navigation =
    useNavigation<VenueRegistrationStackScreenProps<'VenueFeatures'>['navigation']>();

  const [selectedFeatures, setSelectedFeatures] = useState<string[]>([]);

  const features: Feature[] = [
    // Amenities
    {
      id: 'wifi',
      label: 'Wi-Fi grátis',
      icon: <WifiHigh size={24} color={theme.colors.primary} />,
      category: 'amenities',
    },
    {
      id: 'parking',
      label: 'Estacionamento',
      icon: <Car size={24} color={theme.colors.primary} />,
      category: 'amenities',
    },
    {
      id: 'accessible',
      label: 'Acessível',
      icon: <Wheelchair size={24} color={theme.colors.primary} />,
      category: 'amenities',
    },
    {
      id: 'liveMusic',
      label: 'Música ao vivo',
      icon: <MusicNote size={24} color={theme.colors.primary} />,
      category: 'amenities',
    },
    {
      id: 'food',
      label: 'Serve comida',
      icon: <ForkKnife size={24} color={theme.colors.primary} />,
      category: 'amenities',
    },
    {
      id: 'vipArea',
      label: 'Área VIP',
      icon: <Couch size={24} color={theme.colors.primary} />,
      category: 'amenities',
    },

    // Services
    {
      id: 'cards',
      label: 'Aceita cartões',
      icon: <CreditCard size={24} color={theme.colors.success} />,
      category: 'services',
    },
    {
      id: 'reservations',
      label: 'Reservas',
      icon: <Shield size={24} color={theme.colors.success} />,
      category: 'services',
    },
    {
      id: 'delivery',
      label: 'Delivery',
      icon: <Car size={24} color={theme.colors.success} />,
      category: 'services',
    },

    // Policies
    {
      id: 'kidsFriendly',
      label: 'Kids friendly',
      icon: <Baby size={24} color={theme.colors.secondary} />,
      category: 'policies',
    },
    {
      id: 'petFriendly',
      label: 'Pet friendly',
      icon: <PawPrint size={24} color={theme.colors.secondary} />,
      category: 'policies',
    },
    {
      id: 'smokingArea',
      label: 'Área fumantes',
      icon: <Cigarette size={24} color={theme.colors.secondary} />,
      category: 'policies',
    },
  ];

  const toggleFeature = (featureId: string) => {
    setSelectedFeatures(prev =>
      prev.includes(featureId) ? prev.filter(id => id !== featureId) : [...prev, featureId]
    );
  };

  const handleFinish = () => {
    // TODO: Save all venue data and create venue
    navigation.navigate('VenueSuccess', {
      venueId: 'venue-' + Date.now(),
    });
  };

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'amenities':
        return 'Comodidades';
      case 'services':
        return 'Serviços';
      case 'policies':
        return 'Políticas';
      default:
        return '';
    }
  };

  const featuresByCategory = features.reduce(
    (acc, feature) => {
      if (!acc[feature.category]) {
        acc[feature.category] = [];
      }
      acc[feature.category].push(feature);
      return acc;
    },
    {} as Record<string, Feature[]>
  );

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: theme.colors.background }}>
      <ScrollView>
        <Box flex={1} padding="lg_24">
          {/* Header */}
          <Box marginBottom="lg_24">
            <Text variant="h_24SemiBold_section" marginBottom="xs_8">
              ✨ Diferenciais do local
            </Text>
            <Text variant="b_16Regular_input" color="textSecondary">
              O que vocês oferecem?
            </Text>
          </Box>

          {Object.entries(featuresByCategory).map(([category, categoryFeatures]) => (
            <Card key={category} padding="md_16" marginBottom="md_16">
              <Text variant="b_14Medium_button" color="textSecondary" marginBottom="md_16">
                {getCategoryLabel(category)}
              </Text>

              <Box flexDirection="row" flexWrap="wrap" gap="sm_12">
                {categoryFeatures.map(feature => (
                  <TouchableOpacity
                    key={feature.id}
                    onPress={() => toggleFeature(feature.id)}
                    style={{ width: '47%' }}>
                    <Box
                      backgroundColor={
                        selectedFeatures.includes(feature.id) ? 'primaryLight' : 'surfaceBackground'
                      }
                      borderRadius="md_12"
                      padding="md_16"
                      alignItems="center"
                      borderWidth={1}
                      borderColor={selectedFeatures.includes(feature.id) ? 'primary' : 'border'}>
                      {feature.icon}
                      <Text variant="b_14Medium_button" marginTop="xs_8" textAlign="center">
                        {feature.label}
                      </Text>
                    </Box>
                  </TouchableOpacity>
                ))}
              </Box>
            </Card>
          ))}

          {/* Tip */}
          <Box
            backgroundColor="successLight"
            borderRadius="md_12"
            padding="md_16"
            marginBottom="lg_24">
            <Text variant="l_12Medium_message" color="success">
              💡 Quanto mais informações, melhor para os clientes encontrarem vocês!
            </Text>
          </Box>

          {/* Progress Indicator */}
          <Box flexDirection="row" justifyContent="center" marginBottom="lg_24" gap="xs_8">
            <Box width={8} height={8} borderRadius="circle_9999" backgroundColor="border" />
            <Box width={8} height={8} borderRadius="circle_9999" backgroundColor="border" />
            <Box width={8} height={8} borderRadius="circle_9999" backgroundColor="primary" />
          </Box>

          {/* Action Buttons */}
          <Box flexDirection="row" gap="sm_12">
            <Box flex={1}>
              <Button variant="secondary" onPress={() => navigation.goBack()} title="Voltar" />
            </Box>
            <Box flex={1}>
              <Button variant="primary" onPress={handleFinish} title="Finalizar" />
            </Box>
          </Box>
        </Box>
      </ScrollView>
    </SafeAreaView>
  );
};

export default VenueFeaturesScreen;
