import { ScrollView, TouchableOpacity } from 'react-native';

import { useNavigation } from '@react-navigation/native';
import { Calendar, Check, Clock, MapPin, Phone, Star, WifiHigh } from 'phosphor-react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { VenueRegistrationStackScreenProps } from '@/src/core/navigation/types';
import { Box, Text, useTheme } from '@/src/core/theme';
import { But<PERSON>, Card } from '@/src/shared/components';

const VenueSummaryScreen = () => {
  const theme = useTheme();
  const navigation =
    useNavigation<VenueRegistrationStackScreenProps<'VenueSummary'>['navigation']>();

  // TODO: Get venue data from context
  const venueData = {
    name: 'Bar do João',
    type: 'Bar',
    phone: '(11) 98765-4321',
    address: 'Rua Augusta, 1234 - Consolação, São Paulo - SP',
    openingTimes: {
      monday: '17:00 - 23:00',
      tuesday: '17:00 - 23:00',
      wednesday: '17:00 - 00:00',
      thursday: '17:00 - 00:00',
      friday: '17:00 - 02:00',
      saturday: '17:00 - 02:00',
      sunday: 'Fechado',
    },
    features: ['Wi-Fi grátis', 'Aceita cartões', 'Música ao vivo', 'Pet friendly'],
    description:
      'Bar tradicional com mais de 20 anos, especializado em petiscos e cervejas artesanais.',
  };

  const handleEdit = (section: string) => {
    // Navigate back to specific section
    switch (section) {
      case 'basic':
        navigation.navigate('VenueBasicDetails');
        break;
      case 'hours':
        navigation.navigate('VenueOpeningTimes');
        break;
      case 'features':
        navigation.navigate('VenueFeatures');
        break;
    }
  };

  const handleSubmit = () => {
    // TODO: Submit venue registration
    navigation.navigate('VenueSuccess', {
      venueId: 'venue-' + Date.now(),
    });
  };

  const getOpenDaysCount = () => {
    return Object.values(venueData.openingTimes).filter(time => time !== 'Fechado').length;
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: theme.colors.background }}>
      <ScrollView>
        <Box flex={1} padding="lg_24">
          {/* Header */}
          <Box marginBottom="lg_24">
            <Text variant="h_24SemiBold_section" marginBottom="xs_8">
              📋 Resumo do cadastro
            </Text>
            <Text variant="b_16Regular_input" color="textSecondary">
              Revise as informações antes de finalizar
            </Text>
          </Box>

          {/* Success Banner */}
          <Box
            backgroundColor="successLight"
            borderRadius="md_12"
            padding="md_16"
            marginBottom="lg_24"
            flexDirection="row"
            alignItems="center">
            <Box
              width={40}
              height={40}
              borderRadius="circle_9999"
              backgroundColor="success"
              alignItems="center"
              justifyContent="center"
              marginRight="sm_12">
              <Check size={24} color={theme.colors.white} weight="bold" />
            </Box>
            <Box flex={1}>
              <Text variant="b_16SemiBold_button" color="success">
                Tudo pronto!
              </Text>
              <Text variant="b_14Regular_content" color="successDark">
                Seu estabelecimento será listado após revisão
              </Text>
            </Box>
          </Box>

          {/* Basic Info Summary */}
          <Card padding="md_16" marginBottom="md_16">
            <Box flexDirection="row" justifyContent="space-between" alignItems="center">
              <Text variant="b_14Medium_button" color="textSecondary" marginBottom="sm_12">
                Informações básicas
              </Text>
              <TouchableOpacity onPress={() => handleEdit('basic')}>
                <Text variant="l_12Medium_message" color="primary">
                  Editar
                </Text>
              </TouchableOpacity>
            </Box>

            <Box gap="xs_8">
              <Box>
                <Text variant="h_18SemiBold_cardTitle">{venueData.name}</Text>
                <Text variant="b_14Regular_content" color="textSecondary">
                  {venueData.type}
                </Text>
              </Box>

              <Box flexDirection="row" alignItems="center" gap="xxs_4">
                <Phone size={16} color={theme.colors.textSecondary} />
                <Text variant="b_14Regular_content" color="textSecondary">
                  {venueData.phone}
                </Text>
              </Box>

              <Box flexDirection="row" alignItems="flex-start" gap="xxs_4">
                <MapPin size={16} color={theme.colors.textSecondary} style={{ marginTop: 2 }} />
                <Text variant="b_14Regular_content" color="textSecondary" flex={1}>
                  {venueData.address}
                </Text>
              </Box>

              {venueData.description && (
                <Box marginTop="xs_8">
                  <Text variant="b_14Regular_content" color="text" numberOfLines={2}>
                    {venueData.description}
                  </Text>
                </Box>
              )}
            </Box>
          </Card>

          {/* Opening Hours Summary */}
          <Card padding="md_16" marginBottom="md_16">
            <Box flexDirection="row" justifyContent="space-between" alignItems="center">
              <Text variant="b_14Medium_button" color="textSecondary" marginBottom="sm_12">
                Horários de funcionamento
              </Text>
              <TouchableOpacity onPress={() => handleEdit('hours')}>
                <Text variant="l_12Medium_message" color="primary">
                  Editar
                </Text>
              </TouchableOpacity>
            </Box>

            <Box
              backgroundColor="primaryLight"
              borderRadius="sm_8"
              padding="sm_12"
              marginBottom="sm_12">
              <Box flexDirection="row" alignItems="center" gap="xs_8">
                <Clock size={20} color={theme.colors.primary} />
                <Text variant="b_14Medium_button" color="primary">
                  Aberto {getOpenDaysCount()} dias por semana
                </Text>
              </Box>
            </Box>

            <Box gap="xxs_4">
              {Object.entries(venueData.openingTimes).map(([day, hours]) => (
                <Box key={day} flexDirection="row" justifyContent="space-between">
                  <Text
                    variant="b_14Regular_content"
                    color="textSecondary"
                    textTransform="capitalize">
                    {day === 'monday' && 'Segunda'}
                    {day === 'tuesday' && 'Terça'}
                    {day === 'wednesday' && 'Quarta'}
                    {day === 'thursday' && 'Quinta'}
                    {day === 'friday' && 'Sexta'}
                    {day === 'saturday' && 'Sábado'}
                    {day === 'sunday' && 'Domingo'}
                  </Text>
                  <Text
                    variant="b_14Medium_button"
                    color={hours === 'Fechado' ? 'textTertiary' : 'text'}>
                    {hours}
                  </Text>
                </Box>
              ))}
            </Box>
          </Card>

          {/* Features Summary */}
          <Card padding="md_16" marginBottom="lg_24">
            <Box flexDirection="row" justifyContent="space-between" alignItems="center">
              <Text variant="b_14Medium_button" color="textSecondary" marginBottom="sm_12">
                Diferenciais ({venueData.features.length})
              </Text>
              <TouchableOpacity onPress={() => handleEdit('features')}>
                <Text variant="l_12Medium_message" color="primary">
                  Editar
                </Text>
              </TouchableOpacity>
            </Box>

            <Box flexDirection="row" flexWrap="wrap" gap="xs_8">
              {venueData.features.map(feature => (
                <Box
                  key={feature}
                  backgroundColor="surfaceBackground"
                  borderRadius="lg_16"
                  paddingHorizontal="sm_12"
                  paddingVertical="xxs_4"
                  flexDirection="row"
                  alignItems="center"
                  gap="xxs_4">
                  {feature === 'Wi-Fi grátis' && (
                    <WifiHigh size={14} color={theme.colors.primary} />
                  )}
                  {feature === 'Música ao vivo' && <Star size={14} color={theme.colors.primary} />}
                  <Text variant="l_12Medium_message" color="text">
                    {feature}
                  </Text>
                </Box>
              ))}
            </Box>
          </Card>

          {/* Terms Notice */}
          <Box
            backgroundColor="surfaceBackground"
            borderRadius="md_12"
            padding="md_16"
            marginBottom="lg_24">
            <Text variant="l_12Regular_helperText" color="textSecondary" textAlign="center">
              Ao continuar, você concorda com nossos <Text color="primary">Termos de Uso</Text> e{' '}
              <Text color="primary">Política de Privacidade</Text>
            </Text>
          </Box>

          {/* Action Buttons */}
          <Box flexDirection="row" gap="sm_12">
            <Box flex={1}>
              <Button
                variant="secondary"
                onPress={() => navigation.navigate('VenueBasicDetails')}
                title="Revisar"
              />
            </Box>
            <Box flex={1}>
              <Button variant="primary" onPress={handleSubmit} title="Cadastrar Local" />
            </Box>
          </Box>
        </Box>
      </ScrollView>
    </SafeAreaView>
  );
};

export default VenueSummaryScreen;
