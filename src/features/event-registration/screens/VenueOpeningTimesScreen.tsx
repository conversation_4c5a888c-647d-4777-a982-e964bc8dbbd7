import { useState } from 'react';

import { ScrollView, TouchableOpacity } from 'react-native';

import { useNavigation } from '@react-navigation/native';
import { Clock, Coffee, Moon, Sun } from 'phosphor-react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { VenueRegistrationStackScreenProps } from '@/src/core/navigation/types';
import { Box, Text, useTheme } from '@/src/core/theme';
import { Button, Card } from '@/src/shared/components';

type DaySchedule = {
  open: boolean;
  openTime: string;
  closeTime: string;
};

type Schedule = {
  [key: string]: DaySchedule;
};

const VenueOpeningTimesScreen = () => {
  const theme = useTheme();
  const navigation =
    useNavigation<VenueRegistrationStackScreenProps<'VenueOpeningTimes'>['navigation']>();

  const daysOfWeek = [
    { key: 'monday', label: 'Segunda', short: 'Seg' },
    { key: 'tuesday', label: 'Terça', short: 'Ter' },
    { key: 'wednesday', label: 'Quarta', short: 'Qua' },
    { key: 'thursday', label: 'Quinta', short: 'Qui' },
    { key: 'friday', label: 'Sexta', short: 'Sex' },
    { key: 'saturday', label: 'Sábado', short: 'Sáb' },
    { key: 'sunday', label: 'Domingo', short: 'Dom' },
  ];

  const [schedule, setSchedule] = useState<Schedule>({
    monday: { open: false, openTime: '18:00', closeTime: '00:00' },
    tuesday: { open: false, openTime: '18:00', closeTime: '00:00' },
    wednesday: { open: false, openTime: '18:00', closeTime: '00:00' },
    thursday: { open: true, openTime: '18:00', closeTime: '02:00' },
    friday: { open: true, openTime: '18:00', closeTime: '04:00' },
    saturday: { open: true, openTime: '18:00', closeTime: '04:00' },
    sunday: { open: false, openTime: '18:00', closeTime: '00:00' },
  });

  const commonSchedules = [
    {
      label: 'Happy Hour',
      icon: <Sun size={20} color={theme.colors.warning} />,
      schedule: { openTime: '17:00', closeTime: '20:00' },
    },
    {
      label: 'Noturno',
      icon: <Moon size={20} color={theme.colors.primary} />,
      schedule: { openTime: '22:00', closeTime: '04:00' },
    },
    {
      label: 'Almoço',
      icon: <Coffee size={20} color={theme.colors.secondary} />,
      schedule: { openTime: '11:00', closeTime: '15:00' },
    },
  ];

  const toggleDay = (day: string) => {
    setSchedule(prev => ({
      ...prev,
      [day]: { ...prev[day], open: !prev[day].open },
    }));
  };

  const applyCommonSchedule = (commonSchedule: { openTime: string; closeTime: string }) => {
    const updatedSchedule: Schedule = {};
    Object.keys(schedule).forEach(day => {
      updatedSchedule[day] = {
        ...schedule[day],
        ...commonSchedule,
      };
    });
    setSchedule(updatedSchedule);
  };

  const isValid = () => {
    return Object.values(schedule).some(day => day.open);
  };

  const handleNext = () => {
    // TODO: Save schedule to context
    navigation.navigate('VenueFeatures');
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: theme.colors.background }}>
      <ScrollView>
        <Box flex={1} padding="lg_24">
          {/* Header */}
          <Box marginBottom="lg_24">
            <Text variant="h_24SemiBold_section" marginBottom="xs_8">
              🕐 Horários de funcionamento
            </Text>
            <Text variant="b_16Regular_input" color="textSecondary">
              Quando vocês estão abertos?
            </Text>
          </Box>

          {/* Quick Templates */}
          <Box marginBottom="lg_24">
            <Text variant="b_14Medium_button" color="textSecondary" marginBottom="sm_12">
              Modelos rápidos
            </Text>
            <Box flexDirection="row" gap="sm_12">
              {commonSchedules.map(common => (
                <TouchableOpacity
                  key={common.label}
                  onPress={() => applyCommonSchedule(common.schedule)}
                  style={{ flex: 1 }}>
                  <Box
                    backgroundColor="surfaceBackground"
                    borderRadius="md_12"
                    padding="md_16"
                    alignItems="center"
                    borderWidth={1}
                    borderColor="border">
                    {common.icon}
                    <Text variant="l_12Medium_message" marginTop="xs_8">
                      {common.label}
                    </Text>
                    <Text variant="l_10SemiBold_tag" color="textSecondary" marginTop="xxs_4">
                      {common.schedule.openTime} - {common.schedule.closeTime}
                    </Text>
                  </Box>
                </TouchableOpacity>
              ))}
            </Box>
          </Box>

          <Card padding="md_16">
            {/* Days of Week */}
            {daysOfWeek.map(day => (
              <Box key={day.key} marginBottom="md_16">
                <TouchableOpacity onPress={() => toggleDay(day.key)}>
                  <Box
                    flexDirection="row"
                    justifyContent="space-between"
                    alignItems="center"
                    backgroundColor={schedule[day.key].open ? 'primaryLight' : 'surfaceBackground'}
                    borderRadius="md_12"
                    padding="md_16"
                    borderWidth={1}
                    borderColor={schedule[day.key].open ? 'primary' : 'border'}>
                    <Box flexDirection="row" alignItems="center" gap="sm_12">
                      <Box
                        width={24}
                        height={24}
                        borderRadius="circle_9999"
                        backgroundColor={schedule[day.key].open ? 'primary' : 'border'}
                        alignItems="center"
                        justifyContent="center">
                        {schedule[day.key].open && (
                          <Text variant="l_12Medium_message" color="white">
                            ✓
                          </Text>
                        )}
                      </Box>
                      <Text variant="b_16SemiBold_button">{day.label}</Text>
                    </Box>

                    {schedule[day.key].open && (
                      <Box flexDirection="row" alignItems="center" gap="xs_8">
                        <Clock size={16} color={theme.colors.textSecondary} />
                        <Text variant="b_14Medium_button" color="textSecondary">
                          {schedule[day.key].openTime} - {schedule[day.key].closeTime}
                        </Text>
                      </Box>
                    )}
                  </Box>
                </TouchableOpacity>

                {/* Time Selection (shown when day is selected) */}
                {schedule[day.key].open && (
                  <Box flexDirection="row" gap="sm_12" marginTop="sm_12" paddingLeft="xxxl_48">
                    <TouchableOpacity style={{ flex: 1 }}>
                      <Box
                        backgroundColor="inputBackground"
                        borderRadius="md_12"
                        padding="sm_12"
                        alignItems="center"
                        borderWidth={1}
                        borderColor="border">
                        <Text variant="l_10SemiBold_tag" color="textSecondary">
                          Abre
                        </Text>
                        <Text variant="b_16SemiBold_button">{schedule[day.key].openTime}</Text>
                      </Box>
                    </TouchableOpacity>

                    <TouchableOpacity style={{ flex: 1 }}>
                      <Box
                        backgroundColor="inputBackground"
                        borderRadius="md_12"
                        padding="sm_12"
                        alignItems="center"
                        borderWidth={1}
                        borderColor="border">
                        <Text variant="l_10SemiBold_tag" color="textSecondary">
                          Fecha
                        </Text>
                        <Text variant="b_16SemiBold_button">{schedule[day.key].closeTime}</Text>
                      </Box>
                    </TouchableOpacity>
                  </Box>
                )}
              </Box>
            ))}

            {/* Note */}
            <Box
              backgroundColor="warningLight"
              borderRadius="md_12"
              padding="md_16"
              marginTop="md_16">
              <Text variant="l_12Medium_message" color="warning">
                💡 Dica: Você pode ajustar horários específicos depois de salvar
              </Text>
            </Box>
          </Card>

          {/* Progress Indicator */}
          <Box flexDirection="row" justifyContent="center" marginTop="lg_24" gap="xs_8">
            <Box width={8} height={8} borderRadius="circle_9999" backgroundColor="border" />
            <Box width={8} height={8} borderRadius="circle_9999" backgroundColor="primary" />
            <Box width={8} height={8} borderRadius="circle_9999" backgroundColor="border" />
          </Box>

          {/* Action Buttons */}
          <Box flexDirection="row" gap="sm_12" marginTop="lg_24">
            <Box flex={1}>
              <Button variant="secondary" onPress={() => navigation.goBack()} title="Voltar" />
            </Box>
            <Box flex={1}>
              <Button variant="primary" onPress={handleNext} enabled={isValid()} title="Próximo" />
            </Box>
          </Box>
        </Box>
      </ScrollView>
    </SafeAreaView>
  );
};

export default VenueOpeningTimesScreen;
