import React, { useState } from 'react';

import { ScrollView } from 'react-native';

import { useNavigation } from '@react-navigation/native';
import {
  Chat,
  CompassTool,
  DotsThree,
  Eye,
  Link,
  MapPin,
  Megaphone,
  MusicNote,
  Star,
  Ticket,
  UserCheck,
} from 'phosphor-react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';

import { EventRegistrationStackScreenProps } from '@/src/core/navigation/types';
import { Box, Text, useTheme } from '@/src/core/theme';
import { PressableAnimated } from '@/src/core/theme/Pressable';
import { Avatar, Button, Card, Chip, Divider } from '@/src/shared/components';

import { ActionItem } from '../components/ActionItem';
import { EventPreviewCard } from '../components/EventPreviewCard';
import { EventProgressIndicator } from '../components/EventProgressIndicator';
import { useEventDraft } from '../hooks/useEventDraft';

export const EventSummaryScreen = () => {
  const navigation =
    useNavigation<EventRegistrationStackScreenProps<'EventSummary'>['navigation']>();
  const theme = useTheme();
  const insets = useSafeAreaInsets();

  const { draft, currentStep, goToNextStep } = useEventDraft();

  const [actionStates, setActionStates] = useState({
    announcePublicly: false,
    visibleOnSearch: true,
  });

  const handleNext = () => {
    goToNextStep();
    navigation.navigate('EventPreferences');
  };

  return (
    <SafeAreaView style={{ flex: 1 }} edges={['bottom']}>
      <Box flex={1} backgroundColor="background">
        <EventProgressIndicator currentStep={currentStep} />

        <ScrollView showsVerticalScrollIndicator={false}>
          <Box padding="md_16">
            {/* Event Preview Card */}
            <EventPreviewCard event={draft} />

            {/* Event Description & Actions */}
            <Card marginTop="sm_12">
              <Box flexDirection="row" gap="xs_8" flexWrap="wrap">
                <Chip label="Music" leftIcon={<MusicNote size={16} weight="fill" />} />
                <Chip label="Social" leftIcon={<Chat size={16} weight="fill" />} />
              </Box>

              <Text variant="b_14Regular_content" marginTop="sm_12" numberOfLines={4}>
                {draft.description || 'No description provided'}
              </Text>

              <Box flexDirection="row" gap="sm_12" marginTop="md_16">
                <Chip
                  label="Interested"
                  leftIcon={<Star size={16} weight="fill" />}
                  chipProps={{
                    backgroundColor: 'primaryLight',
                  }}
                />
                <Chip
                  label="Going"
                  leftIcon={<UserCheck size={16} weight="fill" />}
                  chipProps={{
                    backgroundColor: 'primaryLight',
                  }}
                />
                <Box flex={1} />
                <DotsThree size={16} weight="fill" />
              </Box>

              {/* Attendees Preview */}
              <Box
                flexDirection="row"
                alignItems="center"
                marginTop="md_16"
                paddingTop="md_16"
                borderTopWidth={1}
                borderTopColor="border">
                <Box flexDirection="row" flex={1}>
                  {[1, 2, 3, 4, 5].map(index => (
                    <Avatar
                      key={index}
                      size="xs"
                      source={{ uri: `https://i.pravatar.cc/150?img=${index}` }}
                    />
                  ))}
                </Box>
                <Text variant="l_12Regular_helperText" color="textSecondary">
                  +400 interested, +900 going
                </Text>
              </Box>
            </Card>

            <Divider marginVertical="md_16" />

            {/* Location Card */}
            <Card>
              <Box flexDirection="row" justifyContent="space-between">
                <Box flex={1}>
                  <Text variant="h_18SemiBold_cardTitle">
                    {draft.locationName || 'Event Location'}
                  </Text>
                  <Text variant="b_14Regular_content" color="textSecondary">
                    {draft.location || 'No location set'}
                  </Text>
                </Box>
                <PressableAnimated onPress={() => {}}>
                  <MapPin size={20} />
                </PressableAnimated>
              </Box>
            </Card>

            {/* Ticket Section */}
            <Card marginTop="sm_12" flexDirection="row" alignItems="center">
              <Box flex={1}>
                <Text variant="h_18SemiBold_cardTitle">Find Your Tickets</Text>
                <Text variant="b_14Regular_content" color="textSecondary">
                  Secure your spot at the event
                </Text>
              </Box>
              <PressableAnimated onPress={() => {}}>
                <Ticket size={20} />
              </PressableAnimated>
            </Card>

            <Divider marginVertical="md_16" />

            {/* Organizer */}
            <Box flexDirection="row" justifyContent="space-between" alignItems="center">
              <Box flexDirection="row" alignItems="center" gap="sm_12" flex={1}>
                <Avatar size="m" source={{ uri: 'https://i.pravatar.cc/150?img=10' }} />
                <Box>
                  <Text variant="b_16SemiBold_button">World of Music</Text>
                  <Text variant="b_14Regular_content" color="textSecondary">
                    Organizer
                  </Text>
                </Box>
              </Box>
              <Chip
                label="Follow"
                leftIcon={<UserCheck size={20} />}
                chipProps={{ backgroundColor: 'primaryLight' }}
                chipVariant="solidSmall"
              />
            </Box>

            <Divider marginVertical="md_16" />

            {/* Action Items */}
            <Card backgroundColor="primaryLight" padding="md_16">
              <Text variant="l_10SemiBold_tag" color="primary" marginBottom="sm_12">
                ACTION ITEMS
              </Text>

              <ActionItem
                icon={<Megaphone size={20} />}
                title="Announce publicly"
                subtitle="Let everyone know about your event"
                toggle
                value={actionStates.announcePublicly}
                onValueChange={value =>
                  setActionStates(prev => ({ ...prev, announcePublicly: value }))
                }
              />

              <Divider marginVertical="sm_12" />

              <ActionItem
                icon={<Eye size={20} />}
                title="Visible on search"
                subtitle="People can discover your event"
                toggle
                value={actionStates.visibleOnSearch}
                onValueChange={value =>
                  setActionStates(prev => ({ ...prev, visibleOnSearch: value }))
                }
              />

              <Divider marginVertical="sm_12" />

              <ActionItem
                icon={<Link size={20} />}
                title="Share link"
                subtitle="Get a shareable link"
                onPress={() => {
                  /* Generate link */
                }}
              />

              <Divider marginVertical="sm_12" />

              <ActionItem
                icon={<CompassTool size={20} />}
                title="Additional settings"
                subtitle="Configure event preferences"
                onPress={() => {
                  /* Open settings */
                }}
              />
            </Card>

            {/* More Events Section */}
            <Box marginTop="lg_24">
              <Box
                flexDirection="row"
                justifyContent="space-between"
                alignItems="center"
                marginBottom="sm_12">
                <Text variant="h_20Medium_subsection">More Events like this</Text>
                <Text variant="b_14Medium_button" color="primary">
                  See All
                </Text>
              </Box>

              {/* Event cards would go here */}
              <Text variant="b_14Regular_content" color="textSecondary">
                Similar events will appear here
              </Text>
            </Box>
          </Box>
        </ScrollView>

        {/* Bottom Actions */}
        <Box
          backgroundColor="surface"
          borderTopWidth={1}
          borderTopColor="border"
          paddingHorizontal="md_16"
          paddingVertical="md_16"
          flexDirection="row"
          style={{ paddingBottom: insets.bottom + theme.spacing.md_16 }}
          gap="sm_12">
          <Button
            variant="ghost"
            onPress={() => {
              /* Preview */
            }}
            flex={1}
            title="View preview"
          />

          <Button variant="primary" onPress={handleNext} flex={1} title="Next" />
        </Box>
      </Box>
    </SafeAreaView>
  );
};
