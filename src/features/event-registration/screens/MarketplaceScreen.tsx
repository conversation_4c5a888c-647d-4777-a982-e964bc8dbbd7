import { useState } from 'react';

import { ScrollView, TouchableOpacity } from 'react-native';

import { useNavigation } from '@react-navigation/native';
import { Calendar, Clock, MapPin, Money, ShoppingCart, Tag } from 'phosphor-react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { EventRegistrationStackScreenProps } from '@/src/core/navigation/types';
import { Box, Text, TextInput, useTheme } from '@/src/core/theme';
import { Button } from '@/src/shared/components';

import { useEventRegistrationContext } from '../context/EventRegistrationContext';

type SwapType = 'sell' | 'trade' | 'donate' | 'lastminute';

const MarketplaceScreen = () => {
  const theme = useTheme();
  const navigation =
    useNavigation<EventRegistrationStackScreenProps<'Marketplace'>['navigation']>();
  const { selectedTemplate } = useEventRegistrationContext();

  const [ticketDetails, setTicketDetails] = useState({
    eventName: '',
    eventDate: '',
    venue: '',
    ticketType: '',
    originalPrice: '',
    askingPrice: '',
    swapType: 'sell' as SwapType,
    wantedEvent: '',
    urgency: false,
  });

  const swapTypes = [
    { id: 'sell', label: 'Vender', icon: '💰', color: 'success' },
    { id: 'trade', label: 'Trocar', icon: '🔄', color: 'primary' },
    { id: 'donate', label: 'Doar', icon: '🎁', color: 'secondary' },
    { id: 'lastminute', label: 'Última hora', icon: '⚡', color: 'warning' },
  ];

  const ticketTypes = [
    { id: 'meia', label: 'Meia-entrada' },
    { id: 'inteira', label: 'Inteira' },
    { id: 'vip', label: 'VIP' },
    { id: 'camarote', label: 'Camarote' },
  ];

  const isValid = () => {
    const baseValid = !!(ticketDetails.eventName && ticketDetails.eventDate && ticketDetails.venue);

    if (ticketDetails.swapType === 'sell' || ticketDetails.swapType === 'lastminute') {
      return !!(baseValid && ticketDetails.askingPrice);
    }

    return baseValid;
  };

  const handleSubmit = () => {
    // TODO: Implement ticket listing logic
    navigation.navigate('EventSuccess', {
      eventId: 'ticket-' + Date.now(),
      eventName: `Ingresso - ${ticketDetails.eventName}`,
    });
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: theme.colors.background }}>
      <ScrollView>
        <Box flex={1} padding="lg_24">
          {/* Header */}
          <Box marginBottom="lg_24">
            <Text variant="h_24SemiBold_section" marginBottom="xs_8">
              {selectedTemplate?.emoji} Detalhes do Ingresso
            </Text>
            <Text variant="b_16Regular_input" color="textSecondary">
              Preencha as informações para listar seu ingresso
            </Text>
          </Box>

          {/* Swap Type Selection */}
          <Box marginBottom="lg_24">
            <Text variant="b_14Medium_button" color="textSecondary" marginBottom="sm_12">
              O que você quer fazer?
            </Text>
            <Box flexDirection="row" gap="xs_8">
              {swapTypes.map(type => (
                <TouchableOpacity
                  key={type.id}
                  onPress={() =>
                    setTicketDetails({ ...ticketDetails, swapType: type.id as SwapType })
                  }
                  style={{ flex: 1 }}>
                  <Box
                    backgroundColor={
                      ticketDetails.swapType === type.id ? (type.color as any) : 'surfaceBackground'
                    }
                    borderRadius="md_12"
                    padding="sm_12"
                    alignItems="center"
                    borderWidth={1}
                    borderColor={
                      ticketDetails.swapType === type.id ? (type.color as any) : 'border'
                    }>
                    <Text variant="h_20Medium_subsection" marginBottom="xxs_4">
                      {type.icon}
                    </Text>
                    <Text
                      variant="l_12Medium_message"
                      color={ticketDetails.swapType === type.id ? 'white' : 'text'}>
                      {type.label}
                    </Text>
                  </Box>
                </TouchableOpacity>
              ))}
            </Box>
          </Box>

          {/* Event Details */}
          <Box marginBottom="lg_24">
            <Text variant="b_14Medium_button" color="textSecondary" marginBottom="sm_12">
              Informações do Evento
            </Text>

            {/* Event Name */}
            <TextInput
              label="Nome do evento"
              placeholder="Nome do evento ou show"
              value={ticketDetails.eventName}
              onChangeText={text => setTicketDetails({ ...ticketDetails, eventName: text })}
              containerProps={{ marginBottom: 'md_16' }}
            />

            {/* Date and Venue */}
            <Box flexDirection="row" gap="sm_12" marginBottom="md_16">
              <Box flex={1}>
                <TextInput
                  label="Data"
                  placeholder="DD/MM/AAAA"
                  value={ticketDetails.eventDate}
                  onChangeText={text => setTicketDetails({ ...ticketDetails, eventDate: text })}
                  leading={<Calendar size={16} color={theme.colors.textSecondary} />}
                />
              </Box>

              <Box flex={1}>
                <TextInput
                  label="Local"
                  placeholder="Local do evento"
                  value={ticketDetails.venue}
                  onChangeText={text => setTicketDetails({ ...ticketDetails, venue: text })}
                  leading={<MapPin size={16} color={theme.colors.textSecondary} />}
                />
              </Box>
            </Box>

            {/* Ticket Type */}
            <Box marginBottom="md_16">
              <Text variant="b_14Medium_button" color="textSecondary" marginBottom="xs_8">
                Tipo de ingresso
              </Text>
              <Box flexDirection="row" gap="xs_8">
                {ticketTypes.map(type => (
                  <TouchableOpacity
                    key={type.id}
                    onPress={() => setTicketDetails({ ...ticketDetails, ticketType: type.id })}>
                    <Box
                      paddingHorizontal="md_16"
                      paddingVertical="xs_8"
                      borderRadius="lg_16"
                      backgroundColor={
                        ticketDetails.ticketType === type.id ? 'primary' : 'surfaceBackground'
                      }
                      borderWidth={1}
                      borderColor={ticketDetails.ticketType === type.id ? 'primary' : 'border'}>
                      <Text
                        variant="b_14Medium_button"
                        color={ticketDetails.ticketType === type.id ? 'white' : 'text'}>
                        {type.label}
                      </Text>
                    </Box>
                  </TouchableOpacity>
                ))}
              </Box>
            </Box>
          </Box>

          {/* Pricing Section (for sell/lastminute) */}
          {(ticketDetails.swapType === 'sell' || ticketDetails.swapType === 'lastminute') && (
            <Box marginBottom="lg_24">
              <Text variant="b_14Medium_button" color="textSecondary" marginBottom="sm_12">
                Valores
              </Text>

              <Box flexDirection="row" gap="sm_12">
                <Box flex={1}>
                  <TextInput
                    label="Valor original"
                    placeholder="R$ 0,00"
                    value={ticketDetails.originalPrice}
                    onChangeText={text =>
                      setTicketDetails({ ...ticketDetails, originalPrice: text })
                    }
                    leading={<Money size={16} color={theme.colors.textSecondary} />}
                  />
                </Box>

                <Box flex={1}>
                  <TextInput
                    label="Pedindo"
                    placeholder="R$ 0,00"
                    value={ticketDetails.askingPrice}
                    onChangeText={text => setTicketDetails({ ...ticketDetails, askingPrice: text })}
                    leading={<Tag size={16} color={theme.colors.success} />}
                  />
                </Box>
              </Box>

              {ticketDetails.swapType === 'lastminute' && (
                <Box
                  backgroundColor="warningLight"
                  borderRadius="md_12"
                  padding="sm_12"
                  marginTop="sm_12">
                  <Text variant="l_12Medium_message" color="warning">
                    ⚡ Ingressos de última hora geralmente têm desconto maior para venda rápida
                  </Text>
                </Box>
              )}
            </Box>
          )}

          {/* Trade Section */}
          {ticketDetails.swapType === 'trade' && (
            <Box marginBottom="lg_24">
              <Text variant="b_14Medium_button" color="textSecondary" marginBottom="sm_12">
                Quero trocar por
              </Text>

              <TextInput
                label="Evento desejado"
                placeholder="Show, festa ou evento que você quer"
                value={ticketDetails.wantedEvent}
                onChangeText={text => setTicketDetails({ ...ticketDetails, wantedEvent: text })}
                leading={<ShoppingCart size={16} color={theme.colors.primary} />}
              />

              <Box
                backgroundColor="primaryLight"
                borderRadius="md_12"
                padding="sm_12"
                marginTop="sm_12">
                <Text variant="l_12Medium_message" color="primary">
                  🔄 Especifique datas flexíveis se possível para aumentar as chances de troca
                </Text>
              </Box>
            </Box>
          )}

          {/* Donate Section */}
          {ticketDetails.swapType === 'donate' && (
            <Box
              backgroundColor="secondaryLight"
              borderRadius="md_12"
              padding="md_16"
              marginBottom="lg_24">
              <Text variant="b_16SemiBold_button" color="secondary" marginBottom="xs_8">
                🎁 Doação de ingresso
              </Text>
              <Text variant="b_14Regular_content" color="secondaryDark">
                Seu ingresso será disponibilizado gratuitamente para alguém que não pode pagar. Uma
                ação linda que espalha alegria!
              </Text>
            </Box>
          )}

          {/* Urgency Toggle for Last Minute */}
          {ticketDetails.swapType === 'lastminute' && (
            <TouchableOpacity
              onPress={() =>
                setTicketDetails({ ...ticketDetails, urgency: !ticketDetails.urgency })
              }>
              <Box
                backgroundColor={ticketDetails.urgency ? 'warningLight' : 'surfaceBackground'}
                borderRadius="md_12"
                padding="md_16"
                marginBottom="lg_24"
                borderWidth={1}
                borderColor={ticketDetails.urgency ? 'warning' : 'border'}
                flexDirection="row"
                alignItems="center">
                <Clock
                  size={20}
                  color={ticketDetails.urgency ? theme.colors.warning : theme.colors.textSecondary}
                  style={{ marginRight: 8 }}
                />
                <Box flex={1}>
                  <Text variant="b_14Medium_button">Urgente - Evento em menos de 24h</Text>
                  <Text variant="l_12Regular_helperText" color="textSecondary">
                    Marque se o evento é hoje ou amanhã
                  </Text>
                </Box>
              </Box>
            </TouchableOpacity>
          )}

          {/* Safety Tips */}
          <Box marginBottom="lg_24">
            <Box
              backgroundColor="surfaceBackground"
              borderRadius="md_12"
              padding="md_16"
              borderWidth={1}
              borderColor="border">
              <Text variant="b_14Medium_button" marginBottom="xs_8">
                🛡️ Dicas de segurança
              </Text>
              <Text variant="l_12Regular_helperText" color="textSecondary">
                • Encontre-se em locais públicos{'\n'}• Verifique a autenticidade do ingresso{'\n'}•
                Use métodos de pagamento seguros
              </Text>
            </Box>
          </Box>

          {/* Action buttons */}
          <Box flexDirection="row" gap="sm_12">
            <Box flex={1}>
              <Button variant="secondary" onPress={() => navigation.goBack()} title="Cancelar" />
            </Box>
            <Box flex={1}>
              <Button
                variant="primary"
                onPress={handleSubmit}
                enabled={isValid()}
                title="Publicar"
              />
            </Box>
          </Box>
        </Box>
      </ScrollView>
    </SafeAreaView>
  );
};

export default MarketplaceScreen;
