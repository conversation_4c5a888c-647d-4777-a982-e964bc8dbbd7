import { useState } from 'react';

import { ScrollView, TouchableOpacity } from 'react-native';

import { useNavigation } from '@react-navigation/native';
import {
  Camera,
  Clock,
  Lightning,
  MapPin,
  MusicNote,
  Timer,
  Users,
  Warning,
} from 'phosphor-react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { EventRegistrationStackScreenProps } from '@/src/core/navigation/types';
import { Box, Text, TextInput, useTheme } from '@/src/core/theme';
import { Button, Card } from '@/src/shared/components';

import { useEventRegistrationContext } from '../context/EventRegistrationContext';

type FlashType = 'now' | '30min' | '1hour' | '2hours';
type MusicType = 'any' | 'electronic' | 'funk' | 'rock' | 'varied';

const FlashPartyScreen = () => {
  const theme = useTheme();
  const navigation = useNavigation<EventRegistrationStackScreenProps<'FlashParty'>['navigation']>();
  const { selectedTemplate } = useEventRegistrationContext();

  const [flashDetails, setFlashDetails] = useState({
    title: '',
    location: '',
    exactAddress: '',
    startTime: 'now' as FlashType,
    customTime: '',
    duration: '2h',
    capacity: '25',
    musicType: 'any' as MusicType,
    requirements: [] as string[],
    whatToBring: '',
    quickDescription: '',
    photo: null as string | null,
  });

  const startTimes = [
    { id: 'now', label: 'Agora!', icon: '⚡', color: 'warning' },
    { id: '30min', label: '30 min', icon: '⏰', color: 'primary' },
    { id: '1hour', label: '1 hora', icon: '🕐', color: 'primary' },
    { id: '2hours', label: '2 horas', icon: '🕑', color: 'primary' },
  ];

  const durations = [
    { id: '1h', label: '1 hora' },
    { id: '2h', label: '2 horas' },
    { id: '3h', label: '3 horas' },
    { id: 'open', label: 'Sem hora pra acabar' },
  ];

  const musicTypes = [
    { id: 'any', label: 'Qualquer', emoji: '🎵' },
    { id: 'electronic', label: 'Eletrônica', emoji: '🎛️' },
    { id: 'funk', label: 'Funk', emoji: '🔊' },
    { id: 'rock', label: 'Rock', emoji: '🎸' },
    { id: 'varied', label: 'Variado', emoji: '🎶' },
  ];

  const quickRequirements = [
    { id: 'byob', label: 'BYOB (traga sua bebida)', emoji: '🍺' },
    { id: 'contributions', label: 'Aceita contribuições', emoji: '💰' },
    { id: 'no-plus-one', label: 'Sem +1 (só convidados)', emoji: '🚫' },
    { id: 'theme', label: 'Tem tema/dress code', emoji: '👗' },
  ];

  const toggleRequirement = (reqId: string) => {
    const current = flashDetails.requirements;
    if (current.includes(reqId)) {
      setFlashDetails({
        ...flashDetails,
        requirements: current.filter(r => r !== reqId),
      });
    } else {
      setFlashDetails({
        ...flashDetails,
        requirements: [...current, reqId],
      });
    }
  };

  const isValid = () => {
    return !!(
      flashDetails.title &&
      flashDetails.location &&
      flashDetails.exactAddress &&
      flashDetails.capacity
    );
  };

  const handleSubmit = () => {
    // TODO: Implement flash party creation
    navigation.navigate('EventSuccess', {
      eventId: 'flash-' + Date.now(),
      eventName: flashDetails.title,
    });
  };

  const getTimeLabel = () => {
    switch (flashDetails.startTime) {
      case 'now':
        return 'Começando agora mesmo!';
      case '30min':
        return 'Começa em 30 minutos';
      case '1hour':
        return 'Começa em 1 hora';
      case '2hours':
        return 'Começa em 2 horas';
      default:
        return '';
    }
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: theme.colors.background }}>
      <ScrollView>
        <Box flex={1} padding="lg_24">
          {/* Header */}
          <Box marginBottom="lg_24">
            <Text variant="h_24SemiBold_section" marginBottom="xs_8">
              {selectedTemplate?.emoji} Flash Party
            </Text>
            <Text variant="b_16Regular_input" color="textSecondary">
              Evento rápido e espontâneo - máximo 3 horas!
            </Text>
          </Box>

          {/* Urgency Alert */}
          <Box
            backgroundColor="warningLight"
            borderRadius="md_12"
            padding="md_16"
            marginBottom="lg_24"
            flexDirection="row"
            alignItems="center">
            <Lightning size={24} color={theme.colors.warning} weight="fill" />
            <Box flex={1} marginLeft="sm_12">
              <Text variant="b_14Medium_button" color="warning">
                Evento relâmpago!
              </Text>
              <Text variant="l_12Regular_helperText" color="warningDark">
                Será criado e divulgado rapidamente. Prepare-se!
              </Text>
            </Box>
          </Box>

          {/* Basic Info */}
          <Card padding="md_16" marginBottom="md_16">
            <TextInput
              label="Nome da flash"
              placeholder="Ex: Flash no apê do João!"
              value={flashDetails.title}
              onChangeText={text => setFlashDetails({ ...flashDetails, title: text })}
              containerProps={{ marginBottom: 'md_16' }}
              autoFocus
            />

            <TextInput
              label="Descrição rápida"
              placeholder="O que vai rolar? (máx 140 caracteres)"
              value={flashDetails.quickDescription}
              onChangeText={text => setFlashDetails({ ...flashDetails, quickDescription: text })}
              maxLength={140}
              multiline
              numberOfLines={2}
            />
          </Card>

          {/* Time Selection */}
          <Card padding="md_16" marginBottom="md_16">
            <Text variant="b_14Medium_button" color="textSecondary" marginBottom="sm_12">
              <Clock size={16} color={theme.colors.textSecondary} /> Quando começa?
            </Text>

            <Box flexDirection="row" gap="xs_8" marginBottom="md_16">
              {startTimes.map(time => (
                <TouchableOpacity
                  key={time.id}
                  onPress={() =>
                    setFlashDetails({ ...flashDetails, startTime: time.id as FlashType })
                  }
                  style={{ flex: 1 }}>
                  <Box
                    backgroundColor={
                      flashDetails.startTime === time.id ? (time.color as any) : 'surfaceBackground'
                    }
                    borderRadius="md_12"
                    padding="sm_12"
                    alignItems="center"
                    borderWidth={1}
                    borderColor={
                      flashDetails.startTime === time.id ? (time.color as any) : 'border'
                    }>
                    <Text variant="h_20Medium_subsection" marginBottom="xxs_4">
                      {time.icon}
                    </Text>
                    <Text
                      variant="l_12Medium_message"
                      color={flashDetails.startTime === time.id ? 'white' : 'text'}>
                      {time.label}
                    </Text>
                  </Box>
                </TouchableOpacity>
              ))}
            </Box>

            {flashDetails.startTime !== 'now' && (
              <Box
                backgroundColor="primaryLight"
                borderRadius="sm_8"
                padding="sm_12"
                marginBottom="md_16">
                <Text variant="b_14Medium_button" color="primary">
                  {getTimeLabel()}
                </Text>
              </Box>
            )}

            {/* Duration */}
            <Box>
              <Text variant="b_14Medium_button" color="textSecondary" marginBottom="xs_8">
                <Timer size={16} color={theme.colors.textSecondary} /> Duração
              </Text>
              <Box flexDirection="row" gap="xs_8">
                {durations.map(duration => (
                  <TouchableOpacity
                    key={duration.id}
                    onPress={() => setFlashDetails({ ...flashDetails, duration: duration.id })}>
                    <Box
                      paddingHorizontal="md_16"
                      paddingVertical="xs_8"
                      borderRadius="lg_16"
                      backgroundColor={
                        flashDetails.duration === duration.id ? 'primary' : 'surfaceBackground'
                      }
                      borderWidth={1}
                      borderColor={flashDetails.duration === duration.id ? 'primary' : 'border'}>
                      <Text
                        variant="b_14Medium_button"
                        color={flashDetails.duration === duration.id ? 'white' : 'text'}>
                        {duration.label}
                      </Text>
                    </Box>
                  </TouchableOpacity>
                ))}
              </Box>
            </Box>
          </Card>

          {/* Location */}
          <Card padding="md_16" marginBottom="md_16">
            <Text variant="b_14Medium_button" color="textSecondary" marginBottom="md_16">
              <MapPin size={16} color={theme.colors.textSecondary} /> Onde vai ser?
            </Text>

            <TextInput
              label="Local/Referência"
              placeholder="Ex: Meu apê, Casa da Maria"
              value={flashDetails.location}
              onChangeText={text => setFlashDetails({ ...flashDetails, location: text })}
              containerProps={{ marginBottom: 'md_16' }}
            />

            <TextInput
              label="Endereço exato"
              placeholder="Será compartilhado só com confirmados"
              value={flashDetails.exactAddress}
              onChangeText={text => setFlashDetails({ ...flashDetails, exactAddress: text })}
              leading={<MapPin size={16} color={theme.colors.textSecondary} />}
            />

            <Box
              backgroundColor="surfaceBackground"
              borderRadius="sm_8"
              padding="sm_12"
              marginTop="sm_12">
              <Text variant="l_12Regular_helperText" color="textSecondary">
                🔒 O endereço completo só aparece para quem confirmar presença
              </Text>
            </Box>
          </Card>

          {/* Party Details */}
          <Card padding="md_16" marginBottom="md_16">
            <Box flexDirection="row" gap="sm_12" marginBottom="md_16">
              <Box flex={1}>
                <TextInput
                  label="Capacidade"
                  placeholder="25"
                  value={flashDetails.capacity}
                  onChangeText={text => setFlashDetails({ ...flashDetails, capacity: text })}
                  leading={<Users size={16} color={theme.colors.textSecondary} />}
                  keyboardType="numeric"
                />
              </Box>

              <Box flex={1}>
                <Text variant="b_14Medium_button" color="textSecondary" marginBottom="xs_8">
                  <MusicNote size={16} color={theme.colors.textSecondary} /> Música
                </Text>
                <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                  <Box flexDirection="row" gap="xs_8">
                    {musicTypes.map(music => (
                      <TouchableOpacity
                        key={music.id}
                        onPress={() =>
                          setFlashDetails({ ...flashDetails, musicType: music.id as MusicType })
                        }>
                        <Box
                          paddingHorizontal="sm_12"
                          paddingVertical="xxs_4"
                          borderRadius="lg_16"
                          backgroundColor={
                            flashDetails.musicType === music.id ? 'primary' : 'surfaceBackground'
                          }
                          borderWidth={1}
                          borderColor={flashDetails.musicType === music.id ? 'primary' : 'border'}>
                          <Text
                            variant="l_12Medium_message"
                            color={flashDetails.musicType === music.id ? 'white' : 'text'}>
                            {music.emoji} {music.label}
                          </Text>
                        </Box>
                      </TouchableOpacity>
                    ))}
                  </Box>
                </ScrollView>
              </Box>
            </Box>

            {/* Requirements */}
            <Box>
              <Text variant="b_14Medium_button" color="textSecondary" marginBottom="xs_8">
                Regras da flash
              </Text>
              <Box flexDirection="row" flexWrap="wrap" gap="xs_8">
                {quickRequirements.map(req => (
                  <TouchableOpacity key={req.id} onPress={() => toggleRequirement(req.id)}>
                    <Box
                      paddingHorizontal="md_16"
                      paddingVertical="xs_8"
                      borderRadius="lg_16"
                      backgroundColor={
                        flashDetails.requirements.includes(req.id)
                          ? 'primaryLight'
                          : 'surfaceBackground'
                      }
                      borderWidth={1}
                      borderColor={
                        flashDetails.requirements.includes(req.id) ? 'primary' : 'border'
                      }>
                      <Text variant="b_14Regular_content">
                        {req.emoji} {req.label}
                      </Text>
                    </Box>
                  </TouchableOpacity>
                ))}
              </Box>
            </Box>
          </Card>

          {/* What to Bring */}
          <Card padding="md_16" marginBottom="md_16">
            <TextInput
              label="O que trazer?"
              placeholder="Bebida, petiscos, bom humor..."
              value={flashDetails.whatToBring}
              onChangeText={text => setFlashDetails({ ...flashDetails, whatToBring: text })}
              multiline
              numberOfLines={2}
            />
          </Card>

          {/* Photo */}
          <Box marginBottom="lg_24">
            <Text variant="b_14Medium_button" color="textSecondary" marginBottom="xs_8">
              Foto da vibe (opcional)
            </Text>
            <TouchableOpacity>
              <Box
                backgroundColor="surfaceBackground"
                borderRadius="md_12"
                padding="xl_32"
                borderWidth={1}
                borderColor="border"
                borderStyle="dashed"
                alignItems="center">
                <Camera size={32} color={theme.colors.textTertiary} />
                <Text variant="b_14Regular_content" color="textTertiary" marginTop="sm_12">
                  Adicionar foto do local/clima
                </Text>
              </Box>
            </TouchableOpacity>
          </Box>

          {/* Warning */}
          <Box
            backgroundColor="warningLight"
            borderRadius="md_12"
            padding="md_16"
            marginBottom="lg_24"
            flexDirection="row"
            alignItems="flex-start">
            <Warning size={20} color={theme.colors.warning} style={{ marginTop: 2 }} />
            <Box flex={1} marginLeft="sm_12">
              <Text variant="b_14Medium_button" color="warning" marginBottom="xxs_4">
                Lembre-se: Flash Party
              </Text>
              <Text variant="l_12Regular_helperText" color="warningDark">
                • Máximo 3 horas de duração{'\n'}• Será divulgado rapidamente{'\n'}• Não pode ser
                editado depois de criado
              </Text>
            </Box>
          </Box>

          {/* Action Buttons */}
          <Box flexDirection="row" gap="sm_12">
            <Box flex={1}>
              <Button variant="secondary" onPress={() => navigation.goBack()} title="Voltar" />
            </Box>
            <Box flex={1}>
              <Button
                variant="primary"
                onPress={handleSubmit}
                enabled={isValid()}
                title="⚡ Criar Flash"
              />
            </Box>
          </Box>
        </Box>
      </ScrollView>
    </SafeAreaView>
  );
};

export default FlashPartyScreen;
