import { useState } from 'react';

import { ScrollView, TouchableOpacity } from 'react-native';

import { useNavigation } from '@react-navigation/native';
import {
  Buildings,
  Camera,
  Envelope,
  FacebookLogo,
  Globe,
  InstagramLogo as Instagram,
  MapPin,
  Phone,
} from 'phosphor-react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { VenueRegistrationStackScreenProps } from '@/src/core/navigation/types';
import { Box, Text, TextInput, useTheme } from '@/src/core/theme';
import { But<PERSON>, Card } from '@/src/shared/components';

import { useEventRegistrationContext } from '../context/EventRegistrationContext';

const VenueBasicDetailsScreen = () => {
  const theme = useTheme();
  const navigation =
    useNavigation<VenueRegistrationStackScreenProps<'VenueBasicDetails'>['navigation']>();
  const { selectedTemplate } = useEventRegistrationContext();

  const [venueDetails, setVenueDetails] = useState({
    name: '',
    type: '',
    phone: '',
    email: '',
    website: '',
    instagram: '',
    facebook: '',
    address: {
      street: '',
      number: '',
      complement: '',
      neighborhood: '',
      city: '',
      state: '',
      zipCode: '',
    },
    description: '',
    photo: null as string | null,
  });

  const venueTypes = [
    { id: 'bar', label: 'Bar', emoji: '🍺' },
    { id: 'restaurant', label: 'Restaurante', emoji: '🍽️' },
    { id: 'nightclub', label: 'Balada', emoji: '🎵' },
    { id: 'lounge', label: 'Lounge', emoji: '🍸' },
    { id: 'cultural', label: 'Espaço Cultural', emoji: '🎭' },
    { id: 'sports', label: 'Bar Esportivo', emoji: '⚽' },
  ];

  const isValid = () => {
    return !!(
      venueDetails.name &&
      venueDetails.type &&
      venueDetails.phone &&
      venueDetails.address.street &&
      venueDetails.address.number &&
      venueDetails.address.neighborhood &&
      venueDetails.address.city &&
      venueDetails.address.state
    );
  };

  const handleNext = () => {
    // TODO: Save venue details to context
    navigation.navigate('VenueOpeningTimes');
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: theme.colors.background }}>
      <ScrollView>
        <Box flex={1} padding="lg_24">
          {/* Header */}
          <Box marginBottom="lg_24">
            <Text variant="h_24SemiBold_section" marginBottom="xs_8">
              🏪 Informações básicas
            </Text>
            <Text variant="b_16Regular_input" color="textSecondary">
              Vamos cadastrar seu estabelecimento
            </Text>
          </Box>

          {/* Basic Info */}
          <Card padding="md_16" marginBottom="md_16">
            <Text variant="b_14Medium_button" color="textSecondary" marginBottom="md_16">
              Dados do estabelecimento
            </Text>

            <TextInput
              label="Nome do estabelecimento"
              placeholder="Como vocês se chamam?"
              value={venueDetails.name}
              onChangeText={text => setVenueDetails({ ...venueDetails, name: text })}
              leading={<Buildings size={20} color={theme.colors.textSecondary} />}
              containerProps={{ marginBottom: 'md_16' }}
              autoFocus
            />

            {/* Venue Type Selection */}
            <Box marginBottom="md_16">
              <Text variant="b_14Medium_button" color="textSecondary" marginBottom="xs_8">
                Tipo de estabelecimento
              </Text>
              <Box flexDirection="row" flexWrap="wrap" gap="xs_8">
                {venueTypes.map(type => (
                  <TouchableOpacity
                    key={type.id}
                    onPress={() => setVenueDetails({ ...venueDetails, type: type.id })}>
                    <Box
                      paddingHorizontal="md_16"
                      paddingVertical="xs_8"
                      borderRadius="lg_16"
                      backgroundColor={
                        venueDetails.type === type.id ? 'primary' : 'surfaceBackground'
                      }
                      borderWidth={1}
                      borderColor={venueDetails.type === type.id ? 'primary' : 'border'}
                      flexDirection="row"
                      alignItems="center">
                      <Text variant="b_16Regular_input" marginRight="xxs_4">
                        {type.emoji}
                      </Text>
                      <Text
                        variant="b_14Medium_button"
                        color={venueDetails.type === type.id ? 'white' : 'text'}>
                        {type.label}
                      </Text>
                    </Box>
                  </TouchableOpacity>
                ))}
              </Box>
            </Box>
          </Card>

          {/* Contact Info */}
          <Card padding="md_16" marginBottom="md_16">
            <Text variant="b_14Medium_button" color="textSecondary" marginBottom="md_16">
              Contato
            </Text>

            <TextInput
              label="Telefone"
              placeholder="(11) 99999-9999"
              value={venueDetails.phone}
              onChangeText={text => setVenueDetails({ ...venueDetails, phone: text })}
              leading={<Phone size={16} color={theme.colors.textSecondary} />}
              containerProps={{ marginBottom: 'md_16' }}
              keyboardType="phone-pad"
            />

            <TextInput
              label="E-mail"
              placeholder="<EMAIL>"
              value={venueDetails.email}
              onChangeText={text => setVenueDetails({ ...venueDetails, email: text })}
              leading={<Envelope size={16} color={theme.colors.textSecondary} />}
              containerProps={{ marginBottom: 'md_16' }}
              keyboardType="email-address"
              autoCapitalize="none"
            />

            <TextInput
              label="Website (opcional)"
              placeholder="www.seusite.com.br"
              value={venueDetails.website}
              onChangeText={text => setVenueDetails({ ...venueDetails, website: text })}
              leading={<Globe size={16} color={theme.colors.textSecondary} />}
              containerProps={{ marginBottom: 'md_16' }}
              autoCapitalize="none"
            />

            <Box flexDirection="row" gap="sm_12">
              <Box flex={1}>
                <TextInput
                  label="Instagram"
                  placeholder="@seuperfil"
                  value={venueDetails.instagram}
                  onChangeText={text => setVenueDetails({ ...venueDetails, instagram: text })}
                  leading={<Instagram size={16} color={theme.colors.textSecondary} />}
                  autoCapitalize="none"
                />
              </Box>
              <Box flex={1}>
                <TextInput
                  label="Facebook"
                  placeholder="facebook.com/..."
                  value={venueDetails.facebook}
                  onChangeText={text => setVenueDetails({ ...venueDetails, facebook: text })}
                  leading={<FacebookLogo size={16} color={theme.colors.textSecondary} />}
                  autoCapitalize="none"
                />
              </Box>
            </Box>
          </Card>

          {/* Address Info */}
          <Card padding="md_16" marginBottom="md_16">
            <Text variant="b_14Medium_button" color="textSecondary" marginBottom="md_16">
              <MapPin size={16} color={theme.colors.textSecondary} /> Endereço
            </Text>

            <Box flexDirection="row" gap="sm_12" marginBottom="md_16">
              <Box flex={2}>
                <TextInput
                  label="Rua"
                  placeholder="Nome da rua"
                  value={venueDetails.address.street}
                  onChangeText={text =>
                    setVenueDetails({
                      ...venueDetails,
                      address: { ...venueDetails.address, street: text },
                    })
                  }
                />
              </Box>
              <Box flex={1}>
                <TextInput
                  label="Número"
                  placeholder="123"
                  value={venueDetails.address.number}
                  onChangeText={text =>
                    setVenueDetails({
                      ...venueDetails,
                      address: { ...venueDetails.address, number: text },
                    })
                  }
                  keyboardType="numeric"
                />
              </Box>
            </Box>

            <TextInput
              label="Complemento (opcional)"
              placeholder="Apto, sala, etc"
              value={venueDetails.address.complement}
              onChangeText={text =>
                setVenueDetails({
                  ...venueDetails,
                  address: { ...venueDetails.address, complement: text },
                })
              }
              containerProps={{ marginBottom: 'md_16' }}
            />

            <TextInput
              label="Bairro"
              placeholder="Nome do bairro"
              value={venueDetails.address.neighborhood}
              onChangeText={text =>
                setVenueDetails({
                  ...venueDetails,
                  address: { ...venueDetails.address, neighborhood: text },
                })
              }
              containerProps={{ marginBottom: 'md_16' }}
            />

            <Box flexDirection="row" gap="sm_12">
              <Box flex={2}>
                <TextInput
                  label="Cidade"
                  placeholder="São Paulo"
                  value={venueDetails.address.city}
                  onChangeText={text =>
                    setVenueDetails({
                      ...venueDetails,
                      address: { ...venueDetails.address, city: text },
                    })
                  }
                />
              </Box>
              <Box flex={1}>
                <TextInput
                  label="Estado"
                  placeholder="SP"
                  value={venueDetails.address.state}
                  onChangeText={text =>
                    setVenueDetails({
                      ...venueDetails,
                      address: { ...venueDetails.address, state: text },
                    })
                  }
                  maxLength={2}
                  autoCapitalize="characters"
                />
              </Box>
            </Box>
          </Card>

          {/* Description */}
          <Card padding="md_16" marginBottom="lg_24">
            <Text variant="b_14Medium_button" color="textSecondary" marginBottom="md_16">
              Sobre o local
            </Text>

            <TextInput
              label="Descrição"
              placeholder="Conte um pouco sobre o ambiente, especialidades, diferenciais..."
              value={venueDetails.description}
              onChangeText={text => setVenueDetails({ ...venueDetails, description: text })}
              multiline
              numberOfLines={4}
              containerProps={{ marginBottom: 'md_16' }}
            />

            {/* Photo Upload */}
            <Box>
              <Text variant="b_14Medium_button" color="textSecondary" marginBottom="xs_8">
                Foto de capa
              </Text>
              <TouchableOpacity>
                <Box
                  backgroundColor="surfaceBackground"
                  borderRadius="md_12"
                  padding="xl_32"
                  borderWidth={1}
                  borderColor="border"
                  borderStyle="dashed"
                  alignItems="center">
                  <Camera size={32} color={theme.colors.textTertiary} />
                  <Text variant="b_14Regular_content" color="textTertiary" marginTop="sm_12">
                    Adicionar foto do estabelecimento
                  </Text>
                  <Text variant="l_12Regular_helperText" color="textTertiary" marginTop="xxs_4">
                    Recomendado: 1920x1080
                  </Text>
                </Box>
              </TouchableOpacity>
            </Box>
          </Card>

          {/* Progress Indicator */}
          <Box flexDirection="row" justifyContent="center" marginBottom="lg_24" gap="xs_8">
            <Box width={8} height={8} borderRadius="circle_9999" backgroundColor="primary" />
            <Box width={8} height={8} borderRadius="circle_9999" backgroundColor="border" />
            <Box width={8} height={8} borderRadius="circle_9999" backgroundColor="border" />
          </Box>

          {/* Action Buttons */}
          <Box flexDirection="row" gap="sm_12">
            <Box flex={1}>
              <Button variant="secondary" onPress={() => navigation.goBack()} title="Cancelar" />
            </Box>
            <Box flex={1}>
              <Button variant="primary" onPress={handleNext} enabled={isValid()} title="Próximo" />
            </Box>
          </Box>
        </Box>
      </ScrollView>
    </SafeAreaView>
  );
};

export default VenueBasicDetailsScreen;
