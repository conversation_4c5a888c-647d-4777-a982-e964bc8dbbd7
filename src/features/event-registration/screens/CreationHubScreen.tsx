import React from 'react';

import { FlatL<PERSON>, ScrollView } from 'react-native';

import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import {
  <PERSON><PERSON><PERSON>,
  Calendar,
  Gift,
  House,
  Lightning,
  MagnifyingGlass,
  MusicNote,
  Share,
  Sparkle,
  Users,
} from 'phosphor-react-native';
import Animated, { FadeInDown } from 'react-native-reanimated';

import { Box, Text, useTheme } from '@/src/core/theme';
import { CategoryCard } from '@/src/features/event-registration/components/CategoryCard';

import { useChooseOptions } from '../hooks/useChooseOptions';
import { EventCategory } from '../types/event.types';

type NavigationProp = NativeStackNavigationProp<any>;

const AnimatedBox = Animated.createAnimatedComponent(Box);

interface CategoryConfig {
  icon: React.ReactNode;
  gradientColors: [string, string];
  description: string;
}

const CreationHubScreen = () => {
  const theme = useTheme();
  const navigation = useNavigation<NavigationProp>();
  const { categories } = useChooseOptions();

  // Category visual configurations with vibrant colors
  const categoryConfigs: Record<string, CategoryConfig> = {
    events: {
      icon: <Calendar size={32} color="#2563EB" weight="bold" />,
      gradientColors: ['#60A5FA', '#3B82F6'], // Light blue to blue
      description: 'Crie eventos incríveis',
    },
    sharing: {
      icon: <Share size={32} color="#7C3AED" weight="bold" />,
      gradientColors: ['#C4B5FD', '#8B5CF6'], // Light purple to purple
      description: 'Compartilhe promos e descontos',
    },
    alerts: {
      icon: <Lightning size={32} color="#DC2626" weight="bold" />,
      gradientColors: ['#FCD34D', '#F59E0B'], // Light yellow to orange
      description: 'Alertas rápidos da noite',
    },
    marketplace: {
      icon: <BeerStein size={32} color="#059669" weight="bold" />,
      gradientColors: ['#34D399', '#10B981'], // Light green to green
      description: 'Venda e troque ingressos',
    },
    social: {
      icon: <Users size={32} color="#9333EA" weight="bold" />,
      gradientColors: ['#F9A8D4', '#EC4899'], // Light pink to pink
      description: 'Encontros e grupos',
    },
    business: {
      icon: <Gift size={32} color="#DC2626" weight="bold" />,
      gradientColors: ['#FCA5A5', '#EF4444'], // Light red to red
      description: 'Promoções para venues',
    },
  };

  const handleCategoryPress = (category: EventCategory) => {
    navigation.navigate('TemplateSelection', {
      category: category.id,
      categoryLabel: category.label,
    });
  };

  const renderCategoryCard = ({ item, index }: { item: EventCategory; index: number }) => {
    const config = categoryConfigs[item.id] || {
      icon: <Sparkle size={32} color="#6B7280" weight="bold" />,
      gradientColors: ['#6B7280', '#4B5563'] as [string, string],
      description: 'Crie algo incrível',
    };

    return (
      <Box flex={1} margin="xs_8">
        <CategoryCard
          title={item.label}
          subtitle={config.description}
          templateCount={item.templates.length}
          icon={config.icon}
          gradientColors={config.gradientColors}
          onPress={() => handleCategoryPress(item)}
          delay={index * 100}
        />
      </Box>
    );
  };

  return (
    <Box flex={1} backgroundColor="background">
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <AnimatedBox entering={FadeInDown.delay(100)} paddingTop="xl_32" paddingHorizontal="lg_24">
          <Text variant="h_32SemiBold_Page" textAlign="center" marginBottom="xs_8">
            O que você quer criar?
          </Text>
          <Text
            variant="b_16Regular_input"
            color="textSecondary"
            textAlign="center"
            marginBottom="xl_32">
            Escolha uma categoria para começar
          </Text>
        </AnimatedBox>

        {/* Quick Stats */}
        <AnimatedBox
          entering={FadeInDown.delay(200)}
          flexDirection="row"
          justifyContent="space-around"
          marginBottom="lg_24"
          paddingHorizontal="lg_24">
          <Box alignItems="center">
            <Box
              backgroundColor="primary"
              opacity={0.15}
              borderRadius="circle_9999"
              padding="sm_12"
              marginBottom="xs_8">
              <Users size={24} color={theme.colors.primary} weight="bold" />
            </Box>
            <Text variant="h_20Medium_subsection" color="text">
              2.5k+
            </Text>
            <Text variant="l_12Regular_helperText" color="textSecondary">
              Eventos criados
            </Text>
          </Box>

          <Box alignItems="center">
            <Box
              backgroundColor="success"
              opacity={0.15}
              borderRadius="circle_9999"
              padding="sm_12"
              marginBottom="xs_8">
              <BeerStein size={24} color={theme.colors.success} weight="bold" />
            </Box>
            <Text variant="h_20Medium_subsection" color="text">
              98%
            </Text>
            <Text variant="l_12Regular_helperText" color="textSecondary">
              Satisfação
            </Text>
          </Box>

          <Box alignItems="center">
            <Box
              backgroundColor="warning"
              opacity={0.15}
              borderRadius="circle_9999"
              padding="sm_12"
              marginBottom="xs_8">
              <Lightning size={24} color={theme.colors.warning} weight="bold" />
            </Box>
            <Text variant="h_20Medium_subsection" color="text">
              5min
            </Text>
            <Text variant="l_12Regular_helperText" color="textSecondary">
              Para criar
            </Text>
          </Box>
        </AnimatedBox>

        {/* Categories Grid */}
        <Box paddingHorizontal="md_16">
          <FlatList
            data={categories}
            renderItem={renderCategoryCard}
            keyExtractor={item => item.id}
            numColumns={2}
            scrollEnabled={false}
            columnWrapperStyle={{
              justifyContent: 'space-between',
            }}
            contentContainerStyle={{
              paddingBottom: theme.spacing.xxxl_48,
            }}
          />
        </Box>
      </ScrollView>
    </Box>
  );
};

export default CreationHubScreen;
