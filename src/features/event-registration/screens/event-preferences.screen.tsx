import React, { useState } from 'react';

import { TouchableOpacity } from 'react-native';

import { useNavigation } from '@react-navigation/native';
import { PlusCircle } from 'phosphor-react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';

import { Box, Text, useTheme } from '@/src/core/theme';
import { Button, Card, Divider, Switch } from '@/src/shared/components';

import { EventProgressIndicator } from '../components/EventProgressIndicator';
import { PreferenceItem } from '../components/PreferenceItem';
import { useCreateEvent } from '../hooks/useCreateEvent';
import { useEventDraft } from '../hooks/useEventDraft';
import { EventPreferences } from '../types/event.types';

export const EventPreferencesScreen = () => {
  const navigation = useNavigation<any>();
  const { draft, updatePreferences, currentStep, getCompleteDraft } = useEventDraft();
  const createEvent = useCreateEvent();
  const theme = useTheme();
  const insets = useSafeAreaInsets();

  const [preferences, setPreferences] = useState<EventPreferences>({
    notifyRadarUsers: draft.preferences?.notifyRadarUsers ?? true,
    drinksAvailable: draft.preferences?.drinksAvailable ?? false,
    privateListing: draft.preferences?.privateListing ?? true,
    allowGroups: draft.preferences?.allowGroups ?? true,
    ...draft.preferences,
  });

  const handlePreferenceChange = (key: keyof EventPreferences, value: boolean) => {
    setPreferences(prev => ({ ...prev, [key]: value }));
  };

  const handleDone = async () => {
    updatePreferences(preferences);

    const completeEvent = getCompleteDraft();
    if (completeEvent) {
      createEvent.mutate({
        ...completeEvent,
        preferences,
      });
    }
  };

  const handleAddNote = () => {
    // Would open a modal or navigate to a note input screen
    console.log('Add drink notes');
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: theme.colors.background }} edges={['bottom']}>
      <Box flex={1} backgroundColor="background">
        <EventProgressIndicator currentStep={currentStep} />

        <Box padding="md_16" flex={1}>
          <Card>
            <Box
              flexDirection="row"
              justifyContent="space-between"
              alignItems="center"
              marginBottom="sm_12">
              <Text variant="l_10SemiBold_tag" color="textSecondary">
                PREFERENCES
              </Text>
              <Text variant="l_10SemiBold_tag" color="textSecondary">
                STEP {currentStep}/3
              </Text>
            </Box>

            <PreferenceItem
              title="Notify user with party radar enabled"
              value={preferences.notifyRadarUsers}
              onValueChange={value => handlePreferenceChange('notifyRadarUsers', value)}
            />

            <Divider />

            <PreferenceItem
              title="Drinks available"
              value={preferences.drinksAvailable}
              onValueChange={value => handlePreferenceChange('drinksAvailable', value)}
              action={
                <Box flexDirection="row" alignItems="center" gap="md_16">
                  {preferences.drinksAvailable && (
                    <TouchableOpacity onPress={handleAddNote}>
                      <Box flexDirection="row" alignItems="center" gap="xxs_4">
                        <Text variant="l_12Medium_message" color="textSecondary">
                          Add note
                        </Text>
                        <PlusCircle size={14} color={theme.colors.iconPrimary} />
                      </Box>
                    </TouchableOpacity>
                  )}
                  <Switch
                    isChecked={preferences.drinksAvailable}
                    onToggle={value => handlePreferenceChange('drinksAvailable', value)}
                  />
                </Box>
              }
            />

            <Divider />

            <PreferenceItem
              title="Private listing"
              value={preferences.privateListing}
              onValueChange={value => handlePreferenceChange('privateListing', value)}
            />

            <Divider />

            <PreferenceItem
              title="Allow groups"
              value={preferences.allowGroups}
              onValueChange={value => handlePreferenceChange('allowGroups', value)}
            />
          </Card>
        </Box>

        {/* Bottom Actions */}
        <Box
          backgroundColor="surface"
          borderTopWidth={1}
          borderTopColor="border"
          paddingHorizontal="md_16"
          flexDirection="column"
          paddingVertical="md_16"
          style={{ paddingBottom: insets.bottom + theme.spacing.md_16 }}
          gap="sm_12">
          <Button
            title="Done"
            variant="primary"
            onPress={handleDone}
            loading={createEvent.isPending}
            enabled={!createEvent.isPending}
          />

          <Button
            title="Back"
            variant="outline"
            onPress={() => navigation.goBack()}
            enabled={!createEvent.isPending}
          />
        </Box>
      </Box>
    </SafeAreaView>
  );
};
