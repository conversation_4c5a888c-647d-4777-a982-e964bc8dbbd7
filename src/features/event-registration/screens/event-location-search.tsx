import React, { useCallback, useEffect, useMemo } from 'react';

import { FlatList, Keyboard, Platform, Pressable } from 'react-native';

import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import * as Location from 'expo-location';
import { CaretDown, MagnifyingGlass, MapPinArea } from 'phosphor-react-native';
import Animated, { FadeInDown } from 'react-native-reanimated';
import { SafeAreaView } from 'react-native-safe-area-context';

import { FOURSQUARE_CATEGORIES, FoursquarePlace } from '@/src/core';
import {
  EventRegistrationStackScreenProps,
  EventsStackScreenProps,
} from '@/src/core/navigation/types';
import { Box, Row, Text, TextInput, useTheme } from '@/src/core/theme';
import { useResponsive } from '@/src/core/theme/useResponsive';
import { Divider } from '@/src/shared/components';

import { useEventRegistrationContext } from '../context/EventRegistrationContext';
import { useFoursquareLocationSearch } from '../hooks/useFoursquareLocationSearch';

// Memoized location item component to prevent unnecessary re-renders
const LocationItem = React.memo(
  ({
    item,
    onPress,
    iconSize,
    iconColor,
  }: {
    item: FoursquarePlace;
    onPress: (item: FoursquarePlace) => void;
    iconSize: number;
    iconColor: string;
  }) => {
    return (
      <Pressable
        onPress={() => onPress(item)}
        style={({ pressed }) => ({
          opacity: pressed ? 0.7 : 1,
        })}>
        <Box
          flexDirection="row"
          alignItems="center"
          paddingHorizontal="md_16"
          paddingVertical="sm_12"
          backgroundColor="background">
          <Box marginRight="sm_12">
            <MapPinArea size={iconSize} color={iconColor} />
          </Box>
          <Box flex={1}>
            <Text variant="b_16SemiBold_button" color="text" numberOfLines={1}>
              {item.name}
            </Text>
            <Text variant="b_14Regular_content" color="textSecondary" numberOfLines={2}>
              {item.location?.formatted_address || 'Address not available'}
            </Text>
            {item.categories && item.categories.length > 0 && (
              <Text variant="l_12Regular_helperText" color="textTertiary" numberOfLines={1}>
                {item.categories[0].name}
              </Text>
            )}
          </Box>
          {item.distance && (
            <Text variant="l_12Regular_helperText" color="textSecondary">
              {Math.round(item.distance)}m
            </Text>
          )}
        </Box>
      </Pressable>
    );
  }
);

LocationItem.displayName = 'LocationItem';

const EventLocationSearchScreen = () => {
  const theme = useTheme();
  const { select } = useResponsive();
  const navigation =
    useNavigation<EventRegistrationStackScreenProps<'EventLocationSearch'>['navigation']>();
  const route = useRoute<EventRegistrationStackScreenProps<'EventLocationSearch'>['route']>();

  const { setSelectedLocation } = useEventRegistrationContext();

  // Initialize Foursquare location search with optimized hook
  const { searchQuery, debouncedQuery, updateSearch, updateLocation, places, isSearching } =
    useFoursquareLocationSearch({
      radius: 10000, // 10km radius for event venues
      limit: 20,
      categories: [FOURSQUARE_CATEGORIES.NIGHTCLUB],
      sort: 'popularity',
      debounceDelay: 300, // Faster debounce
    });

  // Get user location on mount
  useEffect(() => {
    const getCurrentLocation = async () => {
      try {
        if (Platform.OS === 'ios') {
          const { status } = await Location.requestForegroundPermissionsAsync();
          if (status !== 'granted') {
            console.warn('Location permission denied');
            return;
          }
        }

        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.Balanced,
        });

        updateLocation(location.coords.latitude, location.coords.longitude);
      } catch (error) {
        console.warn('Failed to get current location:', error);
        // Fallback to a default location (e.g., city center)
        updateLocation(40.7128, -74.006); // NYC as fallback
      }
    };

    getCurrentLocation();
  }, [updateLocation]);

  // Handle location selection
  const handleSelectLocation = useCallback(
    (place: FoursquarePlace) => {
      Keyboard.dismiss();

      // Extract location data from FoursquarePlace
      const locationData = {
        id: place.fsq_place_id,
        name: place.name,
        address: place.location?.formatted_address || 'Address not available',
        latitude: place.geocodes?.main?.latitude || place.latitude || 0,
        longitude: place.geocodes?.main?.longitude || place.longitude || 0,
      };

      // Navigate back with the selected location as a parameter
      // navigation.navigate('EventDetails', {
      //   selectedLocation: locationData,
      // });

      setSelectedLocation(locationData);

      navigation.goBack();
    },
    [navigation, setSelectedLocation]
  );

  // Memoized values for icon props
  const iconSize = useMemo(() => select({ phone: 20, tablet: 24 }), [select]);
  const iconColor = theme.colors.textSecondary;

  // Render location item with memoized component
  const renderLocationItem = useCallback(
    ({ item }: { item: FoursquarePlace }) => (
      <LocationItem
        item={item}
        onPress={handleSelectLocation}
        iconSize={iconSize}
        iconColor={iconColor}
      />
    ),
    [handleSelectLocation, iconSize, iconColor]
  );

  // Memoized search results
  const searchResults = useMemo(() => {
    if (debouncedQuery.length < 2) return [];
    return places || [];
  }, [places, debouncedQuery]);

  // Memoized keyExtractor
  const keyExtractor = useCallback((item: FoursquarePlace) => item.fsq_place_id, []);

  // Memoized empty states
  const EmptyState = useMemo(() => {
    if (searchQuery.length >= 2 && !isSearching && searchResults.length === 0) {
      return (
        <Animated.View
          entering={FadeInDown.duration(300)}
          style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
          <Box padding="xl_32" alignItems="center">
            <MaterialIcons name="location-off" size={48} color={theme.colors.textTertiary} />
            <Text variant="b_16Regular_input" color="textSecondary" marginTop="md_16">
              No locations found
            </Text>
            <Text
              variant="b_14Regular_content"
              color="textTertiary"
              textAlign="center"
              marginTop="xs_8">
              Try searching for a different venue or address
            </Text>
          </Box>
        </Animated.View>
      );
    }

    if (searchQuery.length < 2) {
      return (
        <Box flex={1} justifyContent="center" alignItems="center">
          <Box padding="xl_32" alignItems="center">
            <MagnifyingGlass size={48} color={theme.colors.textTertiary} />
            <Text variant="b_16Regular_input" color="textSecondary" marginTop="md_16">
              Search for a location
            </Text>
            <Text
              variant="b_14Regular_content"
              color="textTertiary"
              textAlign="center"
              marginTop="xs_8">
              Enter a venue name or address to get started
            </Text>
          </Box>
        </Box>
      );
    }

    return null;
  }, [searchQuery.length, isSearching, searchResults.length, theme.colors.textTertiary]);

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <FlatList
        ListHeaderComponent={
          <Box bg="background" paddingHorizontal="md_16" paddingTop="sm_12">
            <Row alignItems="center" marginBottom="sm_12">
              <Pressable
                onPress={() => navigation.goBack()}
                style={({ pressed }) => ({ opacity: pressed ? 0.7 : 1 })}>
                <Box padding="xs_8">
                  <CaretDown size={select({ phone: 24, tablet: 28 })} color={theme.colors.text} />
                </Box>
              </Pressable>
              <Text variant="h_18SemiBold_cardTitle" color="text" marginLeft="sm_12">
                Search Location
              </Text>
            </Row>

            {/* Search Input */}
            <TextInput
              placeholder="Search for venues, addresses..."
              value={searchQuery}
              label="Search location"
              onChangeText={updateSearch}
              leading={
                <MagnifyingGlass
                  size={select({ phone: 20, tablet: 24 })}
                  color={theme.colors.textSecondary}
                  weight="regular"
                />
              }
              showClearButton
              autoCorrect={false}
              autoCapitalize="none"
              returnKeyType="search"
            />
          </Box>
        }
        stickyHeaderIndices={[0]}
        data={searchResults}
        renderItem={renderLocationItem}
        keyExtractor={keyExtractor}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
        ItemSeparatorComponent={Divider}
        contentContainerStyle={{
          paddingBottom: 20,
          flexGrow: searchResults.length < 5 ? 1 : undefined,
        }}
        contentInset={{ bottom: 10 }}
        contentInsetAdjustmentBehavior="automatic"
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        windowSize={10}
        initialNumToRender={10}
        keyboardDismissMode="on-drag"
        ListEmptyComponent={EmptyState}
      />
    </SafeAreaView>
  );
};

export default EventLocationSearchScreen;
