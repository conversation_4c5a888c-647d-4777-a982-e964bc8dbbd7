import { useState } from 'react';

import { ScrollView, TouchableOpacity } from 'react-native';

import { useNavigation } from '@react-navigation/native';
import {
  Calendar,
  Car,
  Clock,
  MapPin,
  Money,
  Phone,
  Shield,
  UserPlus,
  Users,
} from 'phosphor-react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { EventRegistrationStackScreenProps } from '@/src/core/navigation/types';
import { Box, Text, TextInput, useTheme } from '@/src/core/theme';
import { Button, Card } from '@/src/shared/components';

import { useEventRegistrationContext } from '../context/EventRegistrationContext';

type GroupActivity = 'bar-crawl' | 'show' | 'dinner' | 'club' | 'other';
type SplitType = 'equal' | 'individual' | 'organizer';

const GroupMeetupScreen = () => {
  const theme = useTheme();
  const navigation =
    useNavigation<EventRegistrationStackScreenProps<'GroupMeetup'>['navigation']>();
  const { selectedTemplate } = useEventRegistrationContext();

  const [groupDetails, setGroupDetails] = useState({
    title: '',
    activity: '' as GroupActivity,
    customActivity: '',
    meetingPoint: '',
    date: '',
    time: '',
    maxPeople: '8',
    minAge: '18',
    splitType: 'equal' as SplitType,
    estimatedCost: '',
    transportMethod: '',
    safetyPhone: '',
    description: '',
    rules: [] as string[],
  });

  const activities = [
    { id: 'bar-crawl', label: 'Bar Crawl', emoji: '🍻' },
    { id: 'show', label: 'Show/Festa', emoji: '🎵' },
    { id: 'dinner', label: 'Jantar', emoji: '🍽️' },
    { id: 'club', label: 'Balada', emoji: '🕺' },
    { id: 'other', label: 'Outro', emoji: '✨' },
  ];

  const splitTypes = [
    { id: 'equal', label: 'Dividir igual', icon: '➗' },
    { id: 'individual', label: 'Cada um paga o seu', icon: '💵' },
    { id: 'organizer', label: 'Organizador paga', icon: '🎁' },
  ];

  const transportOptions = [
    { id: 'uber', label: 'Uber/99', icon: '🚗' },
    { id: 'metro', label: 'Metrô', icon: '🚇' },
    { id: 'walk', label: 'A pé', icon: '🚶' },
    { id: 'meet', label: 'Encontrar lá', icon: '📍' },
  ];

  const safetyRules = [
    { id: 'verified', label: 'Apenas perfis verificados', enabled: true },
    { id: 'contact', label: 'Compartilhar contato de emergência', enabled: true },
    { id: 'public', label: 'Encontro em local público', enabled: true },
    { id: 'friends', label: 'Pode levar +1 amigo', enabled: false },
  ];

  const toggleRule = (ruleId: string) => {
    const currentRules = groupDetails.rules;
    if (currentRules.includes(ruleId)) {
      setGroupDetails({
        ...groupDetails,
        rules: currentRules.filter(r => r !== ruleId),
      });
    } else {
      setGroupDetails({
        ...groupDetails,
        rules: [...currentRules, ruleId],
      });
    }
  };

  const isValid = () => {
    const baseValid = !!(
      groupDetails.title &&
      groupDetails.activity &&
      groupDetails.meetingPoint &&
      groupDetails.date &&
      groupDetails.time &&
      groupDetails.maxPeople
    );

    if (groupDetails.activity === 'other' && !groupDetails.customActivity) {
      return false;
    }

    return baseValid;
  };

  const handleSubmit = () => {
    // TODO: Implement group creation logic
    navigation.navigate('EventSuccess', {
      eventId: 'group-' + Date.now(),
      eventName: groupDetails.title,
    });
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: theme.colors.background }}>
      <ScrollView>
        <Box flex={1} padding="lg_24">
          {/* Header */}
          <Box marginBottom="lg_24">
            <Text variant="h_24SemiBold_section" marginBottom="xs_8">
              {selectedTemplate?.emoji} Criar Grupo
            </Text>
            <Text variant="b_16Regular_input" color="textSecondary">
              Forme um grupo para sair junto com segurança
            </Text>
          </Box>

          {/* Basic Info */}
          <Card padding="md_16" marginBottom="md_16">
            <Text variant="b_14Medium_button" color="textSecondary" marginBottom="md_16">
              O que vamos fazer?
            </Text>

            <TextInput
              label="Título do grupo"
              placeholder="Ex: Galera do rock no show do Foo Fighters"
              value={groupDetails.title}
              onChangeText={text => setGroupDetails({ ...groupDetails, title: text })}
              containerProps={{ marginBottom: 'md_16' }}
              autoFocus
            />

            {/* Activity Selection */}
            <Box marginBottom="md_16">
              <Text variant="b_14Medium_button" color="textSecondary" marginBottom="xs_8">
                Tipo de atividade
              </Text>
              <Box flexDirection="row" flexWrap="wrap" gap="xs_8">
                {activities.map(activity => (
                  <TouchableOpacity
                    key={activity.id}
                    onPress={() =>
                      setGroupDetails({ ...groupDetails, activity: activity.id as GroupActivity })
                    }>
                    <Box
                      paddingHorizontal="md_16"
                      paddingVertical="xs_8"
                      borderRadius="lg_16"
                      backgroundColor={
                        groupDetails.activity === activity.id ? 'primary' : 'surfaceBackground'
                      }
                      borderWidth={1}
                      borderColor={groupDetails.activity === activity.id ? 'primary' : 'border'}
                      flexDirection="row"
                      alignItems="center">
                      <Text variant="b_16Regular_input" marginRight="xxs_4">
                        {activity.emoji}
                      </Text>
                      <Text
                        variant="b_14Medium_button"
                        color={groupDetails.activity === activity.id ? 'white' : 'text'}>
                        {activity.label}
                      </Text>
                    </Box>
                  </TouchableOpacity>
                ))}
              </Box>
            </Box>

            {groupDetails.activity === 'other' && (
              <TextInput
                label="Qual atividade?"
                placeholder="Descreva o que vocês vão fazer"
                value={groupDetails.customActivity}
                onChangeText={text => setGroupDetails({ ...groupDetails, customActivity: text })}
                containerProps={{ marginBottom: 'md_16' }}
              />
            )}
          </Card>

          {/* Meeting Details */}
          <Card padding="md_16" marginBottom="md_16">
            <Text variant="b_14Medium_button" color="textSecondary" marginBottom="md_16">
              Onde e quando?
            </Text>

            <TextInput
              label="Ponto de encontro"
              placeholder="Local para se encontrarem"
              value={groupDetails.meetingPoint}
              onChangeText={text => setGroupDetails({ ...groupDetails, meetingPoint: text })}
              leading={<MapPin size={20} color={theme.colors.textSecondary} />}
              containerProps={{ marginBottom: 'md_16' }}
            />

            <Box flexDirection="row" gap="sm_12" marginBottom="md_16">
              <Box flex={1}>
                <TextInput
                  label="Data"
                  placeholder="DD/MM/AAAA"
                  value={groupDetails.date}
                  onChangeText={text => setGroupDetails({ ...groupDetails, date: text })}
                  leading={<Calendar size={16} color={theme.colors.textSecondary} />}
                />
              </Box>

              <Box flex={1}>
                <TextInput
                  label="Horário"
                  placeholder="HH:MM"
                  value={groupDetails.time}
                  onChangeText={text => setGroupDetails({ ...groupDetails, time: text })}
                  leading={<Clock size={16} color={theme.colors.textSecondary} />}
                />
              </Box>
            </Box>

            {/* Transport Method */}
            <Box>
              <Text variant="b_14Medium_button" color="textSecondary" marginBottom="xs_8">
                Como vamos?
              </Text>
              <Box flexDirection="row" gap="xs_8">
                {transportOptions.map(transport => (
                  <TouchableOpacity
                    key={transport.id}
                    onPress={() =>
                      setGroupDetails({ ...groupDetails, transportMethod: transport.id })
                    }>
                    <Box
                      paddingHorizontal="sm_12"
                      paddingVertical="xxs_4"
                      borderRadius="lg_16"
                      backgroundColor={
                        groupDetails.transportMethod === transport.id
                          ? 'primary'
                          : 'surfaceBackground'
                      }
                      borderWidth={1}
                      borderColor={
                        groupDetails.transportMethod === transport.id ? 'primary' : 'border'
                      }>
                      <Text
                        variant="l_12Medium_message"
                        color={groupDetails.transportMethod === transport.id ? 'white' : 'text'}>
                        {transport.icon} {transport.label}
                      </Text>
                    </Box>
                  </TouchableOpacity>
                ))}
              </Box>
            </Box>
          </Card>

          {/* Group Settings */}
          <Card padding="md_16" marginBottom="md_16">
            <Text variant="b_14Medium_button" color="textSecondary" marginBottom="md_16">
              <Users size={16} color={theme.colors.textSecondary} /> Configurações do grupo
            </Text>

            <Box flexDirection="row" gap="sm_12" marginBottom="md_16">
              <Box flex={1}>
                <TextInput
                  label="Máx. pessoas"
                  placeholder="8"
                  value={groupDetails.maxPeople}
                  onChangeText={text => setGroupDetails({ ...groupDetails, maxPeople: text })}
                  leading={<UserPlus size={16} color={theme.colors.textSecondary} />}
                  keyboardType="numeric"
                />
              </Box>

              <Box flex={1}>
                <TextInput
                  label="Idade mínima"
                  placeholder="18"
                  value={groupDetails.minAge}
                  onChangeText={text => setGroupDetails({ ...groupDetails, minAge: text })}
                  keyboardType="numeric"
                />
              </Box>
            </Box>

            {/* Cost Split */}
            <Box marginBottom="md_16">
              <Text variant="b_14Medium_button" color="textSecondary" marginBottom="xs_8">
                <Money size={16} color={theme.colors.textSecondary} /> Como dividir os custos?
              </Text>
              <Box flexDirection="row" gap="xs_8">
                {splitTypes.map(split => (
                  <TouchableOpacity
                    key={split.id}
                    onPress={() =>
                      setGroupDetails({ ...groupDetails, splitType: split.id as SplitType })
                    }
                    style={{ flex: 1 }}>
                    <Box
                      backgroundColor={
                        groupDetails.splitType === split.id ? 'primary' : 'surfaceBackground'
                      }
                      borderRadius="md_12"
                      padding="sm_12"
                      alignItems="center"
                      borderWidth={1}
                      borderColor={groupDetails.splitType === split.id ? 'primary' : 'border'}>
                      <Text
                        variant="h_20Medium_subsection"
                        color={groupDetails.splitType === split.id ? 'white' : 'text'}
                        marginBottom="xxs_4">
                        {split.icon}
                      </Text>
                      <Text
                        variant="l_12Medium_message"
                        color={groupDetails.splitType === split.id ? 'white' : 'text'}
                        textAlign="center">
                        {split.label}
                      </Text>
                    </Box>
                  </TouchableOpacity>
                ))}
              </Box>
            </Box>

            {groupDetails.splitType !== 'organizer' && (
              <TextInput
                label="Custo estimado por pessoa"
                placeholder="R$ 50,00"
                value={groupDetails.estimatedCost}
                onChangeText={text => setGroupDetails({ ...groupDetails, estimatedCost: text })}
                leading={<Money size={16} color={theme.colors.textSecondary} />}
              />
            )}
          </Card>

          {/* Safety Rules */}
          <Card padding="md_16" marginBottom="md_16">
            <Text variant="b_14Medium_button" color="textSecondary" marginBottom="md_16">
              <Shield size={16} color={theme.colors.success} /> Regras de segurança
            </Text>

            {safetyRules.map(rule => (
              <TouchableOpacity key={rule.id} onPress={() => toggleRule(rule.id)}>
                <Box
                  flexDirection="row"
                  alignItems="center"
                  justifyContent="space-between"
                  paddingVertical="sm_12"
                  borderBottomWidth={1}
                  borderBottomColor="border">
                  <Text variant="b_14Regular_content" flex={1}>
                    {rule.label}
                  </Text>
                  <Box
                    width={20}
                    height={20}
                    borderRadius="xxs_2"
                    backgroundColor={
                      groupDetails.rules.includes(rule.id) || rule.enabled
                        ? 'success'
                        : 'surfaceBackground'
                    }
                    borderWidth={1}
                    borderColor={
                      groupDetails.rules.includes(rule.id) || rule.enabled ? 'success' : 'border'
                    }
                    alignItems="center"
                    justifyContent="center">
                    {(groupDetails.rules.includes(rule.id) || rule.enabled) && (
                      <Text variant="l_12Medium_message" color="white">
                        ✓
                      </Text>
                    )}
                  </Box>
                </Box>
              </TouchableOpacity>
            ))}

            <TextInput
              label="Telefone de emergência"
              placeholder="Contato de segurança"
              value={groupDetails.safetyPhone}
              onChangeText={text => setGroupDetails({ ...groupDetails, safetyPhone: text })}
              leading={<Phone size={16} color={theme.colors.success} />}
              containerProps={{ marginTop: 'md_16' }}
              keyboardType="phone-pad"
            />
          </Card>

          {/* Description */}
          <Card padding="md_16" marginBottom="lg_24">
            <TextInput
              label="Descrição do rolê"
              placeholder="Conte mais sobre o que vocês vão fazer, dress code, o que levar..."
              value={groupDetails.description}
              onChangeText={text => setGroupDetails({ ...groupDetails, description: text })}
              multiline
              numberOfLines={4}
            />
          </Card>

          {/* Safety Notice */}
          <Box
            backgroundColor="successLight"
            borderRadius="md_12"
            padding="md_16"
            marginBottom="lg_24">
            <Text variant="b_14Medium_button" color="success" marginBottom="xxs_4">
              🛡️ Segurança em primeiro lugar
            </Text>
            <Text variant="l_12Regular_helperText" color="successDark">
              Todos os participantes são verificados e o encontro segue nossas diretrizes de
              segurança
            </Text>
          </Box>

          {/* Action Buttons */}
          <Box flexDirection="row" gap="sm_12">
            <Box flex={1}>
              <Button variant="secondary" onPress={() => navigation.goBack()} title="Voltar" />
            </Box>
            <Box flex={1}>
              <Button
                variant="primary"
                onPress={handleSubmit}
                enabled={isValid()}
                title="Criar Grupo"
              />
            </Box>
          </Box>
        </Box>
      </ScrollView>
    </SafeAreaView>
  );
};

export default GroupMeetupScreen;
