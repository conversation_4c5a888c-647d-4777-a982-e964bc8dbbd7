import React, { useState } from 'react';

import { FlatList, Pressable, ScrollView, TextInput } from 'react-native';

import {
  <PERSON><PERSON>tein,
  Clock,
  Crown,
  Fire,
  Gift,
  House,
  Lightning,
  MagnifyingGlass,
  MapPin,
  MusicNote,
  Plus,
  Repeat,
  Tag,
  Users,
  Wine,
} from 'phosphor-react-native';
import Animated, { FadeInDown } from 'react-native-reanimated';

import { Box, Text, useTheme } from '@/src/core/theme';
import { EventFilterPills, EventTemplateCard, FilterType } from '@/src/features/events/components';
import SegmentedControl from '@/src/shared/components/SegmentedControl/SegmentedControl';

import { useChooseOptions } from '../hooks/useChooseOptions';
import { EventTemplate } from '../types/event.types';

const AnimatedBox = Animated.createAnimatedComponent(Box);

const ChooseOptionsScreen = () => {
  const theme = useTheme();
  const {
    categories,
    searchQuery,
    setSearchQuery,
    handleTemplateSelect,
    handleMagicInput,
    handleQuickHappyHour,
    handleQuickLiveShow,
    handleQuickFlashParty,
  } = useChooseOptions();

  const [selectedCategoryIndex, setSelectedCategoryIndex] = useState(0);
  const [selectedFilter, setSelectedFilter] = useState<FilterType>('all');

  // Get icon component based on icon name
  const getIconForTemplate = (iconName: string) => {
    const iconProps = {
      size: 28,
      color: theme.colors.white,
      weight: 'regular' as const,
    };

    switch (iconName) {
      case 'home':
        return <House {...iconProps} />;
      case 'beer':
        return <BeerStein {...iconProps} />;
      case 'zap':
        return <Lightning {...iconProps} />;
      case 'tag':
        return <Tag {...iconProps} />;
      case 'clock':
        return <Clock {...iconProps} />;
      case 'music':
        return <MusicNote {...iconProps} />;
      case 'repeat':
        return <Repeat {...iconProps} />;
      case 'users':
        return <Users {...iconProps} />;
      case 'map-pin':
        return <MapPin {...iconProps} />;
      default:
        return <Plus {...iconProps} />;
    }
  };

  const renderTemplateCard = (template: EventTemplate) => {
    // Always use icon, never emoji
    const icon = getIconForTemplate(template.icon);

    // Determine icon background color based on template properties
    let iconBgColor = 'primaryLight';
    if (template.revenueCapable) {
      iconBgColor = 'successLight';
    } else if (template.category === 'flash') {
      iconBgColor = 'errorLight';
    } else if (template.category === 'venue') {
      iconBgColor = 'secondaryLight';
    }

    return (
      <EventTemplateCard
        icon={icon}
        title={template.name}
        subtitle={template.description}
        iconBgColor={iconBgColor}
        onPress={() => handleTemplateSelect(template)}
        revenueCapable={template.revenueCapable}
      />
    );
  };

  // Get templates for selected category
  const selectedCategory = categories[selectedCategoryIndex];
  const filteredTemplates =
    selectedCategory?.templates.filter(template => {
      if (selectedFilter === 'all') return true;

      // Map template properties to filter types
      const templateFilters: FilterType[] = [];
      if (template.revenueCapable) templateFilters.push('specials');
      if (template.category === 'venue') templateFilters.push('vip');
      if (template.category === 'flash') templateFilters.push('urgent');
      if (template.featured) templateFilters.push('trending');
      if (template.musicGenre) templateFilters.push('music');

      return templateFilters.includes(selectedFilter);
    }) || [];

  const categorySegments = categories.map(cat => ({ title: cat.label }));

  return (
    <Box flex={1} backgroundColor="background">
      <ScrollView showsVerticalScrollIndicator={false}>
        <Box paddingTop="lg_24">
          {/* Header */}
          <AnimatedBox entering={FadeInDown.delay(100)} paddingHorizontal="lg_24">
            <Text variant="h_32SemiBold_Page" textAlign="center" marginBottom="xs_8">
              O que tá rolando?
            </Text>
            <Text
              variant="b_16Regular_input"
              color="textSecondary"
              textAlign="center"
              marginBottom="lg_24">
              Eventos, promos, shows, ingressos... Conte pra galera!
            </Text>
          </AnimatedBox>

          {/* Category Selector */}
          <AnimatedBox entering={FadeInDown.delay(200)} marginBottom="md_16">
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <Box paddingHorizontal="lg_24">
                <SegmentedControl
                  items={categorySegments}
                  selected={selectedCategoryIndex}
                  onValueChange={setSelectedCategoryIndex}
                />
              </Box>
            </ScrollView>
          </AnimatedBox>

          {/* Search Bar */}
          <AnimatedBox
            entering={FadeInDown.delay(300)}
            marginBottom="sm_12"
            paddingHorizontal="lg_24">
            <Box
              backgroundColor="surfaceBackground"
              borderRadius="md_12"
              paddingHorizontal="md_16"
              paddingVertical="sm_12"
              flexDirection="row"
              alignItems="center"
              gap="sm_12">
              <MagnifyingGlass size={20} color={theme.colors.textSecondary} />
              <TextInput
                value={searchQuery}
                onChangeText={setSearchQuery}
                onSubmitEditing={handleMagicInput}
                placeholder="festa em casa hoje às 20h..."
                placeholderTextColor={theme.colors.textTertiary}
                style={{
                  flex: 1,
                  fontFamily: theme.textVariants.defaults.fontFamily,
                  fontSize: 16,
                  color: theme.colors.text,
                }}
                returnKeyType="go"
              />
            </Box>
          </AnimatedBox>

          {/* Filter Pills */}
          <AnimatedBox entering={FadeInDown.delay(400)} marginBottom="md_16">
            <EventFilterPills selectedFilter={selectedFilter} onFilterChange={setSelectedFilter} />
          </AnimatedBox>

          {/* Quick Actions */}
          <AnimatedBox entering={FadeInDown.delay(500)} marginBottom="md_16">
            <Text
              variant="b_14Medium_button"
              color="textSecondary"
              marginBottom="sm_12"
              paddingHorizontal="lg_24">
              Ações rápidas:
            </Text>

            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={{ paddingHorizontal: theme.spacing.lg_24 }}>
              <Box flexDirection="row" gap="xs_8">
                {/* Happy Hour */}
                <Pressable onPress={handleQuickHappyHour}>
                  <Box
                    backgroundColor="warningDark"
                    borderRadius="md_12"
                    paddingVertical="xs_8"
                    paddingHorizontal="sm_12"
                    flexDirection="row"
                    alignItems="center"
                    gap="xs_8">
                    <Wine size={16} color={theme.colors.white} weight="bold" />
                    <Text variant="b_14Medium_button" color="white">
                      Happy Hour
                    </Text>
                  </Box>
                </Pressable>

                {/* Live Music */}
                <Pressable onPress={handleQuickLiveShow}>
                  <Box
                    backgroundColor="primaryDark"
                    borderRadius="md_12"
                    paddingVertical="xs_8"
                    paddingHorizontal="sm_12"
                    flexDirection="row"
                    alignItems="center"
                    gap="xs_8">
                    <MusicNote size={16} color={theme.colors.white} weight="bold" />
                    <Text variant="b_14Medium_button" color="white">
                      Show ao Vivo
                    </Text>
                  </Box>
                </Pressable>

                {/* Flash Party */}
                <Pressable onPress={handleQuickFlashParty}>
                  <Box
                    backgroundColor="secondary"
                    borderRadius="md_12"
                    paddingVertical="xs_8"
                    paddingHorizontal="sm_12"
                    flexDirection="row"
                    alignItems="center"
                    gap="xs_8">
                    <Lightning size={16} color={theme.colors.white} weight="bold" />
                    <Text variant="b_14Medium_button" color="white">
                      Flash Party
                    </Text>
                  </Box>
                </Pressable>
              </Box>
            </ScrollView>
          </AnimatedBox>

          {/* Templates Grid */}
          <AnimatedBox entering={FadeInDown.delay(600)} paddingHorizontal="lg_24">
            {selectedCategory && (
              <>
                <Text variant="h_20Medium_subsection" marginBottom="md_16">
                  {selectedCategory.label}
                </Text>
                <FlatList
                  data={filteredTemplates}
                  keyExtractor={item => item.id}
                  numColumns={2}
                  scrollEnabled={false}
                  columnWrapperStyle={{
                    gap: theme.spacing.sm_12,
                  }}
                  ItemSeparatorComponent={() => <Box height={theme.spacing.sm_12} />}
                  renderItem={({ item }) => <Box flex={1}>{renderTemplateCard(item)}</Box>}
                  ListEmptyComponent={
                    <Box alignItems="center" paddingVertical="xxxl_48">
                      <Text variant="b_16Regular_input" color="textSecondary">
                        Nenhum template encontrado.
                      </Text>
                    </Box>
                  }
                />
              </>
            )}
          </AnimatedBox>

          {/* Bottom padding for scroll */}
          <Box height={theme.spacing.xxxl_48} />
        </Box>
      </ScrollView>
    </Box>
  );
};

export default ChooseOptionsScreen;
