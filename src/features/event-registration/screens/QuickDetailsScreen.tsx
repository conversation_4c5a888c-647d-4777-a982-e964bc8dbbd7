import { useState } from 'react';

import { ScrollView, TouchableOpacity } from 'react-native';

import { useNavigation } from '@react-navigation/native';
import { Camera, Clock, MapPin } from 'phosphor-react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { EventRegistrationStackScreenProps } from '@/src/core/navigation/types';
import { Box, Text, TextInput, useTheme } from '@/src/core/theme';
import { Button } from '@/src/shared/components';

import { useEventRegistrationContext } from '../context/EventRegistrationContext';

const QuickDetailsScreen = () => {
  const theme = useTheme();
  const navigation =
    useNavigation<EventRegistrationStackScreenProps<'CreateEventDetails'>['navigation']>();
  const { selectedTemplate } = useEventRegistrationContext();

  // Quick details state
  const [quickDetails, setQuickDetails] = useState({
    venue: '',
    offer: '',
    artist: '',
    promoCode: '',
    expiresIn: '',
    photo: null as string | null,
  });

  const getTitle = () => {
    const titles: Record<string, string> = {
      'promo-share': 'Qual o desconto?',
      'happy-hour-alert': 'Que promoção tá rolando?',
      'live-show-alert': 'Quem tá tocando?',
    };
    return titles[selectedTemplate?.id || ''] || 'Detalhes rápidos';
  };

  const getInputFields = () => {
    if (!selectedTemplate) return null;

    switch (selectedTemplate.id) {
      case 'promo-share':
        return (
          <>
            <Box marginBottom="md_16">
              <TextInput
                label="Código ou desconto"
                placeholder="Ex: NOITE20, 15% OFF, 2x1 drinks"
                value={quickDetails.promoCode}
                onChangeText={text => setQuickDetails({ ...quickDetails, promoCode: text })}
                autoFocus
              />
            </Box>

            <Box flexDirection="row" gap="sm_12" marginBottom="md_16">
              <Box flex={1}>
                <TextInput
                  label="Local"
                  placeholder="Nome do local"
                  value={quickDetails.venue}
                  onChangeText={text => setQuickDetails({ ...quickDetails, venue: text })}
                  leading={<MapPin size={16} color={theme.colors.textSecondary} />}
                />
              </Box>

              <Box flex={1}>
                <TextInput
                  label="Válido até"
                  placeholder="Selecionar"
                  value={quickDetails.expiresIn}
                  onChangeText={text => setQuickDetails({ ...quickDetails, expiresIn: text })}
                  leading={<Clock size={16} color={theme.colors.textSecondary} />}
                />
              </Box>
            </Box>
          </>
        );

      case 'happy-hour-alert':
        return (
          <>
            <Box marginBottom="md_16">
              <TextInput
                label="Qual a oferta?"
                placeholder="Ex: 50% todas as bebidas, Cerveja R$ 5"
                value={quickDetails.offer}
                onChangeText={text => setQuickDetails({ ...quickDetails, offer: text })}
                autoFocus
              />
            </Box>

            <Box marginBottom="md_16">
              <TextInput
                label="Nome do local"
                placeholder="Bar, restaurante, balada..."
                value={quickDetails.venue}
                onChangeText={text => setQuickDetails({ ...quickDetails, venue: text })}
                leading={<MapPin size={20} color={theme.colors.textSecondary} />}
              />
            </Box>

            <Box marginBottom="md_16">
              <Text variant="b_14Medium_button" color="textSecondary" marginBottom="xs_8">
                Horário do happy hour
              </Text>
              <Box flexDirection="row" gap="xs_8">
                {['17:00-19:00', '18:00-20:00', '19:00-21:00'].map(time => (
                  <TouchableOpacity
                    key={time}
                    onPress={() => setQuickDetails({ ...quickDetails, expiresIn: time })}>
                    <Box
                      paddingHorizontal="md_16"
                      paddingVertical="xs_8"
                      borderRadius="lg_16"
                      backgroundColor={
                        quickDetails.expiresIn === time ? 'primary' : 'surfaceBackground'
                      }
                      borderWidth={1}
                      borderColor={quickDetails.expiresIn === time ? 'primary' : 'border'}>
                      <Text
                        variant="b_14Medium_button"
                        color={quickDetails.expiresIn === time ? 'white' : 'text'}>
                        {time}
                      </Text>
                    </Box>
                  </TouchableOpacity>
                ))}
              </Box>
            </Box>
          </>
        );

      case 'live-show-alert':
        return (
          <>
            <Box marginBottom="md_16">
              <TextInput
                label="Nome do artista ou banda"
                placeholder="Quem está se apresentando?"
                value={quickDetails.artist}
                onChangeText={text => setQuickDetails({ ...quickDetails, artist: text })}
                autoFocus
              />
            </Box>

            <Box marginBottom="md_16">
              <TextInput
                label="Local do show"
                placeholder="Onde está acontecendo?"
                value={quickDetails.venue}
                onChangeText={text => setQuickDetails({ ...quickDetails, venue: text })}
                leading={<MapPin size={20} color={theme.colors.textSecondary} />}
              />
            </Box>

            <Box marginBottom="md_16">
              <Text variant="b_14Medium_button" color="textSecondary" marginBottom="xs_8">
                Gênero musical
              </Text>
              <Box flexDirection="row" flexWrap="wrap" gap="xs_8">
                {selectedTemplate?.defaults?.suggestedGenres?.map(genre => (
                  <TouchableOpacity key={genre}>
                    <Box
                      paddingHorizontal="md_16"
                      paddingVertical="xs_8"
                      borderRadius="lg_16"
                      backgroundColor="surfaceBackground"
                      borderWidth={1}
                      borderColor="border">
                      <Text variant="b_14Medium_button" color="text">
                        {genre}
                      </Text>
                    </Box>
                  </TouchableOpacity>
                ))}
              </Box>
            </Box>
          </>
        );

      default:
        return null;
    }
  };

  const isValid = () => {
    if (!selectedTemplate) return false;

    switch (selectedTemplate.id) {
      case 'promo-share':
        return !!(quickDetails.promoCode && quickDetails.venue);
      case 'happy-hour-alert':
        return !!(quickDetails.offer && quickDetails.venue);
      case 'live-show-alert':
        return !!(quickDetails.artist && quickDetails.venue);
      default:
        return true;
    }
  };

  const handleShare = () => {
    // TODO: Implement sharing logic
    navigation.navigate('EventSuccess', {
      eventId: 'quick-' + Date.now(),
      eventName: selectedTemplate?.name || 'Compartilhamento',
    });
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: theme.colors.background }}>
      <ScrollView>
        <Box flex={1} padding="lg_24">
          {/* Header with template info */}
          <Box
            backgroundColor="surfaceBackground"
            borderRadius="md_12"
            padding="md_16"
            flexDirection="row"
            alignItems="center"
            marginBottom="lg_24">
            <Box
              width={48}
              height={48}
              borderRadius="circle_9999"
              backgroundColor="primary"
              alignItems="center"
              justifyContent="center"
              marginRight="sm_12">
              <Text variant="h_24SemiBold_section">{selectedTemplate?.emoji || '📢'}</Text>
            </Box>
            <Box flex={1}>
              <Text variant="b_16SemiBold_button">{selectedTemplate?.name}</Text>
              <Text variant="l_12Regular_helperText" color="textSecondary">
                Compartilhamento rápido • Expira em 2h
              </Text>
            </Box>
          </Box>

          <Text variant="h_24SemiBold_section" marginBottom="xs_8">
            {getTitle()}
          </Text>
          <Text variant="b_16Regular_input" color="textSecondary" marginBottom="lg_24">
            Quanto mais específico, melhor para a galera encontrar!
          </Text>

          {/* Dynamic input fields */}
          {getInputFields()}

          {/* Photo upload section */}
          {selectedTemplate?.defaults?.requiresPhoto && (
            <Box marginBottom="lg_24">
              <Text variant="b_14Medium_button" color="textSecondary" marginBottom="xs_8">
                Foto como prova (opcional)
              </Text>
              <TouchableOpacity>
                <Box
                  backgroundColor="surfaceBackground"
                  borderRadius="md_12"
                  padding="xl_32"
                  borderWidth={1}
                  borderColor="border"
                  borderStyle="dashed"
                  alignItems="center">
                  <Camera size={32} color={theme.colors.textTertiary} />
                  <Text variant="b_14Regular_content" color="textTertiary" marginTop="sm_12">
                    Adicionar foto do cardápio/local
                  </Text>
                </Box>
              </TouchableOpacity>
            </Box>
          )}

          {/* Action buttons */}
          <Box flexDirection="row" gap="sm_12">
            <Box flex={1}>
              <Button variant="secondary" onPress={() => navigation.goBack()} title="Voltar" />
            </Box>
            <Box flex={1}>
              <Button
                variant="primary"
                onPress={handleShare}
                enabled={isValid()}
                title="Compartilhar"
              />
            </Box>
          </Box>
        </Box>
      </ScrollView>
    </SafeAreaView>
  );
};

export default QuickDetailsScreen;
