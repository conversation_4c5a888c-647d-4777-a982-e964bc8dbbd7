import React, { useState } from 'react';

import { FlatList, Pressable, ScrollView, TextInput } from 'react-native';

import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import {
  <PERSON><PERSON><PERSON>,
  CaretLeft,
  Clock,
  Gift,
  House,
  Lightning,
  MagnifyingGlass,
  MapPin,
  MusicNote,
  Plus,
  Repeat,
  Share,
  Tag,
  TrendUp,
  Users,
} from 'phosphor-react-native';
import Animated, { FadeInDown, FadeInRight } from 'react-native-reanimated';

import { Box, Text, useTheme } from '@/src/core/theme';
import {
  EventFilterPills,
  EventTemplateCardNew as EventTemplateCard,
  FilterType,
} from '@/src/features/events/components';

import { useChooseOptions } from '../hooks/useChooseOptions';
import { EventTemplate } from '../types/event.types';

const AnimatedBox = Animated.createAnimatedComponent(Box);
const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

type RouteParams = {
  category: string;
  categoryLabel: string;
};

const TemplateSelectionScreen = () => {
  const theme = useTheme();
  const navigation = useNavigation();
  const route = useRoute<RouteProp<{ params: RouteParams }, 'params'>>();
  const { category, categoryLabel } = route.params;

  const { categories, handleTemplateSelect, searchQuery, setSearchQuery } = useChooseOptions();
  const [selectedFilter, setSelectedFilter] = useState<FilterType>('all');

  // Find the selected category
  const selectedCategory = categories.find(cat => cat.id === category);
  if (!selectedCategory) return null;

  // Template gradient colors based on category - vibrant colors
  const templateGradients: Record<string, [string, string]> = {
    events: ['#60A5FA', '#3B82F6'], // Light blue to blue
    sharing: ['#C4B5FD', '#8B5CF6'], // Light purple to purple
    alerts: ['#FCD34D', '#F59E0B'], // Light yellow to orange
    marketplace: ['#34D399', '#10B981'], // Light green to green
    social: ['#F9A8D4', '#EC4899'], // Light pink to pink
    business: ['#FCA5A5', '#EF4444'], // Light red to red
  };

  // Get icon component based on icon name
  const getIconForTemplate = (iconName: string, categoryColor?: string) => {
    const gradientColor = templateGradients[category]?.[0] || '#3B82F6';
    const iconProps = {
      size: 24,
      color: categoryColor || gradientColor,
      weight: 'bold' as const,
    };

    switch (iconName) {
      case 'home':
        return <House {...iconProps} />;
      case 'beer':
        return <BeerStein {...iconProps} />;
      case 'zap':
        return <Lightning {...iconProps} />;
      case 'tag':
        return <Tag {...iconProps} />;
      case 'clock':
        return <Clock {...iconProps} />;
      case 'music':
        return <MusicNote {...iconProps} />;
      case 'repeat':
        return <Repeat {...iconProps} />;
      case 'users':
        return <Users {...iconProps} />;
      case 'map-pin':
        return <MapPin {...iconProps} />;
      case 'gift':
        return <Gift {...iconProps} />;
      case 'share':
        return <Share {...iconProps} />;
      case 'trending':
        return <TrendUp {...iconProps} />;
      default:
        return <Plus {...iconProps} />;
    }
  };

  // Filter templates
  const filteredTemplates = selectedCategory.templates.filter(template => {
    // Apply search filter
    if (searchQuery && !template.name.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }

    // Apply category filter
    if (selectedFilter === 'all') return true;

    const templateFilters: FilterType[] = [];
    if (template.revenueCapable) templateFilters.push('specials');
    if (template.category === 'business') templateFilters.push('vip');
    if (template.category === 'alerts') templateFilters.push('urgent');
    if (template.category === 'events') templateFilters.push('trending');
    if (template.defaults.suggestedMusic) templateFilters.push('music');

    return templateFilters.includes(selectedFilter);
  });

  const renderTemplate = ({ item, index }: { item: EventTemplate; index: number }) => {
    const icon = getIconForTemplate(item.icon);
    const gradientColors = templateGradients[category] || ['#6B7280', '#4B5563'];

    // Add some popular/new flags based on template properties
    const isPopular = index % 3 === 0;
    const isNew = index % 5 === 0;

    // Generate tags based on template properties
    const tags: string[] = [];
    if (item.defaults.suggestedMusic && item.defaults.suggestedMusic.length > 0) {
      tags.push('Música');
    }
    if (item.defaults.duration) {
      const durationInMinutes = parseInt(item.defaults.duration.replace(/\D/g, ''), 10);
      if (durationInMinutes > 180) tags.push('Longo');
    }
    if (item.defaults.capacity && item.defaults.capacity > 100) {
      tags.push('Grande');
    }
    if (item.category === 'events' || item.category === 'social') {
      tags.push('Público');
    }

    // Demo images for templates (in a real app, these would come from the data)
    const templateImages: Record<string, string> = {
      'home-party':
        'https://images.unsplash.com/photo-1530103862676-de8c9debad1d?w=400&h=400&fit=crop',
      'bar-party':
        'https://images.unsplash.com/photo-1470337458703-46ad1756a187?w=400&h=400&fit=crop',
      'flash-event':
        'https://images.unsplash.com/photo-1514525253161-7a46d19cd819?w=400&h=400&fit=crop',
      'live-music':
        'https://images.unsplash.com/photo-1501386761578-eac5c94b800a?w=400&h=400&fit=crop',
      'recurring-event':
        'https://images.unsplash.com/photo-1506157786151-b8491531f063?w=400&h=400&fit=crop',
      workshop: 'https://images.unsplash.com/photo-1511578314322-379afb476865?w=400&h=400&fit=crop',
    };

    return (
      <Box flex={1} margin="xs_8">
        <EventTemplateCard
          title={item.name}
          subtitle={item.description}
          icon={icon}
          image={templateImages[item.id]}
          gradientColors={gradientColors as [string, string]}
          onPress={() => handleTemplateSelect(item)}
          revenueCapable={item.revenueCapable}
          isPopular={isPopular}
          isNew={isNew}
          tags={tags}
          delay={index * 50}
          aspectRatio={1.1}
          compact
        />
      </Box>
    );
  };

  return (
    <Box flex={1} backgroundColor="background">
      <ScrollView showsVerticalScrollIndicator={false} stickyHeaderIndices={[0]}>
        {/* Sticky Header */}
        <Box backgroundColor="background" paddingTop="sm_12">
          {/* Back Button and Title */}
          <AnimatedBox
            entering={FadeInDown}
            flexDirection="row"
            alignItems="center"
            paddingHorizontal="lg_24"
            paddingBottom="md_16">
            <AnimatedPressable
              entering={FadeInRight}
              onPress={() => navigation.goBack()}
              style={{ marginRight: theme.spacing.md_16 }}>
              <Box backgroundColor="surfaceBackground" borderRadius="circle_9999" padding="xs_8">
                <CaretLeft size={24} color={theme.colors.text} weight="bold" />
              </Box>
            </AnimatedPressable>

            <Box flex={1}>
              <Text variant="h_24SemiBold_section" color="text">
                {categoryLabel}
              </Text>
              <Text variant="b_14Regular_content" color="textSecondary">
                {filteredTemplates.length} modelos disponíveis
              </Text>
            </Box>
          </AnimatedBox>

          {/* Search Bar */}
          <AnimatedBox
            entering={FadeInDown.delay(100)}
            paddingHorizontal="lg_24"
            marginBottom="sm_12">
            <Box
              backgroundColor="surfaceBackground"
              borderRadius="md_12"
              paddingHorizontal="md_16"
              paddingVertical="sm_12"
              flexDirection="row"
              alignItems="center"
              gap="sm_12">
              <MagnifyingGlass size={20} color={theme.colors.textSecondary} />
              <TextInput
                value={searchQuery}
                onChangeText={setSearchQuery}
                placeholder="Buscar modelos..."
                placeholderTextColor={theme.colors.textTertiary}
                style={{
                  flex: 1,
                  fontFamily: theme.textVariants.defaults.fontFamily,
                  fontSize: 16,
                  color: theme.colors.text,
                }}
              />
            </Box>
          </AnimatedBox>

          {/* Filter Pills */}
          <AnimatedBox entering={FadeInDown.delay(200)} marginBottom="md_16">
            <EventFilterPills selectedFilter={selectedFilter} onFilterChange={setSelectedFilter} />
          </AnimatedBox>
        </Box>

        {/* Templates Grid */}
        <Box paddingHorizontal="md_16">
          <FlatList
            data={filteredTemplates}
            renderItem={renderTemplate}
            keyExtractor={item => item.id}
            numColumns={2}
            scrollEnabled={false}
            columnWrapperStyle={{
              justifyContent: 'space-between',
            }}
            contentContainerStyle={{
              paddingBottom: theme.spacing.xxxl_48,
            }}
            ListEmptyComponent={
              <Box alignItems="center" paddingVertical="xxxl_48">
                <MagnifyingGlass size={48} color={theme.colors.textTertiary} weight="thin" />
                <Text variant="b_16Regular_input" color="textSecondary" marginTop="md_16">
                  Nenhum modelo encontrado
                </Text>
                <Text
                  variant="b_14Regular_content"
                  color="textTertiary"
                  marginTop="xs_8"
                  textAlign="center">
                  Tente ajustar os filtros ou buscar por outro termo
                </Text>
              </Box>
            }
          />
        </Box>
      </ScrollView>
    </Box>
  );
};

export default TemplateSelectionScreen;
