export interface CreateEventInput {
  // Basic Details (Step 1)
  name: string;
  description: string;
  startDate: Date;
  endDate?: Date;
  location: string;
  locationName?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  mediaUrl?: string;
  coHosts?: string[];

  // Preferences (Step 3)
  preferences: EventPreferences;
}

export interface EventPreferences {
  notifyRadarUsers: boolean;
  drinksAvailable: boolean;
  drinkNotes?: string;
  privateListing: boolean;
  allowGroups: boolean;
}

export interface Event extends CreateEventInput {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  organizerId: string;
  organizerName: string;
  status: EventStatus;
  attendeesCount: {
    interested: number;
    going: number;
  };
  tags?: string[];
}

export enum EventStatus {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
  CANCELLED = 'CANCELLED',
  COMPLETED = 'COMPLETED',
}

export interface EventLocation {
  address: string;
  name?: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
}

export interface EventMedia {
  url: string;
  type: 'image' | 'video';
  thumbnailUrl?: string;
}

export interface EventHost {
  id: string;
  name: string;
  avatarUrl?: string;
  role: 'organizer' | 'co-host';
}

// Event Template Types for Choose Options
export type EventTemplateCategory =
  | 'events'
  | 'sharing'
  | 'alerts'
  | 'marketplace'
  | 'social'
  | 'business';

export type EventTemplateId =
  | 'house-party'
  | 'bar-club'
  | 'flash-party'
  | 'promo-share'
  | 'happy-hour-alert'
  | 'live-show-alert'
  | 'ticket-swap'
  | 'group-meetup'
  | 'venue-promo';

export interface EventTemplate {
  id: EventTemplateId;
  name: string;
  icon: string;
  emoji?: string;
  description: string;
  category: EventTemplateCategory;
  revenueCapable: boolean;
  defaults: {
    capacity?: number;
    vipEnabled?: boolean;
    suggestedTimes?: string[];
    suggestedMusic?: string[];
    duration?: string;
    suggestedPricing?: {
      tier: string;
      price: number;
      limit?: number;
      perks?: string[];
    }[];
    suggestedDiscounts?: string[];
    suggestedDuration?: string[];
    requiresProof?: boolean;
    suggestedOffers?: string[];
    requiresPhoto?: boolean;
    suggestedGenres?: string[];
    requiresArtistName?: boolean;
    suggestedVenues?: string[];
    swapTypes?: string[];
    paymentMethods?: string[];
    safetyTips?: boolean;
    suggestedActivities?: string[];
    splitCosts?: boolean;
    safetyFeatures?: boolean;
    venueTypes?: string[];
    promoTypes?: string[];
    targetAudience?: string[];
  };
}

export interface EventCategory {
  id: EventTemplateCategory;
  label: string;
  templates: EventTemplate[];
}
