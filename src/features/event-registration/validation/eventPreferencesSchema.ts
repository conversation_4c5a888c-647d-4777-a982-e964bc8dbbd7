import { z } from 'zod';

export const eventPreferencesSchema = z.object({
  notifyRadarUsers: z.boolean().default(true),
  drinksAvailable: z.boolean().default(false),
  drinkNotes: z.string().max(200, 'Notes must be less than 200 characters').optional(),
  privateListing: z.boolean().default(true),
  allowGroups: z.boolean().default(true),
});

export type EventPreferencesFormData = z.infer<typeof eventPreferencesSchema>;

// Complete event schema combining all steps
export const completeEventSchema = z
  .object({
    // From Step 1
    name: z.string().min(3).max(100),
    description: z.string().min(20).max(500),
    startDate: z.date(),
    endDate: z.date().optional(),
    location: z.string().min(5).max(200),
    locationName: z.string().optional(),
    coordinates: z
      .object({
        latitude: z.number(),
        longitude: z.number(),
      })
      .optional(),
    mediaUrl: z.string().url().optional(),
    coHosts: z.array(z.string()).optional(),

    // From Step 3
    preferences: eventPreferencesSchema,
  })
  .refine(data => data.endDate && data.endDate > data.startDate, {
    message: 'End date must be after start date',
    path: ['endDate'],
  });

export type CompleteEventFormData = z.infer<typeof completeEventSchema>;
