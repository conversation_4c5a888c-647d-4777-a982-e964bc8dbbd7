import { z } from 'zod';

export const eventDetailsSchema = z
  .object({
    name: z
      .string()
      .min(3, 'Event name must be at least 3 characters')
      .max(100, 'Event name must be less than 100 characters'),

    description: z
      .string()
      .min(20, 'Please provide more details (at least 20 characters)')
      .max(500, 'Description must be less than 500 characters'),

    startDate: z.date({
      required_error: 'Start date is required',
      invalid_type_error: 'Invalid date',
    }),

    endDate: z.date().optional(),

    location: z.string().min(5, 'Please provide a valid address').max(200, 'Address is too long'),

    locationName: z.string().optional(),

    coordinates: z
      .object({
        latitude: z.number().min(-90).max(90),
        longitude: z.number().min(-180).max(180),
      })
      .optional(),

    mediaUrl: z.string().url('Invalid media URL').optional().or(z.literal('')),

    coHosts: z.array(z.string()).optional(),

    // Template-specific fields
    capacity: z.string().optional(),
    musicGenres: z.array(z.string()).optional(),
    isPaid: z.boolean().optional(),
    ticketPrice: z.string().optional(),
    splitCosts: z.boolean().optional(),
  })
  .superRefine((data, ctx) => {
    // Validate that end date is after start date if both are provided
    if (data.startDate && data.endDate && data.endDate <= data.startDate) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'End date must be after start date',
        path: ['endDate'],
      });
    }
  });

export type EventDetailsFormData = z.infer<typeof eventDetailsSchema>;
