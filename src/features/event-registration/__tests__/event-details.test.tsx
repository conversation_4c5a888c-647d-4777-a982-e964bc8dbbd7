import React from 'react';

import { NavigationContainer } from '@react-navigation/native';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { fireEvent, render, waitFor } from '@testing-library/react-native';

import { ThemeProvider } from '@/src/core/theme';

import { CreateEventDetailsScreen } from '../screens/create-event-details.screen';

// Mock navigation
const mockNavigate = jest.fn();
const mockGoBack = jest.fn();
jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => ({
    navigate: mockNavigate,
    goBack: mockGoBack,
  }),
}));

// Mock hooks
jest.mock('../hooks/useEventDraft', () => ({
  useEventDraft: () => ({
    draft: {},
    updateDraft: jest.fn(),
    currentStep: 1,
    goToNextStep: jest.fn(),
  }),
}));

const queryClient = new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

const Wrapper = ({ children }: { children: React.ReactNode }) => (
  <QueryClientProvider client={queryClient}>
    <ThemeProvider>
      <NavigationContainer>{children}</NavigationContainer>
    </ThemeProvider>
  </QueryClientProvider>
);

describe('EventDetailsScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render correctly', () => {
    const { getByText, getByPlaceholderText } = render(<CreateEventDetailsScreen />, {
      wrapper: Wrapper,
    });

    expect(getByText('DETAILS')).toBeTruthy();
    expect(getByText('STEP 1/3')).toBeTruthy();
    expect(getByPlaceholderText('Birthday party, Team dinner...')).toBeTruthy();
    expect(getByPlaceholderText('Tell people what to expect...')).toBeTruthy();
  });

  it('should enable Next button when form is valid', async () => {
    const { getByText, getByPlaceholderText } = render(<CreateEventDetailsScreen />, {
      wrapper: Wrapper,
    });

    const nameInput = getByPlaceholderText('Birthday party, Team dinner...');
    const descriptionInput = getByPlaceholderText('Tell people what to expect...');
    const locationInput = getByPlaceholderText('Search for location');

    // Initially button should be disabled
    const nextButton = getByText('Next');
    expect(nextButton.props.disabled).toBe(true);

    // Fill in required fields
    fireEvent.changeText(nameInput, 'My Birthday Party');
    fireEvent.changeText(
      descriptionInput,
      'Join us for an amazing celebration with food and music!'
    );
    fireEvent.changeText(locationInput, '123 Main Street, New York');

    // Button should be enabled after valid input
    await waitFor(() => {
      expect(nextButton.props.disabled).toBe(false);
    });
  });

  it('should navigate back when Cancel is pressed', () => {
    const { getByText } = render(<CreateEventDetailsScreen />, { wrapper: Wrapper });

    const cancelButton = getByText('Cancel');
    fireEvent.press(cancelButton);

    expect(mockGoBack).toHaveBeenCalled();
  });
});
