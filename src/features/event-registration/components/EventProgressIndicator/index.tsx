import React from 'react';

import { Box } from '@/src/core/theme';

interface EventProgressIndicatorProps {
  currentStep: number;
  totalSteps?: number;
}

export const EventProgressIndicator = React.memo(
  ({ currentStep, totalSteps = 3 }: EventProgressIndicatorProps) => {
    return (
      <Box flexDirection="row" gap="xs_8" paddingHorizontal="md_16" paddingVertical="sm_12">
        {Array.from({ length: totalSteps }).map((_, index) => (
          <Box
            key={index}
            flex={1}
            height={4}
            backgroundColor={index < currentStep ? 'primary' : 'disabled'}
            borderRadius="xxs_2"
          />
        ))}
      </Box>
    );
  }
);

EventProgressIndicator.displayName = 'EventProgressIndicator';
