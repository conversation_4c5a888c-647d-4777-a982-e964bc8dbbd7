import React from 'react';

import { Pressable } from 'react-native';

import { Canvas, LinearGradient, Rect, vec } from '@shopify/react-native-skia';
import { ArrowRight } from 'phosphor-react-native';
import Animated, {
  FadeInDown,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from 'react-native-reanimated';

import { Box, Text, useTheme } from '@/src/core/theme';

const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

interface CategoryCardProps {
  title: string;
  subtitle: string;
  templateCount: number;
  icon: React.ReactNode;
  gradientColors: [string, string];
  onPress: () => void;
  delay?: number;
}

export const CategoryCard: React.FC<CategoryCardProps> = ({
  title,
  subtitle,
  templateCount,
  icon,
  gradientColors,
  onPress,
  delay = 0,
}) => {
  const theme = useTheme();
  const scale = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const handlePressIn = () => {
    scale.value = withSpring(0.95);
  };

  const handlePressOut = () => {
    scale.value = withSpring(1);
  };

  return (
    <AnimatedPressable
      onPress={onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      style={animatedStyle}>
      <Animated.View entering={FadeInDown.delay(delay).springify()}>
        <Box
          backgroundColor="card"
          borderRadius="lg_16"
          overflow="hidden"
          borderWidth={1}
          borderColor="border"
          shadowOffset={{ width: 0, height: 2 }}
          shadowOpacity={0.08}
          shadowRadius={8}
          elevation={5}>
          {/* Gradient Header */}
          <Box height={120} overflow="hidden">
            <Canvas style={{ flex: 1 }}>
              <Rect x={0} y={0} width={1000} height={1000}>
                <LinearGradient start={vec(0, 0)} end={vec(300, 120)} colors={gradientColors} />
              </Rect>
            </Canvas>
            <Box
              position="absolute"
              top={0}
              left={0}
              right={0}
              bottom={0}
              justifyContent="center"
              alignItems="center"
              padding="md_16">
              <Box
                backgroundColor="white"
                borderRadius="circle_9999"
                padding="md_16"
                shadowOffset={{ width: 0, height: 2 }}
                shadowOpacity={0.15}
                shadowRadius={4}
                elevation={3}>
                {icon}
              </Box>
            </Box>
          </Box>

          {/* Content */}
          <Box padding="md_16">
            <Text variant="h_24SemiBold_section" color="text" marginBottom="xs_8">
              {title}
            </Text>
            <Text variant="b_16Regular_input" color="textSecondary" marginBottom="md_16">
              {subtitle}
            </Text>

            {/* Footer */}
            <Box flexDirection="row" justifyContent="space-between" alignItems="center">
              <Box
                backgroundColor="surfaceBackground"
                borderWidth={1}
                borderColor="border"
                paddingHorizontal="sm_12"
                paddingVertical="xs_8"
                borderRadius="circle_9999"
                flexDirection="row"
                alignItems="center"
                gap="xxs_4">
                <Text variant="l_12Medium_message" color="primary">
                  {templateCount}
                </Text>
                <Text variant="l_12Regular_helperText" color="textSecondary">
                  modelos
                </Text>
              </Box>

              <Box
                backgroundColor="surfaceBackground"
                borderWidth={1}
                borderColor="border"
                borderRadius="circle_9999"
                width={32}
                height={32}
                justifyContent="center"
                alignItems="center">
                <ArrowRight size={16} color={theme.colors.primary} weight="bold" />
              </Box>
            </Box>
          </Box>
        </Box>
      </Animated.View>
    </AnimatedPressable>
  );
};
