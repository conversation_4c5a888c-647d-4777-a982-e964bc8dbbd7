import React, { JSX } from 'react';

import { CaretRight, UsersFour } from 'phosphor-react-native';

import { Box, Column, Row, Text, useTheme } from '@/src/core/theme';
import { PressableAnimated } from '@/src/core/theme/Pressable';
import { TokensThemeColors } from '@/src/core/theme/theme';
import { Card } from '@/src/shared/components';
import { Pill } from '@/src/shared/components/Pill';

interface OptionCardProps {
  label: string;
  description: string;
  features: string[];
  icon: JSX.Element;
  textColor?: TokensThemeColors;
  color: TokensThemeColors;
  featuredHeaderPill?: string;
  onPress?: () => void;
}

export const OptionCard: React.FC<OptionCardProps> = ({
  label,
  description,
  features,
  icon,
  color,
  textColor,
  featuredHeaderPill,
  onPress,
}) => {
  const theme = useTheme();

  return (
    <PressableAnimated onPress={onPress} padding="md_16">
      <Card>
        <Box py="md_16">
          {featuredHeaderPill && (
            <Row justifyContent="flex-end">
              <Pill
                text={featuredHeaderPill}
                variant="small"
                hideIcon
                textColor={textColor ?? 'white'}
                backgroundColor={color}
              />
            </Row>
          )}
          <Row gap="sm_12" justifyContent="space-between">
            <Box
              borderRadius="circle_9999"
              backgroundColor={color}
              p="xs_8"
              height={56}
              width={56}
              justifyContent="center"
              alignItems="center"
              alignSelf="flex-start">
              {icon}
            </Box>

            <Row flex={1} justifyContent="space-between">
              <Column gap="xs_8">
                <Column gap="none_0">
                  <Row gap="xxs_4" flexWrap="wrap" rowGap="xxs_4">
                    <Text variant="h_18SemiBold_cardTitle">{label}</Text>
                    {features.length > 1 &&
                      features
                        .slice(1)
                        .map((feature, index) => (
                          <Pill
                            key={index}
                            text={feature}
                            hideIcon
                            variant="small"
                            textColor={textColor ?? 'white'}
                            backgroundColor={color}
                          />
                        ))}
                  </Row>

                  <Text variant="l_12Regular_helperText" color="textSecondary" numberOfLines={1}>
                    {description}
                  </Text>
                </Column>

                <Pill
                  text={features[0]}
                  variant="small"
                  textColor={textColor ?? 'white'}
                  backgroundColor={color}
                  customIcon={
                    <UsersFour
                      size={14}
                      color={theme.colors[textColor ?? 'white']}
                      weight="regular"
                    />
                  }
                />
              </Column>
            </Row>
            <CaretRight size={24} color={theme.colors.iconActiveInput} />
          </Row>
        </Box>
      </Card>
    </PressableAnimated>
  );
};
