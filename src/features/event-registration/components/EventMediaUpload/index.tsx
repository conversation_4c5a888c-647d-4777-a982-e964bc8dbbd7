import React, { useState } from 'react';

import { ActivityIndicator, Image, TouchableOpacity } from 'react-native';

import * as ImagePicker from 'expo-image-picker';
import { ImageSquare } from 'phosphor-react-native';

import { Box, Text, useTheme } from '@/src/core/theme';

import { useUploadEventMedia } from '../../hooks/useUploadEventMedia';

interface EventMediaUploadProps {
  onUpload: (url: string) => void;
  currentMedia?: string;
}

export const EventMediaUpload = React.memo(({ onUpload, currentMedia }: EventMediaUploadProps) => {
  const uploadMedia = useUploadEventMedia();
  const [localUri, setLocalUri] = useState<string | null>(null);
  const theme = useTheme();

  const handlePickImage = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ['images'],
      allowsEditing: true,
      aspect: [16, 9],
      quality: 1,
    });

    if (!result.canceled && result.assets[0]) {
      const asset = result.assets[0];
      setLocalUri(asset.uri);

      const uploadResult = await uploadMedia.mutateAsync({
        file: {
          uri: asset.uri,
          type: 'image/jpeg',
          name: 'event-media.jpg',
        },
      });

      if (uploadResult.success) {
        onUpload(uploadResult.data.url);
      }
    }
  };

  const displayUri = localUri || currentMedia;

  return (
    <TouchableOpacity onPress={handlePickImage} disabled={uploadMedia.isPending}>
      <Box
        backgroundColor="surface"
        borderRadius="lg_16"
        borderWidth={1}
        borderColor="border"
        borderStyle="dashed"
        height={180}
        justifyContent="center"
        alignItems="center"
        overflow="hidden">
        {uploadMedia.isPending ? (
          <ActivityIndicator size="large" color="#0052CC" />
        ) : displayUri ? (
          <Image
            source={{ uri: displayUri }}
            style={{ width: '100%', height: '100%' }}
            resizeMode="cover"
          />
        ) : (
          <>
            <ImageSquare size={50} color={theme.colors.textSecondary} />
            <Text variant="b_14Medium_button" color="primary" marginTop="sm_12">
              Upload media
            </Text>
          </>
        )}
      </Box>
    </TouchableOpacity>
  );
});

EventMediaUpload.displayName = 'EventMediaUpload';
