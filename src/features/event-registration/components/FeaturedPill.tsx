import React from 'react';

import { Row } from '@/src/core/theme';
import { TokensThemeColors } from '@/src/core/theme/theme';
import { Pill } from '@/src/shared/components/Pill';

interface FeaturedPillProps {
  text: string;
  textColor?: TokensThemeColors;
  backgroundColor: TokensThemeColors;
}

export const FeaturedPill: React.FC<FeaturedPillProps> = ({ text, textColor, backgroundColor }) => {
  return (
    <Row justifyContent="flex-end">
      <Pill
        text={text}
        variant="default"
        hideIcon
        textColor={textColor ?? 'white'}
        backgroundColor={backgroundColor}
      />
    </Row>
  );
};
