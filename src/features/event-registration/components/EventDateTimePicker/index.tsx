import React, { useRef } from 'react';

import { TouchableOpacity } from 'react-native';

import { DatePicker } from '@s77rt/react-native-date-picker';
import type { DatePickerHandle } from '@s77rt/react-native-date-picker';
import { format, isDate } from 'date-fns';
import { formatInTimeZone } from 'date-fns-tz';
import { Calendar, Globe } from 'phosphor-react-native';
import { Control, FieldPath, FieldValues } from 'react-hook-form';

import { Box, Text, useTheme } from '@/src/core/theme';
import { Input } from '@/src/shared/components/Input';

interface EventDateTimePickerProps<
  T extends FieldValues = FieldValues,
  TName extends FieldPath<T> = FieldPath<T>,
> {
  control: Control<T>;
  name: TName;
  label: string;
  placeholder?: string;
  showTimezone?: boolean;
  minDate?: Date;
  error?: string;
}

export const EventDateTimePicker = <
  T extends FieldValues = FieldValues,
  TName extends FieldPath<T> = FieldPath<T>,
>({
  control,
  name,
  label,
  placeholder = 'Select date and time',
  showTimezone = false,
  minDate = new Date(),
  error,
}: EventDateTimePickerProps<T, TName>) => {
  const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  const datePicker = useRef<DatePickerHandle>(null);
  const theme = useTheme();

  const showPicker = () => {
    datePicker.current?.showPicker();
  };

  return (
    <Input
      control={control}
      name={name}
      label={label}
      error={error}
      render={({ field }) => {
        const hasDate = true;
        return (
          <Box>
            <TouchableOpacity onPress={showPicker}>
              <Box
                backgroundColor="white"
                borderRadius="lg_16"
                borderWidth={1}
                borderColor="mainBorder"
                paddingHorizontal="md_16"
                paddingVertical="sm_12"
                minHeight={56}
                justifyContent="center">
                <Box flexDirection="row" alignItems="center" justifyContent="space-between">
                  <Text
                    variant="b_16Regular_input"
                    color={hasDate ? 'mainText' : 'inputPlaceholder'}
                    numberOfLines={1}>
                    {hasDate ? format(field.value, 'EEE, MMM dd, yyyy • HH:mm') : placeholder}
                  </Text>
                  <Calendar size={20} color="textSecondary" />
                </Box>
              </Box>
            </TouchableOpacity>

            <DatePicker
              ref={datePicker}
              value={isDate(field.value) ? field.value : new Date()}
              type="datetime"
              onChange={value => {
                if (value) {
                  field.onChange(value);
                }
              }}
              min={isDate(minDate) ? minDate : new Date()}
            />

            {showTimezone && hasDate && (
              <Box marginTop="xs_8">
                <Box
                  flexDirection="row"
                  alignItems="center"
                  gap="xxs_4"
                  backgroundColor="primaryLight"
                  paddingHorizontal="sm_12"
                  paddingVertical="xxs_4"
                  borderRadius="sm_8"
                  alignSelf="flex-start">
                  <Globe size={16} color={theme.colors.primary} />
                  <Text variant="l_12Medium_message" color="primary">
                    {formatInTimeZone(field.value, timezone, 'zzz')}
                  </Text>
                </Box>
              </Box>
            )}
          </Box>
        );
      }}
    />
  );
};

EventDateTimePicker.displayName = 'EventDateTimePicker';
