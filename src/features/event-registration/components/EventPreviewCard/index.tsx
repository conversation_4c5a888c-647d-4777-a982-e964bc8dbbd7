import React from 'react';

import { Image } from 'react-native';

import { format } from 'date-fns';

import { Box, Text } from '@/src/core/theme';

import { CreateEventInput } from '../../types/event.types';

interface EventPreviewCardProps {
  event: Partial<CreateEventInput>;
}

export const EventPreviewCard = React.memo(({ event }: EventPreviewCardProps) => {
  const formattedDate = event.startDate ? format(event.startDate, 'EEEE, MMMM d, yyyy') : '';

  const formattedTime = event.startDate ? format(event.startDate, 'HH:mm') : '';

  const endTime = event.endDate ? format(event.endDate, 'HH:mm') : '';

  return (
    <Box
      backgroundColor="card"
      borderRadius="xxxl_32"
      overflow="hidden"
      shadowColor="text"
      shadowOffset={{ width: 0, height: 4 }}
      shadowOpacity={0.1}
      shadowRadius={12}
      elevation={5}>
      {event.mediaUrl && (
        <Image
          source={{ uri: event.mediaUrl }}
          style={{ width: '100%', height: 200 }}
          resizeMode="cover"
        />
      )}

      <Box padding="lg_24">
        <Text variant="h_32SemiBold_Page" color="text" marginBottom="xs_8">
          {event.name || 'Event Name'}
        </Text>

        <Text variant="b_16Regular_input" color="text" marginBottom="xxs_4">
          {formattedDate}
        </Text>

        <Text variant="b_14Regular_content" color="textSecondary">
          {formattedTime} {endTime && `- ${endTime}`} (GMT +07:00)
        </Text>

        <Box flexDirection="row" gap="xs_8" marginTop="md_16">
          {[1, 2, 3, 4, 5].map(index => (
            <Box
              key={index}
              width={8}
              height={8}
              borderRadius="circle_9999"
              backgroundColor={index === 1 ? 'primary' : 'disabled'}
            />
          ))}
        </Box>
      </Box>
    </Box>
  );
});

EventPreviewCard.displayName = 'EventPreviewCard';
