import React from 'react';

import { TouchableOpacity } from 'react-native';

import { CaretRight } from 'phosphor-react-native';

import { Box, Text, useTheme } from '@/src/core/theme';
import { Switch } from '@/src/shared/components';

interface ActionItemProps {
  icon: React.ReactElement<{ color?: string; weight?: string }>;
  title: string;
  subtitle?: string;
  toggle?: boolean;
  value?: boolean;
  onPress?: () => void;
  onValueChange?: (value: boolean) => void;
}

export const ActionItem = React.memo(
  ({
    icon,
    title,
    subtitle,
    toggle = false,
    value = false,
    onPress,
    onValueChange,
  }: ActionItemProps) => {
    const Container = toggle ? Box : TouchableOpacity;
    const theme = useTheme();

    return (
      <Container onPress={!toggle ? onPress : undefined}>
        <Box flexDirection="row" alignItems="center" paddingVertical="md_16" gap="sm_12">
          <Box
            width={40}
            height={40}
            borderRadius="circle_9999"
            backgroundColor="primary"
            justifyContent="center"
            alignItems="center">
            {icon}
          </Box>

          <Box flex={1}>
            <Text variant="b_16SemiBold_button" color="text">
              {title}
            </Text>
            {subtitle && (
              <Text variant="b_14Regular_content" color="textSecondary">
                {subtitle}
              </Text>
            )}
          </Box>

          {toggle ? (
            <Switch isChecked={value} onToggle={value => onValueChange?.(!value)} />
          ) : (
            <CaretRight size={20} color={theme.colors.iconPrimary} />
          )}
        </Box>
      </Container>
    );
  }
);

ActionItem.displayName = 'ActionItem';
