import { TouchableOpacity } from 'react-native';

import { useNavigation } from '@react-navigation/native';
import { ArrowRight, MapPin } from 'phosphor-react-native';
import { Control, FieldPath, FieldValues } from 'react-hook-form';

import { EventRegistrationStackScreenProps } from '@/src/core/navigation/types';
import { Box, Text, useTheme } from '@/src/core/theme';
import { Input } from '@/src/shared/components/Input';

import { EventLocation } from '../../types/event.types';

interface EventLocationPickerProps<
  T extends FieldValues = FieldValues,
  TName extends FieldPath<T> = FieldPath<T>,
> {
  control: Control<T>;
  name: TName;
  label: string;
  placeholder?: string;
  onSelect?: (location: EventLocation) => void;
  error?: string;
}

export const EventLocationPicker = <
  T extends FieldValues = FieldValues,
  TName extends FieldPath<T> = FieldPath<T>,
>({
  control,
  name,
  label,
  placeholder = 'Search for location',
  error,
}: EventLocationPickerProps<T, TName>) => {
  const navigation =
    useNavigation<EventRegistrationStackScreenProps<'CreateEventDetails'>['navigation']>();
  const theme = useTheme();

  return (
    <>
      <Input
        control={control}
        name={name}
        label={label}
        error={error}
        render={({ field }) => (
          <TouchableOpacity onPress={() => navigation.navigate('EventLocationSearch')}>
            <Box
              backgroundColor="white"
              borderRadius="lg_16"
              borderWidth={1}
              borderColor="mainBorder"
              paddingHorizontal="md_16"
              paddingVertical="sm_12"
              minHeight={56}
              justifyContent="center">
              <Box flexDirection="row" alignItems="center" justifyContent="space-between">
                <Box flexDirection="row" alignItems="center" flex={1}>
                  <MapPin
                    size={20}
                    color={field.value ? theme.colors.mainText : theme.colors.inputPlaceholder}
                  />
                  <Text
                    variant="b_16Regular_input"
                    color={field.value ? 'mainText' : 'inputPlaceholder'}
                    numberOfLines={1}
                    marginLeft="xs_8"
                    flex={1}>
                    {field.value || placeholder}
                  </Text>
                </Box>
                <ArrowRight size={20} color={theme.colors.textSecondary} />
              </Box>
            </Box>
          </TouchableOpacity>
        )}
      />
    </>
  );
};

EventLocationPicker.displayName = 'EventLocationPicker';
