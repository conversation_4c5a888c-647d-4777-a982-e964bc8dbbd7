import React from 'react';

import { Box, Text } from '@/src/core/theme';
import { Switch } from '@/src/shared/components';

interface PreferenceItemProps {
  title: string;
  value: boolean;
  onValueChange: (value: boolean) => void;
  action?: React.ReactNode;
  disabled?: boolean;
}

export const PreferenceItem = React.memo(
  ({ title, value, onValueChange, action, disabled = false }: PreferenceItemProps) => {
    return (
      <Box
        flexDirection="row"
        justifyContent="space-between"
        alignItems="center"
        paddingVertical="md_16"
        opacity={disabled ? 0.5 : 1}>
        <Text variant="b_16SemiBold_button" flex={1} color={disabled ? 'textTertiary' : 'text'}>
          {title}
        </Text>

        {action || <Switch isChecked={value} onToggle={onValueChange} disabled={disabled} />}
      </Box>
    );
  }
);

PreferenceItem.displayName = 'PreferenceItem';
