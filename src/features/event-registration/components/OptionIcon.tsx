import React, { JSX } from 'react';

import { Box } from '@/src/core/theme';
import { TokensThemeColors } from '@/src/core/theme/theme';

interface OptionIconProps {
  icon: JSX.Element;
  backgroundColor: TokensThemeColors;
}

export const OptionIcon: React.FC<OptionIconProps> = ({ icon, backgroundColor }) => {
  return (
    <Box
      borderRadius="circle_9999"
      backgroundColor={backgroundColor}
      p="xs_8"
      height={56}
      width={56}
      justifyContent="center"
      alignItems="center"
      alignSelf="flex-start">
      {icon}
    </Box>
  );
};
