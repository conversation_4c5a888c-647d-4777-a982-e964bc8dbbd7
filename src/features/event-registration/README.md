# Event Registration Feature

This feature allows users to create private event registrations through a multi-step flow.

## Architecture

### Feature Structure
```
src/features/event-registration/
├── screens/                    # Screen components for each step
├── components/                 # Reusable UI components
├── hooks/                     # TanStack Query hooks & utilities
├── services/                  # API service layer
├── store/                     # Zustand store for form state
├── validation/                # Zod validation schemas
└── types/                     # TypeScript interfaces
```

### State Management

1. **Persistent Form State** (Zustand + MMKV)
   - Draft data persisted across app sessions
   - Current step tracking
   - Auto-save functionality

2. **Async Operations** (TanStack Query)
   - Event creation mutation
   - Media upload with compression
   - Location search
   - Individual loading states per operation

### Flow Overview

1. **Step 1: Event Details** (`EventDetailsScreen`)
   - Event name and description
   - Date/time selection with timezone
   - Location picker with search
   - Media upload
   - Co-hosts selection

2. **Step 2: Summary Overview** (`EventSummaryScreen`)
   - Preview of event card
   - Event description with tags
   - Attendance tracking UI
   - Location details
   - Action items configuration
   - Organizer information

3. **Step 3: Preferences** (`EventPreferencesScreen`)
   - Privacy settings (private listing)
   - Notification settings (radar users)
   - Event features (drinks, groups)
   - Additional notes

4. **Success Screen** (`EventSuccessScreen`)
   - Confirmation message
   - View event action
   - Share options
   - Create another event

## Usage

### Navigation Setup

Add the EventRegistrationNavigator to your main navigation:

```typescript
import { EventRegistrationNavigator } from '@/src/core/navigation/EventRegistrationNavigator';

// In your main navigator
<Stack.Screen 
  name="EventRegistration" 
  component={EventRegistrationNavigator}
  options={{ headerShown: false }}
/>
```

### Starting the Flow

```typescript
navigation.navigate('EventRegistration', {
  screen: 'EventDetails',
});
```

### Key Features

1. **Draft Management**: Form data is automatically saved and restored
2. **Media Compression**: Images are compressed before upload
3. **Real-time Validation**: Buttons enable/disable based on form validity
4. **Optimal UX**: Errors show on blur, buttons enable immediately
5. **Type Safety**: Full TypeScript coverage with Zod validation

### Form Validation

The feature uses Zod schemas for validation:
- `eventDetailsSchema`: Step 1 validation
- `eventPreferencesSchema`: Step 3 validation
- `completeEventSchema`: Full event validation

### API Integration

The service layer follows the established pattern:
```typescript
type ServiceResponse<T> = 
  | { success: true; data: T }
  | { success: false; errors: ErrorResponse[] };
```

### Performance Optimizations

1. **Image Compression**: Automatic resize to 1080px width
2. **Lazy Loading**: Components loaded on demand
3. **Debounced Search**: Location search with 500ms debounce
4. **Auto-save**: Draft saved every 3 seconds

### Testing

Run tests with:
```bash
bun test src/features/event-registration
```

Key test scenarios:
- Form validation and submission
- Draft persistence
- Navigation flow
- Error handling
- Media upload

### Customization

To add new fields:
1. Update types in `event.types.ts`
2. Add validation in schemas
3. Update UI components
4. Modify service layer if needed

### Accessibility

- All inputs have proper labels
- Error messages are announced
- Toggle switches have state descriptions
- Progress indicator has semantic meaning
