import React, { ReactNode, createContext, useCallback, useContext, useState } from 'react';

import { EventTemplate } from '../types/event.types';

export interface LocationData {
  id: string;
  name: string;
  address: string;
  latitude: number;
  longitude: number;
}

interface EventRegistrationContextType {
  selectedLocation: LocationData | null;
  setSelectedLocation: (location: LocationData | null) => void;
  clearSelectedLocation: () => void;
  selectedTemplate: EventTemplate | null;
  setSelectedTemplate: (template: EventTemplate | null) => void;
  clearSelectedTemplate: () => void;
  resetContext: () => void;
}

const EventRegistrationContext = createContext<EventRegistrationContextType | undefined>(undefined);

interface EventRegistrationProviderProps {
  children: ReactNode;
}

export const EventRegistrationProvider: React.FC<EventRegistrationProviderProps> = ({
  children,
}) => {
  const [selectedLocation, setSelectedLocationState] = useState<LocationData | null>(null);
  const [selectedTemplate, setSelectedTemplateState] = useState<EventTemplate | null>(null);

  const setSelectedLocation = useCallback((location: LocationData | null) => {
    setSelectedLocationState(location);
  }, []);

  const clearSelectedLocation = useCallback(() => {
    setSelectedLocationState(null);
  }, []);

  const setSelectedTemplate = useCallback((template: EventTemplate | null) => {
    setSelectedTemplateState(template);
  }, []);

  const clearSelectedTemplate = useCallback(() => {
    setSelectedTemplateState(null);
  }, []);

  const resetContext = useCallback(() => {
    setSelectedLocationState(null);
    setSelectedTemplateState(null);
  }, []);

  const value: EventRegistrationContextType = {
    selectedLocation,
    setSelectedLocation,
    clearSelectedLocation,
    selectedTemplate,
    setSelectedTemplate,
    clearSelectedTemplate,
    resetContext,
  };

  return (
    <EventRegistrationContext.Provider value={value}>{children}</EventRegistrationContext.Provider>
  );
};

export const useEventRegistrationContext = (): EventRegistrationContextType => {
  const context = useContext(EventRegistrationContext);
  if (context === undefined) {
    throw new Error('useEventRegistrationContext must be used within an EventRegistrationProvider');
  }
  return context;
};
