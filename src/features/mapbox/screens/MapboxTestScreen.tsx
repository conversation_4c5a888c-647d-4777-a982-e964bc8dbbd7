import React, { useEffect, useState } from 'react';

import { Platform, StyleSheet, View } from 'react-native';

import { useNavigation } from '@react-navigation/native';
import MapboxGL from '@rnmapbox/maps';
import * as Phosphor from 'phosphor-react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { MapboxStackScreenProps } from '@/src/core/navigation/types';
import { Box, Text, useTheme } from '@/src/core/theme';
import { Button } from '@/src/shared/components';

// Set Mapbox access token from environment variables
// On Android, the token is set via the config plugin
// On iOS and Web, we need to set it programmatically
if (Platform.OS !== 'android' && process.env.EXPO_PUBLIC_MAPBOX_ACCESS_TOKEN) {
  console.log('Setting Mapbox access token for iOS/Web');
  MapboxGL.setAccessToken(process.env.EXPO_PUBLIC_MAPBOX_ACCESS_TOKEN);
}

// Mock event venue data - Golden Gate Park coordinates
const EVENT_CENTER = [-122.4829, 37.7694];
const EVENT_BOUNDS = {
  ne: [-122.4789, 37.7724],
  sw: [-122.4869, 37.7664],
};

// Event zones (polygons) - organic festival layout
const eventZones = {
  mainArea: {
    type: 'Feature',
    properties: {
      name: 'Festival Grounds',
      fillColor: 'rgba(34, 139, 34, 0.6)', // Forest green
      fillOutlineColor: 'rgba(34, 139, 34, 0.9)',
    },
    geometry: {
      type: 'Polygon',
      coordinates: [
        [
          [-122.4855, 37.77],
          [-122.485, 37.771],
          [-122.484, 37.7715],
          [-122.4825, 37.7712],
          [-122.481, 37.7705],
          [-122.4805, 37.7695],
          [-122.4808, 37.7685],
          [-122.4815, 37.7675],
          [-122.4825, 37.767],
          [-122.484, 37.7668],
          [-122.485, 37.7672],
          [-122.4858, 37.768],
          [-122.486, 37.769],
          [-122.4855, 37.77],
        ],
      ],
    },
  },
  parking: {
    type: 'Feature',
    properties: {
      name: 'Parking Area',
      fillColor: 'rgba(169, 169, 169, 0.5)', // Gray
      fillOutlineColor: 'rgba(255, 0, 0, 0.8)', // Red outline
    },
    geometry: {
      type: 'Polygon',
      coordinates: [
        [
          [-122.487, 37.768],
          [-122.4865, 37.7685],
          [-122.486, 37.7683],
          [-122.4858, 37.7675],
          [-122.4862, 37.767],
          [-122.4868, 37.7668],
          [-122.4873, 37.7672],
          [-122.4872, 37.7678],
          [-122.487, 37.768],
        ],
      ],
    },
  },
  camping: {
    type: 'Feature',
    properties: {
      name: 'Camping Zone',
      fillColor: 'rgba(255, 235, 205, 0.5)', // Blanched almond
      fillOutlineColor: 'rgba(139, 90, 43, 0.8)', // Tan
    },
    geometry: {
      type: 'Polygon',
      coordinates: [
        [
          [-122.4805, 37.772],
          [-122.4795, 37.7718],
          [-122.479, 37.771],
          [-122.4792, 37.7705],
          [-122.4798, 37.7702],
          [-122.4805, 37.7704],
          [-122.481, 37.771],
          [-122.4808, 37.7716],
          [-122.4805, 37.772],
        ],
      ],
    },
  },
};

// Points of Interest - Festival amenities
const pointsOfInterest = [
  // Bars scattered around the festival
  { id: 'bar1', coordinate: [-122.4835, 37.7695], type: 'bar', name: 'Main Bar' },
  { id: 'bar2', coordinate: [-122.482, 37.7685], type: 'bar', name: 'VIP Bar' },
  { id: 'bar3', coordinate: [-122.4815, 37.77], type: 'bar', name: 'Cocktail Lounge' },

  // Bathrooms at strategic locations
  { id: 'bathroom1', coordinate: [-122.484, 37.771], type: 'bathroom', name: 'Restrooms North' },
  { id: 'bathroom2', coordinate: [-122.483, 37.7675], type: 'bathroom', name: 'Restrooms South' },
  { id: 'bathroom3', coordinate: [-122.481, 37.769], type: 'bathroom', name: 'Restrooms East' },

  // Stage areas
  { id: 'stage1', coordinate: [-122.483, 37.77], type: 'stage', name: 'Main Stage' },
  { id: 'stage2', coordinate: [-122.482, 37.768], type: 'stage', name: 'Electronic Stage' },

  // Entrances
  { id: 'entrance1', coordinate: [-122.486, 37.769], type: 'entrance', name: 'Main Entrance' },
  { id: 'entrance2', coordinate: [-122.4805, 37.7685], type: 'entrance', name: 'VIP Entrance' },

  // Services
  { id: 'firstaid', coordinate: [-122.4845, 37.769], type: 'medical', name: 'Medical Tent' },
  { id: 'info', coordinate: [-122.485, 37.77], type: 'info', name: 'Info & Lost Items' },
  { id: 'food1', coordinate: [-122.4825, 37.769], type: 'food', name: 'Food Court' },
  { id: 'camping', coordinate: [-122.48, 37.771], type: 'camping', name: 'Camping Check-in' },
];

export const MapboxTestScreen: React.FC = () => {
  const theme = useTheme();
  const navigation = useNavigation<MapboxStackScreenProps<'MapboxTest'>['navigation']>();
  const [isMapReady, setIsMapReady] = useState(false);
  const [selectedPOI, setSelectedPOI] = useState<string | null>(null);
  const [mapError, setMapError] = useState<string | null>(null);

  useEffect(() => {
    // Request location permissions and initialize
    const initializeMap = async () => {
      try {
        if (Platform.OS === 'android') {
          await MapboxGL.requestAndroidLocationPermissions();
        }
        setIsMapReady(true);
      } catch (error) {
        setMapError('Failed to initialize map. Please check your internet connection.');
        console.error('Map initialization error:', error);
      }
    };

    initializeMap();
  }, []);
  return (
    <SafeAreaView style={styles.container}>
      <Box flex={1} backgroundColor="background">
        <Box padding="md_16" paddingBottom="xs_8">
          <Text variant="h_24SemiBold_section" color="text">
            Festival Map
          </Text>
          <Text variant="b_14Regular_content" color="textSecondary" marginTop="xs_8">
            Movuca Music Festival 2024
          </Text>
        </Box>

        <Box flex={1} margin="md_16" borderRadius="lg_16" overflow="hidden">
          {mapError ? (
            <Box flex={1} alignItems="center" justifyContent="center" padding="lg_24">
              <Phosphor.Warning size={48} color={theme.colors.error} weight="fill" />
              <Text variant="h_18Bold_formTitle" color="error" marginTop="md_16" textAlign="center">
                Map Error
              </Text>
              <Text
                variant="b_14Regular_content"
                color="textSecondary"
                marginTop="sm_12"
                textAlign="center">
                {mapError}
              </Text>
              <Button
                variant="primary"
                onPress={() => {
                  setMapError(null);
                  setIsMapReady(false);
                  // Retry initialization
                  const initializeMap = async () => {
                    try {
                      if (Platform.OS === 'android') {
                        await MapboxGL.requestAndroidLocationPermissions();
                      }
                      setIsMapReady(true);
                    } catch (error) {
                      setMapError(
                        'Failed to initialize map. Please check your internet connection.'
                      );
                      console.error('Map initialization error:', error);
                    }
                  };
                  initializeMap();
                }}
                title="Retry"
                style={{ marginTop: 24 }}
              />
            </Box>
          ) : isMapReady ? (
            <MapboxGL.MapView
              style={styles.map}
              styleURL="mapbox://styles/mapbox/dark-v11"
              zoomEnabled={true}
              scrollEnabled={true}
              pitchEnabled={false}
              rotateEnabled={true}
              compassEnabled={true}
              logoEnabled={false}
              attributionEnabled={false}
              onPress={() => setSelectedPOI(null)}
              onDidFailLoadingMap={error => {
                setMapError('Failed to load map. Please check your internet connection.');
                console.error('Map loading error:', error);
              }}>
              <MapboxGL.Camera
                zoomLevel={15.5}
                centerCoordinate={EVENT_CENTER}
                animationDuration={2000}
                pitch={0}
                bounds={{
                  ne: EVENT_BOUNDS.ne,
                  sw: EVENT_BOUNDS.sw,
                  paddingTop: 100,
                  paddingBottom: 250,
                  paddingLeft: 50,
                  paddingRight: 50,
                }}
              />

              {/* Event zone polygons */}
              {Object.entries(eventZones).map(([key, zone]) => (
                <MapboxGL.ShapeSource key={key} id={`zone-${key}`} shape={zone as any}>
                  <MapboxGL.FillLayer
                    id={`fill-${key}`}
                    style={{
                      fillColor: zone.properties.fillColor,
                      fillOpacity: 0.6,
                    }}
                  />
                  <MapboxGL.LineLayer
                    id={`line-${key}`}
                    style={{
                      lineColor: zone.properties.fillOutlineColor,
                      lineWidth: 2,
                    }}
                  />
                </MapboxGL.ShapeSource>
              ))}

              {/* Points of Interest */}
              {pointsOfInterest.map(poi => (
                <MapboxGL.PointAnnotation
                  key={poi.id}
                  id={poi.id}
                  coordinate={poi.coordinate}
                  anchor={{ x: 0.5, y: 0.5 }}
                  onSelected={() => setSelectedPOI(poi.id)}>
                  <View>
                    <Box
                      width={poi.type === 'stage' ? 50 : 36}
                      height={poi.type === 'stage' ? 50 : 36}
                      borderRadius="circle_9999"
                      style={[styles.markerShadow, { backgroundColor: getColorForType(poi.type) }]}
                      alignItems="center"
                      justifyContent="center">
                      {getIconForType(poi.type, '#FFFFFF')}
                    </Box>
                    {selectedPOI === poi.id && (
                      <Box
                        position="absolute"
                        bottom={40}
                        left={-50}
                        right={-50}
                        backgroundColor="background"
                        padding="xs_8"
                        borderRadius="sm_8"
                        style={styles.calloutShadow}>
                        <Text variant="l_12Medium_message" color="text" textAlign="center">
                          {poi.name}
                        </Text>
                      </Box>
                    )}
                  </View>
                </MapboxGL.PointAnnotation>
              ))}

              {/* User location */}
              <MapboxGL.UserLocation visible={true} />
            </MapboxGL.MapView>
          ) : (
            <Box flex={1} alignItems="center" justifyContent="center">
              <Text variant="b_16Regular_input" color="textSecondary">
                Initializing map...
              </Text>
            </Box>
          )}
        </Box>

        {/* Legend and Navigation */}
        <Box padding="md_16" gap="md_16">
          <Box>
            <Text variant="h_18Bold_formTitle" color="text" marginBottom="sm_12">
              Venue Areas
            </Text>
            <Box flexDirection="row" flexWrap="wrap" gap="sm_12">
              {Object.entries(eventZones).map(([key, zone]) => (
                <Box key={key} flexDirection="row" alignItems="center" gap="xs_8">
                  <Box
                    width={16}
                    height={16}
                    style={{ backgroundColor: zone.properties.fillOutlineColor }}
                    borderRadius="xs_4"
                  />
                  <Text variant="l_12Regular_helperText" color="textSecondary">
                    {zone.properties.name}
                  </Text>
                </Box>
              ))}
            </Box>
          </Box>

          <Button
            variant="secondary"
            onPress={() => navigation.navigate('Mapbox3D')}
            title="View 3D Map"
            icon={<Phosphor.Cube size={20} color={theme.colors.primary} />}
          />

          <Button
            variant="outline"
            onPress={() => navigation.navigate('MapboxTerrain')}
            title="Custom Terrain Style"
            icon={<Phosphor.Mountains size={20} color={theme.colors.primary} />}
          />
        </Box>
      </Box>
    </SafeAreaView>
  );
};

// Helper function to get icon components for different POI types
const getIconForType = (type: string, color: string): React.ReactElement => {
  const iconProps = { size: 20, color, weight: 'fill' as const };

  switch (type) {
    case 'bar':
      return <Phosphor.Martini {...iconProps} />;
    case 'bathroom':
      return <Phosphor.Toilet {...iconProps} />;
    case 'entrance':
      return <Phosphor.Door {...iconProps} />;
    case 'medical':
      return <Phosphor.FirstAid {...iconProps} />;
    case 'info':
      return <Phosphor.Info {...iconProps} />;
    case 'stage':
      return <Phosphor.MusicNotes {...iconProps} />;
    case 'food':
      return <Phosphor.ForkKnife {...iconProps} />;
    case 'camping':
      return <Phosphor.Tent {...iconProps} />;
    default:
      return <Phosphor.MapPin {...iconProps} />;
  }
};

// Get background color for POI types
const getColorForType = (type: string): string => {
  switch (type) {
    case 'bar':
      return '#FF1493'; // Deep pink
    case 'bathroom':
      return '#1E90FF'; // Dodger blue
    case 'stage':
      return '#FF6347'; // Tomato red
    case 'entrance':
      return '#32CD32'; // Lime green
    case 'medical':
      return '#FF0000'; // Red
    case 'info':
      return '#FFD700'; // Gold
    case 'food':
      return '#FF8C00'; // Dark orange
    case 'camping':
      return '#8B4513'; // Saddle brown
    default:
      return '#9370DB'; // Medium purple
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  map: {
    flex: 1,
  },
  markerShadow: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.35,
    shadowRadius: 3.84,
    elevation: 5,
  },
  calloutShadow: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
  },
});
