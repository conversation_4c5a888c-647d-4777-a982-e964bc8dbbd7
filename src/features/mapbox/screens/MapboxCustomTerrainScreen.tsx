import React, { useEffect, useState } from 'react';

import { Platform, StyleSheet } from 'react-native';

import MapboxGL from '@rnmapbox/maps';
import { SafeAreaView } from 'react-native-safe-area-context';

import { Box, Text, useTheme } from '@/src/core/theme';

// Set Mapbox access token
if (Platform.OS !== 'android' && process.env.EXPO_PUBLIC_MAPBOX_ACCESS_TOKEN) {
  MapboxGL.setAccessToken(process.env.EXPO_PUBLIC_MAPBOX_ACCESS_TOKEN);
}

// Festival area bounds
const FESTIVAL_CENTER = [-122.4829, 37.7694];

// Custom style JSON for dark green terrain look
const customMapStyle = {
  version: 8,
  sources: {
    'mapbox-terrain': {
      type: 'raster-dem',
      url: 'mapbox://mapbox.mapbox-terrain-dem-v1',
      tileSize: 512,
      maxzoom: 14,
    },
    'mapbox-satellite': {
      type: 'raster',
      url: 'mapbox://mapbox.satellite',
      tileSize: 512,
    },
  },
  layers: [
    {
      id: 'background',
      type: 'background',
      paint: {
        'background-color': '#0A1F0A', // Very dark green
      },
    },
    {
      id: 'satellite-base',
      type: 'raster',
      source: 'mapbox-satellite',
      paint: {
        'raster-opacity': 0.3, // Low opacity to maintain dark green theme
        'raster-saturation': -0.5, // Desaturate for more muted colors
        'raster-contrast': 0.2,
      },
    },
    {
      id: 'hillshade',
      type: 'hillshade',
      source: 'mapbox-terrain',
      paint: {
        'hillshade-shadow-color': '#061506', // Dark green shadow
        'hillshade-highlight-color': '#2D5A2D', // Lighter green highlight
        'hillshade-accent-color': '#1A3A1A', // Mid green accent
        'hillshade-exaggeration': 0.5,
        'hillshade-illumination-direction': 335,
      },
    },
  ],
  terrain: {
    source: 'mapbox-terrain',
    exaggeration: 1.5, // Exaggerate terrain for 3D effect
  },
};

// Festival overlay data
const festivalOverlay = {
  type: 'Feature',
  geometry: {
    type: 'Polygon',
    coordinates: [
      [
        [-122.485, 37.771],
        [-122.481, 37.771],
        [-122.481, 37.767],
        [-122.485, 37.767],
        [-122.485, 37.771],
      ],
    ],
  },
};

export const MapboxCustomTerrainScreen: React.FC = () => {
  const theme = useTheme();
  const [isMapReady, setIsMapReady] = useState(false);

  useEffect(() => {
    const initializeMap = async () => {
      try {
        if (Platform.OS === 'android') {
          await MapboxGL.requestAndroidLocationPermissions();
        }
        setIsMapReady(true);
      } catch (error) {
        console.error('Error initializing map:', error);
        setIsMapReady(true);
      }
    };

    initializeMap();
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      <Box flex={1} backgroundColor="background">
        <Box padding="md_16" paddingBottom="xs_8">
          <Text variant="h_24SemiBold_section" color="text">
            Custom Terrain Map
          </Text>
          <Text variant="b_14Regular_content" color="textSecondary" marginTop="xs_8">
            Festival grounds with custom terrain styling
          </Text>
        </Box>

        <Box flex={1} margin="md_16" borderRadius="lg_16" overflow="hidden">
          {isMapReady ? (
            <MapboxGL.MapView
              style={styles.map}
              styleJSON={JSON.stringify(customMapStyle)}
              zoomEnabled={true}
              scrollEnabled={true}
              pitchEnabled={true}
              rotateEnabled={true}
              compassEnabled={true}
              logoEnabled={false}
              attributionEnabled={false}>
              <MapboxGL.Camera
                zoomLevel={15}
                centerCoordinate={FESTIVAL_CENTER}
                pitch={60}
                bearing={0}
                animationDuration={2000}
              />

              {/* Festival area overlay with custom styling */}
              <MapboxGL.ShapeSource id="festival-area" shape={festivalOverlay}>
                <MapboxGL.FillLayer
                  id="festival-fill"
                  style={{
                    fillColor: 'rgba(50, 205, 50, 0.4)', // Lime green
                    fillOutlineColor: 'rgba(50, 205, 50, 0.8)',
                  }}
                />
                <MapboxGL.LineLayer
                  id="festival-outline"
                  style={{
                    lineColor: '#32CD32',
                    lineWidth: 3,
                    lineGapWidth: 1,
                    lineDasharray: [2, 1],
                  }}
                />
              </MapboxGL.ShapeSource>

              {/* Add custom markers for festival features */}
              <MapboxGL.PointAnnotation id="main-stage" coordinate={[-122.483, 37.77]}>
                <Box
                  width={60}
                  height={60}
                  borderRadius="circle_9999"
                  backgroundColor="surface"
                  alignItems="center"
                  justifyContent="center"
                  style={styles.stageMarker}>
                  <Text variant="h_18Bold_formTitle" color="primary">
                    🎵
                  </Text>
                </Box>
              </MapboxGL.PointAnnotation>

              <MapboxGL.UserLocation visible={true} />
            </MapboxGL.MapView>
          ) : (
            <Box flex={1} alignItems="center" justifyContent="center">
              <Text variant="b_16Regular_input" color="textSecondary">
                Loading custom terrain...
              </Text>
            </Box>
          )}
        </Box>

        <Box padding="md_16">
          <Text variant="h_18Bold_formTitle" color="text" marginBottom="xs_8">
            How to achieve this look:
          </Text>
          <Text variant="b_14Regular_content" color="textSecondary" marginBottom="xs_8">
            1. Create a custom style in Mapbox Studio
          </Text>
          <Text variant="b_14Regular_content" color="textSecondary" marginBottom="xs_8">
            2. Use dark green color palette (#0A1F0A to #2D5A2D)
          </Text>
          <Text variant="b_14Regular_content" color="textSecondary" marginBottom="xs_8">
            3. Add terrain source with hillshade layer
          </Text>
          <Text variant="b_14Regular_content" color="textSecondary">
            4. Apply custom festival overlays and markers
          </Text>
        </Box>
      </Box>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  map: {
    flex: 1,
  },
  stageMarker: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
});
