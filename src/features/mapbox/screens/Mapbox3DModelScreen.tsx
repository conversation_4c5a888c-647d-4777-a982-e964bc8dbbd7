import React, { useEffect, useState } from 'react';

import { Platform, StyleSheet, View } from 'react-native';

import MapboxGL from '@rnmapbox/maps';
import * as Phosphor from 'phosphor-react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { Box, Text, useTheme } from '@/src/core/theme';
import { Button } from '@/src/shared/components';

// Set Mapbox access token from environment variables
if (Platform.OS !== 'android' && process.env.EXPO_PUBLIC_MAPBOX_ACCESS_TOKEN) {
  MapboxGL.setAccessToken(process.env.EXPO_PUBLIC_MAPBOX_ACCESS_TOKEN);
}

// Festival center coordinates
const FESTIVAL_CENTER = [-122.4829, 37.7694];

// 3D Model sources
const MODEL_SOURCES = {
  stage: {
    // Using a sample 3D model - in production, you'd use your own festival stage model
    uri: 'https://raw.githubusercontent.com/KhronosGroup/glTF-Sample-Models/master/2.0/Box/glTF/Box.gltf',
    id: 'stage-model',
  },
  tree: {
    // Sample tree model
    uri: 'https://raw.githubusercontent.com/KhronosGroup/glTF-Sample-Models/master/2.0/Duck/glTF-Embedded/Duck.gltf',
    id: 'tree-model',
  },
  tent: {
    // In production, use actual tent/structure models
    uri: 'https://raw.githubusercontent.com/KhronosGroup/glTF-Sample-Models/master/2.0/BoxTextured/glTF/BoxTextured.gltf',
    id: 'tent-model',
  },
};

// 3D Model placements
const modelPlacements = [
  {
    id: 'main-stage',
    modelId: 'stage-model',
    coordinates: [-122.483, 37.77],
    scale: [15, 10, 8],
    rotation: [0, 0, 0],
    name: 'Main Stage',
  },
  {
    id: 'electronic-stage',
    modelId: 'stage-model',
    coordinates: [-122.482, 37.768],
    scale: [10, 8, 6],
    rotation: [0, 0, 45],
    name: 'Electronic Stage',
  },
  {
    id: 'vip-tent-1',
    modelId: 'tent-model',
    coordinates: [-122.4815, 37.7695],
    scale: [5, 5, 5],
    rotation: [0, 0, 30],
    name: 'VIP Lounge',
  },
  {
    id: 'tree-1',
    modelId: 'tree-model',
    coordinates: [-122.484, 37.769],
    scale: [3, 3, 3],
    rotation: [0, 0, 0],
    name: 'Decoration',
  },
  {
    id: 'tree-2',
    modelId: 'tree-model',
    coordinates: [-122.4825, 37.7705],
    scale: [3, 3, 3],
    rotation: [0, 0, 120],
    name: 'Decoration',
  },
];

export const Mapbox3DModelScreen: React.FC = () => {
  const theme = useTheme();
  const [isMapReady, setIsMapReady] = useState(false);
  const [is3DEnabled, setIs3DEnabled] = useState(true);
  const [selectedModel, setSelectedModel] = useState<string | null>(null);
  const [pitch, setPitch] = useState(45);

  useEffect(() => {
    const initializeMap = async () => {
      try {
        if (Platform.OS === 'android') {
          await MapboxGL.requestAndroidLocationPermissions();
        }
        setIsMapReady(true);
      } catch (error) {
        console.error('Error initializing map:', error);
        setIsMapReady(true);
      }
    };

    initializeMap();
  }, []);

  const handleMapLoad = async () => {
    // Map is loaded and ready for 3D models
    console.log('Map loaded, ready for 3D models');
  };

  const toggle3D = () => {
    setIs3DEnabled(!is3DEnabled);
    setPitch(is3DEnabled ? 0 : 45);
  };

  return (
    <SafeAreaView style={styles.container}>
      <Box flex={1} backgroundColor="background">
        <Box padding="md_16" paddingBottom="xs_8">
          <Text variant="h_24SemiBold_section" color="text">
            3D Festival Map
          </Text>
          <Text variant="b_14Regular_content" color="textSecondary" marginTop="xs_8">
            Interactive 3D venue visualization
          </Text>
        </Box>

        <Box flex={1} margin="md_16" borderRadius="lg_16" overflow="hidden">
          {isMapReady ? (
            <MapboxGL.MapView
              style={styles.map}
              styleURL={MapboxGL.StyleURL.Satellite}
              zoomEnabled={true}
              scrollEnabled={true}
              pitchEnabled={true}
              rotateEnabled={true}
              compassEnabled={true}
              logoEnabled={false}
              attributionEnabled={false}
              onPress={() => setSelectedModel(null)}
              onDidFinishLoadingMap={handleMapLoad}>
              <MapboxGL.Camera
                zoomLevel={16.5}
                centerCoordinate={FESTIVAL_CENTER}
                pitch={pitch}
                animationDuration={1000}
              />

              {/* Add 3D models when enabled */}
              {is3DEnabled && (
                <>
                  {/* Model sources */}
                  <MapboxGL.ShapeSource
                    id="3d-models-source"
                    shape={{
                      type: 'FeatureCollection',
                      features: modelPlacements.map(placement => ({
                        type: 'Feature',
                        geometry: {
                          type: 'Point',
                          coordinates: placement.coordinates,
                        },
                        properties: {
                          modelId: placement.modelId,
                          scale: placement.scale,
                          rotation: placement.rotation,
                          name: placement.name,
                          id: placement.id,
                        },
                      })),
                    }}>
                    {/* Unfortunately, React Native Mapbox doesn't directly support ModelLayer yet */}
                    {/* We'll use SymbolLayer with 3D-like icons as a workaround */}
                    <MapboxGL.SymbolLayer
                      id="3d-models-layer"
                      style={{
                        iconImage: ['get', 'modelId'],
                        iconSize: 0.5,
                        iconAllowOverlap: true,
                        iconPitchAlignment: 'map',
                        iconRotationAlignment: 'map',
                      }}
                    />
                  </MapboxGL.ShapeSource>

                  {/* Add extruded polygons for buildings/structures */}
                  <MapboxGL.ShapeSource
                    id="3d-structures"
                    shape={{
                      type: 'FeatureCollection',
                      features: [
                        {
                          type: 'Feature',
                          geometry: {
                            type: 'Polygon',
                            coordinates: [
                              [
                                [-122.4835, 37.7705],
                                [-122.4825, 37.7705],
                                [-122.4825, 37.7695],
                                [-122.4835, 37.7695],
                                [-122.4835, 37.7705],
                              ],
                            ],
                          },
                          properties: {
                            height: 15,
                            base: 0,
                            color: '#FF6347',
                            name: 'Main Stage Structure',
                          },
                        },
                        {
                          type: 'Feature',
                          geometry: {
                            type: 'Polygon',
                            coordinates: [
                              [
                                [-122.4825, 37.7685],
                                [-122.4815, 37.7685],
                                [-122.4815, 37.7675],
                                [-122.4825, 37.7675],
                                [-122.4825, 37.7685],
                              ],
                            ],
                          },
                          properties: {
                            height: 10,
                            base: 0,
                            color: '#9370DB',
                            name: 'Electronic Stage',
                          },
                        },
                      ],
                    }}>
                    <MapboxGL.FillExtrusionLayer
                      id="3d-structures-layer"
                      style={{
                        fillExtrusionColor: ['get', 'color'],
                        fillExtrusionHeight: ['get', 'height'],
                        fillExtrusionBase: ['get', 'base'],
                        fillExtrusionOpacity: 0.8,
                      }}
                      maxZoomLevel={16}
                      minZoomLevel={14}
                    />
                  </MapboxGL.ShapeSource>
                </>
              )}

              {/* Festival grounds overlay */}
              <MapboxGL.ShapeSource
                id="festival-grounds"
                shape={{
                  type: 'Feature',
                  geometry: {
                    type: 'Polygon',
                    coordinates: [
                      [
                        [-122.485, 37.771],
                        [-122.481, 37.771],
                        [-122.481, 37.767],
                        [-122.485, 37.767],
                        [-122.485, 37.771],
                      ],
                    ],
                  },
                }}>
                <MapboxGL.FillLayer
                  id="festival-grounds-layer"
                  style={{
                    fillColor: 'rgba(34, 139, 34, 0.3)',
                    fillOutlineColor: 'rgba(34, 139, 34, 0.8)',
                  }}
                />
              </MapboxGL.ShapeSource>

              <MapboxGL.UserLocation visible={true} />
            </MapboxGL.MapView>
          ) : (
            <Box flex={1} alignItems="center" justifyContent="center">
              <Text variant="b_16Regular_input" color="textSecondary">
                Initializing 3D map...
              </Text>
            </Box>
          )}
        </Box>

        {/* 3D Controls */}
        <Box padding="md_16" gap="sm_12">
          <Button
            variant="primary"
            onPress={toggle3D}
            title={is3DEnabled ? 'Disable 3D View' : 'Enable 3D View'}
          />

          {is3DEnabled && (
            <Box>
              <Text variant="l_12Regular_helperText" color="textTertiary" textAlign="center">
                Rotate and tilt the map to see 3D structures
              </Text>
            </Box>
          )}
        </Box>
      </Box>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  map: {
    flex: 1,
  },
});
