/**
 * Location Picker Component
 * Reusable component for selecting locations using Foursquare Places API
 */
import React, { forwardRef, useCallback, useDeferredValue, useState, useTransition } from 'react';

import {
  AccessibilityProps,
  ActivityIndicator,
  FlatList,
  KeyboardAvoidingView,
  Modal,
  Platform,
  Pressable,
  TextInput,
  View,
} from 'react-native';

import { Ionicons } from '@expo/vector-icons';
import { useQuery } from '@tanstack/react-query';
import { formatDistance } from 'date-fns';

import { FoursquarePlace, foursquare } from '@/src/core';
import { useTheme } from '@/src/core/theme';
import Box from '@/src/core/theme/Box';
import Text from '@/src/core/theme/Text';

/**
 * Selected location data
 */
export interface SelectedLocation {
  fsqId?: string;
  name: string;
  address: string;
  latitude: number;
  longitude: number;
  categories?: string[];
}

/**
 * Location picker props
 */
interface LocationPickerProps extends AccessibilityProps {
  /** Current value */
  value?: SelectedLocation | null;

  /** Callback when location is selected */
  onSelect: (location: SelectedLocation) => void;

  /** Placeholder text */
  placeholder?: string;

  /** User's current location for distance calculation */
  userLocation?: { latitude: number; longitude: number };

  /** Search radius in meters */
  radius?: number;

  /** Filter by Foursquare categories */
  categories?: string[];

  /** Show as modal or inline */
  mode?: 'modal' | 'inline';

  /** Custom trigger component (for modal mode) */
  trigger?: React.ReactNode;

  /** Error message */
  error?: string;

  /** Disabled state */
  disabled?: boolean;

  /** Show current location option */
  showCurrentLocation?: boolean;

  /** Custom styles */
  containerStyle?: any;
}

/**
 * Location picker component with Foursquare integration
 */
const LocationPicker = forwardRef<View, LocationPickerProps>((props, ref) => {
  const {
    value,
    onSelect,
    placeholder = 'Search for a place...',
    userLocation,
    radius = 10000,
    categories,
    mode = 'inline',
    trigger,
    error,
    disabled,
    showCurrentLocation = true,
    containerStyle,
    ...accessibilityProps
  } = props;

  const theme = useTheme();
  const [isModalVisible, setModalVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isPending, startTransition] = useTransition();

  // React 19 deferred search
  const deferredQuery = useDeferredValue(searchQuery);

  // Foursquare autocomplete query
  const autocompleteQuery = useQuery({
    queryKey: ['foursquare-autocomplete', deferredQuery, userLocation],
    queryFn: async () => {
      if (!deferredQuery.trim()) return { results: [] };

      return foursquare.autocomplete({
        query: deferredQuery,
        ll: userLocation ? `${userLocation.latitude},${userLocation.longitude}` : undefined,
        radius,
        types: ['place'],
        bias: 'proximity',
        limit: 10,
      });
    },
    enabled: deferredQuery.length > 2,
  });

  // Full search query for more results
  const searchResultsQuery = useQuery({
    queryKey: ['foursquare-search', deferredQuery, userLocation, categories],
    queryFn: async () => {
      if (!deferredQuery.trim()) return { results: [] };

      return foursquare.searchPlaces({
        query: deferredQuery,
        ll: userLocation ? `${userLocation.latitude},${userLocation.longitude}` : undefined,
        radius,
        categories,
        limit: 20,
        sort: 'distance',
      });
    },
    enabled: deferredQuery.length > 2,
  });

  // Update search with transition
  const handleSearch = useCallback((text: string) => {
    startTransition(() => {
      setSearchQuery(text);
    });
  }, []);

  // Handle place selection
  const handleSelectPlace = useCallback(
    async (place: FoursquarePlace) => {
      const selected: SelectedLocation = {
        fsqId: place.fsq_place_id,
        name: place.name,
        address: foursquare.getFormattedAddress(place.location),
        latitude: place.latitude || place.geocodes?.main?.latitude || 0,
        longitude: place.longitude || place.geocodes?.main?.longitude || 0,
        categories: place.categories?.map(cat => cat.name) || [],
      };

      onSelect(selected);

      if (mode === 'modal') {
        setModalVisible(false);
      }

      setSearchQuery('');
    },
    [onSelect, mode]
  );

  // Handle current location selection
  const handleSelectCurrentLocation = useCallback(() => {
    if (!userLocation) return;

    const selected: SelectedLocation = {
      name: 'Current Location',
      address: 'Your current location',
      latitude: userLocation.latitude,
      longitude: userLocation.longitude,
    };

    onSelect(selected);

    if (mode === 'modal') {
      setModalVisible(false);
    }
  }, [userLocation, onSelect, mode]);

  // Render place item
  const renderPlaceItem = ({ item }: { item: FoursquarePlace }) => {
    const primaryCategory = foursquare.getPrimaryCategory(item.categories);
    const distance = item.distance && userLocation ? formatDistance(item.distance, 'metric') : null;

    return (
      <Pressable
        onPress={() => handleSelectPlace(item)}
        disabled={disabled}
        accessibilityRole="button"
        accessibilityLabel={`Select ${item.name}`}>
        <Box
          paddingVertical="sm_12"
          paddingHorizontal="md_16"
          borderBottomWidth={1}
          borderBottomColor="border">
          <Box flexDirection="row" alignItems="center">
            <Box flex={1}>
              <Text variant="body" numberOfLines={1}>
                {item.name}
              </Text>
              {primaryCategory && (
                <Text variant="caption" color="textSecondary" numberOfLines={1}>
                  {primaryCategory.name}
                </Text>
              )}
              <Text variant="caption" color="textTertiary" numberOfLines={1}>
                {foursquare.getFormattedAddress(item.location)}
              </Text>
            </Box>
            {distance && (
              <Text variant="caption" color="textSecondary">
                {distance}
              </Text>
            )}
          </Box>
        </Box>
      </Pressable>
    );
  };

  // Render search content
  const renderSearchContent = () => {
    const isLoading = autocompleteQuery.isLoading || searchResultsQuery.isLoading || isPending;
    const places = searchResultsQuery.data?.results || [];

    return (
      <Box flex={1}>
        {/* Search input */}
        <Box
          paddingHorizontal="md_16"
          paddingVertical="sm_12"
          borderBottomWidth={1}
          borderBottomColor="border">
          <Box
            flexDirection="row"
            alignItems="center"
            backgroundColor="surface"
            borderRadius="medium"
            paddingHorizontal="sm_12"
            borderWidth={1}
            borderColor={error ? 'error' : 'border'}>
            <Ionicons name="search" size={20} color={theme.colors.textSecondary} />
            <TextInput
              ref={ref as any}
              style={{
                flex: 1,
                paddingVertical: theme.spacing.sm_12,
                paddingHorizontal: theme.spacing.xs_8,
                fontSize: 16,
                color: theme.colors.text,
              }}
              value={searchQuery}
              onChangeText={handleSearch}
              placeholder={placeholder}
              placeholderTextColor={theme.colors.textTertiary}
              editable={!disabled}
              autoCorrect={false}
              autoCapitalize="none"
              returnKeyType="search"
              {...accessibilityProps}
            />
            {searchQuery.length > 0 && (
              <Pressable
                onPress={() => setSearchQuery('')}
                accessibilityRole="button"
                accessibilityLabel="Clear search">
                <Ionicons name="close-circle" size={20} color={theme.colors.textSecondary} />
              </Pressable>
            )}
          </Box>
        </Box>

        {/* Current location option */}
        {showCurrentLocation && userLocation && !searchQuery && (
          <Pressable
            onPress={handleSelectCurrentLocation}
            disabled={disabled}
            accessibilityRole="button"
            accessibilityLabel="Use current location">
            <Box
              flexDirection="row"
              alignItems="center"
              paddingVertical="md_16"
              paddingHorizontal="md_16"
              borderBottomWidth={1}
              borderBottomColor="border">
              <Ionicons name="location" size={24} color={theme.colors.primary} />
              <Text variant="body" color="primary" marginLeft="sm_12">
                Use current location
              </Text>
            </Box>
          </Pressable>
        )}

        {/* Results */}
        {isLoading ? (
          <Box flex={1} justifyContent="center" alignItems="center">
            <ActivityIndicator size="large" color={theme.colors.primary} />
          </Box>
        ) : (
          <FlatList
            data={places}
            renderItem={renderPlaceItem}
            keyExtractor={item => item.fsq_place_id}
            contentContainerStyle={{
              paddingBottom: theme.spacing.xl_32,
            }}
            ListEmptyComponent={
              searchQuery.length > 2 ? (
                <Box padding="xl_32" alignItems="center">
                  <Text variant="body" color="textSecondary">
                    No places found
                  </Text>
                </Box>
              ) : null
            }
          />
        )}

        {/* Error message */}
        {error && (
          <Box paddingHorizontal="md_16" paddingVertical="xs_8" backgroundColor="errorLight">
            <Text variant="caption" color="error">
              {error}
            </Text>
          </Box>
        )}
      </Box>
    );
  };

  // Modal mode
  if (mode === 'modal') {
    return (
      <>
        {/* Trigger */}
        {trigger ? (
          <Pressable onPress={() => setModalVisible(true)} disabled={disabled}>
            {trigger}
          </Pressable>
        ) : (
          <Pressable
            onPress={() => setModalVisible(true)}
            disabled={disabled}
            accessibilityRole="button"
            accessibilityLabel="Select location">
            <Box
              paddingVertical="sm_12"
              paddingHorizontal="md_16"
              borderWidth={1}
              borderColor={error ? 'error' : 'border'}
              borderRadius="medium"
              backgroundColor="surface">
              <Text variant="body" color={value ? 'text' : 'textTertiary'}>
                {value?.name || placeholder}
              </Text>
              {value?.address && (
                <Text variant="caption" color="textSecondary">
                  {value.address}
                </Text>
              )}
            </Box>
          </Pressable>
        )}

        {/* Modal */}
        <Modal
          visible={isModalVisible}
          animationType="slide"
          presentationStyle="pageSheet"
          onRequestClose={() => setModalVisible(false)}>
          <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={{ flex: 1 }}>
            <Box flex={1} backgroundColor="background">
              {/* Header */}
              <Box
                flexDirection="row"
                alignItems="center"
                paddingHorizontal="md_16"
                paddingVertical="sm_12"
                borderBottomWidth={1}
                borderBottomColor="border">
                <Pressable
                  onPress={() => setModalVisible(false)}
                  accessibilityRole="button"
                  accessibilityLabel="Close">
                  <Ionicons name="close" size={24} color={theme.colors.text} />
                </Pressable>
                <Text variant="headline" marginLeft="md_16" flex={1}>
                  Select Location
                </Text>
              </Box>

              {/* Content */}
              {renderSearchContent()}
            </Box>
          </KeyboardAvoidingView>
        </Modal>
      </>
    );
  }

  // Inline mode
  return <Box style={containerStyle}>{renderSearchContent()}</Box>;
});

LocationPicker.displayName = 'LocationPicker';

export default LocationPicker;
