import React from 'react';

import { CaretRight, Fire, Images, Snowflake, Ticket } from 'phosphor-react-native';

import { Box, Pressable, Text, useTheme } from '@/src/core/theme';

interface PartyPostInteractionsProps {
  fires: number;
  ices: number;
  hasTickets?: boolean;
  hasGallery?: boolean;
  onTicketPress?: () => void;
  onGalleryPress?: () => void;
}

export const PartyPostInteractions: React.FC<PartyPostInteractionsProps> = ({
  fires,
  ices,
  hasTickets,
  hasGallery,
  onTicketPress,
  onGalleryPress,
}) => {
  const theme = useTheme();

  return (
    <Box flexDirection="row" justifyContent="space-between" alignItems="center">
      {/* Reaction counts */}
      <Box flexDirection="row" alignItems="center" gap="sm_12">
        <Box flexDirection="row" alignItems="center" gap="xxs_4">
          <Fire size={24} color={theme.colors.iconAccent} weight="regular" />
          <Text variant="l_10SemiBold_tag" color="mutedText">
            {fires}
          </Text>
        </Box>

        <Box flexDirection="row" alignItems="center" gap="xxs_4">
          <Snowflake size={24} color={theme.colors.iconPrimary} weight="regular" />
          <Text variant="l_10SemiBold_tag" color="mutedText">
            {ices}
          </Text>
        </Box>
      </Box>

      {/* Action buttons */}
      <Box flexDirection="row" alignItems="center" gap="xs_8">
        {hasTickets && (
          <Pressable onPress={onTicketPress} enabled={!!onTicketPress}>
            <Ticket size={24} color={theme.colors.iconDefault} weight="light" />
          </Pressable>
        )}

        {hasGallery && (
          <Pressable onPress={onGalleryPress} enabled={!!onGalleryPress}>
            <Images size={24} color={theme.colors.iconDefault} weight="light" />
          </Pressable>
        )}

        <CaretRight size={24} color={theme.colors.iconMuted} weight="light" />
      </Box>
    </Box>
  );
};
