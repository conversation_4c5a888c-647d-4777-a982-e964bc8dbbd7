import React from 'react';

import { Medal, Star } from 'phosphor-react-native';

import { Box, Pressable, Text, useTheme } from '@/src/core/theme';
import { TokensThemeColors } from '@/src/core/theme/theme';
import { PillVariants } from '@/src/core/theme/variants/pill';
import { Avatar } from '@/src/shared/components/Avatar';
import { Pill } from '@/src/shared/components/Pill';

interface PartyPostUserInfoProps {
  host: {
    id: string;
    name: string;
    avatar?: string;
    rating: number;
    isVerified?: boolean;
  };
  tags: string[];
  onHostPress?: () => void;
}

export const PartyPostUserInfo: React.FC<PartyPostUserInfoProps> = ({
  host,
  tags,
  onHostPress,
}) => {
  const theme = useTheme();

  // Define tag colors based on index or type
  const getTagBGColor = (index: number): TokensThemeColors => {
    const variants: TokensThemeColors[] = [
      'tagInfoBackground',
      'tagWarningBackground',
      'tagSuccessBackground',
      'tagErrorBackground',
    ];
    return variants[index % variants.length];
  };

  const getTagTextColor = (index: number): TokensThemeColors => {
    const variants: TokensThemeColors[] = [
      'tagInfoText',
      'tagWarningText',
      'tagSuccessText',
      'tagErrorText',
    ];
    return variants[index % variants.length];
  };

  return (
    <Box gap="xxs_4" flexGrow={1}>
      {/* Host info */}
      <Box flexDirection="row" alignItems="center" gap="xs_8">
        <Pressable onPress={onHostPress} enabled={!!onHostPress}>
          <Box flexDirection="row" alignItems="center" gap="xs_8">
            <Avatar
              size="xs"
              source={host.avatar ? { uri: host.avatar } : undefined}
              fallbackText={host.name}
            />

            <Box>
              <Box flexDirection="row" alignItems="center" gap="xxs_4">
                <Text variant="h_16SemiBold_button" color="mainText">
                  {host.name}
                </Text>
                {host.isVerified && (
                  <Medal size={16} color={theme.colors.brandMain} weight="fill" />
                )}
              </Box>

              <Box flexDirection="row" alignItems="center" gap="xxs_4">
                <Text variant="l_8SemiBold_hint" color="secondaryText">
                  {host.rating.toFixed(1)}
                </Text>
                <Star size={12} color={theme.colors.iconMuted} weight="fill" />
              </Box>
            </Box>
          </Box>
        </Pressable>
      </Box>
      {/* Tags */}
      <Box flexDirection="row" flexWrap="wrap" gap="xxs_4">
        {tags.map((tag, index) => (
          <Pill
            hideIcon
            key={`${tag}-${index}`}
            variant={'small'}
            text={tag}
            backgroundColor={getTagBGColor(index)}
            textColor={getTagTextColor(index)}
          />
        ))}
      </Box>
    </Box>
  );
};
