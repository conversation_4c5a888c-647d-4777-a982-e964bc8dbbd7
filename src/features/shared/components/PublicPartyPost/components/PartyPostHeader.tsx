import React, { useState } from 'react';

import { LayoutChangeEvent, useWindowDimensions } from 'react-native';

import { Beer<PERSON>tein, Calendar, Clock, MapPin, Ticket } from 'phosphor-react-native';

import { Box, Pressable, Text, useTheme } from '@/src/core/theme';
import { BlurredContentImage } from '@/src/shared/components/BlurredContentImage';

interface PartyPostHeaderProps {
  imageUrl: string;
  title: string;
  location: {
    name: string;
    distance?: string;
  };
  pricing: {
    entry: string;
    drinks: string;
  };
  date: string;
  time: string;
  cardWidth?: number;
  onLocationPress?: () => void;
}

export const PartyPostHeader: React.FC<PartyPostHeaderProps> = ({
  imageUrl,
  title,
  location,
  pricing,
  date,
  time,
  cardWidth,
  onLocationPress,
}) => {
  const theme = useTheme();

  return (
    <Box height={240} overflow="hidden">
      <BlurredContentImage
        source={imageUrl}
        width={cardWidth}
        height={240}
        blur={{ intensity: 5, regionHeight: 0.4 }}
        content={
          <Box
            position="absolute"
            bottom={0}
            left={0}
            right={0}
            padding="md_16"
            gap="xxs_4"
            justifyContent="center">
            <Text variant="h_24SemiBold_section" color="textInverted" numberOfLines={1}>
              {title}
            </Text>

            <Box flexDirection="row" gap="xxs_4" flexWrap="wrap">
              {/* Location */}
              <Pressable onPress={onLocationPress} enabled={!!onLocationPress}>
                <Box flexDirection="row" alignItems="center" gap="xxs_4">
                  <MapPin size={14} color={theme.colors.textInverted} weight="regular" />
                  <Text variant="l_10SemiBold_tag" color="textInverted">
                    {location.name}
                  </Text>
                </Box>
              </Pressable>

              {/* Entry price */}
              <Box flexDirection="row" alignItems="center" gap="xxs_4">
                <Ticket size={14} color={theme.colors.textInverted} weight="regular" />
                <Text variant="l_10SemiBold_tag" color="textInverted">
                  {pricing.entry}
                </Text>
              </Box>
              {/* Drinks price */}
              <Box flexDirection="row" alignItems="center" gap="xxs_4">
                <BeerStein size={14} color={theme.colors.textInverted} weight="regular" />
                <Text variant="l_10SemiBold_tag" color="textInverted">
                  {pricing.drinks}
                </Text>
              </Box>
            </Box>

            {/* Date and time */}
            <Box flexDirection="row" gap="xxs_4">
              <Box flexDirection="row" alignItems="center" gap="xxs_4">
                <Calendar size={14} color={theme.colors.textInverted} weight="regular" />
                <Text variant="l_10Medium_tooltip" color="textInverted">
                  {date}
                </Text>
              </Box>

              <Box flexDirection="row" alignItems="center" gap="xxs_4">
                <Clock size={12} color={theme.colors.textInverted} weight="regular" />
                <Text variant="l_10Medium_tooltip" color="textInverted">
                  {time}
                </Text>
              </Box>
            </Box>
          </Box>
        }
      />
    </Box>
  );
};
