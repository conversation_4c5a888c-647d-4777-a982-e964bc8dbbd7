import React, { useEffect, useState } from 'react';

import { LayoutChangeEvent, StyleSheet } from 'react-native';

import {
  Canvas,
  Group,
  Rect,
  LinearGradient as SkiaLinearGradient,
  rect,
  vec,
} from '@shopify/react-native-skia';
import { FireSimple, Snowflake, ThermometerSimple } from 'phosphor-react-native';
import { useDerivedValue, useSharedValue, withRepeat, withTiming } from 'react-native-reanimated';

import { Box, Text, useTheme } from '@/src/core/theme';
import { RoundedButton } from '@/src/shared/components/RoundedButton';

interface PartyPostVibeRatingProps {
  cold: number;
  hot: number;
}

const BAR_HEIGHT = 13;

export const PartyPostVibeRating: React.FC<PartyPostVibeRatingProps> = ({ cold, hot }) => {
  const theme = useTheme();
  const [canvasWidth, setCanvasWidth] = useState(0);
  const total = cold + hot;
  const coldPercentage = total > 0 ? (cold / total) * 100 : 50;
  const hotPercentage = total > 0 ? (hot / total) * 100 : 50;

  const handleLayout = (event: LayoutChangeEvent) => {
    if (event.nativeEvent.layout.width > 0 && !canvasWidth) {
      setCanvasWidth(event.nativeEvent.layout.width);
    }
  };

  return (
    <Box gap="xxs_4">
      {/* Vibe labels */}
      <Box flexDirection="row" justifyContent="space-between" alignItems="center">
        <Box flexDirection="row" alignItems="center" gap="xxs_4">
          <Text variant="l_10SemiBold_tag" color="brandMain">
            Cold
          </Text>
          <RoundedButton variant="extraSmall">
            <Snowflake size={12} color={theme.colors.brandMain} weight="bold" />
          </RoundedButton>
        </Box>

        <Box flexDirection="row" alignItems="center" gap="xxs_4">
          <RoundedButton variant="extraSmall">
            <FireSimple size={12} color={theme.colors.errorMain} weight="bold" />
          </RoundedButton>
          <Text variant="l_10SemiBold_tag" color="errorMain">
            Hot
          </Text>
        </Box>
      </Box>

      {/* Progress bar */}
      <Box height={13} borderRadius="xs_4" overflow="hidden" onLayout={handleLayout}>
        {canvasWidth > 0 && <HeatwaveProgressBar canvasWidth={canvasWidth} cold={cold} hot={hot} />}
      </Box>
      {/* Percentages */}
      <Box flexDirection="row" justifyContent="center" alignItems="center" gap="sm_12">
        <Box flexDirection="row" alignItems="center">
          <ThermometerSimple size={18} color={theme.colors.iconPrimary} weight="regular" />
          <Text variant="l_12Bold_highlight" color="iconPrimary" marginLeft="xxs_4">
            {coldPercentage.toFixed(0)}%
          </Text>
        </Box>

        <Box flexDirection="row" alignItems="center">
          <Text variant="l_12Bold_highlight" color="errorMain" marginRight="xxs_4">
            {hotPercentage.toFixed(0)}%
          </Text>
          <ThermometerSimple size={18} color={theme.colors.errorMain} weight="regular" />
        </Box>
      </Box>
    </Box>
  );
};

export function HeatwaveProgressBar({
  canvasWidth,
  cold,
  hot,
}: {
  canvasWidth: number;
  cold: number;
  hot: number;
}) {
  const theme = useTheme();
  const progress = useSharedValue(0);

  // Calculate the split point based on cold/hot percentages
  const total = cold + hot;
  const coldPercentage = total > 0 ? cold / total : 0.5;
  const splitPoint = canvasWidth * coldPercentage;

  // Animate between 0 → 1 (looping)
  useEffect(() => {
    progress.value = withRepeat(withTiming(1, { duration: 2000 }), -1, true);
  }, [progress]);

  // Animate cold wave x position within cold section
  const coldX = useDerivedValue(() => {
    // For cold section (0 to splitPoint), wave sweeps from left to right
    return -splitPoint + progress.value * splitPoint * 2;
  });

  // Animate hot wave x position within hot section
  const hotX = useDerivedValue(() => {
    // For hot section (splitPoint to canvasWidth), wave sweeps from right to left
    const hotSectionWidth = canvasWidth - splitPoint;
    return splitPoint + hotSectionWidth - progress.value * hotSectionWidth * 2;
  });

  // Animate opacity with stronger pulsing
  const waveOpacity = useDerivedValue(() => {
    // Strong pulse effect - goes from 0.3 to 1.0
    const pulse = progress.value < 0.5 ? progress.value * 2 : (1 - progress.value) * 2;
    return 0.3 + pulse * 0.7;
  });

  // Scale animation for pulsing effect
  const waveScale = useDerivedValue(() => {
    // Scale from 0.8 to 1.2 for dramatic effect
    const pulse = progress.value < 0.5 ? progress.value * 2 : (1 - progress.value) * 2;
    return [{ scaleX: 0.8 + pulse * 0.4 }];
  });

  return (
    <Canvas style={StyleSheet.absoluteFill}>
      {/* Base gradient with smooth transition */}
      <Rect x={0} y={0} width={canvasWidth} height={BAR_HEIGHT}>
        <SkiaLinearGradient
          start={vec(0, BAR_HEIGHT / 2)}
          end={vec(canvasWidth, BAR_HEIGHT / 2)}
          colors={[theme.colors.brandMain, theme.colors.primary, '#992033', theme.colors.errorMain]}
          positions={[0, Math.max(0, coldPercentage - 0.1), Math.min(1, coldPercentage + 0.1), 1]}
        />
      </Rect>

      {/* Cold wave with clipping */}
      <Group clip={rect(0, 0, splitPoint, BAR_HEIGHT)}>
        <Rect
          x={coldX}
          y={0}
          width={splitPoint}
          height={BAR_HEIGHT}
          opacity={waveOpacity}
          transform={waveScale}
          origin={vec(splitPoint / 2, BAR_HEIGHT / 2)}>
          <SkiaLinearGradient
            start={vec(0, BAR_HEIGHT / 2)}
            end={vec(splitPoint, BAR_HEIGHT / 2)}
            colors={[
              'rgba(0, 163, 255, 0)',
              'rgba(0, 163, 255, 1)', // Full opacity for stronger effect
              'rgba(0, 200, 255, 1)', // Brighter blue
              'rgba(0, 163, 255, 0)',
            ]}
            positions={[0, 0.3, 0.5, 1]}
          />
        </Rect>
      </Group>

      {/* Hot wave with clipping */}
      <Group clip={rect(splitPoint, 0, canvasWidth - splitPoint, BAR_HEIGHT)}>
        <Rect
          x={hotX}
          y={0}
          width={canvasWidth - splitPoint}
          height={BAR_HEIGHT}
          opacity={waveOpacity}
          transform={waveScale}
          origin={vec((canvasWidth - splitPoint) / 2, BAR_HEIGHT / 2)}>
          <SkiaLinearGradient
            start={vec(0, BAR_HEIGHT / 2)}
            end={vec(canvasWidth - splitPoint, BAR_HEIGHT / 2)}
            colors={[
              'rgba(255, 59, 48, 0)',
              'rgba(255, 59, 48, 0)',
              'rgba(255, 100, 50, 1)', // Brighter orange
              'rgba(255, 59, 48, 1)', // Full opacity
            ]}
            positions={[0, 0.3, 0.8, 1]}
          />
        </Rect>
      </Group>

      {/* Optional bright split point glow */}
      <Rect x={splitPoint - 20} y={0} width={40} height={BAR_HEIGHT} opacity={waveOpacity}>
        <SkiaLinearGradient
          start={vec(0, BAR_HEIGHT / 2)}
          end={vec(40, BAR_HEIGHT / 2)}
          colors={['rgba(255, 255, 255, 0)', 'rgba(255, 255, 255, 0.4)', 'rgba(255, 255, 255, 0)']}
          positions={[0, 0.5, 1]}
        />
      </Rect>
    </Canvas>
  );
}
