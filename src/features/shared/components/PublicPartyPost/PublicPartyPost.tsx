import React, { forwardRef } from 'react';

import { Pressable, ViewProps } from 'react-native';

import { Box } from '@/src/core/theme';
import { PublicPartyPostVariants } from '@/src/core/theme/variants/publicPartyPost';
import { Card, Divider } from '@/src/shared/components';

import {
  PartyPostHeader,
  PartyPostInteractions,
  PartyPostUserInfo,
  PartyPostVibeRating,
} from './components';

export interface PublicPartyPostData {
  id: string;
  imageUrl: string;
  title: string;
  location: {
    name: string;
    distance?: string;
  };
  pricing: {
    entry: string;
    drinks: string;
  };
  date: string;
  time: string;
  host: {
    id: string;
    name: string;
    avatar?: string;
    rating: number;
    isVerified?: boolean;
  };
  tags: string[];
  vibeRating: {
    cold: number;
    hot: number;
  };
  interactions: {
    fires: number;
    ices: number;
    hasTickets?: boolean;
    hasGallery?: boolean;
  };
}

export interface PublicPartyPostProps extends ViewProps {
  data: PublicPartyPostData;
  variant?: keyof PublicPartyPostVariants;
  onPress?: () => void;
  onHostPress?: () => void;
  onLocationPress?: () => void;
  onTicketPress?: () => void;
  onGalleryPress?: () => void;
  cardWidth?: number;
}

/**
 * PublicPartyPost component displays a party post card with event details
 *
 * @example
 * ```tsx
 * <PublicPartyPost
 *   data={{
 *     id: '1',
 *     imageUrl: 'https://example.com/party.jpg',
 *     title: 'Churrasco do Menos é Mais',
 *     location: { name: 'Skyline lounge' },
 *     pricing: { entry: '$15-30', drinks: '$3-10' },
 *     date: 'Saturday, Jan 15, 2025',
 *     time: '9:00PM',
 *     host: {
 *       id: '1',
 *       name: 'Lovina',
 *       rating: 4.9,
 *       isVerified: true
 *     },
 *     tags: ['Pagode', 'Live Performance', 'Muita MOVUCA', 'Barbecue', 'Drinks', 'Outdoor', 'Smoking'],
 *     vibeRating: { cold: 53, hot: 47 },
 *     interactions: { fires: 24, ices: 12, hasTickets: true, hasGallery: true }
 *   }}
 *   onPress={() => console.log('Post pressed')}
 * />
 * ```
 */
export const PublicPartyPost = forwardRef<any, PublicPartyPostProps>(
  (
    { data, onPress, onHostPress, onLocationPress, onTicketPress, onGalleryPress, ...props },
    ref
  ) => {
    return (
      <Pressable onPress={onPress}>
        <Card px="none_0">
          <Box>
            <PartyPostHeader
              cardWidth={props.cardWidth}
              imageUrl={data.imageUrl}
              title={data.title}
              location={data.location}
              pricing={data.pricing}
              date={data.date}
              time={data.time}
              onLocationPress={onLocationPress}
            />

            <Box gap="xxs_4" padding="xs_8">
              <PartyPostUserInfo host={data.host} tags={data.tags} onHostPress={onHostPress} />

              <PartyPostVibeRating cold={data.vibeRating.cold} hot={data.vibeRating.hot} />

              <Divider my="xxs_4" />

              <PartyPostInteractions
                fires={data.interactions.fires}
                ices={data.interactions.ices}
                hasTickets={data.interactions.hasTickets}
                hasGallery={data.interactions.hasGallery}
                onTicketPress={onTicketPress}
                onGalleryPress={onGalleryPress}
              />
            </Box>
          </Box>
        </Card>
      </Pressable>
    );
  }
);

PublicPartyPost.displayName = 'PublicPartyPost';
