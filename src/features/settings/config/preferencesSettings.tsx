import React from 'react';

import { Bell, Globe, Moon } from 'phosphor-react-native';

import type { SettingsItemType } from '../types';

export const getPreferencesSettings = (
  isDarkMode: boolean,
  onToggleTheme: () => void,
  actions: {
    handleNotificationSettings: () => void;
    handleLanguageSettings: () => void;
  }
): SettingsItemType[] => [
  {
    title: 'Notifications',
    icon: <Bell />,
    onPress: actions.handleNotificationSettings,
  },
  {
    title: 'Language & Region',
    icon: <Globe />,
    onPress: actions.handleLanguageSettings,
  },
  {
    title: 'Dark mode',
    icon: <Moon />,
    initialValue: isDarkMode,
    toggle: onToggleTheme,
  },
];
