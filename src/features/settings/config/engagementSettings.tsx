import React from 'react';

import { Lightbulb, ShareNetwork, Star, User } from 'phosphor-react-native';

import type { SettingsItemType } from '../types';

export const getEngagementSettings = (actions: {
  handleInviteFriends: () => void;
  handleRateApp: () => void;
  handleFeedback: () => void;
  handleSupport: () => void;
}): SettingsItemType[] => [
  {
    title: 'Invite friends',
    icon: <ShareNetwork />,
    onPress: actions.handleInviteFriends,
  },
  {
    title: 'Rate us',
    icon: <Star />,
    onPress: actions.handleRateApp,
  },
  {
    title: 'Leave your ideas',
    icon: <Lightbulb />,
    onPress: actions.handleFeedback,
  },
  {
    title: 'Support us',
    icon: <User />,
    onPress: actions.handleSupport,
  },
];
