import React from 'react';

import {
  At,
  DeviceMobileSpeaker,
  Envelope,
  Key,
  Password,
  ScanSmiley,
  SignOut,
  Warning,
} from 'phosphor-react-native';

import type { SettingsItemType } from '../types';

export const getSecuritySettings = (
  onBiometricToggle: (enabled: boolean) => void,
  onRememberMeToggle: (enabled: boolean) => void,
  onLogout: () => void,
  onDeleteAccount: () => void,
  onDevicePermissions?: () => void,
  onBiometricSettings?: () => void
): SettingsItemType[] => [
  {
    title: 'Device permissions',
    icon: <DeviceMobileSpeaker />,
    onPress: onDevicePermissions || (() => console.log('Open device permissions')),
  },
  {
    title: 'Biometric authentication',
    icon: <ScanSmiley />,
    onPress: onBiometricSettings || (() => console.log('Open biometric settings')),
  },
  {
    title: 'Remember me',
    icon: <Key />,
    initialValue: true, // TODO: Get from settings store
    toggle: onRememberMeToggle,
  },
  {
    title: 'Change email',
    icon: <Envelope />,
    onPress: () => {
      // TODO: Navigate to change email
      console.log('Navigate to change email');
    },
  },
  {
    title: 'Change password',
    icon: <Password />,
    onPress: () => {
      // TODO: Navigate to change password
      console.log('Navigate to change password');
    },
  },
  {
    title: 'Logout',
    icon: <SignOut />,
    danger: true,
    onPress: onLogout,
  },
  {
    title: 'Delete account',
    icon: <Warning />,
    danger: true,
    onPress: onDeleteAccount,
  },
];
