import React from 'react';

import {
  At,
  ChatTeardropDots,
  DeviceMobileSpeaker,
  Globe,
  Key,
  Lifebuoy,
  Person,
  Question,
  Scales,
} from 'phosphor-react-native';

import type { SettingsItemType } from '../types';

export const getSupportSettings = (actions: {
  handleHelpCenter: () => void;
  handleFAQ: () => void;
  handleContactUs: () => void;
  handleLegal: () => void;
  handleTermsOfService: () => void;
  handlePrivacyPolicy: () => void;
  handleCookiePolicy: () => void;
  handleAccessibility: () => void;
  handleAboutMovuca: () => void;
}): SettingsItemType[] => [
  {
    title: 'Help Center',
    icon: <Lifebuoy />,
    onPress: actions.handleHelpCenter,
  },
  {
    title: 'FAQ',
    icon: <Question />,
    onPress: actions.handleFAQ,
  },
  {
    title: 'Contact us',
    icon: <ChatTeardropDots />,
    onPress: actions.handleContactUs,
  },
  {
    title: 'Legal',
    icon: <Scales />,
    onPress: actions.handleLegal,
  },
  {
    title: 'Terms of Service',
    icon: <Person />,
    onPress: actions.handleTermsOfService,
  },
  {
    title: 'Privacy Policy',
    icon: <Key />,
    onPress: actions.handlePrivacyPolicy,
  },
  {
    title: 'Cookie Policy',
    icon: <Globe />,
    onPress: actions.handleCookiePolicy,
  },
  {
    title: 'Accessibility',
    icon: <DeviceMobileSpeaker />,
    onPress: actions.handleAccessibility,
  },
  {
    title: 'About Movuca',
    icon: <At />,
    onPress: actions.handleAboutMovuca,
  },
];
