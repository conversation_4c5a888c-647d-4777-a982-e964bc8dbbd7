import React from 'react';

import { At, Fingerprint, Person } from 'phosphor-react-native';

import type { SettingsItemType } from '../types';

export const getAccountSettings = (actions: {
  handleEditProfile: () => void;
  handleUsernameSetup: () => void;
  handleSignInOptions: () => void;
}): SettingsItemType[] => [
  {
    title: 'About you',
    icon: <Person />,
    onPress: actions.handleEditProfile,
  },
  {
    title: 'Your unique identity',
    label: 'Create your username',
    icon: <At />,
    onPress: actions.handleUsernameSetup,
  },
  {
    title: 'Sign-in options',
    icon: <Fingerprint />,
    onPress: actions.handleSignInOptions,
  },
];
