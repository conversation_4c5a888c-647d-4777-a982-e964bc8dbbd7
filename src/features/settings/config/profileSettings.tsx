import React from 'react';

import { User } from 'phosphor-react-native';

import type { SettingsItemType } from '../types';

export const getProfileSettings = (
  userInfo: {
    name?: string;
    email?: string;
    avatar?: string;
  },
  actions: {
    handleViewProfile: () => void;
  }
): SettingsItemType[] => [
  {
    title: userInfo.name || 'Your Profile',
    label: userInfo.email || 'View and edit your profile',
    icon: <User weight="bold" />,
    onPress: actions.handleViewProfile,
    showAvatar: true,
  },
];
