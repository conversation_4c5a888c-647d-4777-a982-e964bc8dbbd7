import { useCallback, useState } from 'react';

import { useTranslation } from 'react-i18next';

import {
  type LanguageResource,
  detectBestLanguage,
  getAvailableLanguages,
} from '@/src/core/i18n/languageDetection';

export type SupportedLanguage = {
  code: string;
  name: string;
  nativeName: string;
  flag: string;
  flagUrl: string;
};

// Get dynamic supported languages from available translations
export function getSupportedLanguages(): SupportedLanguage[] {
  return getAvailableLanguages().map(lang => ({
    code: lang.code,
    name: lang.name,
    nativeName: lang.nativeName,
    flag: lang.flag,
    flagUrl: lang.flagUrl,
  }));
}

export const useLanguageSettings = () => {
  const { i18n } = useTranslation();
  const [isChanging, setIsChanging] = useState(false);

  const supportedLanguages = getSupportedLanguages();
  const currentLanguage =
    supportedLanguages.find(lang => lang.code === i18n.language) || supportedLanguages[0];

  const changeLanguage = useCallback(
    async (languageCode: string) => {
      try {
        setIsChanging(true);
        await i18n.changeLanguage(languageCode);
        console.log('Language changed to:', languageCode);

        // TODO: Save to MMKV storage for persistence
        // TODO: Show success toast
      } catch (error) {
        console.error('Failed to change language:', error);
        // TODO: Show error toast
      } finally {
        setIsChanging(false);
      }
    },
    [i18n]
  );

  const getSystemLanguage = useCallback(() => {
    return detectBestLanguage();
  }, []);

  const useSystemLanguage = useCallback(async () => {
    const systemLang = getSystemLanguage();
    await changeLanguage(systemLang);
  }, [changeLanguage, getSystemLanguage]);

  return {
    currentLanguage,
    supportedLanguages,
    isChanging,
    changeLanguage,
    useSystemLanguage,
    getSystemLanguage,
  };
};
