import { useCallback } from 'react';

import { useNavigation } from '@react-navigation/native';

import type { SettingsStackScreenProps } from '@/src/core/navigation/types';

import { useAuth } from '../../auth/services';

export const useSettingsActions = () => {
  const { signOut } = useAuth();
  const navigation = useNavigation<SettingsStackScreenProps<'SettingsMain'>['navigation']>();

  const handleBiometricToggle = useCallback((enabled: boolean) => {
    // TODO: Implement biometric toggle logic
    console.log('Biometric authentication:', enabled);
  }, []);

  const handleRememberMeToggle = useCallback((enabled: boolean) => {
    // TODO: Implement remember me toggle logic
    console.log('Remember me:', enabled);
  }, []);

  const handleLogout = useCallback(() => {
    // TODO: Show confirmation dialog and implement logout
    signOut();
  }, [signOut]);

  const handleDeleteAccount = useCallback(() => {
    // TODO: Show confirmation dialog and implement account deletion
    console.log('Account deletion requested');
  }, []);

  const handleDevicePermissions = useCallback(() => {
    console.log('Navigate to device permissions');
    navigation.navigate('DevicePermissions');
  }, [navigation]);

  const handleBiometricSettings = useCallback(() => {
    console.log('Navigate to biometric settings');
    navigation.navigate('BiometricSettings');
  }, [navigation]);

  return {
    handleBiometricToggle,
    handleRememberMeToggle,
    handleLogout,
    handleDeleteAccount,
    handleDevicePermissions,
    handleBiometricSettings,
  };
};
