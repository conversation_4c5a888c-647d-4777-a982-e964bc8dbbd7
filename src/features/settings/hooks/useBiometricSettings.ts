import { useCallback, useEffect, useState } from 'react';

import { biometricAuth } from '@/src/core/libs';
import type { BiometricCapabilities } from '@/src/core/libs';

export type BiometricSettings = {
  isEnabled: boolean;
  requireForLogin: boolean;
  requireForSensitiveActions: boolean;
  requireForPayments: boolean;
};

export const useBiometricSettings = () => {
  const [settings, setSettings] = useState<BiometricSettings>({
    isEnabled: false,
    requireForLogin: true,
    requireForSensitiveActions: true,
    requireForPayments: true,
  });

  const [capabilities, setCapabilities] = useState<BiometricCapabilities | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticating, setIsAuthenticating] = useState(false);

  const loadBiometricCapabilities = useCallback(async () => {
    try {
      const caps = await biometricAuth.getCapabilities();
      setCapabilities(caps);
      setSettings(prev => ({ ...prev, isEnabled: caps.isAvailable }));
    } catch (error) {
      console.error('Failed to load biometric capabilities:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const toggleBiometricAuth = useCallback(
    async (enabled: boolean) => {
      if (!capabilities?.isAvailable) {
        console.warn('Biometric authentication not available');
        return;
      }

      if (enabled) {
        try {
          setIsAuthenticating(true);
          const result = await biometricAuth.authenticate({
            promptMessage: 'Enable biometric authentication',
            cancelLabel: 'Cancel',
            fallbackLabel: 'Use Passcode',
          });

          if (result.success) {
            setSettings(prev => ({ ...prev, isEnabled: true }));
            console.log('Biometric authentication enabled');
          } else {
            console.log('Biometric authentication failed:', result.error);
          }
        } catch (error) {
          console.error('Error enabling biometric auth:', error);
        } finally {
          setIsAuthenticating(false);
        }
      } else {
        setSettings(prev => ({ ...prev, isEnabled: false }));
        console.log('Biometric authentication disabled');
      }
    },
    [capabilities]
  );

  const updateLoginRequirement = useCallback((required: boolean) => {
    setSettings(prev => ({ ...prev, requireForLogin: required }));
    console.log('Login biometric requirement:', required);
  }, []);

  const updateSensitiveActionsRequirement = useCallback((required: boolean) => {
    setSettings(prev => ({ ...prev, requireForSensitiveActions: required }));
    console.log('Sensitive actions biometric requirement:', required);
  }, []);

  const updatePaymentsRequirement = useCallback((required: boolean) => {
    setSettings(prev => ({ ...prev, requireForPayments: required }));
    console.log('Payments biometric requirement:', required);
  }, []);

  const testBiometricAuth = useCallback(async () => {
    if (!capabilities?.isAvailable) {
      console.warn('Biometric authentication not available');
      return;
    }

    try {
      setIsAuthenticating(true);
      const result = await biometricAuth.authenticate({
        promptMessage: 'Test biometric authentication',
        cancelLabel: 'Cancel',
      });

      if (result.success) {
        console.log('Biometric test successful');
        // TODO: Show success toast
      } else {
        console.log('Biometric test failed:', result.error);
        // TODO: Show error toast
      }
    } catch (error) {
      console.error('Error testing biometric auth:', error);
    } finally {
      setIsAuthenticating(false);
    }
  }, [capabilities]);

  const checkEnrollmentStatus = useCallback(async () => {
    try {
      const needsEnrollment = await biometricAuth.needsEnrollment();
      return needsEnrollment;
    } catch (error) {
      console.error('Error checking enrollment status:', error);
      return false;
    }
  }, []);

  const getSecurityLevel = useCallback(async () => {
    try {
      return await biometricAuth.getSecurityLevel();
    } catch (error) {
      console.error('Error getting security level:', error);
      return 'none';
    }
  }, []);

  useEffect(() => {
    loadBiometricCapabilities();
  }, [loadBiometricCapabilities]);

  return {
    settings,
    capabilities,
    isLoading,
    isAuthenticating,
    toggleBiometricAuth,
    updateLoginRequirement,
    updateSensitiveActionsRequirement,
    updatePaymentsRequirement,
    testBiometricAuth,
    checkEnrollmentStatus,
    getSecurityLevel,
    reload: loadBiometricCapabilities,
  };
};
