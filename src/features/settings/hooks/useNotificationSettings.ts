import { useCallback, useEffect, useState } from 'react';

import { notification } from '@/src/core/libs';
import type { NotificationPermissions } from '@/src/core/libs';

export type NotificationSettings = {
  pushNotifications: boolean;
  smsNotifications: boolean;
  reminders: boolean;
  vibrateAndSound: boolean;
};

export const useNotificationSettings = () => {
  const [settings, setSettings] = useState<NotificationSettings>({
    pushNotifications: true,
    smsNotifications: false,
    reminders: true,
    vibrateAndSound: true,
  });

  const [permissions, setPermissions] = useState<NotificationPermissions | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Load current notification permissions
  const loadPermissions = useCallback(async () => {
    try {
      const currentPermissions = await notification.getPermissions();
      setPermissions(currentPermissions);
    } catch (error) {
      console.error('Failed to load notification permissions:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const updatePushNotifications = useCallback(async (enabled: boolean) => {
    try {
      if (enabled) {
        // Request permission if needed
        const permission = await notification.requestPermission();
        setPermissions(permission);

        if (permission.authorizationStatus === 1) {
          // Authorized
          setSettings(prev => ({ ...prev, pushNotifications: true }));
          console.log('Push notifications enabled');
        } else {
          console.log('Push notification permission denied');
          // TODO: Show alert to go to settings
        }
      } else {
        setSettings(prev => ({ ...prev, pushNotifications: false }));
        console.log('Push notifications disabled');
      }
    } catch (error) {
      console.error('Failed to update push notifications:', error);
    }
  }, []);

  const updateSmsNotifications = useCallback((enabled: boolean) => {
    setSettings(prev => ({ ...prev, smsNotifications: enabled }));
    console.log('SMS notifications:', enabled);
    // TODO: Integrate with SMS service
  }, []);

  const updateReminders = useCallback(async (enabled: boolean) => {
    setSettings(prev => ({ ...prev, reminders: enabled }));
    console.log('Reminders:', enabled);

    if (enabled) {
      // TODO: Schedule example reminder notification
      try {
        await notification.scheduleNotification({
          title: 'Reminder Test',
          body: 'This is a test reminder notification',
          trigger: {
            type: 'timestamp',
            timestamp: Date.now() + 10000, // 10 seconds from now
          },
        });
      } catch (error) {
        console.error('Failed to schedule reminder:', error);
      }
    }
  }, []);

  const updateVibrateAndSound = useCallback((enabled: boolean) => {
    setSettings(prev => ({ ...prev, vibrateAndSound: enabled }));
    console.log('Vibrate & sound:', enabled);
    // TODO: Update system sound/vibration settings
  }, []);

  const openNotificationSettings = useCallback(async () => {
    try {
      await notification.openSettings();
    } catch (error) {
      console.error('Failed to open notification settings:', error);
    }
  }, []);

  const testNotification = useCallback(async () => {
    try {
      await notification.displayNotification({
        title: 'Test Notification',
        body: 'This is a test notification from Movuca!',
        data: { type: 'test' },
      });
    } catch (error) {
      console.error('Failed to send test notification:', error);
    }
  }, []);

  useEffect(() => {
    loadPermissions();
  }, [loadPermissions]);

  return {
    settings,
    permissions,
    isLoading,
    updatePushNotifications,
    updateSmsNotifications,
    updateReminders,
    updateVibrateAndSound,
    openNotificationSettings,
    testNotification,
    reloadPermissions: loadPermissions,
  };
};
