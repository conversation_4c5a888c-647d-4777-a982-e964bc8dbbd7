import { useCallback, useEffect, useState } from 'react';

import * as Application from 'expo-application';
import Constants from 'expo-constants';
import * as Updates from 'expo-updates';

import { deviceInfo } from '@/src/core/libs';

export type AppInfo = {
  name: string;
  version: string;
  buildNumber: string;
  bundleId: string;
  nativeVersion: string;
  expoVersion: string;
  updateId?: string;
  channel?: string;
  runtimeVersion?: string;
};

export type AppDiagnostics = {
  installationId: string;
  sessionId: string;
  isUpdateAvailable: boolean;
  lastUpdateCheck?: Date;
  deviceContext: any;
  memoryUsage?: number;
  storageUsage?: number;
};

export const useAppInfo = () => {
  const [appInfo, setAppInfo] = useState<AppInfo | null>(null);
  const [diagnostics, setDiagnostics] = useState<AppDiagnostics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isCheckingUpdates, setIsCheckingUpdates] = useState(false);

  const loadAppInfo = useCallback(async () => {
    try {
      const [
        applicationName,
        applicationVersion,
        buildVersion,
        applicationId,
        nativeApplicationVersion,
      ] = await Promise.all([
        Application.getApplicationNameAsync(),
        Application.getApplicationVersionAsync(),
        Application.getApplicationBuildVersionAsync(),
        Application.getApplicationIdAsync(),
        Application.getNativeApplicationVersionAsync(),
      ]);

      const info: AppInfo = {
        name: applicationName || 'Movuca',
        version: applicationVersion || '1.0.0',
        buildNumber: buildVersion || '1',
        bundleId: applicationId || 'com.movuca.app',
        nativeVersion: nativeApplicationVersion || '1.0.0',
        expoVersion: Constants.expoVersion || 'Unknown',
      };

      // Add update-specific info if available
      if (Updates.updateId) {
        info.updateId = Updates.updateId;
      }
      if (Updates.channel) {
        info.channel = Updates.channel;
      }
      if (Updates.runtimeVersion) {
        info.runtimeVersion = Updates.runtimeVersion;
      }

      setAppInfo(info);
    } catch (error) {
      console.error('Failed to load app info:', error);
    }
  }, []);

  const loadDiagnostics = useCallback(async () => {
    try {
      const [installationId, deviceContext] = await Promise.all([
        Application.getInstallationIdAsync(),
        deviceInfo.getDeviceContext(),
      ]);

      const sessionId = Constants.sessionId || 'unknown';

      const diagnosticsData: AppDiagnostics = {
        installationId: installationId || 'unknown',
        sessionId,
        isUpdateAvailable: false,
        deviceContext,
      };

      setDiagnostics(diagnosticsData);
    } catch (error) {
      console.error('Failed to load diagnostics:', error);
    }
  }, []);

  const checkForUpdates = useCallback(async () => {
    try {
      setIsCheckingUpdates(true);

      if (!Updates.isEnabled) {
        console.log('Updates are not enabled');
        return false;
      }

      const update = await Updates.checkForUpdateAsync();

      if (update.isAvailable) {
        setDiagnostics(prev =>
          prev
            ? {
                ...prev,
                isUpdateAvailable: true,
                lastUpdateCheck: new Date(),
              }
            : null
        );
        return true;
      } else {
        setDiagnostics(prev =>
          prev
            ? {
                ...prev,
                isUpdateAvailable: false,
                lastUpdateCheck: new Date(),
              }
            : null
        );
        return false;
      }
    } catch (error) {
      console.error('Failed to check for updates:', error);
      return false;
    } finally {
      setIsCheckingUpdates(false);
    }
  }, []);

  const downloadAndInstallUpdate = useCallback(async () => {
    try {
      if (!Updates.isEnabled) {
        console.log('Updates are not enabled');
        return false;
      }

      const update = await Updates.fetchUpdateAsync();

      if (update.isNew) {
        await Updates.reloadAsync();
        return true;
      }

      return false;
    } catch (error) {
      console.error('Failed to download and install update:', error);
      return false;
    }
  }, []);

  const getAppSize = useCallback(async () => {
    try {
      // This is a simplified estimation
      // In a real app, you might use native modules to get actual app size
      return 'Unknown';
    } catch (error) {
      console.error('Failed to get app size:', error);
      return 'Unknown';
    }
  }, []);

  const clearAppCache = useCallback(async () => {
    try {
      // TODO: Implement cache clearing logic
      console.log('Clearing app cache...');
      return true;
    } catch (error) {
      console.error('Failed to clear cache:', error);
      return false;
    }
  }, []);

  const exportDiagnostics = useCallback(() => {
    if (!appInfo || !diagnostics) return null;

    return {
      app: appInfo,
      diagnostics,
      timestamp: new Date().toISOString(),
      platform: {
        os: Constants.platform?.ios ? 'iOS' : 'Android',
        version: Constants.platform?.ios?.systemVersion || Constants.platform?.android?.versionCode,
      },
      expo: {
        version: Constants.expoVersion,
        runtimeVersion: Constants.runtimeVersion,
        debugMode: __DEV__,
      },
    };
  }, [appInfo, diagnostics]);

  useEffect(() => {
    const loadAllInfo = async () => {
      setIsLoading(true);
      await Promise.all([loadAppInfo(), loadDiagnostics()]);
      setIsLoading(false);
    };

    loadAllInfo();
  }, [loadAppInfo, loadDiagnostics]);

  return {
    appInfo,
    diagnostics,
    isLoading,
    isCheckingUpdates,
    checkForUpdates,
    downloadAndInstallUpdate,
    getAppSize,
    clearAppCache,
    exportDiagnostics,
    reload: () => Promise.all([loadAppInfo(), loadDiagnostics()]),
  };
};
