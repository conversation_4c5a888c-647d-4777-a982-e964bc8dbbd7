import { useCallback, useEffect, useState } from 'react';

import { deviceInfo } from '@/src/core/libs';
import type { CellularInfo, DeviceInfo, NetworkInfo } from '@/src/core/libs';

export const useDevicePermissions = () => {
  const [device, setDevice] = useState<DeviceInfo | null>(null);
  const [network, setNetwork] = useState<NetworkInfo | null>(null);
  const [cellular, setCellular] = useState<CellularInfo | null>(null);
  const [capabilities, setCapabilities] = useState<any>(null);
  const [connectionQuality, setConnectionQuality] = useState<string>('unknown');
  const [isLoading, setIsLoading] = useState(true);

  const loadDeviceInfo = useCallback(async () => {
    try {
      setIsLoading(true);
      const context = await deviceInfo.getDeviceContext();

      setDevice(context.device);
      setNetwork(context.network);
      setCellular(context.cellular);
      setCapabilities(context.capabilities);
      setConnectionQuality(context.connectionQuality);
    } catch (error) {
      console.error('Failed to load device info:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshNetworkInfo = useCallback(async () => {
    try {
      const [networkInfo, quality] = await Promise.all([
        deviceInfo.getNetworkInfo(),
        deviceInfo.getConnectionQuality(),
      ]);

      setNetwork(networkInfo);
      setConnectionQuality(quality);
    } catch (error) {
      console.error('Failed to refresh network info:', error);
    }
  }, []);

  const checkDataSaving = useCallback(async () => {
    try {
      return await deviceInfo.shouldUseDataSaving();
    } catch (error) {
      console.error('Failed to check data saving:', error);
      return false;
    }
  }, []);

  useEffect(() => {
    loadDeviceInfo();
  }, [loadDeviceInfo]);

  return {
    device,
    network,
    cellular,
    capabilities,
    connectionQuality,
    isLoading,
    refreshNetworkInfo,
    checkDataSaving,
    reload: loadDeviceInfo,
  };
};
