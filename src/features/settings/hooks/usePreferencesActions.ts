import { useCallback } from 'react';

import { useNavigation } from '@react-navigation/native';

import { SettingsStackScreenProps } from '@/src/core/navigation/types';

export const usePreferencesActions = () => {
  const navigation = useNavigation<SettingsStackScreenProps<'SettingsMain'>['navigation']>();

  const handleNotificationSettings = useCallback(() => {
    console.log('Navigate to notification settings');
    navigation.navigate('NotificationSettings');
  }, [navigation]);

  const handleLanguageSettings = useCallback(() => {
    console.log('Navigate to language & region settings');
    navigation.navigate('LanguageSettings');
  }, [navigation]);

  const handleThemeToggle = useCallback((isDarkMode: boolean) => {
    console.log('Theme toggled:', isDarkMode ? 'dark' : 'light');
    // Theme toggle is handled by useThemeContext
  }, []);

  return {
    handleNotificationSettings,
    handleLanguageSettings,
    handleThemeToggle,
  };
};
