import { useCallback } from 'react';

import { Alert, Linking, Platform, Share } from 'react-native';

export const useEngagementActions = () => {
  const handleInviteFriends = useCallback(async () => {
    try {
      const shareOptions = {
        title: 'Check out Movuca!',
        message: 'Join me on Movuca - the best social platform! Download it now.',
        // url: 'https://movuca.app', // TODO: Add actual app store URL
      };

      await Share.share(shareOptions);
      console.log('Share dialog opened');
    } catch (error) {
      console.error('Error sharing:', error);
    }
  }, []);

  const handleRateApp = useCallback(async () => {
    try {
      // TODO: Replace with actual app store URLs
      const androidUrl = 'market://details?id=com.movuca.app';
      const iosUrl = 'itms-apps://itunes.apple.com/app/id123456789';

      const url = Platform.OS === 'ios' ? iosUrl : androidUrl;
      const canOpen = await Linking.canOpenURL(url);

      if (canOpen) {
        await Linking.openURL(url);
        console.log('App store opened for rating');
      } else {
        Alert.alert('Error', 'Unable to open app store');
      }
    } catch (error) {
      console.error('Error opening app store:', error);
      Alert.alert('Error', 'Unable to open app store');
    }
  }, []);

  const handleFeedback = useCallback(() => {
    // TODO: Navigate to feedback screen or open feedback form
    console.log('Open feedback form');
    // navigation.navigate('Feedback');
  }, []);

  const handleSupport = useCallback(() => {
    // TODO: Navigate to support/donation screen
    console.log('Open support page');
    // navigation.navigate('Support');
  }, []);

  return {
    handleInviteFriends,
    handleRateApp,
    handleFeedback,
    handleSupport,
  };
};
