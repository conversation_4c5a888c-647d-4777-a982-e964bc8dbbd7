import { useCallback, useState } from 'react';

import { AccessibilityInfo } from 'react-native';

export type AccessibilitySettings = {
  reduceMotion: boolean;
  highContrast: boolean;
  largeText: boolean;
  voiceOver: boolean;
  hapticFeedback: boolean;
  audioDescriptions: boolean;
  closedCaptions: boolean;
};

export const useAccessibilitySettings = () => {
  const [settings, setSettings] = useState<AccessibilitySettings>({
    reduceMotion: false,
    highContrast: false,
    largeText: false,
    voiceOver: false,
    hapticFeedback: true,
    audioDescriptions: false,
    closedCaptions: false,
  });

  const [systemInfo, setSystemInfo] = useState({
    isScreenReaderEnabled: false,
    isReduceMotionEnabled: false,
    isReduceTransparencyEnabled: false,
  });

  const loadSystemAccessibilityInfo = useCallback(async () => {
    try {
      const [screenReader, reduceMotion, reduceTransparency] = await Promise.all([
        AccessibilityInfo.isScreenReaderEnabled(),
        AccessibilityInfo.isReduceMotionEnabled(),
        AccessibilityInfo.isReduceTransparencyEnabled?.() || Promise.resolve(false),
      ]);

      setSystemInfo({
        isScreenReaderEnabled: screenReader,
        isReduceMotionEnabled: reduceMotion,
        isReduceTransparencyEnabled: reduceTransparency,
      });

      // Update local settings based on system settings
      setSettings(prev => ({
        ...prev,
        voiceOver: screenReader,
        reduceMotion: reduceMotion,
      }));
    } catch (error) {
      console.error('Failed to load system accessibility info:', error);
    }
  }, []);

  const updateReduceMotion = useCallback((enabled: boolean) => {
    setSettings(prev => ({ ...prev, reduceMotion: enabled }));
    console.log('Reduce motion:', enabled);
    // TODO: Update animation preferences globally
  }, []);

  const updateHighContrast = useCallback((enabled: boolean) => {
    setSettings(prev => ({ ...prev, highContrast: enabled }));
    console.log('High contrast:', enabled);
    // TODO: Update theme to high contrast variant
  }, []);

  const updateLargeText = useCallback((enabled: boolean) => {
    setSettings(prev => ({ ...prev, largeText: enabled }));
    console.log('Large text:', enabled);
    // TODO: Update text scaling globally
  }, []);

  const updateVoiceOver = useCallback((enabled: boolean) => {
    setSettings(prev => ({ ...prev, voiceOver: enabled }));
    console.log('Voice over:', enabled);
    // Note: VoiceOver is controlled by system, this is for app-specific behavior
  }, []);

  const updateHapticFeedback = useCallback((enabled: boolean) => {
    setSettings(prev => ({ ...prev, hapticFeedback: enabled }));
    console.log('Haptic feedback:', enabled);
    // TODO: Update haptic feedback preferences
  }, []);

  const updateAudioDescriptions = useCallback((enabled: boolean) => {
    setSettings(prev => ({ ...prev, audioDescriptions: enabled }));
    console.log('Audio descriptions:', enabled);
    // TODO: Enable audio descriptions for media content
  }, []);

  const updateClosedCaptions = useCallback((enabled: boolean) => {
    setSettings(prev => ({ ...prev, closedCaptions: enabled }));
    console.log('Closed captions:', enabled);
    // TODO: Enable closed captions for media content
  }, []);

  const announceForAccessibility = useCallback((message: string) => {
    AccessibilityInfo.announceForAccessibility(message);
  }, []);

  const setAccessibilityFocus = useCallback((reactTag: number) => {
    AccessibilityInfo.setAccessibilityFocus(reactTag);
  }, []);

  return {
    settings,
    systemInfo,
    updateReduceMotion,
    updateHighContrast,
    updateLargeText,
    updateVoiceOver,
    updateHapticFeedback,
    updateAudioDescriptions,
    updateClosedCaptions,
    loadSystemAccessibilityInfo,
    announceForAccessibility,
    setAccessibilityFocus,
  };
};
