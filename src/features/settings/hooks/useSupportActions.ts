import { useCallback } from 'react';

import { Alert, Linking } from 'react-native';

import { useNavigation } from '@react-navigation/native';

import { SettingsStackScreenProps } from '@/src/core/navigation/types';

export const useSupportActions = () => {
  const navigation = useNavigation<SettingsStackScreenProps<'SettingsMain'>['navigation']>();

  const handleHelpCenter = useCallback(() => {
    // TODO: Navigate to help center or open web URL
    console.log('Open help center');
    // navigation.navigate('HelpCenter');
  }, [navigation]);

  const handleFAQ = useCallback(() => {
    // TODO: Navigate to FAQ screen
    console.log('Open FAQ');
    // navigation.navigate('FAQ');
  }, [navigation]);

  const handleContactUs = useCallback(async () => {
    try {
      // TODO: Replace with actual support email
      const email = '<EMAIL>';
      const subject = 'Support Request';
      const body = 'Hi Movuca team,\n\nI need help with...\n\n';

      const mailtoUrl = `mailto:${email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;

      const canOpen = await Linking.canOpenURL(mailtoUrl);
      if (canOpen) {
        await Linking.openURL(mailtoUrl);
        console.log('Email client opened');
      } else {
        Alert.alert('Email Not Available', 'Please send us an <NAME_EMAIL>');
      }
    } catch (error) {
      console.error('Error opening email:', error);
      Alert.alert('Error', 'Unable to open email client');
    }
  }, []);

  const handleLegal = useCallback(() => {
    // TODO: Navigate to legal overview screen
    console.log('Open legal overview');
    // navigation.navigate('Legal');
  }, [navigation]);

  const handleTermsOfService = useCallback(async () => {
    try {
      // TODO: Replace with actual terms URL
      const termsUrl = 'https://movuca.app/terms';
      const canOpen = await Linking.canOpenURL(termsUrl);

      if (canOpen) {
        await Linking.openURL(termsUrl);
        console.log('Terms of Service opened');
      } else {
        Alert.alert('Error', 'Unable to open Terms of Service');
      }
    } catch (error) {
      console.error('Error opening terms:', error);
    }
  }, []);

  const handlePrivacyPolicy = useCallback(async () => {
    try {
      // TODO: Replace with actual privacy policy URL
      const privacyUrl = 'https://movuca.app/privacy';
      const canOpen = await Linking.canOpenURL(privacyUrl);

      if (canOpen) {
        await Linking.openURL(privacyUrl);
        console.log('Privacy Policy opened');
      } else {
        Alert.alert('Error', 'Unable to open Privacy Policy');
      }
    } catch (error) {
      console.error('Error opening privacy policy:', error);
    }
  }, []);

  const handleCookiePolicy = useCallback(async () => {
    try {
      // TODO: Replace with actual cookie policy URL
      const cookieUrl = 'https://movuca.app/cookies';
      const canOpen = await Linking.canOpenURL(cookieUrl);

      if (canOpen) {
        await Linking.openURL(cookieUrl);
        console.log('Cookie Policy opened');
      } else {
        Alert.alert('Error', 'Unable to open Cookie Policy');
      }
    } catch (error) {
      console.error('Error opening cookie policy:', error);
    }
  }, []);

  const handleAccessibility = useCallback(() => {
    console.log('Open accessibility settings');
    navigation.navigate('AccessibilitySettings');
  }, [navigation]);

  const handleAboutMovuca = useCallback(() => {
    console.log('Open about Movuca');
    navigation.navigate('AboutApp');
  }, [navigation]);

  return {
    handleHelpCenter,
    handleFAQ,
    handleContactUs,
    handleLegal,
    handleTermsOfService,
    handlePrivacyPolicy,
    handleCookiePolicy,
    handleAccessibility,
    handleAboutMovuca,
  };
};
