import { useCallback } from 'react';

import { useNavigation } from '@react-navigation/native';

import { SettingsStackScreenProps } from '@/src/core/navigation/types';

export const useAccountActions = () => {
  const navigation = useNavigation<SettingsStackScreenProps<'SettingsMain'>['navigation']>();

  const handleEditProfile = useCallback(() => {
    // TODO: Navigate to profile edit screen
    console.log('Navigate to profile edit');
    navigation.navigate('DisplayName');
  }, [navigation]);

  const handleUsernameSetup = useCallback(() => {
    // TODO: Navigate to username setup screen
    console.log('Navigate to username setup');
    // navigation.navigate('UsernameSetup');
  }, [navigation]);

  const handleSignInOptions = useCallback(() => {
    // TODO: Navigate to sign-in options screen
    console.log('Navigate to sign-in options');
    // navigation.navigate('SignInOptions');
  }, [navigation]);

  return {
    handleEditProfile,
    handleUsernameSetup,
    handleSignInOptions,
  };
};
