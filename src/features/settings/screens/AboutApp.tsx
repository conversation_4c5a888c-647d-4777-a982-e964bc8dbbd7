import React from 'react';

import { Platform, Share } from 'react-native';

import { useNavigation } from '@react-navigation/native';
import {
  ArrowClockwise,
  BugBeetle,
  CloudArrowDown,
  Copy,
  Download,
  Export,
  Gear,
  Info,
  ShareNetwork,
  Trash,
} from 'phosphor-react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';

import { Box, Row, ScrollableContainer, Text, useTheme } from '@/src/core/theme';
import { Button, Card, NavigationTopBar } from '@/src/shared/components';
import { Divider } from '@/src/shared/components/Divider';

import { useAppInfo } from '../hooks/useAppInfo';

type InfoItemProps = {
  label: string;
  value: string;
  icon: React.ReactElement<{ color?: string; weight?: string; size?: number }>;
  onPress?: () => void;
};

const InfoItem = ({ label, value, icon, onPress }: InfoItemProps) => {
  const { colors } = useTheme();

  return (
    <Row
      width="100%"
      gap="xs_8"
      alignItems="center"
      onPress={onPress}
      style={{ opacity: onPress ? 0.8 : 1 }}>
      {React.cloneElement(icon, {
        color: colors.iconActiveInput,
        weight: 'regular',
        size: 16,
      })}
      <Box flex={1}>
        <Text variant="l_12Regular_helperText" color="textSecondary">
          {label}
        </Text>
        <Text variant="b_14Regular_content" color="text">
          {value}
        </Text>
      </Box>
      {onPress && <Copy size={16} color={colors.textTertiary} />}
    </Row>
  );
};

type ActionButtonProps = {
  title: string;
  description: string;
  icon: React.ReactElement<{ color?: string; weight?: string; size?: number }>;
  onPress: () => void;
  variant?: 'primary' | 'outline' | 'ghost' | 'danger';
  disabled?: boolean;
};

const ActionButton = ({
  title,
  description,
  icon,
  onPress,
  variant = 'outline',
  disabled = false,
}: ActionButtonProps) => {
  return (
    <Box width="100%">
      <Button
        variant={variant}
        title={title}
        onPress={onPress}
        disabled={disabled}
        leftIcon={React.cloneElement(icon, { size: 16 })}
      />
      <Text
        variant="l_12Regular_helperText"
        color="textSecondary"
        textAlign="center"
        marginTop="xs_8">
        {description}
      </Text>
    </Box>
  );
};

const AboutApp = () => {
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();
  const {
    appInfo,
    diagnostics,
    isLoading,
    isCheckingUpdates,
    checkForUpdates,
    downloadAndInstallUpdate,
    clearAppCache,
    exportDiagnostics,
  } = useAppInfo();

  const copyToClipboard = (text: string) => {
    // TODO: Implement clipboard copy
    console.log('Copied to clipboard:', text);
  };

  const shareAppInfo = async () => {
    try {
      const diagnosticsData = exportDiagnostics();
      if (diagnosticsData) {
        await Share.share({
          title: 'Movuca App Information',
          message: `Movuca App v${appInfo?.version} (${appInfo?.buildNumber})\n\nDiagnostics data exported for support.`,
        });
      }
    } catch (error) {
      console.error('Failed to share app info:', error);
    }
  };

  const handleCheckUpdates = async () => {
    const updateAvailable = await checkForUpdates();
    if (updateAvailable) {
      console.log('Update available');
      // TODO: Show update dialog
    } else {
      console.log('App is up to date');
      // TODO: Show success toast
    }
  };

  const handleInstallUpdate = async () => {
    const success = await downloadAndInstallUpdate();
    if (success) {
      console.log('Update installed, restarting...');
    } else {
      console.log('No update to install');
    }
  };

  const handleClearCache = async () => {
    const success = await clearAppCache();
    if (success) {
      console.log('Cache cleared');
      // TODO: Show success toast
    } else {
      console.log('Failed to clear cache');
      // TODO: Show error toast
    }
  };

  if (isLoading) {
    return (
      <SafeAreaView
        style={{
          flex: 1,
          paddingTop: Platform.OS === 'android' ? insets.top + 12 : 0,
        }}
        edges={['top']}>
        <NavigationTopBar title="About Movuca" onPress={() => navigation.goBack()} />
        <Box flex={1} justifyContent="center" alignItems="center">
          <Text variant="b_16Regular_input" color="textSecondary">
            Loading app information...
          </Text>
        </Box>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView
      style={{
        flex: 1,
        paddingTop: Platform.OS === 'android' ? insets.top + 12 : 0,
      }}
      edges={['top']}>
      <NavigationTopBar title="About Movuca" onPress={() => navigation.goBack()} />

      <ScrollableContainer backgroundColor="mainBackground" padding="md_16" gap="sm_12">
        {/* App Information */}
        <Card padding="sm_12" showToolbar toolbarTitle="App Information">
          {appInfo && (
            <>
              <InfoItem
                label="App Name"
                value={appInfo.name}
                icon={<Info />}
                onPress={() => copyToClipboard(appInfo.name)}
              />

              <Divider />

              <InfoItem
                label="Version"
                value={appInfo.version}
                icon={<Info />}
                onPress={() => copyToClipboard(appInfo.version)}
              />

              <Divider />

              <InfoItem
                label="Build Number"
                value={appInfo.buildNumber}
                icon={<Info />}
                onPress={() => copyToClipboard(appInfo.buildNumber)}
              />

              <Divider />

              <InfoItem
                label="Bundle ID"
                value={appInfo.bundleId}
                icon={<Info />}
                onPress={() => copyToClipboard(appInfo.bundleId)}
              />

              <Divider />

              <InfoItem
                label="Expo Version"
                value={appInfo.expoVersion}
                icon={<Info />}
                onPress={() => copyToClipboard(appInfo.expoVersion)}
              />

              {appInfo.updateId && (
                <>
                  <Divider />
                  <InfoItem
                    label="Update ID"
                    value={appInfo.updateId.slice(0, 8)}
                    icon={<CloudArrowDown />}
                    onPress={() => copyToClipboard(appInfo.updateId!)}
                  />
                </>
              )}

              {appInfo.channel && (
                <>
                  <Divider />
                  <InfoItem
                    label="Update Channel"
                    value={appInfo.channel}
                    icon={<CloudArrowDown />}
                    onPress={() => copyToClipboard(appInfo.channel!)}
                  />
                </>
              )}
            </>
          )}
        </Card>

        {/* Diagnostics */}
        {diagnostics && (
          <Card padding="sm_12" showToolbar toolbarTitle="Diagnostics">
            <InfoItem
              label="Installation ID"
              value={diagnostics.installationId.slice(0, 8)}
              icon={<BugBeetle />}
              onPress={() => copyToClipboard(diagnostics.installationId)}
            />

            <Divider />

            <InfoItem
              label="Session ID"
              value={diagnostics.sessionId.slice(0, 8)}
              icon={<BugBeetle />}
              onPress={() => copyToClipboard(diagnostics.sessionId)}
            />

            <Divider />

            <InfoItem
              label="Device Type"
              value={diagnostics.deviceContext?.device?.deviceType || 'Unknown'}
              icon={<Gear />}
              onPress={() =>
                copyToClipboard(diagnostics.deviceContext?.device?.deviceType || 'Unknown')
              }
            />

            <Divider />

            <InfoItem
              label="Connection"
              value={diagnostics.deviceContext?.network?.type || 'Unknown'}
              icon={<Gear />}
              onPress={() => copyToClipboard(diagnostics.deviceContext?.network?.type || 'Unknown')}
            />

            {diagnostics.lastUpdateCheck && (
              <>
                <Divider />
                <InfoItem
                  label="Last Update Check"
                  value={diagnostics.lastUpdateCheck.toLocaleString()}
                  icon={<CloudArrowDown />}
                />
              </>
            )}
          </Card>
        )}

        {/* Actions */}
        <Card padding="sm_12" showToolbar toolbarTitle="App Management">
          <Row gap="sm_12" justifyContent="space-between" marginBottom="sm_12">
            <Box flex={1}>
              <ActionButton
                title="Check Updates"
                description="Check for app updates"
                icon={<CloudArrowDown />}
                onPress={handleCheckUpdates}
                disabled={isCheckingUpdates}
              />
            </Box>
            {diagnostics?.isUpdateAvailable && (
              <Box flex={1}>
                <ActionButton
                  title="Install Update"
                  description="Install available update"
                  icon={<Download />}
                  onPress={handleInstallUpdate}
                  variant="primary"
                />
              </Box>
            )}
          </Row>

          <Row gap="sm_12" justifyContent="space-between" marginBottom="sm_12">
            <Box flex={1}>
              <ActionButton
                title="Clear Cache"
                description="Free up storage space"
                icon={<Trash />}
                onPress={handleClearCache}
                variant="danger"
              />
            </Box>
            <Box flex={1}>
              <ActionButton
                title="Export Diagnostics"
                description="Share app information"
                icon={<Export />}
                onPress={shareAppInfo}
              />
            </Box>
          </Row>
        </Card>

        {/* Legal */}
        <Card padding="sm_12" showToolbar toolbarTitle="Legal & Support">
          <Box gap="sm_12">
            <Text variant="b_14Regular_content" color="text" textAlign="center">
              Movuca - Connect, Share, Celebrate
            </Text>
            <Text variant="l_12Regular_helperText" color="textSecondary" textAlign="center">
              © 2024 Movuca. All rights reserved.
            </Text>
            <Text variant="l_12Regular_helperText" color="textSecondary" textAlign="center">
              Built with React Native, Expo, and ❤️
            </Text>
          </Box>
        </Card>

        <Box padding="sm_12">
          <Text variant="l_12Regular_helperText" color="textSecondary" textAlign="center">
            Tap any value above to copy it to your clipboard. Use the export feature to share
            diagnostics with our support team.
          </Text>
        </Box>
      </ScrollableContainer>
    </SafeAreaView>
  );
};

export default AboutApp;
