import { Image, KeyboardAvoidingView, Platform } from 'react-native';

import { useNavigation } from '@react-navigation/native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';

import EnableFaceIdImage from '@/src/assets/images/enableFaceid.png';
import { SettingsStackScreenProps } from '@/src/core/navigation/types';
import { Box, Text, useTheme } from '@/src/core/theme';
import { Button, NavigationTopBar } from '@/src/shared/components';

const EnableFaceIdScreen = () => {
  const navigation = useNavigation<SettingsStackScreenProps<'EnableFaceId'>['navigation']>();
  const insets = useSafeAreaInsets();
  const theme = useTheme();

  return (
    <SafeAreaView
      style={{
        flex: 1,
        paddingTop: Platform.OS === 'android' ? insets.top + 12 : 0,
      }}
      edges={['top', 'bottom']}>
      <NavigationTopBar onPress={() => navigation.goBack()} />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        style={{ flex: 1 }}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 24 : 0}>
        <Box flex={1} padding="md_16" backgroundColor="mainBackground">
          <Box flex={1} alignItems="center" justifyContent="center" gap="sm_12">
            <Text variant="h_32SemiBold_Page" textAlign="center">
              Enable Face ID
            </Text>

            <Text variant="l_12Regular_helperText" textAlign="center">
              Log in faster and more securely to your{'\n'}account with your biometric ID.
            </Text>

            <Image
              source={EnableFaceIdImage}
              style={{
                resizeMode: 'contain',
              }}
            />
          </Box>

          <Box gap="xs_8" style={{ paddingBottom: insets.bottom }}>
            <Button title="Enable now" width="100%" onPress={() => {}} />

            <Button variant="ghost" title="Maybe later" width="100%" onPress={() => {}} />
          </Box>
        </Box>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default EnableFaceIdScreen;
