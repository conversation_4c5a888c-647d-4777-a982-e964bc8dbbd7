import { KeyboardAvoidingView, Platform } from 'react-native';

import { useNavigation } from '@react-navigation/native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';

import { SettingsStackScreenProps } from '@/src/core/navigation/types';
import { Box, Text, useTheme } from '@/src/core/theme';
import { Avatar, Button, NavigationTopBar } from '@/src/shared/components';

import UserPhotoSelect from '../components/UserPhotoSelect';

const AddProfilePhotoScreen = () => {
  const navigation = useNavigation<SettingsStackScreenProps<'AddProfilePhoto'>['navigation']>();
  const insets = useSafeAreaInsets();
  const theme = useTheme();

  return (
    <SafeAreaView
      style={{
        flex: 1,
        paddingTop: Platform.OS === 'android' ? insets.top + 12 : 0,
      }}
      edges={['top', 'bottom']}>
      <NavigationTopBar onPress={() => navigation.goBack()} />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        style={{ flex: 1 }}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 24 : 0}>
        <Box flex={1} padding="md_16" backgroundColor="mainBackground">
          <Box flex={1} alignItems="center" justifyContent="center" gap="sm_12">
            <Text variant="h_32SemiBold_Page" textAlign="center">
              Add a profile photo
            </Text>

            <Text variant="l_12Regular_helperText">You can update this later in your profile</Text>

            <UserPhotoSelect onPress={() => console.log('Photo')} />

            <Text variant="b_18Bold_ComponentHeader">Henrique.Marques</Text>
          </Box>

          <Box style={{ paddingBottom: insets.bottom }}>
            <Button
              title="Update profile"
              width="100%"
              onPress={() => navigation.navigate('EnableFaceId')}
            />
          </Box>
        </Box>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default AddProfilePhotoScreen;
