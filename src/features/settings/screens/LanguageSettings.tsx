import React from 'react';

import { useNavigation } from '@react-navigation/native';
import { Check, Globe, Phone } from 'phosphor-react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';

import { Box, Row, ScrollableContainer, Text, useTheme } from '@/src/core/theme';
import { PressableAnimated } from '@/src/core/theme/Pressable';
import { Card, NavigationTopBar } from '@/src/shared/components';
import { Divider } from '@/src/shared/components/Divider';

import { type SupportedLanguage, useLanguageSettings } from '../hooks/useLanguageSettings';

type LanguageItemProps = {
  language: SupportedLanguage;
  isSelected: boolean;
  onSelect: () => void;
  isChanging: boolean;
};

const LanguageItem = ({ language, isSelected, onSelect, isChanging }: LanguageItemProps) => {
  const { colors } = useTheme();

  return (
    <PressableAnimated onPress={onSelect} enabled={!isChanging}>
      <Row width="100%" justifyContent="space-between" alignItems="center" paddingVertical="xs_8">
        <Row gap="sm_12" flex={1} alignItems="center">
          <Text variant="h_24SemiBold_section" color="text">
            {language.flag}
          </Text>
          <Box flex={1}>
            <Text variant="b_14SemiBold_listTitle" color="text">
              {language.nativeName}
            </Text>
            <Text variant="l_12Regular_helperText" color="textSecondary">
              {language.name}
            </Text>
          </Box>
        </Row>
        {isSelected && <Check size={20} color={colors.primary} weight="bold" />}
      </Row>
    </PressableAnimated>
  );
};

const LanguageSettings = () => {
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();
  const { currentLanguage, supportedLanguages, isChanging, changeLanguage, useSystemLanguage } =
    useLanguageSettings();

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <NavigationTopBar title="Language & Region" onPress={() => navigation.goBack()} />

      <ScrollableContainer
        backgroundColor="mainBackground"
        padding="md_16"
        gap="sm_12"
        contentContainerStyle={{ paddingBottom: insets.bottom }}>
        {/* Current Language */}
        <Card padding="sm_12" showToolbar toolbarTitle="Current Language">
          <Row gap="sm_12" alignItems="center">
            <Text variant="h_24SemiBold_section" color="text">
              {currentLanguage.flag}
            </Text>
            <Box flex={1}>
              <Text variant="b_16SemiBold_button" color="text">
                {currentLanguage.nativeName}
              </Text>
              <Text variant="l_14Medium_formHelperText" color="textSecondary">
                {currentLanguage.name}
              </Text>
            </Box>
          </Row>
        </Card>

        {/* System Language Option */}
        <Card padding="sm_12" showToolbar toolbarTitle="System Settings">
          <PressableAnimated onPress={useSystemLanguage} enabled={!isChanging}>
            <Row gap="xs_8" alignItems="center">
              <Phone size={20} color="iconActiveInput" weight="regular" />
              <Box flex={1}>
                <Text variant="b_14SemiBold_listTitle" color="text">
                  Use System Language
                </Text>
                <Text variant="l_12Regular_helperText" color="textSecondary">
                  Automatically match your device&apos;s language setting
                </Text>
              </Box>
            </Row>
          </PressableAnimated>
        </Card>

        {/* Available Languages */}
        <Card padding="sm_12" showToolbar toolbarTitle="Available Languages">
          {supportedLanguages.map((language, index) => (
            <React.Fragment key={language.code}>
              <LanguageItem
                language={language}
                isSelected={language.code === currentLanguage.code}
                onSelect={() => changeLanguage(language.code)}
                isChanging={isChanging}
              />
              {index < supportedLanguages.length - 1 && <Divider />}
            </React.Fragment>
          ))}
        </Card>

        {/* Language Info */}
        <Card padding="sm_12" showToolbar toolbarTitle="Language Information">
          <Row gap="xs_8" alignItems="center" marginBottom="sm_12">
            <Globe size={16} color="iconActiveInput" weight="regular" />
            <Text variant="l_12Regular_helperText" color="textSecondary">
              Region Format
            </Text>
          </Row>

          <Box gap="xs_8" paddingLeft="lg_24">
            <Row justifyContent="space-between">
              <Text variant="b_14Regular_content" color="text">
                Date Format:
              </Text>
              <Text variant="l_12Regular_helperText" color="textSecondary">
                {new Date().toLocaleDateString(currentLanguage.code)}
              </Text>
            </Row>

            <Row justifyContent="space-between">
              <Text variant="b_14Regular_content" color="text">
                Time Format:
              </Text>
              <Text variant="l_12Regular_helperText" color="textSecondary">
                {new Date().toLocaleTimeString(currentLanguage.code, {
                  hour: '2-digit',
                  minute: '2-digit',
                })}
              </Text>
            </Row>

            <Row justifyContent="space-between">
              <Text variant="b_14Regular_content" color="text">
                Number Format:
              </Text>
              <Text variant="l_12Regular_helperText" color="textSecondary">
                {(1234.56).toLocaleString(currentLanguage.code)}
              </Text>
            </Row>
          </Box>
        </Card>

        {isChanging && (
          <Card padding="sm_12">
            <Row gap="sm_12" alignItems="center" justifyContent="center">
              <Text variant="b_14Regular_content" color="textSecondary">
                Changing language...
              </Text>
            </Row>
          </Card>
        )}

        <Box padding="sm_12">
          <Text variant="l_12Regular_helperText" color="textSecondary" textAlign="center">
            Language changes will take effect immediately. Date, time, and number formats will be
            updated to match your selection.
          </Text>
        </Box>
      </ScrollableContainer>
    </SafeAreaView>
  );
};

export default LanguageSettings;
