import React from 'react';

import { Platform } from 'react-native';

import { useBottomTabBarHeight } from '@react-navigation/bottom-tabs';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';

import type { SettingsStackParamList } from '@/src/core/navigation/types';
import { Box, ScrollableContainer, useThemeContext } from '@/src/core/theme';
import { useAuthStore } from '@/src/features/auth/store/authStore';
import { Card, NavigationTopBar } from '@/src/shared/components';
import { Divider } from '@/src/shared/components/Divider';

import { ProfileSettingsItem } from '../components/ProfileSettingsItem';
import { SettingsNavigationItem } from '../components/SettingsNavigationItem';
import { SettingsToggle } from '../components/SettingsToggle';
import { getAccountSettings } from '../config/accountSettings';
import { getEngagementSettings } from '../config/engagementSettings';
import { getPreferencesSettings } from '../config/preferencesSettings';
import { getSecuritySettings } from '../config/securitySettings';
import { getSupportSettings } from '../config/supportSettings';
import { useAccountActions } from '../hooks/useAccountActions';
import { useEngagementActions } from '../hooks/useEngagementActions';
import { usePreferencesActions } from '../hooks/usePreferencesActions';
import { useSettingsActions } from '../hooks/useSettingsActions';
import { useSupportActions } from '../hooks/useSupportActions';
import type { SettingsItemType, SettingsSection } from '../types';

type NavigationProp = NativeStackNavigationProp<SettingsStackParamList>;

const SettingsScreen = () => {
  const insets = useSafeAreaInsets();
  const { toggleTheme, themeName } = useThemeContext();
  const tabBarHeight = useBottomTabBarHeight();
  const navigation = useNavigation<NavigationProp>();
  const user = useAuthStore(state => state.user);

  // Initialize all action hooks
  const accountActions = useAccountActions();
  const preferencesActions = usePreferencesActions();
  const engagementActions = useEngagementActions();
  const supportActions = useSupportActions();
  const {
    handleBiometricToggle,
    handleRememberMeToggle,
    handleLogout,
    handleDeleteAccount,
    handleDevicePermissions,
    handleBiometricSettings,
  } = useSettingsActions();

  const handleViewProfile = () => navigation.navigate('Profile', { isOwnProfile: true });

  const settingsSections: SettingsSection[] = [
    {
      title: 'Account',
      items: getAccountSettings(accountActions),
    },
    {
      title: 'Preferences',
      items: getPreferencesSettings(themeName === 'dark', toggleTheme, preferencesActions),
    },
    {
      title: 'Community & Engagement',
      items: getEngagementSettings(engagementActions),
    },
    {
      title: 'Support',
      items: getSupportSettings(supportActions),
    },
    {
      title: 'Security',
      items: getSecuritySettings(
        handleBiometricToggle,
        handleRememberMeToggle,
        handleLogout,
        handleDeleteAccount,
        handleDevicePermissions,
        handleBiometricSettings
      ),
    },
  ];

  return (
    <SafeAreaView
      style={{
        flex: 1,
      }}>
      <NavigationTopBar title="Settings" />
      <ScrollableContainer
        backgroundColor="mainBackground"
        padding="md_16"
        gap="sm_12"
        contentContainerStyle={{
          paddingBottom: Platform.OS === 'ios' ? tabBarHeight : 0,
        }}>
        {/* Profile Section */}
        <Box backgroundColor="cardBackground" borderRadius="lg_16" padding="sm_12">
          <ProfileSettingsItem onPress={handleViewProfile} />
        </Box>

        {settingsSections.map(section => (
          <Card key={section.title} padding="sm_12" showToolbar toolbarTitle={section.title}>
            {section.items.map((item, itemIndex) => (
              <React.Fragment key={item.title}>
                {item.toggle ? (
                  <SettingsToggle
                    title={item.title}
                    label={item.label}
                    icon={item.icon}
                    initialValue={item.initialValue}
                    onToggle={item.toggle}
                    danger={item.danger}
                  />
                ) : (
                  <SettingsNavigationItem
                    title={item.title}
                    label={item.label}
                    icon={item.icon}
                    onPress={item.onPress || (() => {})}
                    danger={item.danger}
                    showAvatar={item.showAvatar}
                  />
                )}
                {itemIndex < section.items.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </Card>
        ))}
      </ScrollableContainer>
    </SafeAreaView>
  );
};

export default SettingsScreen;
