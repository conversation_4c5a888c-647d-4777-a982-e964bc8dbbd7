import React, { useEffect } from 'react';

import { Linking, Platform } from 'react-native';

import { useNavigation } from '@react-navigation/native';
import {
  Eye,
  Gear,
  HandTap,
  Moon,
  SpeakerHigh,
  Subtitles,
  TextAa,
  Vibrate,
} from 'phosphor-react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';

import { Box, Row, ScrollableContainer, Text } from '@/src/core/theme';
import { <PERSON>ton, Card, NavigationTopBar } from '@/src/shared/components';
import { Divider } from '@/src/shared/components/Divider';

import { SettingsStatusDisplay } from '../components/SettingsStatusDisplay';
import { SettingsToggle } from '../components/SettingsToggle';
import { useAccessibilitySettings } from '../hooks/useAccessibilitySettings';

const AccessibilitySettings = () => {
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();
  const {
    settings,
    systemInfo,
    updateReduceMotion,
    updateHighContrast,
    updateLargeText,
    updateVoiceOver,
    updateHapticFeedback,
    updateAudioDescriptions,
    updateClosedCaptions,
    loadSystemAccessibilityInfo,
    announceForAccessibility,
  } = useAccessibilitySettings();

  useEffect(() => {
    loadSystemAccessibilityInfo();
  }, [loadSystemAccessibilityInfo]);

  const openSystemAccessibilitySettings = async () => {
    try {
      if (Platform.OS === 'ios') {
        await Linking.openURL('app-settings:');
      } else {
        await Linking.openSettings();
      }
    } catch (error) {
      console.error('Failed to open accessibility settings:', error);
    }
  };

  const testAnnouncement = () => {
    announceForAccessibility('This is a test announcement for screen readers');
  };

  return (
    <SafeAreaView
      style={{
        flex: 1,
        paddingTop: Platform.OS === 'android' ? insets.top + 12 : 0,
      }}
      edges={['top']}>
      <NavigationTopBar title="Accessibility" onPress={() => navigation.goBack()} />

      <ScrollableContainer backgroundColor="mainBackground" padding="md_16" gap="sm_12">
        {/* System Accessibility Status */}
        <Card padding="sm_12" showToolbar toolbarTitle="System Status">
          <SettingsStatusDisplay
            label="Screen Reader"
            status={systemInfo.isScreenReaderEnabled ? 'success' : 'disabled'}
            description={systemInfo.isScreenReaderEnabled ? 'Enabled' : 'Disabled'}
            customIcon={<SpeakerHigh size={16} />}
          />

          <Divider />

          <SettingsStatusDisplay
            label="Reduce Motion"
            status={systemInfo.isReduceMotionEnabled ? 'success' : 'disabled'}
            description={systemInfo.isReduceMotionEnabled ? 'Enabled' : 'Disabled'}
            customIcon={<HandTap size={16} />}
          />

          <Divider />

          <SettingsStatusDisplay
            label="Reduce Transparency"
            status={systemInfo.isReduceTransparencyEnabled ? 'success' : 'disabled'}
            description={systemInfo.isReduceTransparencyEnabled ? 'Enabled' : 'Disabled'}
            customIcon={<Eye size={16} />}
          />
        </Card>

        {/* Visual Accessibility */}
        <Card padding="sm_12" showToolbar toolbarTitle="Visual Accessibility">
          <SettingsToggle
            title="High Contrast"
            description="Increase contrast for better visibility"
            icon={<Moon />}
            initialValue={settings.highContrast}
            onToggle={updateHighContrast}
          />

          <Divider />

          <SettingsToggle
            title="Large Text"
            description="Increase text size throughout the app"
            icon={<TextAa />}
            initialValue={settings.largeText}
            onToggle={updateLargeText}
          />

          <Divider />

          <SettingsToggle
            title="Reduce Motion"
            description="Minimize animations and transitions"
            icon={<HandTap />}
            initialValue={settings.reduceMotion || systemInfo.isReduceMotionEnabled}
            onToggle={updateReduceMotion}
            disabled={systemInfo.isReduceMotionEnabled}
            systemControlled={systemInfo.isReduceMotionEnabled}
          />
        </Card>

        {/* Audio Accessibility */}
        <Card padding="sm_12" showToolbar toolbarTitle="Audio Accessibility">
          <SettingsToggle
            title="Voice Over Support"
            description="Enhanced support for screen readers"
            icon={<SpeakerHigh />}
            initialValue={settings.voiceOver || systemInfo.isScreenReaderEnabled}
            onToggle={updateVoiceOver}
            disabled={systemInfo.isScreenReaderEnabled}
            systemControlled={systemInfo.isScreenReaderEnabled}
          />

          <Divider />

          <SettingsToggle
            title="Audio Descriptions"
            description="Provide audio descriptions for visual content"
            icon={<SpeakerHigh />}
            initialValue={settings.audioDescriptions}
            onToggle={updateAudioDescriptions}
          />

          <Divider />

          <SettingsToggle
            title="Closed Captions"
            description="Show captions for video and audio content"
            icon={<Subtitles />}
            initialValue={settings.closedCaptions}
            onToggle={updateClosedCaptions}
          />
        </Card>

        {/* Motor Accessibility */}
        <Card padding="sm_12" showToolbar toolbarTitle="Motor Accessibility">
          <SettingsToggle
            title="Haptic Feedback"
            description="Vibration feedback for interactions"
            icon={<Vibrate />}
            initialValue={settings.hapticFeedback}
            onToggle={updateHapticFeedback}
          />
        </Card>

        {/* Testing & System Access */}
        <Card padding="sm_12" showToolbar toolbarTitle="Testing & Configuration">
          <Row gap="sm_12" justifyContent="space-between" marginBottom="sm_12">
            <Box flex={1}>
              <Button
                variant="outline"
                title="Test Announcement"
                onPress={testAnnouncement}
                leftIcon={<SpeakerHigh size={16} />}
              />
            </Box>
            <Box flex={1}>
              <Button
                variant="primary"
                title="System Settings"
                onPress={openSystemAccessibilitySettings}
                leftIcon={<Gear size={16} />}
              />
            </Box>
          </Row>
        </Card>

        <Box padding="sm_12">
          <Text variant="l_12Regular_helperText" color="textSecondary" textAlign="center">
            Accessibility settings help make the app more usable for everyone. Some options are
            controlled by your device&apos;s system settings.
          </Text>
        </Box>
      </ScrollableContainer>
    </SafeAreaView>
  );
};

export default AccessibilitySettings;
