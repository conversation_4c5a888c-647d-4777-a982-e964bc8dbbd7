import React from 'react';

import { Linking, Platform } from 'react-native';

import { useNavigation } from '@react-navigation/native';
import {
  ArrowClockwise,
  Camera,
  DeviceMobile,
  Fingerprint,
  Globe,
  MapPin,
  Microphone,
  Notification,
  WifiHigh,
} from 'phosphor-react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';

import { Box, Row, ScrollableContainer, Text, useTheme } from '@/src/core/theme';
import { Colors, TokensThemeColors } from '@/src/core/theme/theme';
import { Button, Card, NavigationTopBar } from '@/src/shared/components';
import { Divider } from '@/src/shared/components/Divider';

import { useDevicePermissions } from '../hooks/useDevicePermissions';

type PermissionItemProps = {
  title: string;
  description: string;
  icon: React.ReactElement<{ color?: string; weight?: string }>;
  status: 'granted' | 'denied' | 'unknown';
  onPress?: () => void;
};

const PermissionItem = ({ title, description, icon, status, onPress }: PermissionItemProps) => {
  const { colors } = useTheme();

  const getStatusColor: () => TokensThemeColors = () => {
    switch (status) {
      case 'granted':
        return 'success';
      case 'denied':
        return 'error';
      default:
        return 'textSecondary';
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'granted':
        return 'Granted';
      case 'denied':
        return 'Denied';
      default:
        return 'Unknown';
    }
  };

  return (
    <Row width="100%" justifyContent="space-between" alignItems="center">
      <Row gap="xs_8" flex={1}>
        {React.cloneElement(icon, {
          color: colors.iconActiveInput,
          weight: 'regular',
        })}
        <Box flex={1} pr="md_16">
          <Text variant="b_14SemiBold_listTitle" color="text">
            {title}
          </Text>
          <Text variant="l_12Regular_helperText" color="textSecondary" marginTop="xxxs_2">
            {description}
          </Text>
        </Box>
      </Row>
      <Row justifyContent="flex-end" gap="xs_8">
        <Text variant="l_12Medium_message" color={getStatusColor()}>
          {getStatusText()}
        </Text>
        {onPress && <Button variant="ghost" onPress={onPress} title="Settings" />}
      </Row>
    </Row>
  );
};

type InfoItemProps = {
  label: string;
  value: string;
  icon: React.ReactElement<{ color?: string; weight?: string; size?: number }>;
};

const InfoItem = ({ label, value, icon }: InfoItemProps) => {
  const { colors } = useTheme();

  return (
    <Row width="100%" gap="xs_8" alignItems="center">
      {React.cloneElement(icon, {
        color: colors.iconActiveInput,
        weight: 'regular',
        size: 16,
      })}
      <Box flex={1}>
        <Text variant="l_12Regular_helperText" color="textSecondary">
          {label}
        </Text>
        <Text variant="b_14Regular_content" color="text">
          {value}
        </Text>
      </Box>
    </Row>
  );
};

const DevicePermissions = () => {
  const theme = useTheme();
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();
  const { device, network, cellular, capabilities, connectionQuality, isLoading, reload } =
    useDevicePermissions();

  const openAppSettings = async () => {
    try {
      if (Platform.OS === 'ios') {
        await Linking.openURL('app-settings:');
      } else {
        await Linking.openSettings();
      }
    } catch (error) {
      console.error('Failed to open settings:', error);
    }
  };

  if (isLoading) {
    return (
      <SafeAreaView
        style={{
          flex: 1,
          paddingTop: Platform.OS === 'android' ? insets.top + 12 : 0,
        }}
        edges={['top']}>
        <NavigationTopBar title="Device Permissions" onPress={() => navigation.goBack()} />
        <Box flex={1} justifyContent="center" alignItems="center">
          <Text variant="b_16Regular_input" color="textSecondary">
            Loading device information...
          </Text>
        </Box>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView
      style={{
        flex: 1,
        paddingTop: Platform.OS === 'android' ? insets.top + 12 : 0,
      }}
      edges={['top']}>
      <NavigationTopBar title="Device Permissions" onPress={() => navigation.goBack()} />

      <ScrollableContainer backgroundColor="mainBackground" padding="md_16" gap="sm_12">
        {/* App Permissions */}
        <Card padding="sm_12" showToolbar toolbarTitle="App Permissions">
          <PermissionItem
            title="Notifications"
            description="Show notifications for updates and messages"
            icon={<Notification />}
            status="unknown"
            onPress={openAppSettings}
          />

          <Divider />

          <PermissionItem
            title="Camera"
            description="Access camera for photos and videos"
            icon={<Camera />}
            status="unknown"
            onPress={openAppSettings}
          />

          <Divider />

          <PermissionItem
            title="Microphone"
            description="Access microphone for voice messages"
            icon={<Microphone />}
            status="unknown"
            onPress={openAppSettings}
          />

          <Divider />

          <PermissionItem
            title="Location"
            description="Access location for location-based features"
            icon={<MapPin />}
            status="unknown"
            onPress={openAppSettings}
          />

          <Divider />

          <PermissionItem
            title="Biometric Authentication"
            description="Use fingerprint or face recognition"
            icon={<Fingerprint />}
            status={capabilities?.supportsVoip ? 'granted' : 'denied'}
            onPress={openAppSettings}
          />
        </Card>

        {/* Device Information */}
        <Card padding="sm_12" showToolbar toolbarTitle="Device Information">
          {device && (
            <>
              <InfoItem
                label="Device"
                value={`${device.brand || 'Unknown'} ${device.modelName || device.deviceType}`}
                icon={<DeviceMobile />}
              />

              <Divider />

              <InfoItem
                label="Operating System"
                value={`${device.platform.toUpperCase()} ${device.osVersion}`}
                icon={<DeviceMobile />}
              />

              {device.totalMemory && (
                <>
                  <Divider />
                  <InfoItem
                    label="Memory"
                    value={`${Math.round(device.totalMemory / (1024 * 1024 * 1024))} GB`}
                    icon={<DeviceMobile />}
                  />
                </>
              )}
            </>
          )}
        </Card>

        {/* Network Information */}
        <Card padding="sm_12" showToolbar toolbarTitle="Network Information">
          {network && (
            <>
              <InfoItem
                label="Connection Type"
                value={network.type.charAt(0).toUpperCase() + network.type.slice(1)}
                icon={<WifiHigh />}
              />

              <Divider />

              <InfoItem
                label="Connection Status"
                value={network.isConnected ? 'Connected' : 'Disconnected'}
                icon={<Globe />}
              />

              <Divider />

              <InfoItem
                label="Internet Access"
                value={network.isInternetReachable ? 'Available' : 'Not Available'}
                icon={<Globe />}
              />

              <Divider />

              <InfoItem
                label="Connection Quality"
                value={connectionQuality.charAt(0).toUpperCase() + connectionQuality.slice(1)}
                icon={<WifiHigh />}
              />
            </>
          )}

          {cellular?.carrier && (
            <>
              <Divider />
              <InfoItem label="Carrier" value={cellular.carrier} icon={<DeviceMobile />} />

              {cellular.isoCountryCode && (
                <>
                  <Divider />
                  <InfoItem
                    label="Country"
                    value={cellular.isoCountryCode.toUpperCase()}
                    icon={<Globe />}
                  />
                </>
              )}
            </>
          )}
        </Card>

        {/* Actions */}
        <Card padding="sm_12">
          <Row gap="sm_12" justifyContent="space-between">
            <Box flex={1}>
              <Button
                variant="outline"
                title="Refresh Info"
                onPress={reload}
                leftIcon={<ArrowClockwise size={16} color={theme.colors.iconActiveInput} />}
              />
            </Box>
            <Box flex={1}>
              <Button variant="primary" onPress={openAppSettings} title="Open Settings" />
            </Box>
          </Row>
        </Card>

        <Box padding="sm_12">
          <Text variant="l_12Regular_helperText" color="textSecondary" textAlign="center">
            Manage app permissions in your device settings. Some features may not work properly if
            permissions are denied.
          </Text>
        </Box>
      </ScrollableContainer>
    </SafeAreaView>
  );
};

export default DevicePermissions;
