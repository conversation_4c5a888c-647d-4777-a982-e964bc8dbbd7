import React, { useEffect, useState } from 'react';

import { Platform } from 'react-native';

import { useNavigation } from '@react-navigation/native';
import { Fingerprint, Key, Lock, ShieldCheck, ShieldWarning } from 'phosphor-react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';

import { Box, Row, ScrollableContainer, Text } from '@/src/core/theme';
import { Button, Card, NavigationTopBar } from '@/src/shared/components';
import { Divider } from '@/src/shared/components/Divider';

import { SettingsStatusDisplay } from '../components/SettingsStatusDisplay';
import { SettingsToggle } from '../components/SettingsToggle';
import { useBiometricSettings } from '../hooks/useBiometricSettings';

// Using our modular components instead of local component definitions

const BiometricSettings = () => {
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();
  const {
    settings,
    capabilities,
    isLoading,
    isAuthenticating,
    toggleBiometricAuth,
    updateLoginRequirement,
    updateSensitiveActionsRequirement,
    updatePaymentsRequirement,
    testBiometricAuth,
    checkEnrollmentStatus,
    getSecurityLevel,
  } = useBiometricSettings();

  const [needsEnrollment, setNeedsEnrollment] = useState<boolean>(false);
  const [securityLevel, setSecurityLevel] = useState<string>('none');

  useEffect(() => {
    const loadAdditionalInfo = async () => {
      const [enrollment, level] = await Promise.all([checkEnrollmentStatus(), getSecurityLevel()]);
      setNeedsEnrollment(enrollment);
      setSecurityLevel(level);
    };

    if (capabilities) {
      loadAdditionalInfo();
    }
  }, [capabilities, checkEnrollmentStatus, getSecurityLevel]);

  if (isLoading) {
    return (
      <SafeAreaView
        style={{
          flex: 1,
          paddingTop: Platform.OS === 'android' ? insets.top + 12 : 0,
        }}
        edges={['top']}>
        <NavigationTopBar title="Biometric Settings" onPress={() => navigation.goBack()} />
        <Box flex={1} justifyContent="center" alignItems="center">
          <Text variant="b_16Regular_input" color="textSecondary">
            Loading biometric capabilities...
          </Text>
        </Box>
      </SafeAreaView>
    );
  }

  const getAvailabilityStatus = (): 'available' | 'unavailable' | 'warning' | 'error' => {
    if (!capabilities?.hasHardware) return 'error';
    if (!capabilities?.isEnrolled) return 'warning';
    if (capabilities?.isAvailable) return 'available';
    return 'unavailable';
  };

  const getAvailabilityDescription = (): string => {
    if (!capabilities?.hasHardware) return 'No biometric hardware detected';
    if (!capabilities?.isEnrolled) return 'No biometric data enrolled on device';
    if (capabilities?.isAvailable) return 'Available and ready to use';
    return 'Not available';
  };

  return (
    <SafeAreaView
      style={{
        flex: 1,
        paddingTop: Platform.OS === 'android' ? insets.top + 12 : 0,
      }}
      edges={['top']}>
      <NavigationTopBar title="Biometric Settings" onPress={() => navigation.goBack()} />

      <ScrollableContainer backgroundColor="mainBackground" padding="md_16" gap="sm_12">
        {/* Biometric Status */}
        <Card padding="sm_12" showToolbar toolbarTitle="Biometric Status">
          <SettingsStatusDisplay
            label="Biometric Authentication"
            status={
              getAvailabilityStatus() === 'available'
                ? 'success'
                : getAvailabilityStatus() === 'warning'
                  ? 'warning'
                  : getAvailabilityStatus() === 'error'
                    ? 'error'
                    : 'disabled'
            }
            description={getAvailabilityDescription()}
          />

          {capabilities?.supportedTypes && capabilities.supportedTypes.length > 0 && (
            <>
              <Divider />
              <SettingsStatusDisplay
                label="Supported Methods"
                status="success"
                description={capabilities.supportedTypes.join(', ')}
              />
            </>
          )}

          <Divider />

          <SettingsStatusDisplay
            label="Security Level"
            status={securityLevel === 'biometric' ? 'success' : 'warning'}
            description={`Device security: ${securityLevel}`}
          />

          {needsEnrollment && (
            <>
              <Divider />
              <SettingsStatusDisplay
                label="Setup Required"
                status="warning"
                description="Go to device settings to enroll biometric data"
              />
            </>
          )}
        </Card>

        {/* Main Biometric Toggle */}
        <Card padding="sm_12" showToolbar toolbarTitle="Biometric Authentication">
          <SettingsToggle
            title="Enable Biometric Authentication"
            description="Use fingerprint or face recognition for secure access"
            icon={<Fingerprint />}
            initialValue={settings.isEnabled}
            onToggle={toggleBiometricAuth}
            disabled={!capabilities?.isAvailable || isAuthenticating}
          />
        </Card>

        {/* Authentication Requirements */}
        {settings.isEnabled && (
          <Card padding="sm_12" showToolbar toolbarTitle="Authentication Requirements">
            <SettingsToggle
              title="Require for Login"
              description="Use biometric authentication when signing in"
              icon={<Lock />}
              initialValue={settings.requireForLogin}
              onToggle={updateLoginRequirement}
              disabled={!settings.isEnabled}
            />

            <Divider />

            <SettingsToggle
              title="Require for Sensitive Actions"
              description="Use biometric authentication for sensitive operations"
              icon={<ShieldCheck />}
              initialValue={settings.requireForSensitiveActions}
              onToggle={updateSensitiveActionsRequirement}
              disabled={!settings.isEnabled}
            />

            <Divider />

            <SettingsToggle
              title="Require for Payments"
              description="Use biometric authentication for payment confirmations"
              icon={<Key />}
              initialValue={settings.requireForPayments}
              onToggle={updatePaymentsRequirement}
              disabled={!settings.isEnabled}
            />
          </Card>
        )}

        {/* Actions */}
        <Card padding="sm_12">
          <Row gap="sm_12" justifyContent="space-between">
            {settings.isEnabled && (
              <Box flex={1}>
                <Button
                  variant="outline"
                  title="Test Authentication"
                  onPress={testBiometricAuth}
                  enabled={capabilities?.isAvailable && !isAuthenticating}
                  leftIcon={<Fingerprint size={16} />}
                />
              </Box>
            )}
            {!capabilities?.isAvailable && (
              <Box flex={1}>
                <Button
                  variant="primary"
                  title="Device Settings"
                  onPress={() => {
                    // TODO: Open device biometric settings
                    console.log('Open device biometric settings');
                  }}
                  leftIcon={<ShieldWarning size={16} />}
                />
              </Box>
            )}
          </Row>
        </Card>

        {isAuthenticating && (
          <Card padding="sm_12">
            <Row gap="sm_12" alignItems="center" justifyContent="center">
              <Text variant="b_14Regular_content" color="textSecondary">
                Authenticating...
              </Text>
            </Row>
          </Card>
        )}

        <Box padding="sm_12">
          <Text variant="l_12Regular_helperText" color="textSecondary" textAlign="center">
            Biometric authentication provides enhanced security for your account. Ensure your device
            has biometric data enrolled for this feature to work.
          </Text>
        </Box>
      </ScrollableContainer>
    </SafeAreaView>
  );
};

export default BiometricSettings;
