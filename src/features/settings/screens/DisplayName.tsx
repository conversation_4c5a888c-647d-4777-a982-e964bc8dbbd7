import { KeyboardAvoidingView, Platform, Pressable } from 'react-native';

import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import { z } from 'zod';

import { SettingsStackScreenProps } from '@/src/core/navigation/types';
import { Box, Text, TextInput, useTheme } from '@/src/core/theme';
import { Button, NavigationTopBar } from '@/src/shared/components';

const displayNameSchema = z.object({
  displayName: z.string().min(1, 'Display name is required'),
});

const DisplayNameScreen = () => {
  const navigation = useNavigation<SettingsStackScreenProps<'DisplayName'>['navigation']>();
  const insets = useSafeAreaInsets();
  const theme = useTheme();

  return (
    <SafeAreaView
      style={{
        flex: 1,
        paddingTop: Platform.OS === 'android' ? insets.top + 12 : 0,
      }}
      edges={['top', 'bottom']}>
      <NavigationTopBar
        onPress={() => navigation.goBack()}
        trailingActions={
          <Pressable onPress={() => navigation.navigate('GetUsername')}>
            <Text variant="l_14SemiBold_action" color="tagErrorBackground">
              Skip
            </Text>
          </Pressable>
        }
      />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        style={{ flex: 1 }}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 24 : 0}>
        <Box flex={1} padding="md_16" backgroundColor="mainBackground">
          <Box flex={1} alignItems="center" justifyContent="center" gap="sm_12">
            <Text variant="h_32SemiBold_Page" textAlign="center">
              Looks like{'\n'}you're new here
            </Text>

            <TextInput
              autoCapitalize="none"
              placeholder="Displayed name"
              autoComplete="name"
              autoCorrect={false}
              enterKeyHint="next"
              textContentType="name"
              keyboardType="default"
              leading={<MaterialIcons name="person" size={24} color={theme.colors.iconMuted} />}
            />

            <Text variant="l_12Regular_helperText">
              Use a recognisable name so your friends can find you
            </Text>
          </Box>

          <Box style={{ paddingBottom: insets.bottom }}>
            <Button
              title="Continue"
              width="100%"
              onPress={() => navigation.navigate('GetUsername')}
            />
          </Box>
        </Box>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default DisplayNameScreen;
