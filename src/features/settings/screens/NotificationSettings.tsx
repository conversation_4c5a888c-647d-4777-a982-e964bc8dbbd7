import React from 'react';

import { Platform } from 'react-native';

import { useNavigation } from '@react-navigation/native';
import {
  Alarm,
  Bell,
  ChatCircleText,
  ChatTeardropDots,
  <PERSON>ceMobileSpeaker,
  SpeakerHigh,
  Vibrate,
} from 'phosphor-react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';

import { Box, Row, ScrollableContainer, Text, useTheme } from '@/src/core/theme';
import { Card, NavigationTopBar } from '@/src/shared/components';
import { Divider } from '@/src/shared/components/Divider';
import Switch from '@/src/shared/components/Switch';

import { useNotificationSettings } from '../hooks/useNotificationSettings';

type NotificationItemProps = {
  title: string;
  description?: string;
  icon: React.ReactElement<{ color?: string; weight?: string }>;
  isEnabled: boolean;
  onToggle: (enabled: boolean) => void;
};

const NotificationItem = ({
  title,
  description,
  icon,
  isEnabled,
  onToggle,
}: NotificationItemProps) => {
  const { colors } = useTheme();

  return (
    <Row width="100%" justifyContent="space-between" alignItems="center">
      <Row gap="xs_8" flex={1}>
        {React.cloneElement(icon, {
          color: colors.iconActiveInput,
          weight: 'regular',
        })}
        <Box flex={1} pr="md_16">
          <Text variant="b_14SemiBold_listTitle" color="text">
            {title}
          </Text>
          {description && (
            <Text variant="l_12Regular_helperText" color="textSecondary" marginTop="xxxs_2">
              {description}
            </Text>
          )}
        </Box>
      </Row>
      <Switch isChecked={isEnabled} onToggle={onToggle} />
    </Row>
  );
};

const NotificationSettings = () => {
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();
  const {
    settings,
    updatePushNotifications,
    updateSmsNotifications,
    updateReminders,
    updateVibrateAndSound,
  } = useNotificationSettings();

  return (
    <SafeAreaView
      style={{
        flex: 1,
        paddingTop: Platform.OS === 'android' ? insets.top + 12 : 0,
      }}
      edges={['top']}>
      <NavigationTopBar title="Notifications" onPress={() => navigation.goBack()} />

      <ScrollableContainer backgroundColor="mainBackground" padding="md_16" gap="sm_12">
        <Card padding="sm_12" showToolbar toolbarTitle="Notification Preferences">
          <NotificationItem
            title="Push notifications"
            description="Receive push notifications for updates and messages"
            icon={<Bell />}
            isEnabled={settings.pushNotifications}
            onToggle={updatePushNotifications}
          />

          <Divider />

          <NotificationItem
            title="SMS notifications"
            description="Receive important updates via text message"
            icon={<ChatCircleText />}
            isEnabled={settings.smsNotifications}
            onToggle={updateSmsNotifications}
          />

          <Divider />

          <NotificationItem
            title="Reminders"
            description="Get reminded about important events and activities"
            icon={<Alarm />}
            isEnabled={settings.reminders}
            onToggle={updateReminders}
          />

          <Divider />

          <NotificationItem
            title="Vibrate & sound"
            description="Enable vibration and sound for notifications"
            icon={<Vibrate />}
            isEnabled={settings.vibrateAndSound}
            onToggle={updateVibrateAndSound}
          />
        </Card>

        <Box padding="sm_12">
          <Text variant="l_12Regular_helperText" color="textSecondary" textAlign="center">
            You can manage these preferences anytime. Changes will take effect immediately.
          </Text>
        </Box>
      </ScrollableContainer>
    </SafeAreaView>
  );
};

export default NotificationSettings;
