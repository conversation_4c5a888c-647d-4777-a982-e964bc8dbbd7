import { ImageProps, TouchableOpacity, TouchableOpacityProps } from 'react-native';

import { PencilSimple } from 'phosphor-react-native';

import { Box, useTheme } from '@/src/core/theme';
import { Avatar } from '@/src/shared/components';

type UserPhotoSelectComponentProps = TouchableOpacityProps & {
  source?: ImageProps['source'];
};

const UserPhotoSelect = ({ source, ...rest }: UserPhotoSelectComponentProps) => {
  const theme = useTheme();

  return (
    <TouchableOpacity style={{ borderRadius: '100%' }} activeOpacity={0.7} {...rest}>
      <Avatar size="3xl" source={source} />

      <Box
        alignItems="center"
        justifyContent="center"
        position="absolute"
        width={31}
        height={31}
        borderRadius="circle_9999"
        backgroundColor="subtleBackground"
        style={{ bottom: 0, right: 0 }}>
        <PencilSimple size={16} color={theme.colors.iconPrimary} weight="bold" />
      </Box>
    </TouchableOpacity>
  );
};

export default UserPhotoSelect;
