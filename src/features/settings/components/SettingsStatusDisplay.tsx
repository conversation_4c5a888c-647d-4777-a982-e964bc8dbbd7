import React from 'react';

import { StatusIndicator, StatusType } from '@/src/shared/components/StatusIndicator';

export type SettingsStatusDisplayProps = {
  /**
   * Label for the status item
   */
  label: string;

  /**
   * Status type (success, warning, error, info, disabled)
   */
  status: StatusType;

  /**
   * Description text explaining the status
   */
  description: string;

  /**
   * Optional custom icon to override the default status icon
   */
  customIcon?: React.ReactElement<{ color?: string; weight?: string; size?: number }>;
};

/**
 * A component for displaying status information in settings screens
 * Built on top of the StatusIndicator component
 */
export const SettingsStatusDisplay: React.FC<SettingsStatusDisplayProps> = ({
  label,
  status,
  description,
  customIcon,
}) => {
  return (
    <StatusIndicator
      label={label}
      status={status}
      description={description}
      customIcon={customIcon}
      iconSize={20}
    />
  );
};
