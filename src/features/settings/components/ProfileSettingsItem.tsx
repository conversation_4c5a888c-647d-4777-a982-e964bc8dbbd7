import React from 'react';

import { CaretRight } from 'phosphor-react-native';

import { Box, Pressable, Text, useTheme } from '@/src/core/theme';
import { useAuthStore } from '@/src/features/auth/store/authStore';
import { Avatar } from '@/src/shared/components';

interface ProfileSettingsItemProps {
  onPress: () => void;
}

export const ProfileSettingsItem: React.FC<ProfileSettingsItemProps> = ({ onPress }) => {
  const theme = useTheme();
  const user = useAuthStore(state => state.user);

  return (
    <Pressable
      flexDirection="row"
      alignItems="center"
      gap="sm_12"
      paddingVertical="sm_12"
      onPress={onPress}>
      <Avatar
        size="m"
        source={user?.photoURL ? { uri: user.photoURL } : undefined}
        fallbackText={user?.name?.charAt(0) || 'U'}
      />

      <Box flex={1}>
        <Text variant="b_14Bold_CardTitle" color="mainText">
          {user?.name || 'Your Profile'}
        </Text>
        <Text variant="l_12Regular_helperText" color="mutedText">
          {user?.email || 'View and edit your profile'}
        </Text>
      </Box>

      <CaretRight size={20} color={theme.colors.iconDefaultInput} />
    </Pressable>
  );
};
