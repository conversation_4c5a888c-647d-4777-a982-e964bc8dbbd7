import React, { useState } from 'react';

import { ListItemBase } from '@/src/shared/components/ListItemBase';
import Switch from '@/src/shared/components/Switch';

export type SettingsToggleProps = {
  /**
   * Title of the setting
   */
  title: string;

  /**
   * Optional secondary text to display above the title
   */
  label?: string;

  /**
   * Optional description text to display below the title
   */
  description?: string;

  /**
   * Icon to display on the left side
   */
  icon: React.ReactElement<{ color?: string; weight?: string; size?: number }>;

  /**
   * Initial value of the toggle
   */
  initialValue?: boolean;

  /**
   * Function called when the toggle is switched
   */
  onToggle: (isChecked: boolean) => void;

  /**
   * Whether the toggle is disabled
   */
  disabled?: boolean;

  /**
   * Whether the toggle is controlled by the system
   */
  systemControlled?: boolean;

  /**
   * Whether the toggle is in danger state
   */
  danger?: boolean;
};

/**
 * A toggle component specifically designed for settings screens
 * Built on top of ListItemBase with added toggle functionality
 */
export const SettingsToggle: React.FC<SettingsToggleProps> = ({
  title,
  label,
  description,
  icon,
  initialValue = false,
  onToggle,
  disabled = false,
  systemControlled = false,
  danger = false,
}) => {
  const [isChecked, setIsChecked] = useState(initialValue);

  const handleToggle = (checked: boolean) => {
    setIsChecked(checked);
    onToggle(checked);
  };

  return (
    <ListItemBase
      title={title}
      label={label}
      description={description || (systemControlled ? 'Controlled by system settings' : undefined)}
      icon={icon}
      disabled={disabled || systemControlled}
      danger={danger}
      rightElement={
        <Switch
          isChecked={isChecked}
          onToggle={handleToggle}
          disabled={disabled || systemControlled}
        />
      }
    />
  );
};
