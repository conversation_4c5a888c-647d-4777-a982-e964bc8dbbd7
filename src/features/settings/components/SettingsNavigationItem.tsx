import React from 'react';

import { CaretRight } from 'phosphor-react-native';

import { useTheme } from '@/src/core/theme';
import { ListItemBase } from '@/src/shared/components/ListItemBase';

export type SettingsNavigationItemProps = {
  /**
   * Title of the setting
   */
  title: string;

  /**
   * Optional secondary text to display above the title
   */
  label?: string;

  /**
   * Optional description text to display below the title
   */
  description?: string;

  /**
   * Icon to display on the left side
   */
  icon: React.ReactElement<{ color?: string; weight?: string; size?: number }>;

  /**
   * Function called when the item is pressed
   */
  onPress: () => void;

  /**
   * Whether the item is disabled
   */
  disabled?: boolean;

  /**
   * Whether the item represents a dangerous action (e.g., delete account)
   */
  danger?: boolean;

  /**
   * Whether to show avatar instead of icon (for profile)
   */
  showAvatar?: boolean;
};

/**
 * A navigation item component specifically designed for settings screens
 * Built on top of ListItemBase with navigation arrow
 */
export const SettingsNavigationItem: React.FC<SettingsNavigationItemProps> = ({
  title,
  label,
  description,
  icon,
  onPress,
  disabled = false,
  danger = false,
  showAvatar = false,
}) => {
  const { colors } = useTheme();

  return (
    <ListItemBase
      title={title}
      label={label}
      description={description}
      icon={icon}
      onPress={onPress}
      disabled={disabled}
      danger={danger}
      showAvatar={showAvatar}
      rightElement={<CaretRight weight="regular" color={colors.iconDefaultInput} />}
    />
  );
};
