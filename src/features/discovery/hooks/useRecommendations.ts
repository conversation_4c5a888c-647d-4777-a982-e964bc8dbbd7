/**
 * Recommendations Hook
 * AI-powered recommendations for discovery
 */
import { useQuery } from '@tanstack/react-query';

import { foursquare } from '@/src/core/libs';
import { useLocation } from '@/src/shared/hooks/useLocation';

import { useDiscoveryStore } from '../store/discoveryStore';
import type {
  DiscoveryItem,
  DiscoveryPreferences,
  EventDiscoveryItem,
  TicketDiscoveryItem,
  VenueDiscoveryItem,
} from '../types/discovery.types';

interface UseRecommendationsProps {
  preferences?: DiscoveryPreferences;
  limit?: number;
}

interface RecommendationGroup {
  id: string;
  title: string;
  subtitle?: string;
  items: DiscoveryItem[];
}

interface UseRecommendationsReturn {
  recommendations: RecommendationGroup[];
  isLoading: boolean;
  error: Error | null;
  refetch: () => void;
}

/**
 * Mock recommendations based on time of day and preferences
 */
const generateMockRecommendations = (
  location: { latitude: number; longitude: number } | null,
  preferences?: DiscoveryPreferences
): RecommendationGroup[] => {
  const currentHour = new Date().getHours();
  const isWeekend = [0, 6].includes(new Date().getDay());

  const groups: RecommendationGroup[] = [];

  // Time-based recommendations
  if (currentHour >= 16 && currentHour <= 19) {
    groups.push({
      id: 'happy-hour',
      title: '🍻 Happy Hour Spots',
      subtitle: 'Best deals happening now',
      items: [],
    });
  } else if (currentHour >= 20 || currentHour <= 3) {
    groups.push({
      id: 'nightlife',
      title: "🌙 Tonight's Hot Spots",
      subtitle: "Where the party's at",
      items: [],
    });
  } else if (currentHour >= 10 && currentHour <= 14) {
    groups.push({
      id: 'brunch',
      title: '🥂 Brunch & Day Drinking',
      subtitle: 'Perfect for a lazy afternoon',
      items: [],
    });
  }

  // Weekend specials
  if (isWeekend) {
    groups.push({
      id: 'weekend-events',
      title: '🎉 Weekend Events',
      subtitle: "Don't miss out",
      items: [],
    });
  }

  // Based on preferences
  if (preferences?.favoriteCategories.includes('music')) {
    groups.push({
      id: 'live-music',
      title: '🎵 Live Music Tonight',
      subtitle: 'Bands and DJs performing',
      items: [],
    });
  }

  // Trending always
  groups.push({
    id: 'trending',
    title: '🔥 Trending Now',
    subtitle: 'Popular with the crowd',
    items: [],
  });

  // Personalized
  groups.push({
    id: 'for-you',
    title: '✨ Recommended For You',
    subtitle: 'Based on your preferences',
    items: [],
  });

  return groups;
};

/**
 * Hook for AI-powered recommendations
 */
export const useRecommendations = ({
  preferences,
  limit = 5,
}: UseRecommendationsProps = {}): UseRecommendationsReturn => {
  const { location } = useLocation();
  const { preferences: storedPreferences } = useDiscoveryStore();
  const userPreferences = preferences || storedPreferences;

  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['discovery', 'recommendations', location?.coords, userPreferences, limit],
    queryFn: async () => {
      if (!location) {
        return generateMockRecommendations(null, userPreferences);
      }

      try {
        // Get trending venues from Foursquare
        const trendingVenues = await foursquare.getTrendingPlaces(
          location.coords.latitude,
          location.coords.longitude,
          10000 // 10km radius
        );

        // Get recommendations based on categories
        const recommendations = await foursquare.getRecommendations({
          ll: `${location.coords.latitude},${location.coords.longitude}`,
          radius: 10000,
          limit: 20,
          openNow: true,
        });

        // Convert to discovery items
        const venueItems: VenueDiscoveryItem[] = [
          ...trendingVenues.results,
          ...recommendations.results,
        ].map(place => ({
          id: place.fsq_id,
          type: 'venue' as const,
          title: place.name,
          subtitle: place.categories?.[0]?.name,
          imageUrl: place.photos?.[0] ? foursquare.getPhotoUrl(place.photos[0]) : undefined,
          location: {
            latitude: place.geocodes.main.latitude,
            longitude: place.geocodes.main.longitude,
            address: foursquare.getFormattedAddress(place.location),
            neighborhood: place.location.locality,
            city: place.location.region,
            venueId: place.fsq_id,
            venueName: place.name,
          },
          distance: place.distance,
          foursquarePlace: place,
          rating: place.rating,
          priceLevel: place.price,
          isOpen: foursquare.isOpenNow(place),
          checkInCount: 0,
          isTrending: trendingVenues.results.some(v => v.fsq_id === place.fsq_id),
        }));

        // Generate recommendation groups
        const groups = generateMockRecommendations(location.coords, userPreferences);

        // Distribute venues into groups
        groups.forEach(group => {
          group.items = venueItems
            .filter(() => Math.random() > 0.5) // Random distribution for now
            .slice(0, limit);
        });

        // Filter out empty groups
        return groups.filter(group => group.items.length > 0);
      } catch (error) {
        console.error('Recommendations error:', error);
        return generateMockRecommendations(location.coords, userPreferences);
      }
    },
    enabled: !!location,
    staleTime: 10 * 60 * 1000, // 10 minutes
    cacheTime: 30 * 60 * 1000, // 30 minutes
  });

  return {
    recommendations: data || [],
    isLoading,
    error: error as Error | null,
    refetch,
  };
};

/**
 * Get recommendations for a specific category
 */
export const useCategoryRecommendations = (categoryId: string, limit = 10) => {
  const { location } = useLocation();

  return useQuery({
    queryKey: ['discovery', 'category-recommendations', categoryId, location?.coords, limit],
    queryFn: async () => {
      if (!location) return [];

      // Map category to Foursquare categories
      const categoryMap: Record<string, string[]> = {
        nightlife: ['13003', '13018', '10032'], // Bar, Brewery, Music Venue
        'food-drink': ['13000'], // Dining & Drinking
        music: ['10032', '10008'], // Music Venue, Concert Hall
        sports: ['18000'], // Sports & Recreation
        arts: ['10000'], // Arts & Entertainment
        outdoor: ['16000'], // Landmarks & Outdoors
      };

      const foursquareCategories = categoryMap[categoryId] || [];

      try {
        const response = await foursquare.searchByCategory(
          foursquareCategories[0] || '13003', // Default to bars
          location.coords,
          {
            limit,
            openNow: true,
          }
        );

        return response.results.map(place => ({
          id: place.fsq_id,
          type: 'venue' as const,
          title: place.name,
          subtitle: place.categories?.[0]?.name,
          location: {
            latitude: place.geocodes.main.latitude,
            longitude: place.geocodes.main.longitude,
            address: foursquare.getFormattedAddress(place.location),
            venueId: place.fsq_id,
            venueName: place.name,
          },
          distance: place.distance,
          rating: place.rating,
          isOpen: foursquare.isOpenNow(place),
        }));
      } catch (error) {
        console.error('Category recommendations error:', error);
        return [];
      }
    },
    enabled: !!location,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Get similar places to a given venue
 */
export const useSimilarPlaces = (venueId: string, limit = 5) => {
  const { location } = useLocation();

  return useQuery({
    queryKey: ['discovery', 'similar-places', venueId, limit],
    queryFn: async () => {
      // This would integrate with a recommendation API
      // For now, return nearby venues
      if (!location) return [];

      try {
        const venue = await foursquare.getPlaceDetails({ fsqId: venueId });
        const category = venue.categories?.[0]?.id;

        if (!category) return [];

        const similar = await foursquare.searchByCategory(
          category,
          {
            latitude: venue.geocodes.main.latitude,
            longitude: venue.geocodes.main.longitude,
          },
          {
            limit: limit + 1, // +1 to exclude the original venue
          }
        );

        return similar.results
          .filter(place => place.fsq_id !== venueId)
          .slice(0, limit)
          .map(place => ({
            id: place.fsq_id,
            type: 'venue' as const,
            title: place.name,
            subtitle: place.categories?.[0]?.name,
            location: {
              latitude: place.geocodes.main.latitude,
              longitude: place.geocodes.main.longitude,
              venueId: place.fsq_id,
              venueName: place.name,
            },
            distance: place.distance,
            rating: place.rating,
          }));
      } catch (error) {
        console.error('Similar places error:', error);
        return [];
      }
    },
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
};
