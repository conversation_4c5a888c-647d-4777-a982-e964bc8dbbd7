/**
 * Radar Hook
 * Manages radar map functionality for discovery
 */
import { useCallback, useEffect, useRef, useState } from 'react';

import MapView, { Region } from 'react-native-maps';

import { useLocation } from '@/src/shared/hooks/useLocation';

import type { DiscoveryItem, DiscoveryMarker, MapRegion } from '../types/discovery.types';

interface UseRadarProps {
  initialRegion?: MapRegion;
  maxRadius?: number;
  clusteringEnabled?: boolean;
  onRegionChange?: (region: Region) => void;
  onMarkerPress?: (item: DiscoveryItem) => void;
}

interface UseRadarReturn {
  mapRef: React.RefObject<MapView | null>;
  region: Region;
  markers: DiscoveryMarker[];
  selectedMarkerId: string | null;
  isMapReady: boolean;
  centerOnUser: () => void;
  selectMarker: (markerId: string | null) => void;
  animateToRegion: (region: Region, duration?: number) => void;
  fitToMarkers: (markers: DiscoveryMarker[], animated?: boolean) => void;
}

/**
 * Default map region (centered on user or default location)
 */
const DEFAULT_REGION: Region = {
  latitude: -23.5505, // São Paulo
  longitude: -46.6333,
  latitudeDelta: 0.0922,
  longitudeDelta: 0.0421,
};

/**
 * Hook for radar map functionality
 */
export const useRadar = ({
  initialRegion,
  maxRadius = 50000, // 50km
  clusteringEnabled = true,
  onRegionChange,
  onMarkerPress,
}: UseRadarProps = {}): UseRadarReturn => {
  const mapRef = useRef<MapView | null>(null);
  const { location } = useLocation();
  const [region, setRegion] = useState<Region>(initialRegion || DEFAULT_REGION);
  const [markers, setMarkers] = useState<DiscoveryMarker[]>([]);
  const [selectedMarkerId, setSelectedMarkerId] = useState<string | null>(null);
  const [isMapReady, setIsMapReady] = useState(false);

  // Update region when user location changes
  useEffect(() => {
    if (location && !initialRegion) {
      const newRegion: Region = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        latitudeDelta: 0.0922,
        longitudeDelta: 0.0421,
      };
      setRegion(newRegion);

      // Animate to user location if map is ready
      if (isMapReady && mapRef.current) {
        mapRef.current.animateToRegion(newRegion, 1000);
      }
    }
  }, [location, initialRegion, isMapReady]);

  /**
   * Center map on user location
   */
  const centerOnUser = useCallback(() => {
    if (location && mapRef.current) {
      const userRegion: Region = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        latitudeDelta: 0.0922,
        longitudeDelta: 0.0421,
      };
      mapRef.current.animateToRegion(userRegion, 1000);
      setRegion(userRegion);
    }
  }, [location]);

  /**
   * Select a marker
   */
  const selectMarker = useCallback((markerId: string | null) => {
    setSelectedMarkerId(markerId);
  }, []);

  /**
   * Animate to a specific region
   */
  const animateToRegion = useCallback((newRegion: Region, duration = 1000) => {
    if (mapRef.current) {
      mapRef.current.animateToRegion(newRegion, duration);
      setRegion(newRegion);
    }
  }, []);

  /**
   * Fit map to show all markers
   */
  const fitToMarkers = useCallback((markersToFit: DiscoveryMarker[], animated = true) => {
    if (!mapRef.current || markersToFit.length === 0) return;

    const coordinates = markersToFit.map(marker => marker.coordinate);

    mapRef.current.fitToCoordinates(coordinates, {
      edgePadding: {
        top: 100,
        right: 50,
        bottom: 100,
        left: 50,
      },
      animated,
    });
  }, []);

  /**
   * Handle region change
   */
  const handleRegionChange = useCallback(
    (newRegion: Region) => {
      setRegion(newRegion);
      onRegionChange?.(newRegion);
    },
    [onRegionChange]
  );

  /**
   * Calculate if a point is within the max radius
   */
  const isWithinRadius = useCallback(
    (
      point: { latitude: number; longitude: number },
      center: { latitude: number; longitude: number },
      radius: number
    ): boolean => {
      const R = 6371e3; // Earth's radius in meters
      const φ1 = (center.latitude * Math.PI) / 180;
      const φ2 = (point.latitude * Math.PI) / 180;
      const Δφ = ((point.latitude - center.latitude) * Math.PI) / 180;
      const Δλ = ((point.longitude - center.longitude) * Math.PI) / 180;

      const a =
        Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
        Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

      const distance = R * c;
      return distance <= radius;
    },
    []
  );

  /**
   * Update markers based on items and clustering
   */
  const updateMarkers = useCallback(
    (items: DiscoveryItem[], currentRegion: Region) => {
      // Filter items within max radius
      const center = {
        latitude: currentRegion.latitude,
        longitude: currentRegion.longitude,
      };

      const visibleItems = items.filter(item =>
        isWithinRadius(
          { latitude: item.location.latitude, longitude: item.location.longitude },
          center,
          maxRadius
        )
      );

      // Convert to markers
      const newMarkers: DiscoveryMarker[] = visibleItems.map(item => ({
        id: item.id,
        type: item.type,
        coordinate: {
          latitude: item.location.latitude,
          longitude: item.location.longitude,
        },
        title: item.title,
        price: item.price?.label,
        isSelected: item.id === selectedMarkerId,
      }));

      // TODO: Implement clustering if enabled
      if (clusteringEnabled) {
        // Clustering logic would go here
      }

      setMarkers(newMarkers);
    },
    [clusteringEnabled, maxRadius, selectedMarkerId, isWithinRadius]
  );

  return {
    region,
    markers,
    selectedMarkerId,
    isMapReady,
    centerOnUser,
    selectMarker,
    animateToRegion,
    fitToMarkers,
    mapRef: mapRef,
  };
};

/**
 * Calculate appropriate zoom level based on radius
 */
export const getZoomLevel = (radius: number): number => {
  // Approximate zoom levels for different radii
  if (radius <= 1000) return 15;
  if (radius <= 5000) return 13;
  if (radius <= 10000) return 12;
  if (radius <= 20000) return 11;
  if (radius <= 50000) return 10;
  return 9;
};

/**
 * Convert region to bounds
 */
export const regionToBounds = (region: Region) => {
  return {
    northEast: {
      latitude: region.latitude + region.latitudeDelta / 2,
      longitude: region.longitude + region.longitudeDelta / 2,
    },
    southWest: {
      latitude: region.latitude - region.latitudeDelta / 2,
      longitude: region.longitude - region.longitudeDelta / 2,
    },
  };
};
