/**
 * Filters Hook
 * Manages filter state and persistence for discovery
 */
import { useCallback, useEffect, useMemo } from 'react';

import { useMMKVObject } from 'react-native-mmkv';

import { appStorage } from '@/src/core/storage';

import type { DiscoveryFilters } from '../types/discovery.types';

interface UseFiltersReturn {
  filters: DiscoveryFilters;
  updateFilter: <K extends keyof DiscoveryFilters>(key: K, value: DiscoveryFilters[K]) => void;
  updateFilters: (newFilters: Partial<DiscoveryFilters>) => void;
  resetFilters: () => void;
  activeFilterCount: number;
  hasActiveFilters: boolean;
  quickFilters: QuickFilter[];
  applyQuickFilter: (filterId: string) => void;
}

interface QuickFilter {
  id: string;
  label: string;
  icon: string;
  filters: Partial<DiscoveryFilters>;
}

/**
 * Default filter values
 */
const DEFAULT_FILTERS: DiscoveryFilters = {
  radius: 5000, // 5km
  sortBy: 'distance',
  sortOrder: 'asc',
  contentTypes: ['event', 'venue', 'ticket'],
};

/**
 * Quick filter presets
 */
const QUICK_FILTERS: QuickFilter[] = [
  {
    id: 'open-now',
    label: 'Open Now',
    icon: '🕐',
    filters: {
      onlyOpen: true,
    },
  },
  {
    id: 'happy-hour',
    label: 'Happy Hour',
    icon: '🍻',
    filters: {
      timeOfDay: 'afternoon',
      servesAlcohol: true,
    },
  },
  {
    id: 'live-music',
    label: 'Live Music',
    icon: '🎵',
    filters: {
      liveMusic: true,
    },
  },
  {
    id: 'late-night',
    label: 'Late Night',
    icon: '🌙',
    filters: {
      timeOfDay: 'night',
      onlyOpen: true,
    },
  },
  {
    id: 'free-entry',
    label: 'Free Entry',
    icon: '🆓',
    filters: {
      onlyFree: true,
    },
  },
  {
    id: 'trending',
    label: 'Trending',
    icon: '🔥',
    filters: {
      onlyTrending: true,
      sortBy: 'popularity',
    },
  },
];

/**
 * Hook for managing discovery filters
 */
export const useFilters = (): UseFiltersReturn => {
  // Persist filters in MMKV storage
  const [filters = DEFAULT_FILTERS, setFilters] = useMMKVObject<DiscoveryFilters>(
    'discovery.filters',
    appStorage.getMMKVInstance()
  );

  /**
   * Update a single filter
   */
  const updateFilter = useCallback(
    <K extends keyof DiscoveryFilters>(key: K, value: DiscoveryFilters[K]) => {
      setFilters(prev => ({
        ...prev,
        [key]: value,
      }));
    },
    [setFilters]
  );

  /**
   * Update multiple filters at once
   */
  const updateFilters = useCallback(
    (newFilters: Partial<DiscoveryFilters>) => {
      setFilters(prev => ({
        ...prev,
        ...newFilters,
      }));
    },
    [setFilters]
  );

  /**
   * Reset filters to defaults
   */
  const resetFilters = useCallback(() => {
    setFilters(DEFAULT_FILTERS);
  }, [setFilters]);

  /**
   * Count active filters
   */
  const activeFilterCount = useMemo(() => {
    let count = 0;

    // Boolean filters
    if (filters.onlyOpen) count++;
    if (filters.onlyFree) count++;
    if (filters.onlyToday) count++;
    if (filters.onlyThisWeek) count++;
    if (filters.onlyVerified) count++;
    if (filters.onlyTrending) count++;
    if (filters.servesAlcohol) count++;
    if (filters.liveMusic) count++;
    if (filters.outdoorSeating) count++;
    if (filters.hasParking) count++;
    if (filters.wheelchairAccessible) count++;

    // Other filters
    if (filters.categories && filters.categories.length > 0) count++;
    if (filters.dateRange) count++;
    if (filters.timeOfDay) count++;
    if (filters.priceRange) count++;
    if (filters.neighborhood) count++;

    return count;
  }, [filters]);

  /**
   * Check if any filters are active
   */
  const hasActiveFilters = activeFilterCount > 0;

  /**
   * Apply a quick filter preset
   */
  const applyQuickFilter = useCallback(
    (filterId: string) => {
      const quickFilter = QUICK_FILTERS.find(f => f.id === filterId);
      if (quickFilter) {
        updateFilters(quickFilter.filters);
      }
    },
    [updateFilters]
  );

  return {
    filters,
    updateFilter,
    updateFilters,
    resetFilters,
    activeFilterCount,
    hasActiveFilters,
    quickFilters: QUICK_FILTERS,
    applyQuickFilter,
  };
};

/**
 * Convert filters to API query parameters
 */
export const filtersToQueryParams = (filters: DiscoveryFilters): Record<string, any> => {
  const params: Record<string, any> = {};

  // Location
  if (filters.location) {
    params.ll = `${filters.location.latitude},${filters.location.longitude}`;
  }
  if (filters.radius) {
    params.radius = filters.radius;
  }
  if (filters.neighborhood) {
    params.neighborhood = filters.neighborhood;
  }

  // Types and categories
  if (filters.contentTypes && filters.contentTypes.length > 0) {
    params.types = filters.contentTypes.join(',');
  }
  if (filters.categories && filters.categories.length > 0) {
    params.categories = filters.categories.join(',');
  }
  if (filters.foursquareCategories && filters.foursquareCategories.length > 0) {
    params.foursquare_categories = filters.foursquareCategories.join(',');
  }

  // Time
  if (filters.dateRange) {
    params.start_date = filters.dateRange.start.toISOString();
    params.end_date = filters.dateRange.end.toISOString();
  }
  if (filters.timeOfDay) {
    params.time_of_day = filters.timeOfDay;
  }
  if (filters.onlyToday) {
    params.only_today = true;
  }
  if (filters.onlyThisWeek) {
    params.only_this_week = true;
  }

  // Price
  if (filters.priceRange) {
    params.min_price = filters.priceRange.min;
    params.max_price = filters.priceRange.max;
  }
  if (filters.onlyFree) {
    params.only_free = true;
  }

  // Features
  if (filters.hasParking) params.has_parking = true;
  if (filters.wheelchairAccessible) params.wheelchair_accessible = true;
  if (filters.outdoorSeating) params.outdoor_seating = true;
  if (filters.liveMusic) params.live_music = true;
  if (filters.servesAlcohol) params.serves_alcohol = true;

  // Status
  if (filters.onlyOpen) params.only_open = true;
  if (filters.onlyVerified) params.only_verified = true;
  if (filters.onlyTrending) params.only_trending = true;

  // Sort
  if (filters.sortBy) {
    params.sort_by = filters.sortBy;
    params.sort_order = filters.sortOrder || 'asc';
  }

  return params;
};
