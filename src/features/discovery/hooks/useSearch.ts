/**
 * Search Hook
 * Handles search logic with Foursquare integration and React 19 features
 */
import { useDeferredValue } from 'react';

import { useQuery } from '@tanstack/react-query';

import { foursquare } from '@/src/core/libs';
import type { AutocompleteResult } from '@/src/core/libs/foursquare/types';
import { useLocation } from '@/src/shared/hooks/useLocation';

import type { SearchSuggestion } from '../types/discovery.types';

/**
 * Convert Foursquare autocomplete results to search suggestions
 */
const convertToSuggestions = (results: AutocompleteResult[]): SearchSuggestion[] => {
  return results.map((result, index) => {
    // Determine suggestion type based on result
    let type: SearchSuggestion['type'] = 'query';
    if (result.type === 'place') {
      type = 'venue';
    } else if (result.type === 'search') {
      type = 'category';
    }

    // Extract text properly - result.text might be an object with primary property
    const displayText =
      typeof result.text === 'string' ? result.text : result.text?.primary || 'Unknown';

    return {
      id: `${displayText}_${type}_${index}`, // Add index to ensure uniqueness
      type,
      title: displayText,
      subtitle: result.place?.categories?.[0]?.name,
      text: displayText,
      metadata: {
        category: result.place?.categories?.[0]?.name,
        distance: result.place?.distance,
      },
      icon:
        result.place?.categories?.[0]?.icon?.prefix +
        '32' +
        result.place?.categories?.[0]?.icon?.suffix,
    };
  });
};

/**
 * Hook for Foursquare autocomplete search with React 19 deferred values
 */
export const useFoursquareAutocomplete = (query: string) => {
  const { location } = useLocation();

  // Use React 19's useDeferredValue for optimal performance
  const deferredQuery = useDeferredValue(query);

  return useQuery({
    queryKey: ['foursquare', 'autocomplete', deferredQuery, location?.coords],
    queryFn: async () => {
      if (!deferredQuery || deferredQuery.length < 2) {
        return [];
      }

      try {
        const response = await foursquare.autocomplete({
          query: deferredQuery,
          ll: location?.coords
            ? `${location.coords.latitude},${location.coords.longitude}`
            : undefined,
          radius: 50000, // 50km radius
          limit: 10,
          types: ['place', 'address', 'geo', 'poi'],
        });

        return convertToSuggestions(response.results);
      } catch (error) {
        console.error('Autocomplete error:', error);
        return [];
      }
    },
    enabled: deferredQuery.length >= 2,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook for full search with filters
 */
export const useDiscoverySearch = (query: string, filters?: any) => {
  const { location } = useLocation();

  return useQuery({
    queryKey: ['discovery', 'search', query, filters, location?.coords],
    queryFn: async () => {
      // This will integrate with the backend API
      // For now, return mock data
      return {
        items: [],
        totalCount: 0,
        hasMore: false,
      };
    },
    enabled: query.length > 0,
  });
};

/**
 * Hook for recent searches
 */
export const useRecentSearches = () => {
  // This will integrate with local storage
  return {
    searches: [],
    addSearch: (search: string) => {},
    clearSearches: () => {},
  };
};

/**
 * Hook for popular searches
 */
export const usePopularSearches = () => {
  return useQuery({
    queryKey: ['discovery', 'popular-searches'],
    queryFn: async () => {
      // This will integrate with the backend API
      return [
        'Bars near me',
        'Live music tonight',
        'Happy hour',
        'Nightclubs open late',
        'Craft breweries',
      ];
    },
    staleTime: 60 * 60 * 1000, // 1 hour
  });
};
