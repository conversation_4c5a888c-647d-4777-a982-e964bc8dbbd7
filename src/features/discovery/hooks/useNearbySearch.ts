/**
 * Hook for searching nearby content using location services
 * Integrates events, venues, and tickets with Foursquare data
 */
import { useCallback, useDeferredValue, useEffect, useState, useTransition } from 'react';

import { useQueries, useQuery } from '@tanstack/react-query';
import * as Location from 'expo-location';

import { FoursquarePlace, foursquare } from '@/src/core';

import type {
  DiscoveryContentType,
  DiscoveryFilters,
  DiscoveryItem,
  EventDiscoveryItem,
  MapRegion,
  TicketDiscoveryItem,
  VenueDiscoveryItem,
} from '../types/discovery.types';

interface UseNearbySearchOptions {
  radius?: number;
  enabledTypes?: DiscoveryContentType[];
  autoLoad?: boolean;
  refreshInterval?: number;
}

interface NearbySearchResult {
  // Data
  items: DiscoveryItem[];
  venues: VenueDiscoveryItem[];
  events: EventDiscoveryItem[];
  tickets: TicketDiscoveryItem[];

  // Location
  userLocation: Location.LocationObject | null;
  mapRegion: MapRegion | null;

  // Filters
  filters: DiscoveryFilters;
  updateFilters: (filters: Partial<DiscoveryFilters>) => void;
  clearFilters: () => void;

  // Search
  searchQuery: string;
  deferredSearchQuery: string;
  updateSearch: (query: string) => void;

  // Loading states
  isLoading: boolean;
  isRefreshing: boolean;
  isPending: boolean;

  // Actions
  refresh: () => void;
  loadMore: () => void;

  // Error handling
  error: Error | null;
  locationError: string | null;
}

/**
 * Default filters
 */
const DEFAULT_FILTERS: DiscoveryFilters = {
  radius: 5000, // 5km
  contentTypes: ['event', 'venue', 'ticket'],
  sortBy: 'distance',
  onlyOpen: true,
};

/**
 * Custom hook for nearby search functionality
 */
export const useNearbySearch = (options: UseNearbySearchOptions = {}): NearbySearchResult => {
  const {
    radius = 5000,
    enabledTypes = ['event', 'venue', 'ticket'],
    autoLoad = true,
    refreshInterval,
  } = options;

  // State management
  const [userLocation, setUserLocation] = useState<Location.LocationObject | null>(null);
  const [locationError, setLocationError] = useState<string | null>(null);
  const [filters, setFilters] = useState<DiscoveryFilters>({
    ...DEFAULT_FILTERS,
    radius,
    contentTypes: enabledTypes,
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [isPending, startTransition] = useTransition();

  // React 19 deferred value for search
  const deferredSearchQuery = useDeferredValue(searchQuery);
  const deferredFilters = useDeferredValue(filters);

  // Request location permission and get user location
  useEffect(() => {
    if (!autoLoad) return;

    const getLocation = async () => {
      try {
        const { status } = await Location.requestForegroundPermissionsAsync();
        if (status !== 'granted') {
          setLocationError('Location permission denied');
          return;
        }

        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.Balanced,
        });
        setUserLocation(location);

        // Update filters with user location
        setFilters(prev => ({
          ...prev,
          location: {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
          },
        }));
      } catch (error) {
        console.error('Location error:', error);
        setLocationError('Failed to get location');
      }
    };

    getLocation();
  }, [autoLoad]);

  // Foursquare venues query
  const venuesQuery = useQuery({
    queryKey: ['nearby-venues', userLocation, deferredFilters, deferredSearchQuery],
    queryFn: async () => {
      if (!userLocation || !deferredFilters.contentTypes?.includes('venue')) {
        return [];
      }

      const response = await foursquare.searchPlaces({
        query: deferredSearchQuery,
        ll: `${userLocation.coords.latitude},${userLocation.coords.longitude}`,
        radius: deferredFilters.radius,
        categories: deferredFilters.foursquareCategories,
        openNow: deferredFilters.onlyOpen,
        minPrice: deferredFilters.priceRange?.min,
        maxPrice: deferredFilters.priceRange?.max,
        limit: 50,
        sort: deferredFilters.sortBy === 'rating' ? 'rating' : 'distance',
      });

      // Transform Foursquare places to VenueDiscoveryItems
      return response.results.map(place => transformFoursquareToVenue(place));
    },
    enabled: !!userLocation && deferredFilters.contentTypes?.includes('venue'),
    staleTime: refreshInterval || 5 * 60 * 1000, // 5 minutes
    refetchInterval: refreshInterval,
  });

  // Events query (from your backend)
  const eventsQuery = useQuery({
    queryKey: ['nearby-events', userLocation, deferredFilters, deferredSearchQuery],
    queryFn: async () => {
      if (!userLocation || !deferredFilters.contentTypes?.includes('event')) {
        return [];
      }

      // TODO: Replace with actual event service call
      // const response = await eventService.getNearby({
      //   location: userLocation.coords,
      //   filters: deferredFilters,
      //   search: deferredSearchQuery,
      // });

      // Mock data for now
      return [] as EventDiscoveryItem[];
    },
    enabled: !!userLocation && deferredFilters.contentTypes?.includes('event'),
    staleTime: refreshInterval || 5 * 60 * 1000,
  });

  // Tickets query (from your backend)
  const ticketsQuery = useQuery({
    queryKey: ['nearby-tickets', userLocation, deferredFilters, deferredSearchQuery],
    queryFn: async () => {
      if (!userLocation || !deferredFilters.contentTypes?.includes('ticket')) {
        return [];
      }

      // TODO: Replace with actual ticket service call
      // const response = await ticketService.getNearby({
      //   location: userLocation.coords,
      //   filters: deferredFilters,
      //   search: deferredSearchQuery,
      // });

      // Mock data for now
      return [] as TicketDiscoveryItem[];
    },
    enabled: !!userLocation && deferredFilters.contentTypes?.includes('ticket'),
    staleTime: refreshInterval || 5 * 60 * 1000,
  });

  // Combine and sort all items
  const allItems = [
    ...(venuesQuery.data || []),
    ...(eventsQuery.data || []),
    ...(ticketsQuery.data || []),
  ].sort((a, b) => {
    switch (deferredFilters.sortBy) {
      case 'distance':
        return (a.distance || 0) - (b.distance || 0);
      case 'date':
        return (a.startTime?.getTime() || 0) - (b.startTime?.getTime() || 0);
      case 'price':
        return (a.price?.min || 0) - (b.price?.min || 0);
      default:
        return 0;
    }
  });

  // Calculate map region based on items
  const mapRegion = calculateMapRegion(userLocation, allItems);

  // Update filters with transition
  const updateFilters = useCallback((newFilters: Partial<DiscoveryFilters>) => {
    startTransition(() => {
      setFilters(prev => ({ ...prev, ...newFilters }));
    });
  }, []);

  // Clear filters
  const clearFilters = useCallback(() => {
    startTransition(() => {
      setFilters({
        ...DEFAULT_FILTERS,
        location: filters.location, // Keep location
      });
      setSearchQuery('');
    });
  }, [filters.location]);

  // Update search with transition
  const updateSearch = useCallback((query: string) => {
    startTransition(() => {
      setSearchQuery(query);
    });
  }, []);

  // Refresh all queries
  const refresh = useCallback(() => {
    venuesQuery.refetch();
    eventsQuery.refetch();
    ticketsQuery.refetch();
  }, [venuesQuery, eventsQuery, ticketsQuery]);

  // Load more (pagination)
  const loadMore = useCallback(() => {
    // Implement pagination logic here
    console.log('Load more not implemented yet');
  }, []);

  return {
    // Data
    items: allItems,
    venues: venuesQuery.data || [],
    events: eventsQuery.data || [],
    tickets: ticketsQuery.data || [],

    // Location
    userLocation,
    mapRegion,

    // Filters
    filters: deferredFilters,
    updateFilters,
    clearFilters,

    // Search
    searchQuery,
    deferredSearchQuery,
    updateSearch,

    // Loading states
    isLoading: venuesQuery.isLoading || eventsQuery.isLoading || ticketsQuery.isLoading,
    isRefreshing: venuesQuery.isRefetching || eventsQuery.isRefetching || ticketsQuery.isRefetching,
    isPending,

    // Actions
    refresh,
    loadMore,

    // Error handling
    error: venuesQuery.error || eventsQuery.error || ticketsQuery.error,
    locationError,
  };
};

/**
 * Transform Foursquare place to VenueDiscoveryItem
 */
function transformFoursquareToVenue(place: FoursquarePlace): VenueDiscoveryItem {
  const primaryCategory = place.categories.find(cat => cat.primary) || place.categories[0];

  return {
    id: `venue-${place.fsqId}`,
    type: 'venue',
    title: place.name,
    subtitle: primaryCategory?.name,
    imageUrl: place.photos?.[0] ? foursquare.getPhotoUrl(place.photos[0], '500x500') : undefined,
    location: {
      latitude: place.geocodes?.main.latitude || 0,
      longitude: place.geocodes?.main.longitude || 0,
      address: place.location.address,
      neighborhood: place.location.neighborhood?.[0],
      city: place.location.locality,
      venueId: place.fsqId,
      venueName: place.name,
    },
    distance: place.distance,
    price: place.price
      ? {
          min: place.price * 10,
          max: place.price * 30,
          currency: 'USD',
          isFree: false,
          label: foursquare.getPriceDisplay(place.price),
        }
      : {
          currency: 'USD',
          isFree: true,
        },
    tags: place.categories.map(cat => cat.name),
    foursquarePlace: place,
    rating: place.rating,
    priceLevel: place.price,
    isOpen: place.hours?.openNow,
    checkInCount: place.stats?.totalPhotos,
  };
}

/**
 * Calculate map region to show all items
 */
function calculateMapRegion(
  userLocation: Location.LocationObject | null,
  items: DiscoveryItem[]
): MapRegion | null {
  if (!userLocation) return null;

  if (items.length === 0) {
    // Default region around user
    return {
      latitude: userLocation.coords.latitude,
      longitude: userLocation.coords.longitude,
      latitudeDelta: 0.05,
      longitudeDelta: 0.05,
    };
  }

  // Calculate bounds
  const lats = [userLocation.coords.latitude, ...items.map(item => item.location.latitude)];
  const lngs = [userLocation.coords.longitude, ...items.map(item => item.location.longitude)];

  const minLat = Math.min(...lats);
  const maxLat = Math.max(...lats);
  const minLng = Math.min(...lngs);
  const maxLng = Math.max(...lngs);

  const centerLat = (minLat + maxLat) / 2;
  const centerLng = (minLng + maxLng) / 2;

  const latDelta = (maxLat - minLat) * 1.2; // Add 20% padding
  const lngDelta = (maxLng - minLng) * 1.2;

  return {
    latitude: centerLat,
    longitude: centerLng,
    latitudeDelta: Math.max(latDelta, 0.01),
    longitudeDelta: Math.max(lngDelta, 0.01),
  };
}
