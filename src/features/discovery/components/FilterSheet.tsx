/**
 * FilterSheet Component
 * Bottom sheet with discovery filters for events, venues, and tickets
 */
import React, { useMemo } from 'react';

import { ScrollView } from 'react-native';

import BottomSheet, { BottomSheetBackdrop, BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { useTheme } from '@shopify/restyle';

import { Box, Pressable, Text } from '@/src/core/theme';
import { PressableAnimated } from '@/src/core/theme/Pressable';
import { Button, Divider, Switch } from '@/src/shared/components';

import type { DiscoveryFilters } from '../types/discovery.types';

interface FilterSheetProps {
  visible: boolean;
  onClose: () => void;
  filters: DiscoveryFilters;
  onApply: (filters: DiscoveryFilters) => void;
}

interface FilterSectionProps {
  title: string;
  children: React.ReactNode;
}

const FilterSection: React.FC<FilterSectionProps> = ({ title, children }) => (
  <Box marginBottom="lg_24">
    <Text variant="h_24SemiBold_section" color="mainText" marginBottom="sm_12">
      {title}
    </Text>
    {children}
  </Box>
);

interface FilterOptionProps {
  label: string;
  value: boolean;
  onValueChange: (value: boolean) => void;
  icon?: string;
}

const FilterOption: React.FC<FilterOptionProps> = ({ label, value, onValueChange, icon }) => {
  return (
    <PressableAnimated onPress={() => onValueChange(!value)}>
      <Box
        flexDirection="row"
        alignItems="center"
        justifyContent="space-between"
        paddingVertical="sm_12">
        <Box flexDirection="row" alignItems="center" flex={1}>
          {icon && (
            <Box marginRight="sm_12">
              <Text variant="b_14Medium_button">{icon}</Text>
            </Box>
          )}
          <Text variant="b_14Medium_button" color="mainText">
            {label}
          </Text>
        </Box>
        <Switch isChecked={value} onToggle={onValueChange} />
      </Box>
    </PressableAnimated>
  );
};

const FilterSheet: React.FC<FilterSheetProps> = ({ visible, onClose, filters, onApply }) => {
  const snapPoints = useMemo(() => ['85%'], []);
  const theme = useTheme();

  // Local state for filters
  const [localFilters, setLocalFilters] = React.useState<DiscoveryFilters>(filters);

  // Update local filters when prop changes
  React.useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  // Handle filter changes
  const updateFilter = <K extends keyof DiscoveryFilters>(key: K, value: DiscoveryFilters[K]) => {
    setLocalFilters(prev => ({ ...prev, [key]: value }));
  };

  // Handle reset
  const handleReset = () => {
    const resetFilters: DiscoveryFilters = {
      radius: 5000,
      sortBy: 'distance',
      sortOrder: 'asc',
    };
    setLocalFilters(resetFilters);
  };

  // Handle apply
  const handleApply = () => {
    onApply(localFilters);
    onClose();
  };

  // Count active filters
  const activeFilterCount = useMemo(() => {
    let count = 0;
    if (localFilters.onlyOpen) count++;
    if (localFilters.onlyFree) count++;
    if (localFilters.onlyToday) count++;
    if (localFilters.onlyThisWeek) count++;
    if (localFilters.onlyVerified) count++;
    if (localFilters.onlyTrending) count++;
    if (localFilters.servesAlcohol) count++;
    if (localFilters.liveMusic) count++;
    if (localFilters.outdoorSeating) count++;
    if (localFilters.hasParking) count++;
    if (localFilters.wheelchairAccessible) count++;
    return count;
  }, [localFilters]);

  if (!visible) return null;

  return (
    <BottomSheet
      index={0}
      snapPoints={snapPoints}
      onClose={onClose}
      enablePanDownToClose
      backgroundStyle={{ backgroundColor: theme.colors.elevatedBackground }}
      backdropComponent={props => (
        <BottomSheetBackdrop {...props} disappearsOnIndex={-1} appearsOnIndex={0} />
      )}>
      <BottomSheetScrollView
        style={{ backgroundColor: theme.colors.elevatedBackground }}
        contentContainerStyle={{
          paddingBottom: 100, // Space for footer
        }}
        showsVerticalScrollIndicator={false}>
        {/* Header */}
        <Box
          flexDirection="row"
          alignItems="center"
          justifyContent="space-between"
          paddingHorizontal="md_16"
          paddingVertical="md_16">
          <Box flexDirection="row" alignItems="center">
            <Text variant="h_24SemiBold_section" color="mainText">
              Filters
            </Text>
            {activeFilterCount > 0 && (
              <Box
                marginLeft="xs_8"
                backgroundColor="brandMain"
                borderRadius="circle_9999"
                paddingHorizontal="xs_8"
                paddingVertical="xxs_4">
                <Text variant="l_12Medium_message" color="white">
                  {activeFilterCount}
                </Text>
              </Box>
            )}
          </Box>
          <Pressable onPress={handleReset}>
            <Text variant="b_14Medium_button" color="brandMain">
              Reset
            </Text>
          </Pressable>
        </Box>

        <Divider />

        {/* Filter Content */}
        <Box paddingHorizontal="md_16" paddingBottom="xl_32">
          {/* Status Filters */}
          <FilterSection title="Status">
            <FilterOption
              label="Open Now"
              value={localFilters.onlyOpen || false}
              onValueChange={value => updateFilter('onlyOpen', value)}
              icon="🕐"
            />
            <FilterOption
              label="Free Entry"
              value={localFilters.onlyFree || false}
              onValueChange={value => updateFilter('onlyFree', value)}
              icon="🆓"
            />
            <FilterOption
              label="Verified Venues"
              value={localFilters.onlyVerified || false}
              onValueChange={value => updateFilter('onlyVerified', value)}
              icon="✓"
            />
            <FilterOption
              label="Trending Now"
              value={localFilters.onlyTrending || false}
              onValueChange={value => updateFilter('onlyTrending', value)}
              icon="🔥"
            />
          </FilterSection>

          {/* Time Filters */}
          <FilterSection title="When">
            <FilterOption
              label="Today"
              value={localFilters.onlyToday || false}
              onValueChange={value => updateFilter('onlyToday', value)}
              icon="📅"
            />
            <FilterOption
              label="This Week"
              value={localFilters.onlyThisWeek || false}
              onValueChange={value => updateFilter('onlyThisWeek', value)}
              icon="📆"
            />
          </FilterSection>

          {/* Party Features */}
          <FilterSection title="Party Features">
            <FilterOption
              label="Serves Alcohol"
              value={localFilters.servesAlcohol || false}
              onValueChange={value => updateFilter('servesAlcohol', value)}
              icon="🍺"
            />
            <FilterOption
              label="Live Music"
              value={localFilters.liveMusic || false}
              onValueChange={value => updateFilter('liveMusic', value)}
              icon="🎵"
            />
            <FilterOption
              label="Outdoor Seating"
              value={localFilters.outdoorSeating || false}
              onValueChange={value => updateFilter('outdoorSeating', value)}
              icon="🌳"
            />
          </FilterSection>

          {/* Accessibility */}
          <FilterSection title="Accessibility">
            <FilterOption
              label="Parking Available"
              value={localFilters.hasParking || false}
              onValueChange={value => updateFilter('hasParking', value)}
              icon="🅿️"
            />
            <FilterOption
              label="Wheelchair Accessible"
              value={localFilters.wheelchairAccessible || false}
              onValueChange={value => updateFilter('wheelchairAccessible', value)}
              icon="♿"
            />
          </FilterSection>

          {/* Footer Button */}
          <Box paddingTop="md_16" paddingBottom="lg_24">
            <Button onPress={handleApply} title="Apply Filters" variant="primary" />
          </Box>
        </Box>
      </BottomSheetScrollView>
    </BottomSheet>
  );
};

export default FilterSheet;
