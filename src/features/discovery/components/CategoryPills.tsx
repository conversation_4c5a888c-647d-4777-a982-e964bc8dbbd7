/**
 * CategoryPills Component
 * Quick category filters for discovery
 */
import React, { useRef } from 'react';

import { FlatList, Pressable } from 'react-native';

import { useTheme } from '@shopify/restyle';
import Animated, {
  FadeIn,
  FadeOut,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from 'react-native-reanimated';

import { Box, Text, Theme } from '@/src/core/theme';

import { DISCOVERY_CATEGORIES, type DiscoveryCategory } from '../types/discovery.types';

interface CategoryPillsProps {
  categories?: DiscoveryCategory[];
  selected: string[];
  onSelect: (categoryId: string) => void;
  scrollable?: boolean;
}

interface CategoryPillProps {
  category: DiscoveryCategory;
  isSelected: boolean;
  onPress: () => void;
}

const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

const CategoryPill: React.FC<CategoryPillProps> = ({ category, isSelected, onPress }) => {
  const theme = useTheme<Theme>();
  const scale = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const handlePressIn = () => {
    scale.set(
      withSpring(0.95, {
        damping: 15,
        stiffness: 400,
      })
    );
  };

  const handlePressOut = () => {
    scale.set(
      withSpring(1, {
        damping: 15,
        stiffness: 400,
      })
    );
  };

  return (
    <AnimatedPressable
      onPress={onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      style={[
        animatedStyle,
        {
          marginRight: theme.spacing.xs_8,
          marginBottom: theme.spacing.xs_8,
        },
      ]}>
      <Box
        flexDirection="row"
        alignItems="center"
        paddingHorizontal="md_16"
        paddingVertical="xs_8"
        borderRadius="circle_9999"
        backgroundColor={isSelected ? 'brandMain' : 'surfaceBackground'}
        borderWidth={1}
        borderColor={isSelected ? 'brandMain' : 'border'}>
        <Text variant="b_14Medium_button" marginRight="xxs_4">
          {category.icon}
        </Text>
        <Text variant="b_14Medium_button" color={isSelected ? 'white' : 'mainText'}>
          {category.name}
        </Text>
        {isSelected && (
          <Animated.View entering={FadeIn} exiting={FadeOut}>
            <Box marginLeft="xxs_4">
              <Text variant="b_14Medium_button" color="white">
                ✓
              </Text>
            </Box>
          </Animated.View>
        )}
      </Box>
    </AnimatedPressable>
  );
};

const CategoryPills: React.FC<CategoryPillsProps> = ({
  categories = DISCOVERY_CATEGORIES,
  selected,
  onSelect,
  scrollable = true,
}) => {
  const theme = useTheme<Theme>();
  const scrollRef = useRef<FlatList | null>(null);

  const handleCategoryPress = (categoryId: string) => {
    onSelect(categoryId);
  };

  const renderCategory = ({ item }: { item: DiscoveryCategory }) => (
    <CategoryPill
      category={item}
      isSelected={selected.includes(item.id)}
      onPress={() => handleCategoryPress(item.id)}
    />
  );

  if (scrollable) {
    return (
      <FlatList
        ref={scrollRef}
        data={categories}
        renderItem={renderCategory}
        keyExtractor={item => item.id}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{
          paddingHorizontal: theme.spacing.md_16,
          paddingVertical: theme.spacing.xs_8,
        }}
        ItemSeparatorComponent={() => <Box width={8} />}
      />
    );
  }

  // Non-scrollable wrap layout
  return (
    <Box flexDirection="row" flexWrap="wrap" paddingHorizontal="md_16" paddingVertical="xs_8">
      {categories.map(category => (
        <CategoryPill
          key={category.id}
          category={category}
          isSelected={selected.includes(category.id)}
          onPress={() => handleCategoryPress(category.id)}
        />
      ))}
    </Box>
  );
};

export default CategoryPills;
