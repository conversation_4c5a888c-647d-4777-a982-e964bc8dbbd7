/**
 * SearchBar Component
 * Global search with Foursquare autocomplete for discovering party venues, events, and tickets
 */
import React, {
  RefObject,
  forwardRef,
  useCallback,
  useDeferredValue,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import {
  ActivityIndicator,
  FlatList,
  Keyboard,
  Pressable,
  TextInput as RNTextInput,
} from 'react-native';

import { Feather, MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@shopify/restyle';
import Animated, { FadeIn, FadeInDown, FadeOut, FadeOutUp } from 'react-native-reanimated';

import { Box, Text, TextInput, Theme } from '@/src/core/theme';
import { useResponsive } from '@/src/core/theme/useResponsive';
import { Divider } from '@/src/shared/components';

import { useFoursquareAutocomplete } from '../hooks/useSearch';
import type { SearchSuggestion } from '../types/discovery.types';

/**
 * Memoized No Results Component with smooth animations
 * Only re-renders when query changes
 */
const NoResultsMessage = React.memo(({ query }: { query: string }) => {
  return (
    <Animated.View
      key={`no-results-${query}`}
      entering={FadeInDown.duration(300).springify()}
      exiting={FadeOutUp.duration(200)}>
      <Box padding="md_16" alignItems="center">
        <Text variant="b_14Regular_content" color="textSecondary">
          No results found for {query}
        </Text>
      </Box>
    </Animated.View>
  );
});

NoResultsMessage.displayName = 'NoResultsMessage';

/**
 * Memoized Suggestions List Component
 * Only re-renders when suggestions change
 */
const SuggestionsList = React.memo(
  ({
    suggestions,
    onSuggestionPress,
    renderSuggestion,
  }: {
    suggestions: SearchSuggestion[];
    onSuggestionPress: (suggestion: SearchSuggestion) => void;
    renderSuggestion: ({ item }: { item: SearchSuggestion }) => React.ReactElement;
  }) => {
    return (
      <Animated.View entering={FadeInDown.duration(250)} exiting={FadeOutUp.duration(200)}>
        <FlatList
          data={suggestions}
          renderItem={renderSuggestion}
          keyExtractor={item => item.id}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
          ItemSeparatorComponent={() => <Divider />}
        />
      </Animated.View>
    );
  }
);

SuggestionsList.displayName = 'SuggestionsList';

interface SearchBarProps {
  value: string;
  onChangeText: (text: string) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  placeholder?: string;
  showFilters?: boolean;
  onFilterPress?: () => void;
  onSuggestionPress?: (suggestion: SearchSuggestion) => void;
}

const SearchBar = forwardRef<RNTextInput, SearchBarProps>(
  (
    {
      value,
      onChangeText,
      onFocus,
      onBlur,
      placeholder = 'Search venues, events, tickets...',
      showFilters = true,
      onFilterPress,
      onSuggestionPress,
    },
    ref
  ) => {
    const theme = useTheme<Theme>();
    const { select } = useResponsive();
    const [isFocused, setIsFocused] = useState(false);

    // Defer only the search query for API calls, not the input value
    const deferredQuery = useDeferredValue(value);

    // Internal ref for managing focus
    const inputRef = useRef<RefObject<RNTextInput> | null>(null);
    const searchRef = ref || inputRef;

    // Get autocomplete suggestions using deferred query
    const { data: suggestions, isLoading } = useFoursquareAutocomplete(deferredQuery);

    // Handle focus
    const handleFocus = () => {
      setIsFocused(true);
      onFocus?.();
    };

    // Handle blur
    const handleBlur = () => {
      // Delay blur to allow suggestion tap
      setTimeout(() => {
        setIsFocused(false);
        onBlur?.();
      }, 100);
    };

    // Handle text change immediately for responsive typing
    const handleChangeText = (text: string) => {
      onChangeText(text);
    };

    // Handle suggestion press
    const handleSuggestionPress = useCallback(
      (suggestion: SearchSuggestion) => {
        Keyboard.dismiss();
        onChangeText(suggestion.text);
        onSuggestionPress?.(suggestion);
        setIsFocused(false);
      },
      [onChangeText, onSuggestionPress]
    );

    // Show suggestions when focused and has deferred value
    const showSuggestions =
      isFocused && deferredQuery.length > 0 && suggestions && suggestions.length > 0;

    // Store the query that was actually searched to avoid flickering
    const [lastSearchedQuery, setLastSearchedQuery] = useState('');

    // Update the searched query only when a search completes
    useEffect(() => {
      if (!isLoading && deferredQuery.length >= 2) {
        setLastSearchedQuery(deferredQuery);
      }
    }, [isLoading, deferredQuery]);

    // Show "no results" when we have completed a search with no results
    const showNoResults = useMemo(() => {
      return (
        isFocused &&
        lastSearchedQuery.length >= 2 &&
        !isLoading &&
        !showSuggestions &&
        suggestions?.length === 0
      );
    }, [isFocused, lastSearchedQuery, isLoading, showSuggestions, suggestions]);

    // Render suggestion icon
    const renderSuggestionIcon = useCallback(
      (type: string) => {
        const iconSize = select({ phone: 20, tablet: 24 });
        const iconColor = theme.colors.textSecondary;

        switch (type) {
          case 'venue':
            return <MaterialIcons name="location-on" size={iconSize} color={iconColor} />;
          case 'event':
            return <MaterialIcons name="event" size={iconSize} color={iconColor} />;
          case 'category':
            return <MaterialIcons name="category" size={iconSize} color={iconColor} />;
          default:
            return <Feather name="search" size={iconSize} color={iconColor} />;
        }
      },
      [select, theme.colors.textSecondary]
    );

    // Render suggestion item (memoized to prevent unnecessary re-renders)
    const renderSuggestion = useCallback(
      ({ item }: { item: SearchSuggestion }) => (
        <Pressable
          onPress={() => handleSuggestionPress(item)}
          style={({ pressed }) => ({
            opacity: pressed ? 0.7 : 1,
          })}>
          <Box
            flexDirection="row"
            alignItems="center"
            paddingHorizontal="md_16"
            paddingVertical="sm_12"
            backgroundColor="elevatedBackground">
            <Box marginRight="sm_12">{renderSuggestionIcon(item.type)}</Box>
            <Box flex={1}>
              <Text variant="b_14Medium_button" color="mainText" numberOfLines={1}>
                {item.text}
              </Text>
              {item.metadata?.category && (
                <Text variant="l_12Regular_helperText" color="textSecondary" numberOfLines={1}>
                  {item.metadata.category}
                </Text>
              )}
            </Box>
            {item.metadata?.distance && (
              <Text variant="l_12Regular_helperText" color="textSecondary">
                {item.metadata.distance}m
              </Text>
            )}
          </Box>
        </Pressable>
      ),
      [handleSuggestionPress, renderSuggestionIcon]
    );

    // Loading indicator only for API loading
    const loadingIndicator = isLoading && (
      <ActivityIndicator size="small" color={theme.colors.brandMain} />
    );

    // Filter button
    const filterButton = showFilters && (
      <Pressable
        onPress={onFilterPress}
        style={({ pressed }) => ({
          opacity: pressed ? 0.7 : 1,
        })}>
        <Box padding="xs_8">
          <Feather
            name="filter"
            size={select({ phone: 20, tablet: 24 })}
            color={theme.colors.textSecondary}
          />
        </Box>
      </Pressable>
    );

    return (
      <Box width="100%">
        <TextInput
          ref={searchRef}
          value={value}
          onChangeText={handleChangeText}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          label="Search"
          leading={
            <Feather
              name="search"
              size={select({ phone: 20, tablet: 24 })}
              color={theme.colors.textSecondary}
            />
          }
          trailing={
            <Box flexDirection="row" alignItems="center" gap="xxs_4">
              {loadingIndicator}
              {filterButton}
            </Box>
          }
          showClearButton
          autoCorrect={false}
          autoCapitalize="none"
          returnKeyType="search"
        />

        {(showSuggestions || showNoResults) && (
          <Animated.View
            entering={FadeIn.duration(200)}
            exiting={FadeOut.duration(200)}
            style={{
              position: 'absolute',
              top: '100%',
              left: 0,
              right: 0,
              zIndex: 999,
              maxHeight: 300,
              backgroundColor: theme.colors.background,
              borderRadius: theme.borderRadii.md_12,
              ...theme.cardVariants.elevated,
            }}>
            {showSuggestions ? (
              <SuggestionsList
                suggestions={suggestions}
                onSuggestionPress={handleSuggestionPress}
                renderSuggestion={renderSuggestion}
              />
            ) : showNoResults ? (
              <NoResultsMessage query={lastSearchedQuery} />
            ) : null}
          </Animated.View>
        )}
      </Box>
    );
  }
);

SearchBar.displayName = 'SearchBar';

export default SearchBar;
