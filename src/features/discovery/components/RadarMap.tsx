/**
 * Radar Map Component
 * Interactive map showing nearby events, venues, and tickets with clustering
 */
import React, { useCallback, useEffect, useRef, useState } from 'react';

import { LayoutAnimation, Platform, Pressable, View } from 'react-native';

import MapView from '@allisonadam81/react-native-super-clusters';
import {
  Calendar,
  GpsFix,
  MagnifyingGlassMinus,
  MagnifyingGlassPlus,
  MapPin,
  Ticket,
  Users,
} from 'phosphor-react-native';
import { Callout, Marker, PROVIDER_DEFAULT, PROVIDER_GOOGLE, Region } from 'react-native-maps';
import Animated, {
  Extrapolation,
  interpolate,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from 'react-native-reanimated';

import Box from '@/src/core/theme/Box';
import Text from '@/src/core/theme/Text';
import { TokensThemeColors, useTheme } from '@/src/core/theme/theme';

import type { DiscoveryContentType, DiscoveryItem, MapRegion } from '../types/discovery.types';
import DiscoveryCard from './DiscoveryCard';

/**
 * Radar map props
 */
interface RadarMapProps {
  /** Discovery items to display */
  items: DiscoveryItem[];

  /** User's current location */
  userLocation?: { latitude: number; longitude: number };

  /** Initial map region */
  initialRegion?: MapRegion;

  /** Selected marker ID */
  selectedMarkerId?: string | null;

  /** Enable clustering */
  clusteringEnabled?: boolean;

  /** Marker press handler */
  onMarkerPress?: (item: DiscoveryItem) => void;

  /** Map press handler */
  onMapPress?: () => void;

  /** Region change handler */
  onRegionChange?: (region: Region) => void;

  /** Custom marker colors */
  markerColors?: Record<DiscoveryContentType, TokensThemeColors>;

  /** Show user location */
  showUserLocation?: boolean;

  /** Map style */
  mapStyle?: any[];

  /** Custom cluster styling */
  clusterColor?: string;
  clusterTextColor?: string;
  clusterRadius?: number;
  maxZoom?: number;
  minZoom?: number;
}

/**
 * Default marker colors
 */
const DEFAULT_MARKER_COLORS: Record<DiscoveryContentType, TokensThemeColors> = {
  event: 'primary',
  venue: 'success',
  ticket: 'warning',
};

/**
 * Marker icons mapping
 */
const MARKER_ICONS: Record<DiscoveryContentType, React.ComponentType<any>> = {
  event: Calendar,
  venue: MapPin,
  ticket: Ticket,
};

/**
 * Enhanced cluster marker component with animations
 */
const ClusterMarker = ({
  count,
  color = '#FF6B6B',
  textColor = '#FFFFFF',
}: {
  count: number;
  color?: string;
  textColor?: string;
}) => {
  const scale = useSharedValue(0);
  const opacity = useSharedValue(0);

  useEffect(() => {
    scale.value = withSpring(1, {
      damping: 15,
      stiffness: 400,
    });
    opacity.value = withSpring(1, {
      damping: 20,
      stiffness: 300,
    });
  }, [opacity, scale]);

  const animatedStyle = useAnimatedStyle(() => {
    const size = interpolate(count, [2, 10, 50, 100], [50, 60, 70, 80], Extrapolation.CLAMP);

    return {
      transform: [{ scale: scale.value }],
      opacity: opacity.value,
      width: size,
      height: size,
    };
  });

  const fontSize = count >= 100 ? 14 : count >= 10 ? 16 : 18;

  return (
    <Pressable>
      <Animated.View style={animatedStyle}>
        <Box
          width="100%"
          height="100%"
          borderRadius="circle_9999"
          backgroundColor={color as TokensThemeColors}
          justifyContent="center"
          alignItems="center"
          borderWidth={3}
          borderColor="white"
          style={{
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 3 },
            shadowOpacity: 0.3,
            shadowRadius: 4.65,
            elevation: 8,
          }}>
          {/* Cluster count */}
          <Text
            variant="b_16Regular_input"
            color={textColor as TokensThemeColors}
            style={{ fontSize }}>
            {count}
          </Text>

          {/* Users icon for large clusters */}
          {count >= 10 && (
            <Box position="absolute" top={-2} right={-2}>
              <Users size={16} color={textColor} weight="fill" />
            </Box>
          )}
        </Box>
      </Animated.View>
    </Pressable>
  );
};

/**
 * Enhanced item marker component
 */
const ItemMarker = ({
  item,
  color,
  isSelected,
  onPress,
}: {
  item: DiscoveryItem;
  color: TokensThemeColors;
  isSelected: boolean;
  onPress: () => void;
}) => {
  const scale = useSharedValue(isSelected ? 1.2 : 1);
  const pulse = useSharedValue(1);

  useEffect(() => {
    scale.value = withSpring(isSelected ? 1.2 : 1, {
      damping: 15,
      stiffness: 400,
    });
  }, [isSelected, scale]);

  // Pulse animation for selected marker
  useEffect(() => {
    if (isSelected) {
      pulse.value = withSpring(1.1, {
        damping: 5,
        stiffness: 200,
      });
    } else {
      pulse.value = withSpring(1, {
        damping: 15,
        stiffness: 400,
      });
    }
  }, [isSelected, pulse]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }, { scale: pulse.value }],
  }));

  const IconComponent = MARKER_ICONS[item.type];
  const markerSize = isSelected ? 52 : 44;
  const iconSize = isSelected ? 26 : 22;

  return (
    <Marker
      coordinate={{
        latitude: item.location.latitude,
        longitude: item.location.longitude,
      }}
      onPress={onPress}
      tracksViewChanges={false}>
      <Animated.View style={animatedStyle}>
        <Box
          width={markerSize}
          height={markerSize}
          borderRadius="circle_9999"
          backgroundColor={color}
          justifyContent="center"
          alignItems="center"
          borderWidth={isSelected ? 3 : 2}
          borderColor="white"
          style={{
            shadowColor: '#000',
            shadowOffset: { width: 0, height: isSelected ? 4 : 2 },
            shadowOpacity: isSelected ? 0.35 : 0.25,
            shadowRadius: isSelected ? 5.46 : 3.84,
            elevation: isSelected ? 8 : 5,
          }}>
          <IconComponent size={iconSize} color="white" weight={isSelected ? 'fill' : 'bold'} />
        </Box>

        {/* Price badge for paid items */}
        {item.price && !item.price.isFree && (
          <Box
            position="absolute"
            top={-6}
            right={-6}
            backgroundColor="white"
            paddingHorizontal="xxs_4"
            paddingVertical="xxs_4"
            borderRadius="md_12"
            borderWidth={1}
            borderColor={color}
            style={{
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 1 },
              shadowOpacity: 0.2,
              shadowRadius: 1.41,
              elevation: 2,
            }}>
            <Text variant="l_12Regular_helperText" color={color} style={{ fontSize: 10 }}>
              ${item.price.min}
            </Text>
          </Box>
        )}

        {/* Availability indicator */}
        {item.type === 'ticket' && (
          <Box
            position="absolute"
            bottom={-3}
            right={-3}
            width={12}
            height={12}
            borderRadius="circle_9999"
            backgroundColor={item.availability === 'available' ? 'success' : 'error'}
            borderWidth={2}
            borderColor="white"
          />
        )}
      </Animated.View>

      {/* Enhanced callout with preview card */}
      <Callout tooltip>
        <Box
          width={320}
          backgroundColor="transparent"
          style={{
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.3,
            shadowRadius: 4.65,
            elevation: 8,
          }}>
          <DiscoveryCard item={item} variant="compact" showDistance={false} />
        </Box>
      </Callout>
    </Marker>
  );
};

/**
 * Enhanced Radar map component with clustering
 */
const RadarMap: React.FC<RadarMapProps> = ({
  items,
  userLocation,
  initialRegion,
  selectedMarkerId,
  clusteringEnabled = true,
  onMarkerPress,
  onMapPress,
  onRegionChange,
  markerColors = DEFAULT_MARKER_COLORS,
  showUserLocation = true,
  mapStyle,
  clusterColor = '#FF6B6B',
  clusterTextColor = '#FFFFFF',
  clusterRadius = 60,
  maxZoom = 17,
  minZoom = 3,
}) => {
  const theme = useTheme();
  const mapRef = useRef<any>(null);
  const [region, setRegion] = useState<Region | undefined>(initialRegion);

  // Handle marker press with animation
  const handleMarkerPress = useCallback(
    (item: DiscoveryItem) => {
      onMarkerPress?.(item);

      // Smooth animation to marker
      mapRef.current?.animateToRegion(
        {
          latitude: item.location.latitude,
          longitude: item.location.longitude,
          latitudeDelta: 0.008,
          longitudeDelta: 0.008,
        },
        500
      );
    },
    [onMarkerPress]
  );

  // Handle region change
  const handleRegionChange = useCallback(
    (newRegion: Region) => {
      setRegion(newRegion);
      onRegionChange?.(newRegion);
    },
    [onRegionChange]
  );

  // Handle cluster press
  const handleClusterPress = useCallback((cluster: any, markers: any[]) => {
    const coordinates = markers.map(marker => ({
      latitude: marker.location.latitude,
      longitude: marker.location.longitude,
    }));

    // Calculate bounds for all markers in cluster
    const minLat = Math.min(...coordinates.map(c => c.latitude));
    const maxLat = Math.max(...coordinates.map(c => c.latitude));
    const minLng = Math.min(...coordinates.map(c => c.longitude));
    const maxLng = Math.max(...coordinates.map(c => c.longitude));

    const centerLat = (minLat + maxLat) / 2;
    const centerLng = (minLng + maxLng) / 2;
    const latDelta = (maxLat - minLat) * 1.5;
    const lngDelta = (maxLng - minLng) * 1.5;

    mapRef.current?.animateToRegion(
      {
        latitude: centerLat,
        longitude: centerLng,
        latitudeDelta: Math.max(latDelta, 0.01),
        longitudeDelta: Math.max(lngDelta, 0.01),
      },
      600
    );
  }, []);

  // Focus on selected marker
  useEffect(() => {
    if (selectedMarkerId) {
      const selectedItem = items.find(item => item.id === selectedMarkerId);
      if (selectedItem) {
        mapRef.current?.animateToRegion(
          {
            latitude: selectedItem.location.latitude,
            longitude: selectedItem.location.longitude,
            latitudeDelta: 0.01,
            longitudeDelta: 0.01,
          },
          300
        );
      }
    }
  }, [selectedMarkerId, items]);

  return (
    <View style={{ flex: 1 }}>
      <MapView
        ref={mapRef}
        style={{ flex: 1 }}
        provider={Platform.OS === 'android' ? PROVIDER_GOOGLE : PROVIDER_DEFAULT}
        initialRegion={initialRegion}
        onRegionChangeComplete={handleRegionChange}
        onPress={onMapPress}
        showsUserLocation={showUserLocation}
        showsMyLocationButton={false}
        showsCompass={false}
        customMapStyle={mapStyle}
        // Clustering configuration
        clusteringEnabled={clusteringEnabled}
        clusterColor={clusterColor}
        clusterTextColor={clusterTextColor}
        clusterFontFamily="Urbanist_Regular"
        radius={clusterRadius}
        maxZoom={maxZoom}
        minZoom={minZoom}
        animationEnabled={true}
        preserveClusterPressBehavior={false}
        onClusterPress={(cluster, markers) => {
          // @ts-ignore
          handleClusterPress(cluster, markers);
        }}
        // Custom cluster renderer
        renderCluster={cluster => {
          return (
            <ClusterMarker
              count={cluster.point_count}
              color={clusterColor}
              textColor={clusterTextColor}
            />
          );
        }}
        // Layout animation for smooth transitions
        layoutAnimationConf={{
          duration: 300,
          create: {
            duration: 300,
            type: LayoutAnimation.Types.easeInEaseOut,
            property: LayoutAnimation.Properties.scaleXY,
          },
        }}>
        {/* Custom user location marker */}
        {userLocation && !showUserLocation && (
          <Marker coordinate={userLocation} anchor={{ x: 0.5, y: 0.5 }} zIndex={1000}>
            <Box
              width={20}
              height={20}
              borderRadius="circle_9999"
              backgroundColor="primary"
              borderWidth={4}
              borderColor="white"
              style={{
                shadowColor: theme.colors.primary,
                shadowOffset: { width: 0, height: 0 },
                shadowOpacity: 0.5,
                shadowRadius: 10,
                elevation: 8,
              }}
            />
            {/* Pulse animation ring */}
            <Box
              position="absolute"
              width={40}
              height={40}
              borderRadius="circle_9999"
              backgroundColor="primary"
              opacity={0.2}
              top={-10}
              left={-10}
            />
          </Marker>
        )}

        {/* Discovery markers */}
        {items.map(item => (
          <ItemMarker
            key={item.id}
            item={item}
            color={markerColors[item.type]}
            isSelected={item.id === selectedMarkerId}
            onPress={() => handleMarkerPress(item)}
          />
        ))}
      </MapView>

      {/* Enhanced map controls */}
      <Box position="absolute" right={theme.spacing.md_16} bottom={theme.spacing.xxxl_48}>
        {/* My location button */}
        {userLocation && (
          <Pressable
            onPress={() => {
              mapRef.current?.animateToRegion(
                {
                  ...userLocation,
                  latitudeDelta: 0.01,
                  longitudeDelta: 0.01,
                },
                500
              );
            }}
            style={{
              width: 48,
              height: 48,
              borderRadius: 24,
              backgroundColor: 'white',
              justifyContent: 'center',
              alignItems: 'center',
              marginBottom: theme.spacing.sm_12,
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.25,
              shadowRadius: 3.84,
              elevation: 5,
            }}
            accessibilityRole="button"
            accessibilityLabel="Center on my location">
            <GpsFix size={26} color={theme.colors.primary} weight="bold" />
          </Pressable>
        )}

        {/* Enhanced zoom controls */}
        <Box
          backgroundColor="white"
          borderRadius="lg_16"
          overflow="hidden"
          style={{
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 3 },
            shadowOpacity: 0.27,
            shadowRadius: 4.65,
            elevation: 6,
          }}>
          <Pressable
            onPress={() => {
              const currentRegion = region || initialRegion;
              if (currentRegion) {
                mapRef.current?.animateToRegion(
                  {
                    ...currentRegion,
                    latitudeDelta: currentRegion.latitudeDelta * 0.5,
                    longitudeDelta: currentRegion.longitudeDelta * 0.5,
                  },
                  300
                );
              }
            }}
            style={{
              width: 48,
              height: 48,
              justifyContent: 'center',
              alignItems: 'center',
              borderBottomWidth: 1,
              borderBottomColor: theme.colors.border,
            }}
            accessibilityRole="button"
            accessibilityLabel="Zoom in">
            <MagnifyingGlassPlus size={26} color={theme.colors.text} weight="bold" />
          </Pressable>

          <Pressable
            onPress={() => {
              const currentRegion = region || initialRegion;
              if (currentRegion) {
                mapRef.current?.animateToRegion(
                  {
                    ...currentRegion,
                    latitudeDelta: Math.min(currentRegion.latitudeDelta * 2, 10),
                    longitudeDelta: Math.min(currentRegion.longitudeDelta * 2, 10),
                  },
                  300
                );
              }
            }}
            style={{
              width: 48,
              height: 48,
              justifyContent: 'center',
              alignItems: 'center',
            }}
            accessibilityRole="button"
            accessibilityLabel="Zoom out">
            <MagnifyingGlassMinus size={26} color={theme.colors.text} weight="bold" />
          </Pressable>
        </Box>
      </Box>
    </View>
  );
};

export default RadarMap;
