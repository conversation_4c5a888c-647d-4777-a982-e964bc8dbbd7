/**
 * Discovery Card Component
 * Unified card for displaying events, venues, and tickets
 */
import React, { memo, useMemo } from 'react';

import { Image, Pressable, View } from 'react-native';

import {
  Bookmark,
  BuildingOffice,
  Calendar,
  CalendarDot,
  Check,
  NavigationArrow,
  Share,
  Star,
  Ticket,
  Users,
} from 'phosphor-react-native';
import Animated, {
  interpolate,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from 'react-native-reanimated';

import { formatDistance } from '@/src/core/libs/foursquare';
import { Theme, useTheme } from '@/src/core/theme';
import Box from '@/src/core/theme/Box';
import Text from '@/src/core/theme/Text';

import type {
  DiscoveryItem,
  EventDiscoveryItem,
  TicketDiscoveryItem,
  VenueDiscoveryItem,
} from '../../discovery/types/discovery.types';

/**
 * Discovery card props
 */
interface DiscoveryCardProps {
  /** Discovery item data */
  item: DiscoveryItem;

  /** Card layout variant */
  variant?: 'default' | 'compact' | 'featured';

  /** Show distance */
  showDistance?: boolean;

  /** User location for distance display */
  userLocation?: { latitude: number; longitude: number };

  /** Press handler */
  onPress?: (item: DiscoveryItem) => void;

  /** Long press handler */
  onLongPress?: (item: DiscoveryItem) => void;

  /** Save/bookmark handler */
  onSave?: (item: DiscoveryItem) => void;

  /** Share handler */
  onShare?: (item: DiscoveryItem) => void;

  /** Whether item is saved */
  isSaved?: boolean;

  /** Test ID */
  testID?: string;
}

/**
 * Animated card wrapper
 */
const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

/**
 * Discovery card component
 */
const DiscoveryCard = memo<DiscoveryCardProps>(
  ({
    item,
    variant = 'default',
    showDistance = true,
    userLocation,
    onPress,
    onLongPress,
    onSave,
    onShare,
    isSaved = false,
    testID,
  }) => {
    const theme = useTheme();
    const scale = useSharedValue(1);

    // Animation styles
    const animatedStyle = useAnimatedStyle(() => ({
      transform: [{ scale: scale.value }],
    }));

    // Handle press animations
    const handlePressIn = () => {
      scale.set(
        withSpring(0.98, {
          damping: 15,
          stiffness: 400,
        })
      );
    };

    const handlePressOut = () => {
      scale.set(
        withSpring(1, {
          damping: 15,
          stiffness: 400,
        })
      );
    };

    // Get type-specific props
    const typeProps: Record<string, any> = useMemo(() => {
      switch (item.type) {
        case 'event':
          return getEventProps(item as EventDiscoveryItem, theme);
        case 'venue':
          return getVenueProps(item as VenueDiscoveryItem, theme);
        case 'ticket':
          return getTicketProps(item as TicketDiscoveryItem, theme);
        default:
          return {};
      }
    }, [item, theme]);

    // Calculate distance
    const distance = useMemo(() => {
      if (!showDistance || !item.distance) return null;
      return formatDistance(item.distance, 'metric');
    }, [showDistance, item.distance]);

    // Render compact variant
    if (variant === 'compact') {
      return (
        <AnimatedPressable
          style={[animatedStyle, { marginBottom: theme.spacing.xs_8 }]}
          onPress={() => onPress?.(item)}
          onLongPress={() => onLongPress?.(item)}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          testID={testID}
          accessibilityRole="button"
          accessibilityLabel={`${item.type}: ${item.title}`}>
          <Box
            flexDirection="row"
            alignItems="center"
            padding="sm_12"
            backgroundColor="surface"
            borderRadius="md_12"
            borderWidth={1}
            borderColor="border">
            {/* Thumbnail */}
            {item.imageUrl && (
              <Image
                source={{ uri: item.imageUrl }}
                style={{
                  width: 48,
                  height: 48,
                  borderRadius: theme.borderRadii.sm_8,
                  marginRight: theme.spacing.sm_12,
                }}
              />
            )}

            {/* Content */}
            <Box flex={1}>
              <Box flexDirection="row" alignItems="center">
                <Text variant="l_12Bold_highlight" numberOfLines={1} flex={1}>
                  {item.title}
                </Text>
                {typeProps.badge && (
                  <Box
                    backgroundColor={typeProps.badgeColor}
                    paddingHorizontal="xs_8"
                    paddingVertical="xxs_4"
                    borderRadius="md_12"
                    marginLeft="xs_8">
                    <Text variant="l_12Bold_highlight" color="white" fontSize={10}>
                      {typeProps.badge}
                    </Text>
                  </Box>
                )}
              </Box>

              <Box flexDirection="row" alignItems="center" marginTop="xxs_4">
                {typeProps.icon && typeProps.icon}
                <Text variant="l_12Bold_highlight" color="textSecondary" numberOfLines={1}>
                  {typeProps.subtitle || item.subtitle}
                </Text>
                {distance && (
                  <Text variant="l_12Bold_highlight" color="textTertiary" marginLeft="xs_8">
                    • {distance}
                  </Text>
                )}
              </Box>
            </Box>

            {/* Price */}
            {item.price && !item.price.isFree && (
              <Text variant="l_12Bold_highlight" color="primary" fontWeight="600">
                {item.price.label || `$${item.price.min}`}
              </Text>
            )}
          </Box>
        </AnimatedPressable>
      );
    }

    // Render default/featured variant
    const isFeatured = variant === 'featured';

    return (
      <AnimatedPressable
        style={[
          animatedStyle,
          {
            marginBottom: theme.spacing.md_16,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 4,
            elevation: 3,
          },
        ]}
        onPress={() => onPress?.(item)}
        onLongPress={() => onLongPress?.(item)}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        testID={testID}
        accessibilityRole="button"
        accessibilityLabel={`${item.type}: ${item.title}`}>
        <Box
          backgroundColor="surface"
          borderRadius="xl_20"
          overflow="hidden"
          borderWidth={1}
          borderColor="border">
          {/* Image */}
          {item.imageUrl && (
            <Box position="relative">
              <Image
                source={{ uri: item.imageUrl }}
                style={{
                  width: '100%',
                  height: isFeatured ? 200 : 160,
                }}
              />

              {/* Overlay badges */}
              <Box
                position="absolute"
                top={theme.spacing.sm_12}
                left={theme.spacing.sm_12}
                right={theme.spacing.sm_12}
                flexDirection="row"
                justifyContent="space-between">
                {/* Type badge */}
                <Box
                  backgroundColor={typeProps.badgeColor}
                  paddingHorizontal="sm_12"
                  paddingVertical="xs_8"
                  borderRadius="md_12">
                  <Text variant="l_12Bold_highlight" color="white" fontWeight="600">
                    {typeProps.badge}
                  </Text>
                </Box>

                {/* Save button */}
                {onSave && (
                  <Pressable
                    onPress={e => {
                      e.stopPropagation();
                      onSave(item);
                    }}
                    style={{
                      backgroundColor: theme.colors.brandDark,
                      borderRadius: theme.borderRadii.circle_9999,
                      padding: theme.spacing.xs_8,
                    }}
                    accessibilityRole="button"
                    accessibilityLabel={isSaved ? 'Remove from saved' : 'Save'}>
                    <Bookmark color="white" size={20} weight={isSaved ? 'fill' : 'regular'} />
                  </Pressable>
                )}
              </Box>

              {/* Live indicator */}
              {item.isLive && (
                <Box
                  position="absolute"
                  bottom={theme.spacing.sm_12}
                  left={theme.spacing.sm_12}
                  backgroundColor="error"
                  paddingHorizontal="sm_12"
                  paddingVertical="xxs_4"
                  borderRadius="md_12"
                  flexDirection="row"
                  alignItems="center">
                  <View
                    style={{
                      width: 6,
                      height: 6,
                      borderRadius: 3,
                      backgroundColor: 'white',
                      marginRight: 6,
                    }}
                  />
                  <Text variant="l_12Bold_highlight" color="white" fontWeight="600">
                    LIVE
                  </Text>
                </Box>
              )}
            </Box>
          )}

          {/* Content */}
          <Box padding="md_16">
            {/* Header */}
            <Box flexDirection="row" alignItems="flex-start">
              <Box flex={1}>
                <Text variant={isFeatured ? 'h_32SemiBold_Page' : 'b_24Bold_CTA'} numberOfLines={2}>
                  {item.title}
                </Text>

                {/* Subtitle with icon */}
                <Box flexDirection="row" alignItems="center" marginTop="xxs_4">
                  {typeProps.icon && typeProps.icon}
                  <Text variant="h_20Medium_subsection" color="textSecondary" numberOfLines={1}>
                    {typeProps.subtitle || item.subtitle}
                  </Text>
                </Box>
              </Box>

              {/* Price */}
              {item.price && (
                <Box alignItems="flex-end" marginLeft="sm_12">
                  {item.price.isFree ? (
                    <Box
                      backgroundColor="successLight"
                      paddingHorizontal="sm_12"
                      paddingVertical="xxs_4"
                      borderRadius="sm_8">
                      <Text variant="l_12Bold_highlight" color="success" fontWeight="600">
                        FREE
                      </Text>
                    </Box>
                  ) : (
                    <>
                      <Text variant="l_12Bold_highlight" color="primary" fontWeight="700">
                        {item.price.label || `$${item.price.min}`}
                      </Text>
                      {item.price.min !== item.price.max && (
                        <Text variant="l_12Bold_highlight" color="textTertiary">
                          ${item.price.min} - ${item.price.max}
                        </Text>
                      )}
                    </>
                  )}
                </Box>
              )}
            </Box>

            {/* Type-specific content */}
            {typeProps.content}

            {/* Footer */}
            <Box
              flexDirection="row"
              alignItems="center"
              justifyContent="space-between"
              marginTop="sm_12">
              {/* Left side - distance and saved count */}
              <Box flexDirection="row" alignItems="center">
                {distance && (
                  <Box flexDirection="row" alignItems="center">
                    <NavigationArrow size={14} color={theme.colors.textTertiary} />
                    <Text variant="l_12Bold_highlight" color="textTertiary" marginLeft="xxs_4">
                      {distance}
                    </Text>
                  </Box>
                )}

                {item.savedCount !== undefined && item.savedCount > 0 && (
                  <Box
                    flexDirection="row"
                    alignItems="center"
                    marginLeft={distance ? 'md_16' : undefined}>
                    <Bookmark size={14} color={theme.colors.textTertiary} />
                    <Text variant="l_12Bold_highlight" color="textTertiary" marginLeft="xxs_4">
                      {item.savedCount}
                    </Text>
                  </Box>
                )}
              </Box>

              {/* Right side - share button */}
              {onShare && (
                <Pressable
                  onPress={e => {
                    e.stopPropagation();
                    onShare(item);
                  }}
                  accessibilityRole="button"
                  accessibilityLabel="Share">
                  <Share size={20} color={theme.colors.textSecondary} />
                </Pressable>
              )}
            </Box>
          </Box>
        </Box>
      </AnimatedPressable>
    );
  }
);

/**
 * Get event-specific props
 */
function getEventProps(item: EventDiscoveryItem, theme: Theme) {
  return {
    badge: 'EVENT',
    badgeColor: theme.colors.primary,
    icon: <Calendar size={14} color={theme.colors.textSecondary} />,
    subtitle: item.hostName,
    content: (
      <Box marginTop="xs_8">
        {/* Date and time */}
        {item.startTime && (
          <Box flexDirection="row" alignItems="center">
            <CalendarDot size={14} color={theme.colors.textSecondary} />
            <Text variant="l_12Bold_highlight" color="textSecondary" marginLeft="xxs_4">
              {formatEventTime(item.startTime)}
            </Text>
          </Box>
        )}

        {/* Attendees */}
        {item.attendeeCount > 0 && (
          <Box flexDirection="row" alignItems="center" marginTop="xxs_4">
            <Users size={14} color={theme.colors.textSecondary} />
            <Text variant="l_12Bold_highlight" color="textSecondary" marginLeft="xxs_4">
              {item.attendeeCount} going
              {item.capacity &&
                ` • ${Math.round((item.attendeeCount / item.capacity) * 100)}% full`}
            </Text>
          </Box>
        )}
      </Box>
    ),
  };
}

/**
 * Get venue-specific props
 */
function getVenueProps(item: VenueDiscoveryItem, theme: Theme) {
  return {
    badge: 'VENUE',
    badgeColor: theme.colors.secondary,
    icon: <BuildingOffice size={14} color={theme.colors.textSecondary} />,
    subtitle: item.location.neighborhood || item.location.city,
    content: (
      <Box marginTop="xs_8">
        {/* Rating and price */}
        <Box flexDirection="row" alignItems="center">
          {item.rating && (
            <Box flexDirection="row" alignItems="center">
              <Star size={14} color={theme.colors.warning} />
              <Text variant="l_12Bold_highlight" color="text" marginLeft="xxs_4">
                {(item.rating / 2).toFixed(1)}
              </Text>
            </Box>
          )}

          {item.priceLevel && (
            <Text
              variant="l_12Bold_highlight"
              color="textSecondary"
              marginLeft={item.rating ? 'sm_12' : undefined}>
              {'$'.repeat(item.priceLevel)}
            </Text>
          )}

          {/* Open status */}
          {item.isOpen !== undefined && (
            <Box marginLeft={item.rating || item.priceLevel ? 'sm_12' : undefined}>
              <Text
                variant="l_12Bold_highlight"
                color={item.isOpen ? 'success' : 'error'}
                fontWeight="600">
                {item.isOpen ? 'OPEN' : 'CLOSED'}
              </Text>
            </Box>
          )}
        </Box>

        {/* Categories */}
        {item.tags && item.tags.length > 0 && (
          <Box flexDirection="row" flexWrap="wrap" marginTop="xxs_4">
            {item.tags.slice(0, 3).map((tag, index) => (
              <Text
                key={index}
                variant="l_12Bold_highlight"
                color="textTertiary"
                marginRight="xs_8">
                {tag}
                {index < 2 && index < item.tags!.length - 1 && ' •'}
              </Text>
            ))}
          </Box>
        )}
      </Box>
    ),
  };
}

/**
 * Get ticket-specific props
 */
function getTicketProps(item: TicketDiscoveryItem, theme: Theme) {
  return {
    badge: 'TICKETS',
    badgeColor: theme.colors.tagAlertMain,
    icon: <Ticket size={14} color={theme.colors.textSecondary} />,
    subtitle: item.organizerName,
    content: (
      <Box marginTop="xs_8">
        {/* Date and tickets available */}
        <Box flexDirection="row" alignItems="center">
          {item.startTime && (
            <>
              <CalendarDot size={14} color={theme.colors.textSecondary} />
              <Text variant="l_12Bold_highlight" color="textSecondary" marginLeft="xxs_4">
                {formatEventTime(item.startTime)}
              </Text>
            </>
          )}

          {item.ticketsAvailable !== undefined && (
            <Box
              flexDirection="row"
              alignItems="center"
              marginLeft={item.startTime ? 'md_16' : undefined}>
              <Ticket size={14} color={theme.colors.textSecondary} />
              <Text variant="l_12Bold_highlight" color="textSecondary" marginLeft="xxs_4">
                {item.ticketsAvailable} left
              </Text>
            </Box>
          )}
        </Box>

        {/* Organizer verified badge */}
        {item.organizerVerified && (
          <Box flexDirection="row" alignItems="center" marginTop="xxs_4">
            <Check size={14} color={theme.colors.success} />
            <Text variant="l_12Bold_highlight" color="success" marginLeft="xxs_4">
              Verified Organizer
            </Text>
          </Box>
        )}
      </Box>
    ),
  };
}

/**
 * Format event time
 */
function formatEventTime(date: Date): string {
  const now = new Date();
  const diffMs = date.getTime() - now.getTime();
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffDays === 0) {
    return `Today at ${date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
    })}`;
  } else if (diffDays === 1) {
    return `Tomorrow at ${date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
    })}`;
  } else if (diffDays > 1 && diffDays < 7) {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      hour: 'numeric',
      minute: '2-digit',
    });
  } else {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
    });
  }
}

DiscoveryCard.displayName = 'DiscoveryCard';

export default DiscoveryCard;
