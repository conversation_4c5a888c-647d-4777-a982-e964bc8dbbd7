/**
 * Discovery Feature Types
 * Unified types for discovering events, venues, and tickets
 */
import type { FoursquarePlace } from '@/src/core/libs/foursquare/types';

/**
 * Content types that can be discovered
 */
export type DiscoveryContentType = 'event' | 'venue' | 'ticket';

/**
 * Base discovery item interface
 */
export interface DiscoveryItem {
  id: string;
  type: DiscoveryContentType;
  title: string;
  subtitle?: string;
  imageUrl?: string;
  location: LocationInfo;
  availability?: string;
  distance?: number;
  price?: PriceInfo;
  startTime?: Date;
  endTime?: Date;
  tags?: string[];
  isLive?: boolean;
  isTrending?: boolean;
  savedCount?: number;
}

/**
 * Location information
 */
export interface LocationInfo {
  latitude: number;
  longitude: number;
  address?: string;
  neighborhood?: string;
  city?: string;
  venueId?: string; // Foursquare venue ID
  venueName?: string;
}

/**
 * Price information
 */
export interface PriceInfo {
  min?: number;
  max?: number;
  currency: string;
  isFree: boolean;
  label?: string; // e.g., "Starting at $20"
}

/**
 * Filter options for discovery
 */
export interface DiscoveryFilters {
  // Location filters
  radius?: number; // meters
  location?: {
    latitude: number;
    longitude: number;
  };
  neighborhood?: string;

  // Type filters
  contentTypes?: DiscoveryContentType[];

  // Category filters
  categories?: string[];
  foursquareCategories?: string[]; // Foursquare category IDs

  // Time filters
  dateRange?: {
    start: Date;
    end: Date;
  };
  timeOfDay?: 'morning' | 'afternoon' | 'evening' | 'night';
  onlyToday?: boolean;
  onlyThisWeek?: boolean;

  // Price filters
  priceRange?: {
    min: number;
    max: number;
  };
  onlyFree?: boolean;

  // Feature filters
  hasParking?: boolean;
  wheelchairAccessible?: boolean;
  outdoorSeating?: boolean;
  liveMusic?: boolean;
  servesAlcohol?: boolean;

  // Status filters
  onlyOpen?: boolean;
  onlyVerified?: boolean;
  onlyTrending?: boolean;

  // Sort options
  sortBy?: 'distance' | 'popularity' | 'date' | 'price' | 'rating';
  sortOrder?: 'asc' | 'desc';
}

/**
 * Search suggestion
 */
export interface SearchSuggestion {
  id: string;
  type: 'query' | 'venue' | 'event' | 'category';
  text: string;
  icon?: string;
  metadata?: {
    count?: number;
    distance?: number;
    category?: string;
  };
}

/**
 * Map marker for discovery items
 */
export interface DiscoveryMarker {
  id: string;
  type: DiscoveryContentType;
  coordinate: {
    latitude: number;
    longitude: number;
  };
  title: string;
  price?: string;
  isSelected?: boolean;
  isCluster?: boolean;
  clusterCount?: number;
}

/**
 * Map region
 */
export interface MapRegion {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}

/**
 * Discovery radar configuration
 */
export interface RadarConfig {
  // Map settings
  initialRegion?: MapRegion;
  maxRadius: number; // Maximum search radius in meters
  clusteringEnabled: boolean;
  showUserLocation: boolean;

  // Content settings
  enabledTypes: DiscoveryContentType[];
  defaultFilters?: DiscoveryFilters;

  // UI settings
  showSearchBar: boolean;
  showCategoryPills: boolean;
  showFilterButton: boolean;
  markerColorScheme?: Record<DiscoveryContentType, string>;
}

/**
 * Category definition
 */
export interface DiscoveryCategory {
  id: string;
  name: string;
  icon: string;
  color: string;
  foursquareCategoryIds?: string[];
  contentTypes: DiscoveryContentType[];
  subcategories?: DiscoveryCategory[];
}

/**
 * Popular discovery categories
 */
export const DISCOVERY_CATEGORIES: DiscoveryCategory[] = [
  {
    id: 'nightlife',
    name: 'Nightlife',
    icon: '🍺',
    color: '#8B5CF6',
    contentTypes: ['event', 'venue', 'ticket'],
    foursquareCategoryIds: ['13003', '13018', '10032'], // Bar, Brewery, Music Venue
  },
  {
    id: 'food-drink',
    name: 'Food & Drink',
    icon: '🍽️',
    color: '#EF4444',
    contentTypes: ['venue'],
    foursquareCategoryIds: ['13000'], // Dining & Drinking
  },
  {
    id: 'music',
    name: 'Music',
    icon: '🎵',
    color: '#3B82F6',
    contentTypes: ['event', 'ticket', 'venue'],
    foursquareCategoryIds: ['10032', '10008'], // Music Venue, Concert Hall
  },
  {
    id: 'sports',
    name: 'Sports',
    icon: '⚽',
    color: '#10B981',
    contentTypes: ['event', 'ticket', 'venue'],
    foursquareCategoryIds: ['18000'], // Sports & Recreation
  },
  {
    id: 'arts',
    name: 'Arts & Culture',
    icon: '🎨',
    color: '#F59E0B',
    contentTypes: ['event', 'ticket', 'venue'],
    foursquareCategoryIds: ['10000'], // Arts & Entertainment
  },
  {
    id: 'outdoor',
    name: 'Outdoor',
    icon: '🏞️',
    color: '#6366F1',
    contentTypes: ['event', 'venue'],
    foursquareCategoryIds: ['16000'], // Landmarks & Outdoors
  },
];

/**
 * Discovery API response
 */
export interface DiscoveryResponse {
  items: DiscoveryItem[];
  totalCount: number;
  hasMore: boolean;
  nextCursor?: string;
  region?: MapRegion;
}

/**
 * User's discovery preferences
 */
export interface DiscoveryPreferences {
  favoriteCategories: string[];
  savedSearches: SavedSearch[];
  preferredRadius: number;
  showOnlyVerified: boolean;
  enableNotifications: boolean;
  notificationCategories: string[];
}

/**
 * Saved search
 */
export interface SavedSearch {
  id: string;
  name: string;
  filters: DiscoveryFilters;
  createdAt: Date;
  notificationsEnabled: boolean;
}

/**
 * Discovery analytics event
 */
export interface DiscoveryAnalyticsEvent {
  action: 'view' | 'tap_marker' | 'tap_card' | 'filter' | 'search' | 'save' | 'share';
  contentType: DiscoveryContentType;
  contentId: string;
  source: 'radar' | 'search' | 'category' | 'recommendation';
  metadata?: Record<string, any>;
}

/**
 * Extended event discovery item
 */
export interface EventDiscoveryItem extends DiscoveryItem {
  type: 'event';
  hostName: string;
  hostAvatar?: string;
  attendeeCount: number;
  capacity?: number;
  rsvpStatus?: 'going' | 'interested' | 'not_going';
  friendsGoing?: string[];
}

/**
 * Extended venue discovery item
 */
export interface VenueDiscoveryItem extends DiscoveryItem {
  type: 'venue';
  foursquarePlace?: FoursquarePlace;
  rating?: number;
  priceLevel?: number;
  isOpen?: boolean;
  nextOpenTime?: Date;
  popularTimes?: PopularTime[];
  checkInCount?: number;
}

/**
 * Extended ticket discovery item
 */
export interface TicketDiscoveryItem extends DiscoveryItem {
  type: 'ticket';
  organizerName: string;
  organizerVerified?: boolean;
  ticketsAvailable?: number;
  ticketTypes?: TicketType[];
  earlyBirdEnds?: Date;
}

/**
 * Popular times for venues
 */
export interface PopularTime {
  dayOfWeek: number; // 0-6
  hour: number; // 0-23
  popularity: number; // 0-100
}

/**
 * Ticket type
 */
export interface TicketType {
  id: string;
  name: string;
  price: number;
  available: number;
  description?: string;
}
