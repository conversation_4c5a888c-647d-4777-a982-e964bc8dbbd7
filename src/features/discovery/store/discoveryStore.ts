/**
 * Discovery Store
 * Global state management for discovery features
 */
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

import { cacheStorage, createZustandMMKVStorage } from '@/src/core';

import {
  DISCOVERY_CATEGORIES,
  DiscoveryContentType,
  DiscoveryFilters,
  MapRegion,
  SavedSearch,
} from '../types/discovery.types';

/**
 * Discovery store state
 */
interface DiscoveryState {
  // Filters
  filters: DiscoveryFilters;
  activeCategories: string[];
  enabledContentTypes: DiscoveryContentType[];

  // Search
  searchQuery: string;
  searchHistory: string[];
  savedSearches: SavedSearch[];

  // Map
  mapRegion: MapRegion | null;
  selectedMarkerId: string | null;
  clusteringEnabled: boolean;

  // UI State
  isFilterSheetOpen: boolean;
  viewMode: 'map' | 'list';
  sortBy: DiscoveryFilters['sortBy'];

  // User preferences
  preferredRadius: number;
  showOnlyVerified: boolean;
  enableNotifications: boolean;
  notificationCategories: string[];

  // Recent items
  recentlyViewed: {
    id: string;
    type: DiscoveryContentType;
    timestamp: number;
  }[];
}

/**
 * Discovery store actions
 */
interface DiscoveryActions {
  // Filter actions
  updateFilters: (filters: Partial<DiscoveryFilters>) => void;
  resetFilters: () => void;
  toggleCategory: (categoryId: string) => void;
  setActiveCategories: (categoryIds: string[]) => void;
  toggleContentType: (type: DiscoveryContentType) => void;

  // Search actions
  setSearchQuery: (query: string) => void;
  addToSearchHistory: (query: string) => void;
  clearSearchHistory: () => void;
  saveSearch: (name: string, filters: DiscoveryFilters) => void;
  deleteSavedSearch: (id: string) => void;
  applySavedSearch: (id: string) => void;

  // Map actions
  setMapRegion: (region: MapRegion) => void;
  selectMarker: (markerId: string | null) => void;
  toggleClustering: () => void;

  // UI actions
  toggleFilterSheet: () => void;
  setViewMode: (mode: 'map' | 'list') => void;
  setSortBy: (sortBy: DiscoveryFilters['sortBy']) => void;

  // Preference actions
  setPreferredRadius: (radius: number) => void;
  toggleShowOnlyVerified: () => void;
  toggleNotifications: () => void;
  toggleNotificationCategory: (categoryId: string) => void;

  // Recent items
  addRecentlyViewed: (id: string, type: DiscoveryContentType) => void;
  clearRecentlyViewed: () => void;
}

/**
 * Discovery store
 */
export const useDiscoveryStore = create<DiscoveryState & DiscoveryActions>()(
  persist(
    immer(set => ({
      // Initial state
      filters: {
        radius: 5000,
        contentTypes: ['event', 'venue', 'ticket'],
        sortBy: 'distance',
        onlyOpen: true,
      },
      activeCategories: [],
      enabledContentTypes: ['event', 'venue', 'ticket'],

      searchQuery: '',
      searchHistory: [],
      savedSearches: [],

      mapRegion: null,
      selectedMarkerId: null,
      clusteringEnabled: true,

      isFilterSheetOpen: false,
      viewMode: 'map',
      sortBy: 'distance',

      preferredRadius: 5000,
      showOnlyVerified: false,
      enableNotifications: false,
      notificationCategories: [],

      recentlyViewed: [],

      // Filter actions
      updateFilters: newFilters =>
        set(state => {
          Object.assign(state.filters, newFilters);
        }),

      resetFilters: () =>
        set(state => {
          state.filters = {
            radius: state.preferredRadius,
            contentTypes: state.enabledContentTypes,
            sortBy: 'distance',
            onlyOpen: true,
          };
          state.activeCategories = [];
        }),

      toggleCategory: categoryId =>
        set(state => {
          const index = state.activeCategories.indexOf(categoryId);
          if (index >= 0) {
            state.activeCategories.splice(index, 1);
          } else {
            state.activeCategories.push(categoryId);
          }

          // Update filters with Foursquare categories
          const foursquareCategories = state.activeCategories
            .map(id => DISCOVERY_CATEGORIES.find(cat => cat.id === id))
            .filter(Boolean)
            .flatMap(cat => cat!.foursquareCategoryIds || []);

          state.filters.foursquareCategories = foursquareCategories;
        }),

      setActiveCategories: categoryIds =>
        set(state => {
          state.activeCategories = categoryIds;

          // Update filters with Foursquare categories
          const foursquareCategories = categoryIds
            .map(id => DISCOVERY_CATEGORIES.find(cat => cat.id === id))
            .filter(Boolean)
            .flatMap(cat => cat!.foursquareCategoryIds || []);

          state.filters.foursquareCategories = foursquareCategories;
        }),

      toggleContentType: type =>
        set(state => {
          const types = state.enabledContentTypes;
          const index = types.indexOf(type);

          if (index >= 0) {
            // Don't allow disabling all types
            if (types.length > 1) {
              types.splice(index, 1);
            }
          } else {
            types.push(type);
          }

          state.filters.contentTypes = [...types];
        }),

      // Search actions
      setSearchQuery: query =>
        set(state => {
          state.searchQuery = query;
        }),

      addToSearchHistory: query =>
        set(state => {
          // Remove duplicates and limit to 10 items
          state.searchHistory = [query, ...state.searchHistory.filter(q => q !== query)].slice(
            0,
            10
          );
        }),

      clearSearchHistory: () =>
        set(state => {
          state.searchHistory = [];
        }),

      saveSearch: (name, filters) =>
        set(state => {
          state.savedSearches.push({
            id: Date.now().toString(),
            name,
            filters,
            createdAt: new Date(),
            notificationsEnabled: false,
          });
        }),

      deleteSavedSearch: id =>
        set(state => {
          state.savedSearches = state.savedSearches.filter(s => s.id !== id);
        }),

      applySavedSearch: id =>
        set(state => {
          const savedSearch = state.savedSearches.find(s => s.id === id);
          if (savedSearch) {
            state.filters = { ...savedSearch.filters };
          }
        }),

      // Map actions
      setMapRegion: region =>
        set(state => {
          state.mapRegion = region;
        }),

      selectMarker: markerId =>
        set(state => {
          state.selectedMarkerId = markerId;
        }),

      toggleClustering: () =>
        set(state => {
          state.clusteringEnabled = !state.clusteringEnabled;
        }),

      // UI actions
      toggleFilterSheet: () =>
        set(state => {
          state.isFilterSheetOpen = !state.isFilterSheetOpen;
        }),

      setViewMode: mode =>
        set(state => {
          state.viewMode = mode;
        }),

      setSortBy: sortBy =>
        set(state => {
          state.sortBy = sortBy;
          state.filters.sortBy = sortBy;
        }),

      // Preference actions
      setPreferredRadius: radius =>
        set(state => {
          state.preferredRadius = radius;
          state.filters.radius = radius;
        }),

      toggleShowOnlyVerified: () =>
        set(state => {
          state.showOnlyVerified = !state.showOnlyVerified;
          state.filters.onlyVerified = state.showOnlyVerified;
        }),

      toggleNotifications: () =>
        set(state => {
          state.enableNotifications = !state.enableNotifications;
        }),

      toggleNotificationCategory: categoryId =>
        set(state => {
          const index = state.notificationCategories.indexOf(categoryId);
          if (index >= 0) {
            state.notificationCategories.splice(index, 1);
          } else {
            state.notificationCategories.push(categoryId);
          }
        }),

      // Recent items
      addRecentlyViewed: (id, type) =>
        set(state => {
          // Remove duplicate and add to front
          state.recentlyViewed = [
            { id, type, timestamp: Date.now() },
            ...state.recentlyViewed.filter(item => item.id !== id),
          ].slice(0, 20); // Keep last 20 items
        }),

      clearRecentlyViewed: () =>
        set(state => {
          state.recentlyViewed = [];
        }),
    })),
    {
      name: 'discovery-store',
      storage: createJSONStorage(() => createZustandMMKVStorage(cacheStorage)),
      partialize: state => ({
        // Only persist user preferences and saved data
        preferredRadius: state.preferredRadius,
        showOnlyVerified: state.showOnlyVerified,
        enableNotifications: state.enableNotifications,
        notificationCategories: state.notificationCategories,
        savedSearches: state.savedSearches,
        searchHistory: state.searchHistory,
        recentlyViewed: state.recentlyViewed,
        clusteringEnabled: state.clusteringEnabled,
      }),
    }
  )
);

/**
 * Discovery store selectors
 */
export const discoverySelectors = {
  // Get active filters count
  getActiveFiltersCount: (state: DiscoveryState): number => {
    let count = 0;
    const filters = state.filters;

    if (filters.categories?.length) count++;
    if (filters.priceRange) count++;
    if (filters.dateRange) count++;
    if (filters.onlyFree) count++;
    if (filters.onlyOpen) count++;
    if (filters.onlyVerified) count++;
    if (filters.onlyTrending) count++;
    if (state.activeCategories.length > 0) count++;

    return count;
  },

  // Check if filters are active
  hasActiveFilters: (state: DiscoveryState): boolean => {
    return discoverySelectors.getActiveFiltersCount(state) > 0;
  },

  // Get formatted search history
  getFormattedSearchHistory: (state: DiscoveryState) => {
    return state.searchHistory.map(query => ({
      id: query,
      type: 'query' as const,
      text: query,
      icon: 'search',
    }));
  },
};
