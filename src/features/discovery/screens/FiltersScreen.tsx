/**
 * Filters Screen
 * Advanced filtering options for discovery
 */
import React, { useState } from 'react';

import { ScrollView } from 'react-native';

import { Feather, MaterialIcons } from '@expo/vector-icons';
import Slider from '@react-native-community/slider';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '@shopify/restyle';

import { Box, Pressable, Text, Theme } from '@/src/core/theme';
import { PressableAnimated } from '@/src/core/theme/Pressable';
import { useResponsive } from '@/src/core/theme/useResponsive';
import { Button, Container, Divider, NavigationTopBar, Switch } from '@/src/shared/components';

import CategoryPills from '../components/CategoryPills';
import { useFilters } from '../hooks/useFilters';
import type { DiscoveryFilters } from '../types/discovery.types';

interface FilterSectionProps {
  title: string;
  description?: string;
  children: React.ReactNode;
}

const FilterSection: React.FC<FilterSectionProps> = ({ title, description, children }) => (
  <Box marginBottom="lg_24">
    <Text variant="h_24SemiBold_section" color="mainText" marginBottom="xs_8">
      {title}
    </Text>
    {description && (
      <Text variant="b_14Regular_input" color="textSecondary" marginBottom="md_16">
        {description}
      </Text>
    )}
    {children}
  </Box>
);

const FiltersScreen: React.FC = () => {
  const theme = useTheme<Theme>();
  const { select } = useResponsive();
  const navigation = useNavigation();
  const { filters, updateFilters, resetFilters, activeFilterCount } = useFilters();

  // Local state for filters
  const [localFilters, setLocalFilters] = useState<DiscoveryFilters>(filters);

  // Update local filter
  const updateLocalFilter = <K extends keyof DiscoveryFilters>(
    key: K,
    value: DiscoveryFilters[K]
  ) => {
    setLocalFilters(prev => ({ ...prev, [key]: value }));
  };

  // Handle distance change
  const handleDistanceChange = (value: number) => {
    updateLocalFilter('radius', Math.round(value * 1000)); // Convert km to meters
  };

  // Handle price range change
  const handleMinPriceChange = (value: number) => {
    updateLocalFilter('priceRange', {
      min: value,
      max: localFilters.priceRange?.max || 100,
    });
  };

  const handleMaxPriceChange = (value: number) => {
    updateLocalFilter('priceRange', {
      min: localFilters.priceRange?.min || 0,
      max: value,
    });
  };

  // Handle category selection
  const handleCategorySelect = (categoryId: string) => {
    const currentCategories = localFilters.categories || [];
    const newCategories = currentCategories.includes(categoryId)
      ? currentCategories.filter(id => id !== categoryId)
      : [...currentCategories, categoryId];
    updateLocalFilter('categories', newCategories);
  };

  // Handle reset
  const handleReset = () => {
    const defaultFilters: DiscoveryFilters = {
      radius: 5000,
      sortBy: 'distance',
      sortOrder: 'asc',
    };
    setLocalFilters(defaultFilters);
  };

  // Handle apply
  const handleApply = () => {
    updateFilters(localFilters);
    navigation.goBack();
  };

  return (
    <Box backgroundColor="background">
      <NavigationTopBar
        title="Filters"
        trailingActions={
          <Pressable onPress={handleReset}>
            <Text variant="b_14Medium_button" color="brandMain">
              Reset
            </Text>
          </Pressable>
        }
      />

      <ScrollView
        contentContainerStyle={{
          paddingHorizontal: theme.spacing.md_16,
          paddingBottom: theme.spacing.xl_32 + 80, // Account for button
        }}
        showsVerticalScrollIndicator={false}>
        {/* Distance */}
        <FilterSection title="Distance" description="How far are you willing to travel?">
          <Box>
            <Box flexDirection="row" justifyContent="space-between" marginBottom="sm_12">
              <Text variant="b_14Medium_button" color="mainText">
                {(localFilters.radius || 5000) / 1000} km
              </Text>
              <Text variant="b_14Regular_input" color="textSecondary">
                Max 50 km
              </Text>
            </Box>
            <Slider
              value={(localFilters.radius || 5000) / 1000}
              onValueChange={handleDistanceChange}
              minimumValue={1}
              maximumValue={50}
              step={1}
              minimumTrackTintColor={theme.colors.brandMain}
              maximumTrackTintColor={theme.colors.border}
              thumbTintColor={theme.colors.brandMain}
            />
          </Box>
        </FilterSection>

        {/* Categories */}
        <FilterSection title="Categories" description="What are you looking for?">
          <CategoryPills
            selected={localFilters.categories || []}
            onSelect={handleCategorySelect}
            scrollable={false}
          />
        </FilterSection>

        {/* Time filters */}
        <FilterSection title="When">
          <Box gap="sm_12">
            <PressableAnimated
              onPress={() => updateLocalFilter('onlyToday', !localFilters.onlyToday)}>
              <Box
                flexDirection="row"
                alignItems="center"
                justifyContent="space-between"
                paddingVertical="xs_8">
                <Box flexDirection="row" alignItems="center">
                  <MaterialIcons
                    name="today"
                    size={select({ phone: 20, tablet: 24 })}
                    color={theme.colors.textSecondary}
                  />
                  <Text variant="b_14Medium_button" color="mainText" marginLeft="sm_12">
                    Today Only
                  </Text>
                </Box>
                <Switch
                  isChecked={localFilters.onlyToday || false}
                  onToggle={value => updateLocalFilter('onlyToday', value)}
                />
              </Box>
            </PressableAnimated>

            <PressableAnimated
              onPress={() => updateLocalFilter('onlyThisWeek', !localFilters.onlyThisWeek)}>
              <Box
                flexDirection="row"
                alignItems="center"
                justifyContent="space-between"
                paddingVertical="xs_8">
                <Box flexDirection="row" alignItems="center">
                  <MaterialIcons
                    name="date-range"
                    size={select({ phone: 20, tablet: 24 })}
                    color={theme.colors.textSecondary}
                  />
                  <Text variant="b_14Medium_button" color="mainText" marginLeft="sm_12">
                    This Week
                  </Text>
                </Box>
                <Switch
                  isChecked={localFilters.onlyThisWeek || false}
                  onToggle={value => updateLocalFilter('onlyThisWeek', value)}
                />
              </Box>
            </PressableAnimated>
          </Box>
        </FilterSection>

        {/* Price range */}
        <FilterSection title="Price Range" description="Set your budget">
          <Box>
            <Box flexDirection="row" justifyContent="space-between" marginBottom="sm_12">
              <Text variant="b_14Medium_button" color="mainText">
                ${localFilters.priceRange?.min || 0} - ${localFilters.priceRange?.max || 100}
              </Text>
              <PressableAnimated
                onPress={() => updateLocalFilter('onlyFree', !localFilters.onlyFree)}>
                <Box flexDirection="row" alignItems="center">
                  <Text
                    variant="b_14Medium_button"
                    color={localFilters.onlyFree ? 'brandMain' : 'textSecondary'}>
                    Free Only
                  </Text>
                  <Switch
                    isChecked={localFilters.onlyFree || false}
                    onToggle={value => updateLocalFilter('onlyFree', value)}
                    style={{ marginLeft: theme.spacing.xs_8 }}
                  />
                </Box>
              </PressableAnimated>
            </Box>

            {!localFilters.onlyFree && (
              <>
                <Text variant="l_12Regular_helperText" color="textSecondary" marginBottom="xs_8">
                  Minimum
                </Text>
                <Slider
                  value={localFilters.priceRange?.min || 0}
                  onValueChange={handleMinPriceChange}
                  minimumValue={0}
                  maximumValue={localFilters.priceRange?.max || 100}
                  step={5}
                  minimumTrackTintColor={theme.colors.brandMain}
                  maximumTrackTintColor={theme.colors.border}
                  thumbTintColor={theme.colors.brandMain}
                />

                <Text
                  variant="l_12Regular_helperText"
                  color="textSecondary"
                  marginTop="md_16"
                  marginBottom="xs_8">
                  Maximum
                </Text>
                <Slider
                  value={localFilters.priceRange?.max || 100}
                  onValueChange={handleMaxPriceChange}
                  minimumValue={localFilters.priceRange?.min || 0}
                  maximumValue={200}
                  step={5}
                  minimumTrackTintColor={theme.colors.brandMain}
                  maximumTrackTintColor={theme.colors.border}
                  thumbTintColor={theme.colors.brandMain}
                />
              </>
            )}
          </Box>
        </FilterSection>

        {/* Features */}
        <FilterSection title="Features">
          <Box gap="sm_12">
            <PressableAnimated
              onPress={() => updateLocalFilter('servesAlcohol', !localFilters.servesAlcohol)}>
              <Box
                flexDirection="row"
                alignItems="center"
                justifyContent="space-between"
                paddingVertical="xs_8">
                <Text variant="b_14Medium_button" color="mainText">
                  🍺 Serves Alcohol
                </Text>
                <Switch
                  isChecked={localFilters.servesAlcohol || false}
                  onToggle={value => updateLocalFilter('servesAlcohol', value)}
                />
              </Box>
            </PressableAnimated>

            <PressableAnimated
              onPress={() => updateLocalFilter('liveMusic', !localFilters.liveMusic)}>
              <Box
                flexDirection="row"
                alignItems="center"
                justifyContent="space-between"
                paddingVertical="xs_8">
                <Text variant="b_14Medium_button" color="mainText">
                  🎵 Live Music
                </Text>
                <Switch
                  isChecked={localFilters.liveMusic || false}
                  onToggle={value => updateLocalFilter('liveMusic', value)}
                />
              </Box>
            </PressableAnimated>

            <PressableAnimated
              onPress={() => updateLocalFilter('outdoorSeating', !localFilters.outdoorSeating)}>
              <Box
                flexDirection="row"
                alignItems="center"
                justifyContent="space-between"
                paddingVertical="xs_8">
                <Text variant="b_14Medium_button" color="mainText">
                  🌳 Outdoor Seating
                </Text>
                <Switch
                  isChecked={localFilters.outdoorSeating || false}
                  onToggle={value => updateLocalFilter('outdoorSeating', value)}
                />
              </Box>
            </PressableAnimated>
          </Box>
        </FilterSection>

        {/* Status */}
        <FilterSection title="Status">
          <Box gap="sm_12">
            <PressableAnimated
              onPress={() => updateLocalFilter('onlyOpen', !localFilters.onlyOpen)}>
              <Box
                flexDirection="row"
                alignItems="center"
                justifyContent="space-between"
                paddingVertical="xs_8">
                <Text variant="b_14Medium_button" color="mainText">
                  🕐 Open Now
                </Text>
                <Switch
                  isChecked={localFilters.onlyOpen || false}
                  onToggle={value => updateLocalFilter('onlyOpen', value)}
                />
              </Box>
            </PressableAnimated>

            <PressableAnimated
              onPress={() => updateLocalFilter('onlyVerified', !localFilters.onlyVerified)}>
              <Box
                flexDirection="row"
                alignItems="center"
                justifyContent="space-between"
                paddingVertical="xs_8">
                <Text variant="b_14Medium_button" color="mainText">
                  ✓ Verified Only
                </Text>
                <Switch
                  isChecked={localFilters.onlyVerified || false}
                  onToggle={value => updateLocalFilter('onlyVerified', value)}
                />
              </Box>
            </PressableAnimated>

            <PressableAnimated
              onPress={() => updateLocalFilter('onlyTrending', !localFilters.onlyTrending)}>
              <Box
                flexDirection="row"
                alignItems="center"
                justifyContent="space-between"
                paddingVertical="xs_8">
                <Text variant="b_14Medium_button" color="mainText">
                  🔥 Trending Now
                </Text>
                <Switch
                  isChecked={localFilters.onlyTrending || false}
                  onToggle={value => updateLocalFilter('onlyTrending', value)}
                />
              </Box>
            </PressableAnimated>
          </Box>
        </FilterSection>

        {/* Accessibility */}
        <FilterSection title="Accessibility">
          <Box gap="sm_12">
            <PressableAnimated
              onPress={() => updateLocalFilter('hasParking', !localFilters.hasParking)}>
              <Box
                flexDirection="row"
                alignItems="center"
                justifyContent="space-between"
                paddingVertical="xs_8">
                <Text variant="b_14Medium_button" color="mainText">
                  🅿️ Parking Available
                </Text>
                <Switch
                  isChecked={localFilters.hasParking || false}
                  onToggle={value => updateLocalFilter('hasParking', value)}
                />
              </Box>
            </PressableAnimated>

            <PressableAnimated
              onPress={() =>
                updateLocalFilter('wheelchairAccessible', !localFilters.wheelchairAccessible)
              }>
              <Box
                flexDirection="row"
                alignItems="center"
                justifyContent="space-between"
                paddingVertical="xs_8">
                <Text variant="b_14Medium_button" color="mainText">
                  ♿ Wheelchair Accessible
                </Text>
                <Switch
                  isChecked={localFilters.wheelchairAccessible || false}
                  onToggle={value => updateLocalFilter('wheelchairAccessible', value)}
                />
              </Box>
            </PressableAnimated>
          </Box>
        </FilterSection>
      </ScrollView>

      {/* Fixed footer */}
      <Box
        position="absolute"
        bottom={0}
        left={0}
        right={0}
        paddingHorizontal="md_16"
        paddingTop="md_16"
        paddingBottom="lg_24"
        backgroundColor="background"
        style={{
          shadowColor: '#000',
          shadowOffset: { width: 0, height: -2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
          elevation: 5,
        }}>
        <Button
          onPress={handleApply}
          title={`Apply ${activeFilterCount > 0 ? `(${activeFilterCount})` : ''}`}
          variant="primary"
        />
      </Box>
    </Box>
  );
};

export default FiltersScreen;
