/**
 * Search Screen
 * Full search experience with suggestions and results
 */
import React, { useCallback, useState } from 'react';

import { FlatList, Keyboard, KeyboardAvoidingView, Platform, Pressable } from 'react-native';

import { Feather } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';
import { useTheme } from '@shopify/restyle';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';

import { Box, Text, Theme } from '@/src/core/theme';
import { useResponsive } from '@/src/core/theme/useResponsive';

import CategoryPills from '../components/CategoryPills';
import DiscoveryCard from '../components/DiscoveryCard';
import SearchBar from '../components/SearchBar';
import { useDiscoverySearch, usePopularSearches, useRecentSearches } from '../hooks/useSearch';
import { useDiscoveryStore } from '../store/discoveryStore';
import type { DiscoveryItem, SearchSuggestion } from '../types/discovery.types';

const SearchScreen: React.FC = () => {
  const theme = useTheme<Theme>();
  const { select } = useResponsive();
  const insets = useSafeAreaInsets();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);

  const { addToSearchHistory } = useDiscoveryStore();
  const { searches: recentSearches, addSearch, clearSearches } = useRecentSearches();
  const { data: popularSearches } = usePopularSearches();
  const { data: searchResults, isLoading } = useDiscoverySearch(searchQuery, {
    categories: selectedCategories,
  });

  // Focus search bar on mount
  useFocusEffect(
    useCallback(() => {
      // Small delay to ensure keyboard animation is smooth
      const timer = setTimeout(() => {
        // SearchBar will auto-focus
      }, 100);
      return () => clearTimeout(timer);
    }, [])
  );

  // Handle search submission
  const handleSearch = (query: string) => {
    if (query.trim()) {
      setSearchQuery(query);
      addSearch(query);
      addToSearchHistory(query);
      Keyboard.dismiss();
    }
  };

  // Handle suggestion selection
  const handleSuggestionPress = (suggestion: SearchSuggestion) => {
    handleSearch(suggestion.text);
  };

  // Handle category selection
  const handleCategorySelect = (categoryId: string) => {
    setSelectedCategories(prev =>
      prev.includes(categoryId) ? prev.filter(id => id !== categoryId) : [...prev, categoryId]
    );
  };

  // Handle recent search press
  const handleRecentSearchPress = (search: string) => {
    setSearchQuery(search);
    handleSearch(search);
  };

  // Clear recent searches
  const handleClearRecent = () => {
    clearSearches();
  };

  // Render recent search item
  const renderRecentSearch = (search: string) => (
    <Pressable
      key={search}
      onPress={() => handleRecentSearchPress(search)}
      style={({ pressed }) => ({
        opacity: pressed ? 0.7 : 1,
      })}>
      <Box
        flexDirection="row"
        alignItems="center"
        paddingVertical="sm_12"
        paddingHorizontal="md_16">
        <Feather
          name="clock"
          size={select({ phone: 20, tablet: 24 })}
          color={theme.colors.textSecondary}
        />
        <Text
          variant="b_14Medium_button"
          color="mainText"
          marginLeft="sm_12"
          flex={1}
          numberOfLines={1}>
          {search}
        </Text>
        <Feather
          name="arrow-up-left"
          size={select({ phone: 16, tablet: 20 })}
          color={theme.colors.textSecondary}
        />
      </Box>
    </Pressable>
  );

  // Render popular search item
  const renderPopularSearch = (search: string, index: number) => (
    <Pressable
      key={search}
      onPress={() => handleRecentSearchPress(search)}
      style={({ pressed }) => ({
        opacity: pressed ? 0.7 : 1,
      })}>
      <Box
        flexDirection="row"
        alignItems="center"
        paddingVertical="sm_12"
        paddingHorizontal="md_16">
        <Box
          width={select({ phone: 24, tablet: 28 })}
          height={select({ phone: 24, tablet: 28 })}
          borderRadius="circle_9999"
          backgroundColor="elevatedBackground"
          alignItems="center"
          justifyContent="center">
          <Text variant="l_12Medium_message" color="textSecondary">
            {index + 1}
          </Text>
        </Box>
        <Text
          variant="b_14Medium_button"
          color="mainText"
          marginLeft="sm_12"
          flex={1}
          numberOfLines={1}>
          {search}
        </Text>
        <Feather
          name="trending-up"
          size={select({ phone: 16, tablet: 20 })}
          color={theme.colors.brandMain}
        />
      </Box>
    </Pressable>
  );

  // Render discovery item
  const renderDiscoveryItem = ({ item }: { item: DiscoveryItem }) => (
    <Box paddingHorizontal="md_16" marginBottom="sm_12">
      <DiscoveryCard item={item} variant="default" onPress={() => {}} />
    </Box>
  );

  // Show search results or suggestions
  const showResults = searchQuery.length > 0;
  const showSuggestions = !showResults && (recentSearches.length > 0 || popularSearches);

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={insets.top}>
        <Box backgroundColor="background">
          {/* Header with search bar */}
          <Box
            paddingTop="md_16"
            paddingHorizontal="md_16"
            paddingBottom="xs_8"
            backgroundColor="background">
            <SearchBar
              value={searchQuery}
              onChangeText={setSearchQuery}
              onSuggestionPress={handleSuggestionPress}
              placeholder="Search venues, events, tickets..."
              showFilters={false}
            />
          </Box>

          {/* Category pills */}
          <CategoryPills selected={selectedCategories} onSelect={handleCategorySelect} scrollable />

          {/* Content */}
          {showResults ? (
            <FlatList
              data={searchResults?.items || []}
              renderItem={renderDiscoveryItem}
              keyExtractor={item => item.id}
              contentContainerStyle={{
                paddingTop: theme.spacing.md_16,
                paddingBottom: theme.spacing.xl_32,
              }}
              showsVerticalScrollIndicator={false}
              ListEmptyComponent={
                !isLoading ? (
                  <Box alignItems="center" paddingTop="xl_32">
                    <Text variant="b_14Regular_input" color="textSecondary" textAlign="center">
                      No results found for {searchQuery}
                    </Text>
                  </Box>
                ) : null
              }
            />
          ) : showSuggestions ? (
            <>
              {/* Recent searches */}
              {recentSearches.length > 0 && (
                <Box>
                  <Box
                    flexDirection="row"
                    alignItems="center"
                    justifyContent="space-between"
                    paddingHorizontal="md_16"
                    paddingTop="lg_24"
                    paddingBottom="sm_12">
                    <Text variant="h_18Bold_formTitle" color="mainText">
                      Recent Searches
                    </Text>
                    <Pressable onPress={handleClearRecent}>
                      <Text variant="b_14Medium_button" color="brandMain">
                        Clear
                      </Text>
                    </Pressable>
                  </Box>
                  {recentSearches.map(renderRecentSearch)}
                </Box>
              )}

              {/* Popular searches */}
              {popularSearches && popularSearches.length > 0 && (
                <Box>
                  <Box paddingHorizontal="md_16" paddingTop="lg_24" paddingBottom="sm_12">
                    <Text variant="h_18Bold_formTitle" color="mainText">
                      Popular Searches
                    </Text>
                  </Box>
                  {popularSearches.map((search, index) => renderPopularSearch(search, index))}
                </Box>
              )}
            </>
          ) : (
            <Box flex={1} alignItems="center" justifyContent="center" paddingHorizontal="xl_32">
              <Feather
                name="search"
                size={select({ phone: 48, tablet: 64 })}
                color={theme.colors.textSecondary}
                style={{ marginBottom: theme.spacing.md_16 }}
              />
              <Text
                variant="h_24SemiBold_section"
                color="mainText"
                textAlign="center"
                marginBottom="xs_8">
                Find Your Next Party
              </Text>
              <Text variant="b_14Regular_input" color="textSecondary" textAlign="center">
                Search for venues, events, and tickets in your area
              </Text>
            </Box>
          )}
        </Box>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default SearchScreen;
