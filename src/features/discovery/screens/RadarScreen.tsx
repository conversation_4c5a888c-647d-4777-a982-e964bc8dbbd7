/**
 * Radar Screen
 * Main discovery screen showing nearby content on an interactive map
 */
import React, { useCallback, useMemo } from 'react';

import { ActivityIndicator, Pressable, RefreshControl, ScrollView } from 'react-native';

import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { useTheme } from '@/src/core/theme';
import Box from '@/src/core/theme/Box';
import Text from '@/src/core/theme/Text';

import DiscoveryCard from '../components/DiscoveryCard';
import RadarMap from '../components/RadarMap';
import { useNearbySearch } from '../hooks/useNearbySearch';
import { useDiscoveryStore } from '../store/discoveryStore';
import type { DiscoveryItem } from '../types/discovery.types';

/**
 * Radar screen component
 */
const RadarScreen: React.FC = () => {
  const theme = useTheme();
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();

  // Discovery store
  const {
    viewMode,
    setViewMode,
    selectedMarkerId,
    selectMarker,
    mapRegion,
    setMapRegion,
    toggleFilterSheet,
    activeCategories,
  } = useDiscoveryStore();

  // Nearby search
  const {
    items,
    userLocation,
    isLoading,
    isRefreshing,
    refresh,
    updateFilters,
    searchQuery,
    updateSearch,
  } = useNearbySearch({
    autoLoad: true,
    radius: 5000,
  });

  // Get selected item
  const selectedItem = useMemo(
    () => items.find(item => item.id === selectedMarkerId),
    [items, selectedMarkerId]
  );

  // Handle item press
  const handleItemPress = useCallback(
    (item: DiscoveryItem) => {
      selectMarker(item.id);

      // Navigate to details based on type
      switch (item.type) {
        case 'event':
          navigation.navigate('EventDetails', { eventId: item.id });
          break;
        case 'venue':
          navigation.navigate('VenueDetails', { venueId: item.id });
          break;
        case 'ticket':
          navigation.navigate('TicketDetails', { ticketId: item.id });
          break;
      }
    },
    [navigation, selectMarker]
  );

  // Handle save
  const handleSave = useCallback((item: DiscoveryItem) => {
    // TODO: Implement save functionality
    console.log('Save item:', item.id);
  }, []);

  // Handle share
  const handleShare = useCallback((item: DiscoveryItem) => {
    // TODO: Implement share functionality
    console.log('Share item:', item.id);
  }, []);

  // Calculate initial region
  const initialRegion = useMemo(() => {
    if (mapRegion) return mapRegion;
    if (!userLocation) return undefined;

    return {
      latitude: userLocation.coords.latitude,
      longitude: userLocation.coords.longitude,
      latitudeDelta: 0.05,
      longitudeDelta: 0.05,
    };
  }, [mapRegion, userLocation]);

  return (
    <Box flex={1} backgroundColor="background">
      {/* Header */}
      <Box
        style={{ paddingTop: insets.top }}
        paddingHorizontal="md_16"
        paddingBottom="sm_12"
        backgroundColor="background"
        borderBottomWidth={1}
        borderBottomColor="border">
        <Box flexDirection="row" alignItems="center" justifyContent="space-between">
          <Text variant="h_24SemiBold_section">Discover</Text>

          <Box flexDirection="row" alignItems="center" gap="sm_12">
            {/* Search button */}
            <Pressable
              onPress={() => navigation.navigate('Search')}
              accessibilityRole="button"
              accessibilityLabel="Search">
              <Ionicons name="search" size={24} color={theme.colors.text} />
            </Pressable>

            {/* View mode toggle */}
            <Pressable
              onPress={() => setViewMode(viewMode === 'map' ? 'list' : 'map')}
              accessibilityRole="button"
              accessibilityLabel={`Switch to ${viewMode === 'map' ? 'list' : 'map'} view`}>
              <Ionicons
                name={viewMode === 'map' ? 'list' : 'map'}
                size={24}
                color={theme.colors.text}
              />
            </Pressable>

            {/* Filter button */}
            <Pressable
              onPress={toggleFilterSheet}
              accessibilityRole="button"
              accessibilityLabel="Filters">
              <Box position="relative">
                <Ionicons name="filter" size={24} color={theme.colors.text} />
                {activeCategories.length > 0 && (
                  <Box
                    position="absolute"
                    top={-4}
                    right={-4}
                    width={12}
                    height={12}
                    borderRadius="circle_9999"
                    backgroundColor="primary"
                  />
                )}
              </Box>
            </Pressable>
          </Box>
        </Box>

        {/* Quick stats */}
        {!isLoading && items.length > 0 && (
          <Box flexDirection="row" marginTop="xs_8" gap="md_16">
            <Text variant="l_12Bold_highlight" color="textSecondary">
              {items.length} nearby
            </Text>
            {userLocation && (
              <Text variant="l_12Bold_highlight" color="textSecondary">
                Within 5km
              </Text>
            )}
          </Box>
        )}
      </Box>

      {/* Content */}
      {isLoading ? (
        <Box flex={1} justifyContent="center" alignItems="center">
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text variant="l_12Bold_highlight" color="textSecondary" marginTop="md_16">
            Finding nearby places...
          </Text>
        </Box>
      ) : viewMode === 'map' ? (
        <RadarMap
          items={items}
          userLocation={userLocation?.coords}
          initialRegion={initialRegion}
          selectedMarkerId={selectedMarkerId}
          onMarkerPress={handleItemPress}
          onMapPress={() => selectMarker(null)}
          onRegionChange={setMapRegion}
          showUserLocation
        />
      ) : (
        <ScrollView
          contentContainerStyle={{
            padding: theme.spacing.md_16,
            paddingBottom: theme.spacing.xxxl_48,
          }}
          refreshControl={<RefreshControl refreshing={isRefreshing} onRefresh={refresh} />}>
          {items.map(item => (
            <DiscoveryCard
              key={item.id}
              item={item}
              userLocation={userLocation?.coords}
              onPress={handleItemPress}
              onSave={handleSave}
              onShare={handleShare}
            />
          ))}

          {items.length === 0 && (
            <Box alignItems="center" paddingVertical="xxxl_48">
              <Ionicons name="location-outline" size={64} color={theme.colors.textTertiary} />
              <Text
                variant="l_12Bold_highlight"
                color="textSecondary"
                marginTop="md_16"
                textAlign="center">
                No places found nearby
              </Text>
              <Text
                variant="l_12Bold_highlight"
                color="textTertiary"
                marginTop="xs_8"
                textAlign="center">
                Try adjusting your filters or search radius
              </Text>
            </Box>
          )}
        </ScrollView>
      )}

      {/* Selected item preview (map mode only) */}
      {viewMode === 'map' && selectedItem && (
        <Box
          position="absolute"
          bottom={insets.bottom + theme.spacing.md_16}
          left={theme.spacing.md_16}
          right={theme.spacing.md_16}>
          <DiscoveryCard
            item={selectedItem}
            variant="compact"
            onPress={() => handleItemPress(selectedItem)}
            onSave={handleSave}
            onShare={handleShare}
          />
        </Box>
      )}
    </Box>
  );
};

export default RadarScreen;
