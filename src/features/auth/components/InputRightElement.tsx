import { Eye, EyeSlash } from 'phosphor-react-native';

import { useTheme } from '@/src/core/theme';
import { PressableAnimated } from '@/src/core/theme/Pressable';

const InputRightElement = ({ onPress, icon }: InputRightElementProps) => {
  const theme = useTheme();
  return (
    <PressableAnimated onPress={onPress}>
      {icon === 'EyeSlash' ? (
        <EyeSlash size={24} color={theme.colors.iconActiveInput} />
      ) : (
        <Eye
          size={24}
          color={theme.colors.iconActiveInput}
          weight="duotone"
          duotoneColor={theme.colors.iconActiveInput}
        />
      )}
    </PressableAnimated>
  );
};

export default InputRightElement;

export type InputRightElementProps = {
  onPress: () => void;
  icon: string;
};
