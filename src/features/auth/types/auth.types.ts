/**
 * Authentication Types
 * Comprehensive type definitions for all authentication methods
 */

// User types
export interface User {
  id: string;
  name: string;
  email?: string;
  avatar?: string;
  profileImageURL?: string;
  coverImageURL?: string;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  role?: string;
  // Legacy fields for backward compatibility
  phone?: string;
  username?: string;
  emailVerified?: boolean;
  phoneVerified?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

// Auth response types
export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface AuthResponse {
  success: boolean;
  user: User | null;
  tokens: AuthTokens | null;
  requiresVerification?: boolean;
  verificationType?: 'email' | 'phone' | 'both';
}

// Registration types
export interface PhoneRegistrationInput {
  phoneNumber: string;
  name: string;
}

export interface EmailRegistrationInput {
  email: string;
  password: string;
  name: string;
  username?: string;
}

export interface OTPRegistrationInput {
  email: string;
  name: string;
}

export interface PasswordlessRegistrationInput {
  email: string;
  name: string;
  username?: string;
}

// Login types
export interface PhoneLoginInput {
  phoneNumber: string;
  password: string;
}

export interface EmailLoginInput {
  email: string;
  password: string;
}

export interface PasswordlessLoginInput {
  email: string;
  token?: string; // Magic link token
}

export interface OTPLoginInput {
  email: string;
  code: string; // OTP code received via email
}

export interface OTPVerificationInput {
  email: string;
  code: string;
}

export interface SocialLoginInput {
  provider: SocialProvider;
  accessToken: string;
  profile?: SocialProfile;
}

// Social auth types
export type SocialProvider = 'google' | 'facebook' | 'apple' | 'twitter' | 'github';

export interface SocialProfile {
  id: string;
  email?: string;
  name?: string;
  avatar?: string;
  provider: SocialProvider;
}

// Verification types
export interface VerificationInput {
  type: 'email' | 'phone';
  identifier: string; // email or phone number
  code: string;
}

export interface ResendVerificationInput {
  type: 'email' | 'phone';
  identifier: string;
}

// Password reset types
export interface RequestPasswordResetInput {
  email: string;
}

export interface ResetPasswordInput {
  email: string;
  token: string;
  newPassword: string;
}

// Auth method types for UI
export type AuthMethod = 'phone' | 'email' | 'passwordless' | 'social';

export interface AuthMethodConfig {
  method: AuthMethod;
  enabled: boolean;
  providers?: SocialProvider[]; // For social auth
}

// Session types
export interface Session {
  id: string;
  userId: string;
  deviceId?: string;
  deviceName?: string;
  ipAddress?: string;
  userAgent?: string;
  createdAt: string;
  lastActiveAt: string;
}

// Multi-factor authentication types
export interface MFAConfig {
  enabled: boolean;
  methods: MFAMethod[];
  preferredMethod?: MFAMethod;
}

export type MFAMethod = 'sms' | 'authenticator' | 'email';

export interface EnableMFAInput {
  method: MFAMethod;
  phoneNumber?: string; // For SMS
  email?: string; // For email
}

export interface VerifyMFAInput {
  method: MFAMethod;
  code: string;
}

// Account linking types
export interface LinkedAccount {
  id: string;
  provider: SocialProvider | 'email' | 'phone';
  identifier: string; // email, phone, or social ID
  isPrimary: boolean;
  verifiedAt?: string;
}

export interface LinkAccountInput {
  provider: SocialProvider | 'email' | 'phone';
  identifier: string;
  verificationCode?: string; // For email/phone
  accessToken?: string; // For social
}

// Error types
export interface AuthErrorExtension {
  code: AuthErrorCode;
  message: string;
  field?: string;
  suggestion?: string;
}

export type AuthErrorCode =
  | 'INVALID_CREDENTIALS'
  | 'WRONG_PASSWORD'
  | 'INVALID_PASSWORD'
  | 'INVALID_EMAIL'
  | 'WEAK_PASSWORD'
  | 'EMAIL_ALREADY_EXISTS'
  | 'USER_ALREADY_EXISTS'
  | 'USER_NOT_FOUND'
  | 'USER_EXISTS'
  | 'ACCOUNT_DISABLED'
  | 'ACCOUNT_LOCKED'
  | 'UNAUTHENTICATED'
  | 'FORBIDDEN'
  | 'INVALID_TOKEN'
  | 'TOKEN_EXPIRED'
  | 'VERIFICATION_REQUIRED'
  | 'INVALID_VERIFICATION_CODE'
  | 'TOO_MANY_ATTEMPTS'
  | 'ACCOUNT_LOCKED'
  | 'SOCIAL_AUTH_FAILED'
  | 'MFA_REQUIRED'
  | 'INVALID_MFA_CODE'
  | 'NETWORK_ERROR'
  | 'UNKNOWN_ERROR';
