import { AuthTokens, User } from './auth.types';

export interface AuthState {
  // Core state
  user: User | null;
  tokens: AuthTokens | null;

  // Loading states (separated concerns)
  isInitializing: boolean; // App startup auth check
  isSigningOut: boolean; // Sign out operation

  // Derived state
  isAuthenticated: boolean;
  isOnboardingComplete: boolean;

  // Actions
  setUser: (user: User | null) => void;
  setTokens: (tokens: AuthTokens | null) => void;
  setIsInitializing: (loading: boolean) => void;
  setIsSigningOut: (loading: boolean) => void;
  setIsAuthenticated: (authenticated: boolean) => void;
  setOnboardingComplete: (complete: boolean) => void;

  // Complex actions
  initialize: () => Promise<void>;
  signOut: () => Promise<void>;
  reset: () => void;
  clearAuth: () => void;
}
