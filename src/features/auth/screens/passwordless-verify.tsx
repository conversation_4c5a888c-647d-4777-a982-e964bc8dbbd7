import { useEffect, useState } from 'react';

import { Alert, KeyboardAvoidingView, Platform } from 'react-native';

import { useNavigation, useRoute } from '@react-navigation/native';
import { Controller } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import { z } from 'zod';

import { i18n } from '@/src/core';
import { AuthStackScreenProps } from '@/src/core/navigation/types';
import { Box, ScrollableContainer, Text, useTheme } from '@/src/core/theme';
import TextInput from '@/src/core/theme/TextInput';
import { Button, NavigationTopBar } from '@/src/shared/components';
import { useZodForm } from '@/src/shared/forms/useZodForm';

import { useRequestPasswordlessLogin, useVerifyPasswordlessLogin } from '../hooks';

export const passwordlessVerifySchema = () =>
  z.object({
    token: z
      .string()
      .min(1, { message: i18n.t('validation.token.required') })
      .min(6, { message: i18n.t('validation.token.minLength') }),
  });

export default function PasswordlessVerifyScreen() {
  const navigation = useNavigation<AuthStackScreenProps<'PasswordlessVerify'>['navigation']>();
  const route = useRoute<AuthStackScreenProps<'PasswordlessVerify'>['route']>();
  const { t } = useTranslation();
  const insets = useSafeAreaInsets();
  const theme = useTheme();
  const verifyPasswordlessLoginMutation = useVerifyPasswordlessLogin();
  const requestPasswordlessLoginMutation = useRequestPasswordlessLogin();

  const { email } = route.params;
  const [countdown, setCountdown] = useState(60);
  const [canResend, setCanResend] = useState(false);

  const { control, handleSubmit, formState, trigger, watch } = useZodForm(
    passwordlessVerifySchema(),
    {
      mode: 'onBlur',
      reValidateMode: 'onChange',
    }
  );

  const tokenValue = watch('token');
  const isFormValid = formState.isValid && tokenValue;

  // Countdown timer for resend
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    } else {
      setCanResend(true);
    }
  }, [countdown]);

  const handlePasswordlessVerify = async (data: { token: string }) => {
    if (verifyPasswordlessLoginMutation.isPending) return;

    try {
      const result = await verifyPasswordlessLoginMutation.mutateAsync({
        email,
        token: data.token,
      });

      if (result.success) {
        if (result.isNewUser) {
          // New user, navigate to onboarding
          navigation.navigate('Onboarding');
        } else {
          // Existing user, auth context will handle navigation
        }
      } else {
        Alert.alert(
          i18n.t('common.error'),
          result.errors?.[0].message || i18n.t('auth.invalidToken')
        );
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : i18n.t('common.tryAgain');
      Alert.alert(i18n.t('common.error'), message);
    }
  };

  const handleResendToken = async () => {
    if (!canResend || requestPasswordlessLoginMutation.isPending) return;

    try {
      const result = await requestPasswordlessLoginMutation.mutateAsync(email);

      if (result.success) {
        setCountdown(60);
        setCanResend(false);
        Alert.alert(i18n.t('common.success'), i18n.t('auth.magicLinkSent'));
      } else {
        Alert.alert(
          i18n.t('common.error'),
          result.errors?.[0].message || i18n.t('common.tryAgain')
        );
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : i18n.t('common.tryAgain');
      Alert.alert(i18n.t('common.error'), message);
    }
  };

  const handleBack = () => {
    navigation.goBack();
  };

  return (
    <SafeAreaView style={{ flex: 1, paddingTop: theme.spacing.sm_12 }} edges={['top']}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        <Box flex={1} backgroundColor="background">
          <NavigationTopBar title="Verify Email" onPress={handleBack} />

          <ScrollableContainer
            gap="lg_24"
            contentContainerStyle={{
              flexGrow: 1,
              paddingHorizontal: 24,
              paddingBottom: Math.max(insets.bottom, 24),
            }}>
            {/* Title */}
            <Box gap="sm_12">
              <Text variant="h_24SemiBold_section" tx="auth.verifyTokenTitle" color="text" />
              <Text variant="b_14Regular_input" color="textSecondary">
                {t('auth.verifyTokenSubtitle', { email })}
              </Text>
            </Box>

            {/* Form */}
            <Box gap="lg_24">
              <Controller
                control={control}
                name="token"
                render={({ field }) => (
                  <TextInput
                    label={t('auth.tokenLabel')}
                    placeholder={t('auth.tokenPlaceholder')}
                    autoCapitalize="none"
                    autoComplete="one-time-code"
                    autoCorrect={false}
                    enterKeyHint="done"
                    textContentType="oneTimeCode"
                    keyboardType="default"
                    onChangeText={value => {
                      field.onChange(value);
                      if (formState.errors.token?.message) {
                        trigger('token');
                      }
                    }}
                    error={formState.errors.token?.message}
                    onBlur={field.onBlur}
                    value={field.value}
                  />
                )}
              />
            </Box>

            {/* Verify Button */}
            <Button
              tx="auth.verify"
              variant="primary"
              onPress={() => {
                handleSubmit(handlePasswordlessVerify)();
              }}
              enabled={Boolean(isFormValid && !verifyPasswordlessLoginMutation.isPending)}
              loading={verifyPasswordlessLoginMutation.isPending}
            />

            {/* Resend Section */}
            <Box gap="sm_12" alignItems="center">
              <Text variant="b_14Regular_input" tx="auth.didntReceiveToken" color="textSecondary" />

              {canResend ? (
                <Button
                  title={t('auth.resendToken')}
                  variant="ghost"
                  onPress={handleResendToken}
                  enabled={!requestPasswordlessLoginMutation.isPending}
                />
              ) : (
                <Text variant="b_14Regular_input" color="textSecondary">
                  {t('auth.resendIn', { seconds: countdown })}
                </Text>
              )}
            </Box>

            {/* Info Text */}
            <Box gap="sm_12">
              <Text
                variant="b_14Regular_input"
                tx="auth.checkSpamFolder"
                color="textSecondary"
                textAlign="center"
              />
            </Box>
          </ScrollableContainer>
        </Box>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
