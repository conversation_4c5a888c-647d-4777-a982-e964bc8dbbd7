import React, { useEffect, useRef, useState } from 'react';

import { Alert, KeyboardAvoidingView, Platform, TextInput as RNTextInput } from 'react-native';

import { useNavigation, useRoute } from '@react-navigation/native';
import { useTranslation } from 'react-i18next';
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { AuthStackScreenProps } from '@/src/core/navigation/types';
import { Box, ScrollableContainer, Text, useTheme } from '@/src/core/theme';
import { Button, NavigationTopBar } from '@/src/shared/components';

import { useOTPLogin, useResendVerification, useVerifyEmailOTP, useVerifyPhoneOTP } from '../hooks';

type VerificationScreenProps =
  | AuthStackScreenProps<'PhoneVerification'>
  | AuthStackScreenProps<'EmailVerification'>;

export default function VerificationScreen() {
  const navigation = useNavigation<VerificationScreenProps['navigation']>();
  const route = useRoute<VerificationScreenProps['route']>();
  const { t } = useTranslation();
  const insets = useSafeAreaInsets();
  const theme = useTheme();
  const verifyPhoneOTPMutation = useVerifyPhoneOTP();
  const verifyEmailOTPMutation = useVerifyEmailOTP();
  const otpLoginMutation = useOTPLogin();
  const resendVerificationMutation = useResendVerification();

  // Handle both phone and email verification params
  const params = route.params || {};
  const { phone, email, isOTPLogin } = params as {
    phone?: string;
    email?: string;
    isOTPLogin?: boolean;
  };
  const verificationType = phone ? 'phone' : 'email';
  const identifier = phone || email || '';

  const [code, setCode] = useState(['', '', '', '', '', '']);
  const [error, setError] = useState('');
  const [resendCooldown, setResendCooldown] = useState(60);

  const inputRefs = useRef<(RNTextInput | null)[]>([]);

  // Countdown timer for resend button
  useEffect(() => {
    if (resendCooldown > 0) {
      const timer = setTimeout(() => {
        setResendCooldown(resendCooldown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [resendCooldown]);

  const handleCodeChange = (value: string, index: number) => {
    // Only allow digits
    if (!/^\d*$/.test(value)) return;

    const newCode = [...code];
    newCode[index] = value;
    setCode(newCode);
    setError('');

    // Auto-focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }

    // Auto-submit when all fields are filled
    if (newCode.every(digit => digit !== '') && index === 5) {
      handleVerify(newCode.join(''));
    }
  };

  const handleKeyPress = (index: number, key: string) => {
    if (key === 'Backspace' && !code[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleVerify = async (verificationCode?: string) => {
    const codeToVerify = verificationCode || code.join('');

    if (codeToVerify.length !== 6) {
      setError(t('auth.verification.errors.incompleteCode'));
      return;
    }

    if (!identifier) {
      const typeText =
        verificationType === 'phone'
          ? t('auth.phoneNumberRegistrationLabel')
          : t('auth.emailLabel');
      setError(t('auth.verification.errors.identifierNotFound', { type: typeText }));
      return;
    }

    let result;
    if (verificationType === 'phone') {
      result = await verifyPhoneOTPMutation.mutateAsync({
        phone: identifier,
        code: codeToVerify,
      });
    } else if (isOTPLogin) {
      // Use OTP login mutation for login flow
      result = await otpLoginMutation.mutateAsync({
        email: identifier,
        code: codeToVerify,
      });
    } else {
      // Use email verification mutation for registration flow
      result = await verifyEmailOTPMutation.mutateAsync({
        email: identifier,
        code: codeToVerify,
      });
    }

    if (!result.success) {
      setError(result.errors?.[0].message || t('auth.verification.errors.invalidCode'));
      // Clear the code on error
      setCode(['', '', '', '', '', '']);
      inputRefs.current[0]?.focus();
    } else {
      // Navigation will be handled by auth guards
      // New users might need onboarding
    }
  };

  const handleResendCode = async () => {
    if (!identifier) return;

    try {
      const result = await resendVerificationMutation.mutateAsync({
        type: verificationType,
        identifier,
      });
      if (result.success) {
        setResendCooldown(60);
        const typeText = verificationType === 'phone' ? 'phone' : 'email';
        Alert.alert(
          t('auth.verification.codeSentTitle'),
          t('auth.verification.codeSentMessage', { type: typeText })
        );
      } else {
        Alert.alert(
          'Error',
          result.errors?.[0].message || t('auth.verification.errors.resendFailed')
        );
      }
    } catch {
      Alert.alert('Error', t('auth.verification.errors.resendFailed'));
    }
  };

  const handleBack = () => {
    navigation.goBack();
  };

  const maskedIdentifier =
    verificationType === 'phone' && phone
      ? `${phone.slice(0, -4).replace(/\d/g, '•')}${phone.slice(-4)}`
      : verificationType === 'email' && email
        ? `${email.slice(0, 3)}${'•'.repeat(email.length - 6)}${email.slice(-3)}`
        : `your ${verificationType}`;

  const title =
    verificationType === 'phone'
      ? t('auth.verification.phoneTitle')
      : isOTPLogin
        ? t('auth.login')
        : t('auth.verification.emailTitle');
  const heading =
    verificationType === 'phone'
      ? t('auth.verification.phoneHeading')
      : isOTPLogin
        ? t('auth.verification.otpLoginHeading', { fallback: 'Enter login code' })
        : t('auth.verification.emailHeading');
  const description =
    verificationType === 'phone'
      ? t('auth.verification.phoneDescription', { phone: maskedIdentifier })
      : isOTPLogin
        ? t('auth.verification.otpLoginDescription', {
            fallback: 'Enter the 6-digit code we sent to {{email}}',
            email: maskedIdentifier,
          })
        : t('auth.verification.emailDescription', { email: maskedIdentifier });
  const icon = verificationType === 'phone' ? '📱' : '📧';

  return (
    <Box flex={1} backgroundColor="background">
      <KeyboardAvoidingView style={{ flex: 1 }}>
        <KeyboardAwareScrollView
          contentContainerStyle={{ flexGrow: 1, paddingTop: insets.top }}
          bottomOffset={Platform.OS === 'ios' ? 0 : 20}
          keyboardShouldPersistTaps="handled"
          extraKeyboardSpace={20}
          showsVerticalScrollIndicator={false}>
          <NavigationTopBar title={title} onPress={handleBack} />

          {/* Content */}
          <Box flex={1} paddingHorizontal="lg_24" justifyContent="center">
            {/* Title */}
            <Box alignItems="center" marginBottom="xxl_40">
              <Box
                width={80}
                height={80}
                backgroundColor="surfaceBackground"
                borderRadius="circle_9999"
                justifyContent="center"
                alignItems="center"
                marginBottom="lg_24">
                <Text style={{ fontSize: 32 }}>{icon}</Text>
              </Box>

              <Text variant="H_40Bold_title" textAlign="center" color="text" marginBottom="xs_8">
                {heading}
              </Text>
              <Text
                variant="b_16Regular_input"
                textAlign="center"
                color="textSecondary"
                lineHeight={24}>
                {description}
              </Text>
            </Box>

            {/* Code Input */}
            <Box marginBottom="lg_24">
              <Box
                flexDirection="row"
                justifyContent="space-between"
                gap="sm_12"
                marginBottom="md_16">
                {code.map((digit, index) => (
                  <Box
                    key={index}
                    flex={1}
                    height={60}
                    borderWidth={2}
                    borderColor={error ? 'error' : digit ? 'primary' : 'surfaceBackground'}
                    borderRadius="md_12"
                    backgroundColor="surface"
                    justifyContent="center"
                    alignItems="center">
                    <RNTextInput
                      ref={ref => {
                        inputRefs.current[index] = ref;
                      }}
                      value={digit}
                      onChangeText={value => handleCodeChange(value, index)}
                      onKeyPress={({ nativeEvent }) => handleKeyPress(index, nativeEvent.key)}
                      keyboardType="number-pad"
                      maxLength={1}
                      textAlign="center"
                      style={{
                        width: '100%',
                        height: '100%',
                        fontSize: 24,
                        fontWeight: 'bold',
                        color: theme.colors.text,
                      }}
                      selectionColor={theme.colors.primary}
                    />
                  </Box>
                ))}
              </Box>

              {error ? (
                <Text variant="b_14Regular_content" color="error" textAlign="center">
                  {error}
                </Text>
              ) : null}
            </Box>

            {/* Verify Button */}
            <Box marginBottom="lg_24">
              <Button
                title={
                  verifyPhoneOTPMutation.isPending ||
                  verifyEmailOTPMutation.isPending ||
                  otpLoginMutation.isPending
                    ? t('auth.verification.verifyingButton')
                    : isOTPLogin
                      ? t('auth.login')
                      : t('auth.verification.verifyButton', {
                          type:
                            verificationType === 'phone'
                              ? t('auth.phoneNumberRegistrationLabel')
                              : t('auth.emailLabel'),
                        })
                }
                variant="primary"
                onPress={() => handleVerify()}
                enabled={
                  !verifyPhoneOTPMutation.isPending &&
                  !verifyEmailOTPMutation.isPending &&
                  !otpLoginMutation.isPending &&
                  code.every(digit => digit !== '')
                }
                loading={
                  verifyPhoneOTPMutation.isPending ||
                  verifyEmailOTPMutation.isPending ||
                  otpLoginMutation.isPending
                }
              />
            </Box>

            {/* Resend Code */}
            <Box alignItems="center">
              <Text variant="b_14Regular_input" color="textSecondary" marginBottom="xs_8">
                {t('auth.verification.resendCodeQuestion')}
              </Text>

              {resendCooldown > 0 ? (
                <Text variant="b_14Medium_button" color="textTertiary">
                  {t('auth.verification.resendCooldown', { seconds: resendCooldown })}
                </Text>
              ) : (
                <Button
                  title={t('auth.verification.resendCodeButton')}
                  variant="ghost"
                  onPress={handleResendCode}
                  enabled={
                    !verifyPhoneOTPMutation.isPending &&
                    !verifyEmailOTPMutation.isPending &&
                    !otpLoginMutation.isPending
                  }
                />
              )}
            </Box>
          </Box>
        </KeyboardAwareScrollView>
      </KeyboardAvoidingView>
    </Box>
  );
}
