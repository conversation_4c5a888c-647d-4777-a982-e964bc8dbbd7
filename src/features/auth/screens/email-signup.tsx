import { useState } from 'react';

import { KeyboardAvoidingView, Platform } from 'react-native';

import { useNavigation } from '@react-navigation/native';
import { Controller } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import { z } from 'zod';

import { i18n } from '@/src/core';
import { AuthStackScreenProps } from '@/src/core/navigation/types';
import { Box, ScrollableContainer, Text, useTheme } from '@/src/core/theme';
import TextInput from '@/src/core/theme/TextInput';
import { Button, NavigationTopBar } from '@/src/shared/components';
import { useZodForm } from '@/src/shared/forms/useZodForm';

import InputRightElement from '../components/InputRightElement';
import { useEmailRegister } from '../hooks';
import { EmailRegistrationInput } from '../types';

export const emailSignupSchema = () =>
  z
    .object({
      email: z
        .string()
        .min(1, { message: i18n.t('validation.email.required') })
        .email({ message: i18n.t('validation.email.invalid') }),
      password: z
        .string()
        .min(8, { message: i18n.t('validation.password.minLength', { min: 8 }) })
        .regex(/[A-Z]/, { message: i18n.t('validation.password.uppercase') })
        .regex(/[a-z]/, { message: i18n.t('validation.password.lowercase') })
        .regex(/[0-9]/, { message: i18n.t('validation.password.number') }),
      confirmPassword: z
        .string()
        .min(1, { message: i18n.t('validation.confirmPassword.required') }),
      name: z
        .string()
        .min(2, { message: i18n.t('validation.name.minLength', { min: 2 }) })
        .max(50, { message: i18n.t('validation.name.maxLength', { max: 50 }) }),
    })
    .refine(data => data.password === data.confirmPassword, {
      message: i18n.t('validation.confirmPassword.mismatch'),
      path: ['confirmPassword'],
    });

export default function EmailSignupScreen() {
  const navigation = useNavigation<AuthStackScreenProps<'EmailSignup'>['navigation']>();
  const { t } = useTranslation();
  const insets = useSafeAreaInsets();
  const theme = useTheme();
  const emailRegisterMutation = useEmailRegister();

  const { control, handleSubmit, formState, trigger, watch } = useZodForm(emailSignupSchema(), {
    mode: 'onBlur',
    reValidateMode: 'onChange',
  });

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const emailValue = watch('email');
  const passwordValue = watch('password');
  const confirmPasswordValue = watch('confirmPassword');
  const nameValue = watch('name');

  const isFormValid =
    formState.isValid && emailValue && passwordValue && confirmPasswordValue && nameValue;

  const handleEmailSignup = async (data: {
    email: string;
    password: string;
    confirmPassword: string;
    name: string;
  }) => {
    if (emailRegisterMutation.isPending) return;

    try {
      const input: EmailRegistrationInput = {
        email: data.email.trim().toLowerCase(),
        password: data.password,
        name: data.name.trim(),
      };

      const result = await emailRegisterMutation.mutateAsync(input);

      if (result.success) {
        if (result.requiresVerification) {
          // Navigate to email verification screen
          navigation.navigate('EmailVerification', { email: data.email });
        } else {
          // Registration complete - the AppNavigator will automatically
          // switch to the Main flow when isAuthenticated becomes true
          console.log('Registration successful! Auth state will handle navigation.');
        }
      }
      // Note: Error messages are handled by the auth context with toasts
    } catch (error) {
      // Error handling is done in the auth context
      console.error('Registration error:', error);
    }
  };

  const handleBack = () => {
    navigation.goBack();
  };

  const handleSignIn = () => {
    navigation.navigate('EmailLogin');
  };

  return (
    <SafeAreaView style={{ flex: 1, paddingTop: theme.spacing.sm_12 }} edges={['top']}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        <Box flex={1} backgroundColor="background">
          <NavigationTopBar title="Sign Up" onPress={handleBack} />

          <ScrollableContainer
            gap="lg_24"
            contentContainerStyle={{
              flexGrow: 1,
              paddingHorizontal: 24,
              paddingBottom: Math.max(insets.bottom, 24),
            }}>
            {/* Title */}
            <Box gap="sm_12">
              <Text variant="h_24SemiBold_section" tx="auth.emailSignupTitle" color="text" />
              <Text
                variant="b_14Regular_input"
                tx="auth.emailSignupSubtitle"
                color="textSecondary"
              />
            </Box>

            {/* Form */}
            <Box gap="lg_24">
              <Controller
                control={control}
                name="name"
                render={({ field }) => (
                  <TextInput
                    label={t('auth.nameLabel')}
                    placeholder={t('auth.namePlaceholder')}
                    autoCapitalize="words"
                    autoComplete="name"
                    autoCorrect={false}
                    enterKeyHint="next"
                    textContentType="name"
                    onChangeText={value => {
                      field.onChange(value);
                      if (formState.errors.name?.message) {
                        trigger('name');
                      }
                    }}
                    error={formState.errors.name?.message}
                    onBlur={field.onBlur}
                    value={field.value}
                  />
                )}
              />

              <Controller
                control={control}
                name="email"
                render={({ field }) => (
                  <TextInput
                    label={t('auth.emailLabel')}
                    placeholder={t('auth.emailPlaceholder')}
                    autoCapitalize="none"
                    autoComplete="email"
                    autoCorrect={false}
                    enterKeyHint="next"
                    textContentType="emailAddress"
                    keyboardType="email-address"
                    onChangeText={value => {
                      field.onChange(value);
                      if (formState.errors.email?.message) {
                        trigger('email');
                      }
                    }}
                    error={formState.errors.email?.message}
                    onBlur={field.onBlur}
                    value={field.value}
                  />
                )}
              />

              <Controller
                control={control}
                name="password"
                render={({ field }) => (
                  <TextInput
                    label={t('auth.passwordLabel')}
                    placeholder={t('auth.passwordPlaceholder')}
                    autoCapitalize="none"
                    autoComplete="new-password"
                    autoCorrect={false}
                    enterKeyHint="next"
                    textContentType="oneTimeCode"
                    secureTextEntry={!showPassword}
                    trailing={
                      <InputRightElement
                        onPress={() => setShowPassword(!showPassword)}
                        icon={showPassword ? 'EyeSlash' : 'Eye'}
                      />
                    }
                    onChangeText={value => {
                      field.onChange(value);
                      if (formState.errors.password?.message) {
                        trigger('password');
                      }
                    }}
                    error={formState.errors.password?.message}
                    onBlur={field.onBlur}
                    value={field.value}
                  />
                )}
              />

              <Controller
                control={control}
                name="confirmPassword"
                render={({ field }) => (
                  <TextInput
                    label={t('auth.confirmPasswordLabel')}
                    placeholder={t('auth.confirmPasswordPlaceholder')}
                    autoCapitalize="none"
                    autoComplete="new-password"
                    autoCorrect={false}
                    enterKeyHint="done"
                    textContentType="oneTimeCode"
                    secureTextEntry={!showConfirmPassword}
                    trailing={
                      <InputRightElement
                        onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                        icon={showConfirmPassword ? 'EyeSlash' : 'Eye'}
                      />
                    }
                    onChangeText={value => {
                      field.onChange(value);
                      if (formState.errors.confirmPassword?.message) {
                        trigger('confirmPassword');
                      }
                    }}
                    error={formState.errors.confirmPassword?.message}
                    onBlur={field.onBlur}
                    value={field.value}
                  />
                )}
              />
            </Box>

            {/* Sign Up Button */}
            <Button
              tx="auth.signup"
              variant="primary"
              onPress={() => {
                handleSubmit(handleEmailSignup)();
              }}
              enabled={Boolean(isFormValid && !emailRegisterMutation.isPending)}
              loading={emailRegisterMutation.isPending}
            />

            {/* Sign In Link */}
            <Box flexDirection="row" justifyContent="center" alignItems="center" gap="xxs_4">
              <Text
                variant="b_14Regular_input"
                tx="auth.alreadyHaveAccount"
                color="textSecondary"
              />
              <Button title={t('auth.signIn')} variant="ghost" onPress={handleSignIn} />
            </Box>
          </ScrollableContainer>
        </Box>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
