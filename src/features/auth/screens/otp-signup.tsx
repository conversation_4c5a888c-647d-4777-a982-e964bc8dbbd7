import React from 'react';

import { KeyboardAvoidingView, Platform } from 'react-native';

import { useNavigation } from '@react-navigation/native';
import { Controller } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import { z } from 'zod';

import { i18n } from '@/src/core';
import { AuthStackScreenProps } from '@/src/core/navigation/types';
import { Box, ScrollableContainer, Text, useTheme } from '@/src/core/theme';
import TextInput from '@/src/core/theme/TextInput';
import { Button, Card, NavigationTopBar } from '@/src/shared/components';
import { useZodForm } from '@/src/shared/forms/useZodForm';

import { useOTPRegister } from '../hooks';
import { OTPRegistrationInput } from '../types';

export const otpSignupSchema = z.object({
  email: z
    .string()
    .min(1, { message: i18n.t('validation.email.required') })
    .email({ message: i18n.t('validation.email.invalid') }),
  name: z
    .string()
    .min(2, { message: i18n.t('validation.name.minLength', { min: 2 }) })
    .max(50, { message: i18n.t('validation.name.maxLength', { max: 50 }) }),
});

export default function OTPSignupScreen() {
  const navigation = useNavigation<AuthStackScreenProps<'OTPSignup'>['navigation']>();
  const { t } = useTranslation();
  const insets = useSafeAreaInsets();
  const theme = useTheme();
  const otpRegisterMutation = useOTPRegister();

  const { control, handleSubmit, formState, trigger, watch } = useZodForm(otpSignupSchema, {
    mode: 'onBlur', // Keep onBlur to avoid showing errors while typing
    reValidateMode: 'onChange',
    defaultValues: {
      name: '',
      email: '',
    },
  });

  // Watch form values
  const watchedValues = watch();

  // Use Zod to check if values are valid (without triggering form errors)
  const isInputValid = React.useMemo(() => {
    const result = otpSignupSchema.safeParse({
      name: watchedValues.name || '',
      email: watchedValues.email || '',
    });
    return result.success;
  }, [watchedValues.name, watchedValues.email]);

  // Enable button if form is valid OR if input passes Zod validation
  const isFormValid = formState.isValid || isInputValid;

  const handleOTPSignup = async (data: { email: string; name: string }) => {
    if (otpRegisterMutation.isPending) return;

    try {
      const input: OTPRegistrationInput = {
        email: data.email.trim().toLowerCase(),
        name: data.name.trim(),
      };

      const result = await otpRegisterMutation.mutateAsync(input);

      if (result.success) {
        navigation.navigate('EmailVerification', { email: data.email });
      }
      // Note: Error messages are handled by the auth context with toasts
    } catch (error) {
      // Error handling is done in the auth context
      console.error('OTP Registration error:', error);
    }
  };

  const handleBack = () => {
    navigation.goBack();
  };

  const handleSignIn = () => {
    navigation.navigate('EmailLogin');
  };

  return (
    <SafeAreaView style={{ flex: 1, paddingTop: theme.spacing.sm_12 }} edges={['top']}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        <Box flex={1} backgroundColor="background">
          <NavigationTopBar title="Movuca" onPress={handleBack} />

          <ScrollableContainer
            gap="lg_24"
            contentContainerStyle={{
              flexGrow: 1,
              paddingHorizontal: 24,
              paddingBottom: Math.max(insets.bottom, 24),
            }}>
            {/* Title */}
            <Box gap="sm_12">
              <Text variant="h_24SemiBold_section" tx="auth.emailSignupTitle" color="text" />
              <Text
                variant="b_14Regular_input"
                tx="auth.emailSignupSubtitle"
                color="textSecondary"
              />
            </Box>

            {/* Form */}
            <Box gap="lg_24">
              <Controller
                control={control}
                name="name"
                render={({ field }) => (
                  <TextInput
                    label={t('auth.nameLabel')}
                    placeholder={t('auth.namePlaceholder')}
                    autoCapitalize="words"
                    autoComplete="name"
                    autoCorrect={false}
                    enterKeyHint="next"
                    textContentType="name"
                    onChangeText={value => {
                      field.onChange(value);
                      // Only trigger validation if there's an error (to clear it)
                      if (formState.errors.name?.message) {
                        trigger('name');
                      }
                    }}
                    error={formState.errors.name?.message}
                    onBlur={field.onBlur}
                    value={field.value}
                  />
                )}
              />

              <Controller
                control={control}
                name="email"
                render={({ field }) => (
                  <TextInput
                    label={t('auth.emailLabel')}
                    placeholder={t('auth.emailPlaceholder')}
                    autoCapitalize="none"
                    autoComplete="email"
                    autoCorrect={false}
                    enterKeyHint="done"
                    textContentType="emailAddress"
                    keyboardType="email-address"
                    onChangeText={value => {
                      field.onChange(value);
                      // Only trigger validation if there's an error (to clear it)
                      if (formState.errors.email?.message) {
                        trigger('email');
                      }
                    }}
                    error={formState.errors.email?.message}
                    onBlur={field.onBlur}
                    value={field.value}
                  />
                )}
              />
            </Box>

            {/* Sign Up Button */}
            <Button
              tx="auth.createAccount"
              variant="primary"
              onPress={() => {
                handleSubmit(handleOTPSignup)();
              }}
              enabled={Boolean(isFormValid && !otpRegisterMutation.isPending)}
              loading={otpRegisterMutation.isPending}
            />

            {/* Info Box */}
            <Card padding="md_16" gap="xs_8">
              <Text variant="l_12SemiBold_button" color="textSecondary">
                ✨ Why OTP Registration?
              </Text>
              <Text variant="l_12Regular_helperText" color="textSecondary">
                • No passwords to remember • Quick and secure verification • Easy account recovery
              </Text>
            </Card>

            {/* Sign In Link */}
            <Box flexDirection="row" justifyContent="center" alignItems="center" gap="xxs_4">
              <Text
                variant="b_14Regular_input"
                tx="auth.alreadyHaveAccount"
                color="textSecondary"
              />
              <Button tx="auth.signIn" variant="ghost" onPress={handleSignIn} />
            </Box>
          </ScrollableContainer>
        </Box>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
