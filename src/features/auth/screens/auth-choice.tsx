import React from 'react';

import { Platform } from 'react-native';

import { useNavigation } from '@react-navigation/native';
import {
  AppleLogo,
  Envelope,
  GoogleLogo,
  MagicWand,
  PhoneCall,
  ShieldCheck,
} from 'phosphor-react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';

import { AuthStackScreenProps } from '@/src/core/navigation/types';
import { Box, Row, Text, useTheme } from '@/src/core/theme';
import { Button, NavigationTopBar } from '@/src/shared/components';
import { Divider } from '@/src/shared/components/Divider';

import { useSocialAuth } from '../hooks/useSocialAuth';

export default function AuthChoiceScreen() {
  const insets = useSafeAreaInsets();
  const theme = useTheme();
  const navigation = useNavigation<AuthStackScreenProps<'AuthChoice'>['navigation']>();
  const { appleSignIn, googleSignIn, isAppleAvailable } = useSocialAuth();

  const handlePhoneAuth = () => {
    navigation.navigate('PhoneSignup');
  };

  const handleEmailAuth = () => {
    // Navigate to login by default, they can switch to signup from there
    navigation.navigate('EmailLogin');
  };

  const handlePasswordlessAuth = () => {
    navigation.navigate('PasswordlessRequest');
  };

  const handleOTPAuth = () => {
    navigation.navigate('OTPSignup');
  };

  const handleBack = () => {
    navigation.goBack();
  };

  const showAppleSignIn = Platform.OS === 'ios' && isAppleAvailable;

  return (
    <SafeAreaView
      edges={['top']}
      style={{
        flex: 1,
        paddingBottom: insets.bottom + theme.spacing.sm_12,
        paddingTop: theme.spacing.sm_12,
      }}>
      <NavigationTopBar onPress={handleBack} />
      <Box flex={1} backgroundColor="background" paddingHorizontal="lg_24">
        {/* Title Section */}
        <Box flex={1} justifyContent="center">
          <Text tx="auth.welcomeMessage" variant="H_40Bold_title" marginBottom="md_16" />
          <Text tx="auth.chooseMethodSubtitle" variant="b_16Regular_input" color="textSecondary" />
        </Box>

        {/* Authentication Options */}
        <Box gap="md_16">
          <Button
            title="Sign up with OTP"
            variant="primary"
            onPress={handleOTPAuth}
            leftIcon={<ShieldCheck color={theme.colors.white} />}
          />

          <Button
            tx="auth.emailOptionTitle"
            variant="secondary"
            onPress={handleEmailAuth}
            leftIcon={<Envelope color={theme.colors.white} />}
          />

          <Button
            tx="auth.phoneOptionTitle"
            variant="outline"
            onPress={handlePhoneAuth}
            leftIcon={<PhoneCall color={theme.colors.iconPrimary} />}
          />

          <Button
            tx="auth.magicLinkOptionTitle"
            variant="ghost"
            onPress={handlePasswordlessAuth}
            leftIcon={<MagicWand color={theme.colors.iconPrimary} />}
          />
        </Box>

        {/* Social Login Section */}
        <Row justifyContent="center" alignItems="center" gap="md_16" padding="md_16">
          <Divider width="50%" />
          <Text variant="b_16Bold_keypoint" tx="auth.or" />
          <Divider width="50%" />
        </Row>

        <Row gap="sm_12" width="100%" marginBottom="lg_24">
          {showAppleSignIn && (
            <Button
              title="Apple"
              variant="tertiary"
              leftIcon={<AppleLogo weight="fill" color={theme.colors.iconActiveInput} />}
              onPress={appleSignIn}
              flex={1}
            />
          )}
          <Button
            title="Google"
            variant="tertiary"
            leftIcon={<GoogleLogo weight="fill" color={theme.colors.iconActiveInput} />}
            onPress={googleSignIn}
            flex={1}
          />
        </Row>
      </Box>
    </SafeAreaView>
  );
}
