import { useCallback, useEffect } from 'react';

import { Linking, StyleSheet } from 'react-native';

import { useNavigation } from '@react-navigation/native';
import { Trans, useTranslation } from 'react-i18next';
import Animated, {
  FadeOutDown,
  ZoomInEasyDown,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { AuthStackScreenProps } from '@/src/core/navigation/types';
import { Box, Text } from '@/src/core/theme';
import { Button, Stagger } from '@/src/shared/components';
import Logo from '@/src/shared/components/Images/Logo';
import { RadialBackground } from '@/src/shared/components/RadialBackground';

/**
 * The Welcome screen is the first screen that users see when they open the app.
 *
 * It displays a logo and title at the bottom of the screen, which then slide
 * up to the natural center position of the screen. The content of the screen
 * is then displayed using a stagger animation.
 *
 * The content includes a description of the app, a button to get started with
 * the onboarding process, a button to sign in with an existing account, and a
 * legal notice.
 *
 * The screen also handles the navigation to the onboarding process and the
 * sign in process.
 */

// Constants

const ANIMATION_CONFIG = {
  INITIAL_DELAY: 1000,
  STAGGER_DELAY: 150,
  DAMPING: 20,
  STIFFNESS: 300,
  DURATION: 400,
};

const LINKS = {
  TERMS: 'https://example.com/terms',
  PRIVACY: 'https://example.com/privacy',
};

export default function WelcomeScreen() {
  const navigation = useNavigation<AuthStackScreenProps<'Welcome'>['navigation']>();
  const insets = useSafeAreaInsets();
  const { t } = useTranslation();

  // Animation values
  const logoTranslateY = useSharedValue(200);

  useEffect(() => {
    const timer = setTimeout(() => {
      logoTranslateY.value = withSpring(0, {
        damping: ANIMATION_CONFIG.DAMPING,
        stiffness: ANIMATION_CONFIG.STIFFNESS,
      });
    }, ANIMATION_CONFIG.INITIAL_DELAY);

    return () => clearTimeout(timer);
  }, [logoTranslateY]);

  // Animated styles
  const logoAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: logoTranslateY.value }],
  }));

  // Memoized handlers
  const handleGetStarted = useCallback(() => {
    navigation.push('Onboarding');
  }, [navigation]);

  const handleSignIn = useCallback(() => {
    navigation.push('AuthChoice');
  }, [navigation]);

  return (
    <Box flex={1}>
      <RadialBackground />
      <Box position="absolute" width="100%" height="100%">
        <Box
          flex={1}
          backgroundColor="transparent"
          paddingHorizontal="lg_24"
          style={[styles.container, { paddingTop: insets.top + 40, paddingBottom: insets.bottom }]}>
          <Animated.View style={[styles.logoContainer, logoAnimatedStyle]}>
            <Logo />
            <Text
              variant="h_48Black_splash"
              textAlign="center"
              color="white"
              marginTop="lg_24"
              tx="welcome.title"
              accessible
              accessibilityLabel={t('welcome.title')}
            />
          </Animated.View>

          <Stagger
            initialEnteringDelay={ANIMATION_CONFIG.INITIAL_DELAY + 50}
            stagger={ANIMATION_CONFIG.STAGGER_DELAY}
            duration={ANIMATION_CONFIG.DURATION}
            exitDirection={1}
            entering={() => ZoomInEasyDown.springify()}
            exiting={() => FadeOutDown.springify()}
            flexDirection="column"
            flexWrap="wrap"
            justifyContent="center"
            gap="md_16">
            <Text
              variant="l_12Regular_helperText"
              textAlign="center"
              color="white"
              tx="welcome.description"
              accessible
              accessibilityLabel={t('welcome.description')}
            />
            <Button
              tx="welcome.getStarted"
              variant="primary"
              onPress={handleGetStarted}
              accessibilityLabel={t('welcome.getStarted')}
              accessibilityRole="button"
            />
            <Button
              tx="welcome.alreadyHaveAccount"
              variant="ghost"
              textColor="white"
              onPress={handleSignIn}
              accessibilityLabel={t('welcome.alreadyHaveAccount')}
              accessibilityRole="button"
            />
            <LegalNotice />
          </Stagger>
        </Box>
      </Box>
    </Box>
  );
}

function LegalNotice() {
  const { t } = useTranslation();

  return (
    <Text variant="b_14Medium_button" textAlign="center" color="white">
      <Trans
        i18nKey="welcome.legalNotice"
        values={{
          TermsOfService: t('welcome.termsOfService'),
          PrivacyPolicy: t('welcome.privacyPolicy'),
        }}
        components={[
          <Text
            key="0"
            variant="b_14Medium_link"
            color="secondary"
            onPress={() => Linking.openURL(LINKS.TERMS)}
            accessibilityRole="link"
            accessibilityLabel={t('welcome.termsOfService')}
          />,
          <Text
            key="1"
            variant="b_14Medium_link"
            color="secondary"
            onPress={() => Linking.openURL(LINKS.PRIVACY)}
            accessibilityRole="link"
            accessibilityLabel={t('welcome.privacyPolicy')}
          />,
        ]}
      />
    </Text>
  );
}

const styles = StyleSheet.create({
  container: {
    justifyContent: 'space-between',
  },
  logoContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
});
