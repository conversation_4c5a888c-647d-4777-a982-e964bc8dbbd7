import React, { useState } from 'react';

import { KeyboardAvoidingView, Platform } from 'react-native';

import { useNavigation } from '@react-navigation/native';
import { Controller } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import { z } from 'zod';

import { i18n } from '@/src/core';
import { AuthStackScreenProps } from '@/src/core/navigation/types';
import { Box, ScrollableContainer, Text, useTheme } from '@/src/core/theme';
import TextInput from '@/src/core/theme/TextInput';
import { Button, NavigationTopBar } from '@/src/shared/components';
import { useZodForm } from '@/src/shared/forms/useZodForm';
import { useToast } from '@/src/shared/hooks';

import InputRightElement from '../components/InputRightElement';
import { useEmailLogin, useSendEmailOTP } from '../hooks';
import { EmailLoginInput } from '../types';
import { getAuthErrorMessage, getAuthErrorTitle } from '../utils/errorMessages';

export const emailLoginSchema = () =>
  z.object({
    email: z
      .string()
      .min(1, { message: i18n.t('validation.email.required') })
      .email({ message: i18n.t('validation.email.invalid') }),
    password: z.string().min(1, { message: i18n.t('validation.password.required') }),
  });

export default function EmailLoginScreen() {
  const navigation = useNavigation<AuthStackScreenProps<'EmailLogin'>['navigation']>();
  const { t } = useTranslation();
  const insets = useSafeAreaInsets();
  const theme = useTheme();
  const emailLoginMutation = useEmailLogin();
  const sendEmailOTPMutation = useSendEmailOTP();

  const toast = useToast();

  const { control, handleSubmit, formState, trigger, watch } = useZodForm(emailLoginSchema(), {
    mode: 'onBlur',
    reValidateMode: 'onChange',
  });

  const [showPassword, setShowPassword] = useState(false);

  const emailValue = watch('email');
  const passwordValue = watch('password');

  const isFormValid = formState.isValid && emailValue && passwordValue;

  const handleEmailLogin = async (data: { email: string; password: string }) => {
    if (emailLoginMutation.isPending) return;

    try {
      const input: EmailLoginInput = {
        email: data.email.trim().toLowerCase(),
        password: data.password,
      };

      const result = await emailLoginMutation.mutateAsync(input);

      if (result.success) {
        // Auth context will handle navigation via guards
        // Show success message
        toast.successWithHaptic(
          i18n.t('auth.loginSuccess', { fallback: 'Successfully signed in!' })
        );
      } else {
        // Error message is already processed by AuthContext
        const errorMessage = result.errors?.[0].message || 'Login failed';
        toast.errorWithHaptic(errorMessage, { title: 'Login Error' });
      }
    } catch (error) {
      // Enhanced error handling for authentication errors
      const errorMessage = getAuthErrorMessage(error);
      const errorTitle = getAuthErrorTitle(error);
      toast.errorWithHaptic(errorMessage, { title: errorTitle });
    }
  };

  const handleForgotPassword = () => {
    navigation.navigate('PasswordlessRequest');
  };

  const handleSignUp = () => {
    navigation.navigate('EmailSignup');
  };

  const handleOTPLogin = async () => {
    if (!emailValue) {
      toast.errorWithHaptic('Please enter your email address first');
      return;
    }

    if (sendEmailOTPMutation.isPending) return;

    try {
      const result = await sendEmailOTPMutation.mutateAsync({
        email: emailValue.trim().toLowerCase(),
        purpose: 'LOGIN',
      });

      if (result.success) {
        // Navigate to email verification screen with special parameter for OTP login
        navigation.navigate('EmailVerification', {
          email: emailValue.trim().toLowerCase(),
          isOTPLogin: true,
        });
      }
    } catch (error) {
      const errorMessage = getAuthErrorMessage(error);
      toast.errorWithHaptic(errorMessage);
    }
  };

  const handleBack = () => {
    navigation.goBack();
  };

  return (
    <SafeAreaView style={{ flex: 1, paddingTop: theme.spacing.sm_12 }} edges={['top']}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        <Box flex={1} backgroundColor="background">
          <NavigationTopBar title="Sign In" onPress={handleBack} />

          <ScrollableContainer
            gap="lg_24"
            contentContainerStyle={{
              flexGrow: 1,
              paddingHorizontal: 24,
              paddingBottom: Math.max(insets.bottom, 24),
            }}>
            {/* Title */}
            <Box gap="sm_12">
              <Text variant="h_24SemiBold_section" tx="auth.emailLoginTitle" color="text" />
              <Text
                variant="b_14Regular_input"
                tx="auth.emailLoginSubtitle"
                color="textSecondary"
              />
            </Box>

            {/* Form */}
            <Box gap="lg_24">
              <Controller
                control={control}
                name="email"
                render={({ field }) => (
                  <TextInput
                    label={t('auth.emailLabel')}
                    placeholder={t('auth.emailPlaceholder')}
                    autoCapitalize="none"
                    autoComplete="email"
                    autoCorrect={false}
                    enterKeyHint="next"
                    textContentType="emailAddress"
                    keyboardType="email-address"
                    onChangeText={value => {
                      field.onChange(value);
                      if (formState.errors.email?.message) {
                        trigger('email');
                      }
                    }}
                    error={formState.errors.email?.message}
                    onBlur={field.onBlur}
                    value={field.value}
                  />
                )}
              />

              <Controller
                control={control}
                name="password"
                render={({ field }) => (
                  <TextInput
                    label={t('auth.passwordLabel')}
                    placeholder={t('auth.passwordPlaceholder')}
                    autoCapitalize="none"
                    autoComplete="current-password"
                    autoCorrect={false}
                    enterKeyHint="done"
                    textContentType="password"
                    secureTextEntry={!showPassword}
                    trailing={
                      <InputRightElement
                        onPress={() => setShowPassword(!showPassword)}
                        icon={showPassword ? 'EyeSlash' : 'Eye'}
                      />
                    }
                    onChangeText={value => {
                      field.onChange(value);
                      if (formState.errors.password?.message) {
                        trigger('password');
                      }
                    }}
                    error={formState.errors.password?.message}
                    onBlur={field.onBlur}
                    value={field.value}
                  />
                )}
              />

              <Box alignItems="flex-end">
                <Button
                  title={t('auth.forgotPassword')}
                  variant="ghost"
                  onPress={handleForgotPassword}
                />
              </Box>
            </Box>

            {/* Sign In Button */}
            <Button
              tx="auth.signIn"
              variant="primary"
              onPress={() => {
                handleSubmit(handleEmailLogin)();
              }}
              enabled={Boolean(isFormValid && !emailLoginMutation.isPending)}
              loading={emailLoginMutation.isPending}
            />

            {/* Alternative: OTP Login */}
            <Box alignItems="center" gap="xs_8">
              <Text variant="l_12Regular_helperText" color="textTertiary">
                Or sign in without password
              </Text>
              <Button
                title="Send login code to email"
                variant="outline"
                onPress={handleOTPLogin}
                enabled={
                  !!emailValue && !sendEmailOTPMutation.isPending && !emailLoginMutation.isPending
                }
                loading={sendEmailOTPMutation.isPending}
              />
            </Box>

            {/* Sign Up Link */}
            <Box flexDirection="row" justifyContent="center" alignItems="center" gap="xxs_4">
              <Text variant="b_14Regular_input" tx="auth.dontHaveAccount" color="textSecondary" />
              <Button title={t('auth.signUp')} variant="ghost" onPress={handleSignUp} />
            </Box>
          </ScrollableContainer>
        </Box>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
