import { KeyboardAvoidingView, Platform } from 'react-native';

import { useNavigation } from '@react-navigation/native';
import { parsePhoneNumberFromString } from 'libphonenumber-js';
import { Controller } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import { z } from 'zod';

import { i18n } from '@/src/core';
import { AuthStackScreenProps } from '@/src/core/navigation/types';
import { Box, ScrollableContainer, Text, useTheme } from '@/src/core/theme';
import TextInput from '@/src/core/theme/TextInput';
import { Button, NavigationTopBar } from '@/src/shared/components';
import { useZodForm } from '@/src/shared/forms/useZodForm';
import { useToast } from '@/src/shared/hooks';

import { usePhoneRegister } from '../hooks';
import { PhoneRegistrationInput } from '../types';
import { getAuthErrorMessage, getAuthErrorTitle } from '../utils/errorMessages';

export const phoneSchema = () =>
  z.object({
    phone: z
      .string()
      .min(1, { message: i18n.t('validation.phone.required') })
      .refine(
        value => {
          try {
            // Try to parse with automatic country detection
            const phoneNumber = parsePhoneNumberFromString(value);
            if (phoneNumber && phoneNumber.isValid()) {
              return true;
            }

            // If no country code provided, try with default country (BR)
            if (!value.startsWith('+')) {
              const phoneNumberBR = parsePhoneNumberFromString(value, 'BR');
              return phoneNumberBR && phoneNumberBR.isValid();
            }

            return false;
          } catch {
            return false;
          }
        },
        {
          message: i18n.t('validation.phone.invalid'),
        }
      ),
  });

export default function SignUpScreen() {
  const navigation = useNavigation<AuthStackScreenProps<'Register'>['navigation']>();
  const { t } = useTranslation();
  const insets = useSafeAreaInsets();
  const theme = useTheme();
  const phoneRegisterMutation = usePhoneRegister();
  const toast = useToast();
  const { control, handleSubmit, formState, trigger, watch } = useZodForm(phoneSchema(), {
    mode: 'onBlur',
    reValidateMode: 'onChange',
  });

  const phoneValue = watch('phone');

  const handleSignUp = async (data: { phone: string }) => {
    if (phoneRegisterMutation.isPending) return;

    try {
      const input: PhoneRegistrationInput = {
        phone: data.phone,
      };

      const result = await phoneRegisterMutation.mutateAsync(input);

      if (result.success) {
        if (result.requiresVerification) {
          // Navigate to verification screen with phone number
          navigation.navigate('PhoneVerification', { phone: data.phone });
        } else {
          // Registration complete - the AppNavigator will automatically
          // switch to the Main flow when isAuthenticated becomes true
          console.log('Registration successful! Auth state will handle navigation.');
        }
      } else {
        // Use custom error handling for better UX
        const errorMessage = getAuthErrorMessage(
          result.errors?.[0].message || 'Registration failed'
        );
        const errorTitle = getAuthErrorTitle(result.errors?.[0].message || 'Registration failed');
        toast.errorWithHaptic(errorMessage, { title: errorTitle });
      }
    } catch (error) {
      // Enhanced error handling for authentication errors
      const errorMessage = getAuthErrorMessage(error);
      const errorTitle = getAuthErrorTitle(error);
      toast.errorWithHaptic(errorMessage, { title: errorTitle });
    }
  };

  const handleBack = () => {
    navigation.goBack();
  };

  return (
    <SafeAreaView style={{ flex: 1, paddingTop: theme.spacing.sm_12 }} edges={['top']}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        <Box flex={1} backgroundColor="background">
          <NavigationTopBar title="Movuca" onPress={handleBack} />

          <ScrollableContainer
            gap="lg_24"
            contentContainerStyle={{
              flexGrow: 1,
              paddingHorizontal: 24,
              paddingBottom: Math.max(insets.bottom, 24),
            }}>
            {/* Title */}
            <Box gap="sm_12">
              <Text
                variant="h_24SemiBold_section"
                tx="auth.phoneNumberRegistrationTitle"
                color="text"
              />

              <Controller
                control={control}
                name="phone"
                render={({ field }) => (
                  <TextInput
                    label={t('auth.phoneNumberRegistrationLabel')}
                    placeholder={t('auth.phoneNumberRegistrationPlaceholder')}
                    autoCapitalize="none"
                    autoComplete="off"
                    autoCorrect={false}
                    enterKeyHint="done"
                    textContentType="telephoneNumber"
                    keyboardType="phone-pad"
                    dataDetectorTypes="phoneNumber"
                    onChangeText={value => {
                      field.onChange(value);
                      if (formState.errors.phone?.message) {
                        trigger('phone');
                      }
                    }}
                    error={formState.errors.phone?.message}
                    onBlur={field.onBlur}
                    value={field.value}
                  />
                )}
              />
            </Box>

            <Button
              tx="auth.continue"
              variant="primary"
              onPress={() => {
                handleSubmit(handleSignUp)();
              }}
              enabled={Boolean(
                formState.isValid && !!phoneValue && !phoneRegisterMutation.isPending
              )}
              loading={phoneRegisterMutation.isPending}
            />
          </ScrollableContainer>
        </Box>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
