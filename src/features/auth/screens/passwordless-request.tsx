import { Alert, KeyboardAvoidingView, Platform } from 'react-native';

import { useNavigation } from '@react-navigation/native';
import { Controller } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import { z } from 'zod';

import { i18n } from '@/src/core';
import { AuthStackScreenProps } from '@/src/core/navigation/types';
import { Box, ScrollableContainer, Text, useTheme } from '@/src/core/theme';
import TextInput from '@/src/core/theme/TextInput';
import { Button, NavigationTopBar } from '@/src/shared/components';
import { useZodForm } from '@/src/shared/forms/useZodForm';

import { useRequestPasswordlessLogin } from '../hooks';

export const passwordlessRequestSchema = () =>
  z.object({
    email: z
      .string()
      .min(1, { message: i18n.t('validation.email.required') })
      .email({ message: i18n.t('validation.email.invalid') }),
  });

export default function PasswordlessRequestScreen() {
  const navigation = useNavigation<AuthStackScreenProps<'PasswordlessRequest'>['navigation']>();
  const { t } = useTranslation();
  const insets = useSafeAreaInsets();
  const theme = useTheme();
  const requestPasswordlessLoginMutation = useRequestPasswordlessLogin();

  const { control, handleSubmit, formState, trigger, watch } = useZodForm(
    passwordlessRequestSchema(),
    {
      mode: 'onBlur',
      reValidateMode: 'onChange',
    }
  );

  const emailValue = watch('email');
  const isFormValid = formState.isValid && emailValue;

  const handlePasswordlessRequest = async (data: { email: string }) => {
    if (requestPasswordlessLoginMutation.isPending) return;

    try {
      const result = await requestPasswordlessLoginMutation.mutateAsync(
        data.email.trim().toLowerCase()
      );

      if (result.success) {
        // Navigate to verification screen
        navigation.navigate('PasswordlessVerify', { email: data.email });
      } else {
        Alert.alert(
          i18n.t('common.error'),
          result.errors?.[0].message || i18n.t('common.tryAgain')
        );
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : i18n.t('common.tryAgain');
      Alert.alert(i18n.t('common.error'), message);
    }
  };

  const handleBack = () => {
    navigation.goBack();
  };

  const handleSignUp = () => {
    navigation.navigate('EmailSignup');
  };

  return (
    <SafeAreaView style={{ flex: 1, paddingTop: theme.spacing.sm_12 }} edges={['top']}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        <Box flex={1} backgroundColor="background">
          <NavigationTopBar title="Magic Link" onPress={handleBack} />

          <ScrollableContainer
            gap="lg_24"
            contentContainerStyle={{
              flexGrow: 1,
              paddingHorizontal: 24,
              paddingBottom: Math.max(insets.bottom, 24),
            }}>
            {/* Title */}
            <Box gap="sm_12">
              <Text variant="h_24SemiBold_section" tx="auth.passwordlessTitle" color="text" />
              <Text
                variant="b_14Regular_input"
                tx="auth.passwordlessSubtitle"
                color="textSecondary"
              />
            </Box>

            {/* Form */}
            <Box gap="lg_24">
              <Controller
                control={control}
                name="email"
                render={({ field }) => (
                  <TextInput
                    label={t('auth.emailLabel')}
                    placeholder={t('auth.emailPlaceholder')}
                    autoCapitalize="none"
                    autoComplete="email"
                    autoCorrect={false}
                    enterKeyHint="done"
                    textContentType="emailAddress"
                    keyboardType="email-address"
                    onChangeText={value => {
                      field.onChange(value);
                      if (formState.errors.email?.message) {
                        trigger('email');
                      }
                    }}
                    error={formState.errors.email?.message}
                    onBlur={field.onBlur}
                    value={field.value}
                  />
                )}
              />
            </Box>

            {/* Send Magic Link Button */}
            <Button
              tx="auth.sendMagicLink"
              variant="primary"
              onPress={() => {
                handleSubmit(handlePasswordlessRequest)();
              }}
              enabled={Boolean(isFormValid && !requestPasswordlessLoginMutation.isPending)}
              loading={requestPasswordlessLoginMutation.isPending}
            />

            {/* Info Text */}
            <Box gap="sm_12">
              <Text
                variant="b_14Regular_input"
                tx="auth.magicLinkInfo"
                color="textSecondary"
                textAlign="center"
              />
            </Box>

            {/* Sign Up Link */}
            <Box flexDirection="row" justifyContent="center" alignItems="center" gap="xxs_4">
              <Text variant="b_14Regular_input" tx="auth.dontHaveAccount" color="textSecondary" />
              <Button title={t('auth.signUp')} variant="ghost" onPress={handleSignUp} />
            </Box>
          </ScrollableContainer>
        </Box>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
