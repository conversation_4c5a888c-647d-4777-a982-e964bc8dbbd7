import React, { useRef, useState } from 'react';

import { useNavigation } from '@react-navigation/native';
import { useSharedValue, withSpring } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { useTranslation } from '@/src/core/i18n';
import { AuthStackScreenProps } from '@/src/core/navigation/types';
import { Box, Text } from '@/src/core/theme';
import { Button, Carousel, PaginationDot } from '@/src/shared/components';
import type { CarouselRef } from '@/src/shared/components';
import { IllustrationPlaceholder, Illustrations } from '@/src/shared/components/Placeholders';

import { useAuth } from '../services';

interface OnboardingSlide {
  id: string;
  titleKey: string;
  descriptionKey: string;
  illustration: typeof Illustrations.ONBOARDING_WELCOME;
}

export default function OnboardingScreen() {
  const navigation = useNavigation<AuthStackScreenProps<'Onboarding'>['navigation']>();
  const insets = useSafeAreaInsets();
  const { completeOnboarding } = useAuth();
  const { t } = useTranslation();

  const onboardingData: OnboardingSlide[] = [
    {
      id: '1',
      titleKey: 'onboarding.welcome.title',
      descriptionKey: 'onboarding.welcome.description',
      illustration: Illustrations.ONBOARDING_WELCOME,
    },
    {
      id: '2',
      titleKey: 'onboarding.share.title',
      descriptionKey: 'onboarding.share.description',
      illustration: Illustrations.ONBOARDING_SHARE,
    },
    {
      id: '3',
      titleKey: 'onboarding.connect.title',
      descriptionKey: 'onboarding.connect.description',
      illustration: Illustrations.ONBOARDING_CONNECT,
    },
  ];

  const [currentIndex, setCurrentIndex] = useState(0);
  const carouselRef = useRef<CarouselRef>(null);
  const progress = useSharedValue(0);
  const scrollProgress = useSharedValue(0);

  const handlePageScroll = ({ position, offset }: { position: number; offset: number }) => {
    // Calculate smooth progress from 0 to length-1
    scrollProgress.value = position + offset;
  };

  const handlePageSelected = (index: number) => {
    setCurrentIndex(index);
    progress.value = withSpring((index + 1) / onboardingData.length);
  };

  const handleNext = () => {
    if (currentIndex < onboardingData.length - 1) {
      carouselRef.current?.setPage(currentIndex + 1);
    } else {
      handleComplete();
    }
  };

  const handleSkip = () => {
    handleComplete();
  };

  const handleComplete = () => {
    completeOnboarding();

    // After onboarding, take user to auth choice to sign up or sign in
    navigation.replace('AuthChoice');
  };

  const renderSlide = (item: OnboardingSlide) => (
    <Box
      key={item.id}
      width="100%"
      flex={1}
      justifyContent="center"
      alignItems="center"
      paddingHorizontal="lg_24">
      {/* Illustration */}
      <Box marginBottom="xxl_40">
        <IllustrationPlaceholder
          illustrationName={item.illustration.name}
          description={item.illustration.description}
          width={240}
          height={200}
          backgroundColor="primaryLight"
        />
      </Box>

      {/* Content */}
      <Box alignItems="center" maxWidth={300}>
        <Text variant="h_32SemiBold_Page" textAlign="center" color="text" marginBottom="md_16">
          {t(item.titleKey)}
        </Text>
        <Text variant="b_16Regular_input" textAlign="center" color="textSecondary" lineHeight={24}>
          {t(item.descriptionKey)}
        </Text>
      </Box>
    </Box>
  );

  const renderPagination = () => (
    <Box
      flexDirection="row"
      justifyContent="center"
      alignItems="center"
      gap="xs_8"
      marginBottom="xl_32">
      {onboardingData.map((_, index) => (
        <PaginationDot
          key={index}
          index={index}
          scrollProgress={scrollProgress}
          currentIndex={currentIndex}
        />
      ))}
    </Box>
  );

  return (
    <Box flex={1} backgroundColor="background">
      {/* Skip Button */}
      <Box alignItems="flex-end" paddingHorizontal="lg_24" style={{ paddingTop: insets.top + 16 }}>
        <Button title={t('onboarding.skip')} variant="ghost" onPress={handleSkip} />
      </Box>

      {/* Slides */}
      <Carousel
        ref={carouselRef}
        style={{ flex: 1 }}
        initialPage={0}
        onPageScroll={handlePageScroll}
        onPageSelected={handlePageSelected}>
        {onboardingData.map(renderSlide)}
      </Carousel>

      {/* Footer */}
      <Box paddingHorizontal="lg_24" style={{ paddingBottom: Math.max(insets.bottom, 24) }}>
        {renderPagination()}

        <Button
          title={
            currentIndex === onboardingData.length - 1
              ? t('onboarding.getStarted')
              : t('onboarding.next')
          }
          variant="primary"
          onPress={handleNext}
        />
      </Box>
    </Box>
  );
}
