import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

import { secureStorage } from '@/src/core/storage';
import { authOperationManager } from '@/src/core/utils/authOperationManager';

import { AuthState, AuthTokens, User } from '../types';

export const useAuthStore = create<AuthState>()(
  subscribeWithSelector(set => ({
    // Initial state
    user: null,
    tokens: null,
    isInitializing: true,
    isSigningOut: false,
    isAuthenticated: false,
    isOnboardingComplete: false,

    // Basic setters
    setUser: user => {
      set(state => ({
        user,
        isAuthenticated: !!(user && state.tokens), // Only authenticated if both user AND tokens exist
      }));
      if (user) {
        secureStorage.setObject('auth.user', user);
      } else {
        secureStorage.delete('auth.user');
      }
    },
    setIsAuthenticated: isAuthenticated => set({ isAuthenticated }),

    setTokens: tokens => {
      set(state => ({
        tokens,
        isAuthenticated: !!(tokens && state.user), // Only authenticated if both tokens AND user exist
      }));
      if (tokens) {
        secureStorage.setObject('auth.tokens', tokens);
        // Register background token refresh when tokens are set
        // Using dynamic import to avoid circular dependency
        import('@/src/core/services/tokenRefreshBackground')
          .then(({ registerTokenRefreshTask }) => {
            registerTokenRefreshTask().catch(error => {
              console.error('Failed to register token refresh task:', error);
            });
          })
          .catch(error => {
            console.error('Failed to import token refresh module:', error);
          });
      } else {
        secureStorage.delete('auth.tokens');
        // Unregister background token refresh when tokens are cleared
        // Using dynamic import to avoid circular dependency
        import('@/src/core/services/tokenRefreshBackground')
          .then(({ unregisterTokenRefreshTask }) => {
            unregisterTokenRefreshTask().catch(error => {
              // Only log warning, don't treat as critical error
              console.warn('⚠️ Failed to unregister token refresh task:', error.message);
            });
          })
          .catch(error => {
            console.warn('⚠️ Failed to import token refresh module:', error.message);
          });
      }
    },

    setIsInitializing: isInitializing => set({ isInitializing }),
    setIsSigningOut: isSigningOut => set({ isSigningOut }),

    setOnboardingComplete: complete => {
      set({ isOnboardingComplete: complete });
      secureStorage.setString('auth.onboarding', complete ? 'true' : 'false');
    },

    // Complex actions
    initialize: async () => {
      try {
        console.log('🔄 Initializing auth state...');
        set({ isInitializing: true });

        // Load tokens and user from secure storage
        const storedTokens = secureStorage.getObject<AuthTokens>('auth.tokens');
        const storedUser = secureStorage.getObject<User>('auth.user');
        const onboardingComplete = secureStorage.getString('auth.onboarding') === 'true';

        if (storedTokens) {
          console.log('🔐 Found stored tokens, setting auth state');
          set({
            tokens: storedTokens,
            isAuthenticated: !!(storedTokens && storedUser), // Only authenticated if both exist
            user: storedUser || null,
            isOnboardingComplete: onboardingComplete,
          });

          // Background user refresh (non-blocking)
          if (storedUser) {
            // We have user data, set up background refresh
            setTimeout(async () => {
              try {
                console.log('🔄 Background: Refreshing user data...');
                // This would call the auth service to refresh user data
                // authService.getCurrentUser() etc.
              } catch (error) {
                console.log('📱 Background user refresh failed, continuing with stored data');
              }
            }, 1000);
          }
        } else {
          console.log('🔓 No stored tokens found');
          set({
            tokens: null,
            isAuthenticated: false,
            user: null,
            isOnboardingComplete: false,
          });
        }
      } catch (error) {
        console.error('❌ Auth initialization error:', error);
        // Don't set error state anymore - let TanStack Query handle operation-specific errors
        set({
          tokens: null,
          isAuthenticated: false,
          user: null,
        });
      } finally {
        set({ isInitializing: false });
        console.log('✅ Auth initialization complete');
      }
    },

    signOut: async () => {
      return authOperationManager.executeSignOut(async () => {
        try {
          set({ isSigningOut: true });

          // Unregister background token refresh
          try {
            const { unregisterTokenRefreshTask } = await import(
              '@/src/core/services/tokenRefreshBackground'
            );
            await unregisterTokenRefreshTask();
          } catch (error: any) {
            console.warn('⚠️ Failed to unregister token refresh task:', error.message);
            // Continue with sign out even if unregister fails
          }

          // Clear all auth data
          secureStorage.delete('auth.tokens');
          secureStorage.delete('auth.user');
          secureStorage.delete('auth.onboarding');

          set({
            user: null,
            tokens: null,
            isAuthenticated: false,
            isOnboardingComplete: false,
          });

          console.log('✅ Sign out complete');
        } catch (error) {
          console.error('❌ Sign out error:', error);
          // Don't set error state - if signOut fails, we still want to clear local state
        } finally {
          set({ isSigningOut: false });
        }
      });
    },

    reset: () => {
      set({
        user: null,
        tokens: null,
        isInitializing: false,
        isSigningOut: false,
        isAuthenticated: false,
        isOnboardingComplete: false,
      });
    },

    // Clear auth state without the signing out flag
    clearAuth: async () => {
      console.log('🔄 clearAuth called - starting logout process');

      // Unregister background token refresh
      try {
        const { unregisterTokenRefreshTask } = await import(
          '@/src/core/services/tokenRefreshBackground'
        );
        await unregisterTokenRefreshTask();
      } catch (error: any) {
        console.warn('⚠️ Failed to unregister token refresh task:', error.message);
        // Continue even if unregister fails
      }

      // Clear storage
      secureStorage.delete('auth.tokens');
      secureStorage.delete('auth.user');

      // Update state
      set({
        user: null,
        tokens: null,
        isAuthenticated: false,
      });

      console.log('✅ clearAuth completed - user should be logged out');
    },
  }))
);

// Selectors for common use cases
export const useIsAuthenticated = () => useAuthStore(state => state.isAuthenticated);
export const useIsInitializing = () => useAuthStore(state => state.isInitializing);
export const useAuthUser = () => useAuthStore(state => state.user);
export const useAuthTokens = () => useAuthStore(state => state.tokens);

// Initialize auth on store creation
useAuthStore.getState().initialize();
