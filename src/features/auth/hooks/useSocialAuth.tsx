import React, { useCallback } from 'react';

import { Alert, Platform } from 'react-native';

import * as AppleAuthentication from 'expo-apple-authentication';
import { makeRedirectUri } from 'expo-auth-session';
import * as Google from 'expo-auth-session/providers/google';
import * as WebBrowser from 'expo-web-browser';

import { useAuth } from '../services';
import { SocialLoginInput } from '../types';
import { useSocialLogin } from './useAuthMutations';

WebBrowser.maybeCompleteAuthSession();

interface UseSocialAuthReturn {
  appleSignIn: () => Promise<void>;
  googleSignIn: () => Promise<void>;
  isAppleAvailable: boolean;
}

export const useSocialAuth = (): UseSocialAuthReturn => {
  const socialLoginMutation = useSocialLogin();

  const [isAppleAvailable, setIsAppleAvailable] = React.useState(false);

  // Google Sign In configuration
  const [request, response, promptAsync] = Google.useIdTokenAuthRequest({
    clientId: process.env.EXPO_PUBLIC_GOOGLE_CLIENT_ID || '',
    redirectUri: makeRedirectUri({
      scheme: 'com.movuca.app',
    }),
  });

  // Check if Apple Sign In is available
  React.useEffect(() => {
    if (Platform.OS === 'ios') {
      AppleAuthentication.isAvailableAsync().then(setIsAppleAvailable);
    }
  }, []);

  const handleSocialLogin = useCallback(
    async (input: SocialLoginInput) => {
      try {
        const result = await socialLoginMutation.mutateAsync(input);

        if (result.success) {
          // Navigation will be handled by auth guards
          if (result.isNewUser) {
            // New user might need onboarding
            console.log('New user from social login');
          }
        } else {
          Alert.alert('Error', result.errors?.[0].message || 'Social login failed');
        }
      } catch (error) {
        console.error('Social login error:', error);
        Alert.alert('Error', 'An unexpected error occurred');
      }
    },
    [socialLoginMutation]
  );

  // Handle Google response
  React.useEffect(() => {
    if (response?.type === 'success') {
      const { id_token } = response.params;
      handleSocialLogin({
        provider: 'google',
        accessToken: id_token,
        profile: {
          id: response.params.id,
          provider: 'google',
          email: response.params.email,
          name: response.params.given_name,
          avatar: response.params.picture,
        },
      });
    }
  }, [handleSocialLogin, response]);

  const appleSignIn = async () => {
    try {
      const credential = await AppleAuthentication.signInAsync({
        requestedScopes: [
          AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
          AppleAuthentication.AppleAuthenticationScope.EMAIL,
        ],
      });

      await handleSocialLogin({
        provider: 'apple',
        accessToken: credential.identityToken!,
        profile: {
          id: credential.user,
          provider: 'apple',
          email: credential.email || undefined,
          name: credential.fullName
            ? `${credential.fullName.givenName || ''} ${credential.fullName.familyName || ''}`.trim()
            : undefined,
          avatar: credential.user,
        },
      });
    } catch (error: any) {
      if (error.code === 'ERR_CANCELED') {
        // User canceled the sign in
        return;
      }
      console.error('Apple Sign In error:', error);
      Alert.alert('Error', 'Apple Sign In failed');
    }
  };

  const googleSignIn = async () => {
    try {
      const result = await promptAsync();
      // Response handling is done in the useEffect above
    } catch (error) {
      console.error('Google Sign In error:', error);
      Alert.alert('Error', 'Google Sign In failed');
    }
  };

  return {
    appleSignIn,
    googleSignIn,
    isAppleAvailable,
  };
};
