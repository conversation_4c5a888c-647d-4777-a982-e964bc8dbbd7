import { useMutation } from '@tanstack/react-query';

import { useToast } from '@/src/shared/hooks';
import { extractErrorMessage } from '@/src/shared/utils/errors';

import { authService } from '../services/auth.service';
import { useAuthStore } from '../store/authStore';
import {
  EmailLoginInput,
  EmailRegistrationInput,
  OTPLoginInput,
  OTPRegistrationInput,
  OTPVerificationInput,
  PhoneLoginInput,
  PhoneRegistrationInput,
  SocialLoginInput,
  VerificationInput,
} from '../types';

/**
 * TanStack Query mutations for authentication operations
 * Each mutation has independent loading/error states to prevent conflicts
 */

// Helper to extract error message from auth service response

// Phone Authentication
export const usePhoneRegister = () => {
  const { setTokens, setUser } = useAuthStore();
  const toast = useToast();

  return useMutation({
    mutationFn: (input: PhoneRegistrationInput) => authService.phoneRegister(input),
    onSuccess: response => {
      if (response.success) {
        if (!response.requiresVerification && response.tokens) {
          setTokens(response.tokens);
          setUser(response.user);
        }
        return {
          success: true,
          requiresVerification: response.requiresVerification,
        };
      }
    },
    onError: (error, variables, context) => {
      const message = extractErrorMessage(error, 'Registration failed');
      toast.errorWithHaptic(message);
    },
  });
};

export const usePhoneLogin = () => {
  const { setTokens, setUser } = useAuthStore();
  const toast = useToast();

  return useMutation({
    mutationFn: (input: PhoneLoginInput) => authService.phoneLogin(input),
    onSuccess: response => {
      if (response.success && response.tokens) {
        setTokens(response.tokens);
        setUser(response.user);
      }
    },
    onError: error => {
      const message = extractErrorMessage(error, 'Login failed');
      toast.errorWithHaptic(message);
    },
  });
};

export const useVerifyPhoneOTP = () => {
  const { setTokens, setUser } = useAuthStore();
  const toast = useToast();

  return useMutation({
    mutationFn: ({ phone, code }: { phone: string; code: string }) =>
      authService.verifyPhoneOTP(phone, code),
    onSuccess: response => {
      if (response.success && response.tokens) {
        setTokens(response.tokens);
        setUser(response.user);
      }
    },
    onError: error => {
      const message = extractErrorMessage(error, 'Verification failed');
      toast.errorWithHaptic(message);
    },
  });
};

// Email Authentication
export const useEmailRegister = () => {
  const { setTokens, setUser } = useAuthStore();
  const toast = useToast();

  return useMutation({
    mutationFn: (input: EmailRegistrationInput) => authService.emailRegister(input),
    onSuccess: async response => {
      if (response.success) {
        if (!response.requiresVerification && response.tokens) {
          setTokens(response.tokens);

          if (response.user) {
            setUser(response.user);
          } else {
            // Fetch user data using the new tokens
            try {
              const currentUser = await authService.getCurrentUser();
              if (currentUser) {
                setUser(currentUser);
              }
            } catch (userError) {
              console.error('Failed to fetch user after registration:', userError);
            }
          }
        }

        // Show success toast for registration
        if (!response.requiresVerification) {
          toast.successWithHaptic('🎉 Account created successfully!');
        }
      }
    },
    onError: error => {
      const message = extractErrorMessage(error, 'Registration failed');
      toast.errorWithHaptic(message);
    },
  });
};

export const useEmailLogin = () => {
  const { setTokens, setUser } = useAuthStore();
  const toast = useToast();

  return useMutation({
    mutationFn: (input: EmailLoginInput) => authService.emailLogin(input),
    onSuccess: async response => {
      if (response.success && response.tokens) {
        setTokens(response.tokens);

        if (response.user) {
          setUser(response.user);
        } else {
          // Fetch user data using the new tokens
          try {
            const currentUser = await authService.getCurrentUser();
            if (currentUser) {
              setUser(currentUser);
            }
          } catch (userError) {
            console.error('Failed to fetch user after login:', userError);
          }
        }
      }
    },
    onError: error => {
      const message = extractErrorMessage(error, 'Login failed');
      toast.errorWithHaptic(message);
    },
  });
};

// OTP Authentication
export const useOTPRegister = () => {
  const toast = useToast();

  return useMutation({
    mutationFn: (input: OTPRegistrationInput) => authService.otpRegister(input),
    onSuccess: response => {
      if (!response.success) {
        toast.errorWithHaptic('🚫 Registration failed');
        return;
      }

      // For OTP registration, we DON'T set tokens/user until email verification
      // The tokens are returned in response but not persisted yet
      toast.successWithHaptic(
        '🎉 Registration successful! Check your email for verification code.'
      );
    },
    onError: error => {
      const message = extractErrorMessage(error, 'OTP registration failed');
      toast.errorWithHaptic(message);
    },
  });
};

export const useSendEmailOTP = () => {
  const toast = useToast();

  return useMutation({
    mutationFn: ({
      email,
      purpose,
    }: {
      email: string;
      purpose?: 'LOGIN' | 'REGISTRATION' | 'VERIFICATION';
    }) => authService.sendEmailOTP(email, purpose),
    onSuccess: response => {
      if (response.success) {
        toast.successWithHaptic('🔐 Verification code sent to your email!');
      }
    },
    onError: error => {
      const message = extractErrorMessage(error, 'Failed to send OTP');
      toast.errorWithHaptic(message);
    },
  });
};

export const useOTPLogin = () => {
  const { setTokens, setUser } = useAuthStore();
  const toast = useToast();

  return useMutation({
    mutationFn: (input: OTPLoginInput) => authService.otpLogin(input),
    onSuccess: response => {
      if (response.success && response.tokens) {
        setTokens(response.tokens);
        setUser(response.user);
        toast.successWithHaptic('✅ Login successful!');
      }
    },
    onError: error => {
      const message = extractErrorMessage(error, 'OTP login failed');
      toast.errorWithHaptic(message);
    },
  });
};

export const useVerifyEmailOTP = () => {
  const { setUser, setTokens } = useAuthStore();
  const toast = useToast();

  return useMutation({
    mutationFn: (input: OTPVerificationInput) => authService.verifyEmailOTP(input),
    onSuccess: async response => {
      if (response.success) {
        toast.successWithHaptic('✅ Email verified successfully!');

        // If the service returned user and tokens, set them
        if (response.user && response.tokens) {
          setUser(response.user);
          setTokens(response.tokens);
        }
      }
    },
    onError: error => {
      const message = extractErrorMessage(error, 'OTP verification failed');
      toast.errorWithHaptic(message);
    },
  });
};

// Passwordless Authentication
export const useRequestPasswordlessLogin = () => {
  return useMutation({
    mutationFn: (email: string) => authService.requestPasswordlessLogin(email),
  });
};

export const useVerifyPasswordlessLogin = () => {
  const { setTokens, setUser } = useAuthStore();
  const toast = useToast();

  return useMutation({
    mutationFn: ({ email, token }: { email: string; token: string }) =>
      authService.verifyPasswordlessLogin(email, token),
    onSuccess: response => {
      if (response.success && response.tokens) {
        setTokens(response.tokens);
        setUser(response.user);
      }
    },
    onError: error => {
      const message = extractErrorMessage(error, 'Verification failed');
      toast.errorWithHaptic(message);
    },
  });
};

// Social Authentication
export const useSocialLogin = () => {
  const { setTokens, setUser } = useAuthStore();
  const toast = useToast();

  return useMutation({
    mutationFn: (input: SocialLoginInput) => authService.socialLogin(input),
    onSuccess: response => {
      if (response.success && response.tokens) {
        setTokens(response.tokens);
        setUser(response.user);
      }
    },
    onError: error => {
      const message = extractErrorMessage(error, 'Social login failed');
      toast.errorWithHaptic(message);
    },
  });
};

// Verification
export const useVerifyAccount = () => {
  const { setUser } = useAuthStore();

  return useMutation({
    mutationFn: (input: VerificationInput) => authService.verifyAccount(input),
    onSuccess: response => {
      if (response.success && response.user) {
        setUser(response.user);
      }
    },
  });
};

export const useResendVerification = () => {
  return useMutation({
    mutationFn: ({ type, identifier }: { type: 'email' | 'phone'; identifier: string }) =>
      authService.resendVerification(type, identifier),
  });
};

// Password Reset
export const useResetPassword = () => {
  return useMutation({
    mutationFn: (email: string) => authService.requestPasswordReset(email),
  });
};
