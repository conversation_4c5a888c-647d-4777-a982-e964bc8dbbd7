import { ApolloError } from '@apollo/client';

import { ApolloClientType, client } from '@/src/core/api/config';
import { GraphQLErrorResponse } from '@/src/core/api/errors';
import { secureStorage } from '@/src/core/storage';
import { authOperationManager } from '@/src/core/utils/authOperationManager';

import {
  AuthErrorCode,
  AuthResponse,
  AuthTokens,
  EmailLoginInput,
  EmailRegistrationInput,
  LinkAccountInput,
  OTPLoginInput,
  OTPRegistrationInput,
  OTPVerificationInput,
  PhoneLoginInput,
  PhoneRegistrationInput,
  ResetPasswordInput,
  SocialLoginInput,
  User,
  VerificationInput,
} from '../types/auth.types';
import { AuthError } from '../utils/errorMessages';
import {
  EMAIL_LOGIN_MUTATION,
  EMAIL_REGISTER_MUTATION,
  GET_CURRENT_USER_QUERY,
  LINK_ACCOUNT_MUTATION,
  LOGIN_WITH_OTP_MUTATION,
  LOGOUT_MUTATION,
  PASSWORDLESS_REQUEST_MUTATION,
  PASSWORDLESS_VERIFY_MUTATION,
  PHONE_LOGIN_MUTATION,
  PHONE_REGISTER_MUTATION,
  PHONE_VERIFY_OTP_MUTATION,
  REFRESH_TOKEN_MUTATION,
  REGISTER_WITH_OTP_MUTATION,
  REQUEST_PASSWORD_RESET_MUTATION,
  RESEND_VERIFICATION_MUTATION,
  RESET_PASSWORD_MUTATION,
  SEND_EMAIL_OTP_MUTATION,
  SOCIAL_LOGIN_MUTATION,
  VERIFY_ACCOUNT_MUTATION,
  VERIFY_EMAIL_OTP_MUTATION,
} from './mutations.graphql';

export type Response =
  | (AuthResponse & { success: true; isNewUser?: boolean })
  | (AuthError & { success?: false });

interface OTPRegisterResponse {
  token: string;
  refreshToken: string;
  user: User;
}

interface OTPVerifyResponse {
  success: boolean;
  message: string;
  otpId: string | null;
}

/**
 * Authentication Service
 * Handles all authentication operations with GraphQL backend
 */
class AuthService {
  private client: ApolloClientType;
  private readonly TOKEN_KEY = 'auth.tokens';
  private readonly USER_KEY = 'auth.user';

  constructor(client: ApolloClientType) {
    this.client = client;
  }

  // Token Management
  private saveTokens(tokens: AuthTokens) {
    secureStorage.setObject(this.TOKEN_KEY, tokens);
  }

  private getTokens() {
    return secureStorage.getObject<AuthTokens>(this.TOKEN_KEY) || null;
  }

  private clearTokens() {
    secureStorage.delete(this.TOKEN_KEY);
  }

  private saveUser(user: User) {
    secureStorage.setObject(this.USER_KEY, user);
  }

  private clearUser() {
    secureStorage.delete(this.USER_KEY);
  }

  // Phone Authentication
  async phoneRegister(input: PhoneRegistrationInput): Promise<Response> {
    try {
      const { data, errors } = await this.client.mutate({
        mutation: PHONE_REGISTER_MUTATION,
        variables: { input },
      });

      const response = data.registerWithPhone;

      if (response.token) {
        // Map the API response to our AuthTokens format
        const tokens: AuthTokens = {
          accessToken: response.token,
          refreshToken: response.refreshToken,
          expiresIn: 3600, // Default since API doesn't return this
        };

        this.saveTokens(tokens);
        this.saveUser(response.user);
        return {
          success: true,
          user: response.user,
          tokens: tokens,
          requiresVerification: false, // API doesn't return this
        };
      }

      return {
        success: false,
        errors: errors as GraphQLErrorResponse<AuthErrorCode>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR' as AuthErrorCode,
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  async phoneLogin(input: PhoneLoginInput): Promise<Response> {
    try {
      const { data, errors } = await this.client.mutate({
        mutation: PHONE_LOGIN_MUTATION,
        variables: { input },
      });

      const response = data.loginWithPhone;
      if (response.token && response.user) {
        // Map the API response to our AuthTokens format
        const tokens: AuthTokens = {
          accessToken: response.token,
          refreshToken: response.refreshToken,
          expiresIn: 3600, // Default since API doesn't return this
        };

        this.saveTokens(tokens);
        this.saveUser(response.user);
        return {
          success: true,
          user: response.user,
          tokens: tokens,
        };
      }

      return {
        success: false,
        errors: errors as GraphQLErrorResponse<AuthErrorCode>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR' as AuthErrorCode,
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  async verifyPhoneOTP(phone: string, code: string): Promise<Response> {
    try {
      const { data, errors } = await this.client.mutate({
        mutation: PHONE_VERIFY_OTP_MUTATION,
        variables: { phone, code },
      });

      const response = data.verifyPhoneOTP;
      if (response.tokens && response.user) {
        this.saveTokens(response.tokens);
        this.saveUser(response.user);
        return {
          success: true,
          user: response.user,
          tokens: response.tokens,
        };
      }

      return {
        success: false,
        errors: errors as GraphQLErrorResponse<AuthErrorCode>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR' as AuthErrorCode,
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  // Email Authentication
  async emailRegister(input: EmailRegistrationInput): Promise<Response> {
    try {
      const { data, errors } = await this.client.mutate({
        mutation: EMAIL_REGISTER_MUTATION,
        variables: { input },
      });

      const response = data?.register;

      if (response?.token && response?.refreshToken) {
        const tokens = {
          accessToken: response.token,
          refreshToken: response.refreshToken,
          expiresIn: 3600, // Default to 1 hour if not provided
        };
        this.saveTokens(tokens);

        // Since this endpoint doesn't return user data, we'll need to fetch it
        // For now, return success response
        return {
          success: true,
          tokens,
          user: null, // Will be fetched separately
          requiresVerification: false,
        };
      }

      return {
        success: false,
        errors: errors as GraphQLErrorResponse<AuthErrorCode>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR' as AuthErrorCode,
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  async emailLogin(input: EmailLoginInput): Promise<Response> {
    try {
      const { data, errors } = await this.client.mutate({
        mutation: EMAIL_LOGIN_MUTATION,
        variables: { input },
      });

      const response = data?.login;

      if (response?.token && response?.refreshToken) {
        const tokens = {
          accessToken: response.token,
          refreshToken: response.refreshToken,
          expiresIn: 3600, // Default to 1 hour if not provided
        };
        this.saveTokens(tokens);

        // Since this endpoint doesn't return user data, we'll need to fetch it
        // For now, return success response
        return {
          success: true,
          tokens,
          user: null, // Will be fetched separately
        };
      }

      return {
        success: false,
        errors: errors as GraphQLErrorResponse<AuthErrorCode>[],
      };
    } catch (error: any) {
      // Handle network errors, Apollo client errors, etc.
      // Convert them to our standardized error format
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR' as AuthErrorCode,
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  // OTP Authentication
  async otpRegister(input: OTPRegistrationInput): Promise<Response> {
    try {
      const { data, errors } = await this.client.mutate<{ registerWithOTP: OTPRegisterResponse }>({
        mutation: REGISTER_WITH_OTP_MUTATION,
        variables: { email: input.email, name: input.name },
      });

      const response = data?.registerWithOTP;

      if (response?.user) {
        const tokens = {
          accessToken: response.token,
          refreshToken: response.refreshToken,
          expiresIn: 3600,
        };

        // Save tokens for OTP registration, but don't save user until verification
        // This allows API calls to work but keeps user as unauthenticated
        this.saveTokens(tokens);

        return {
          success: true,
          tokens,
          user: response.user || null,
          requiresVerification: true, // OTP registration always requires verification
        };
      }

      return {
        success: false,
        errors: errors as GraphQLErrorResponse<AuthErrorCode>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR' as AuthErrorCode,
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  async sendEmailOTP(
    email: string,
    purpose: 'LOGIN' | 'REGISTRATION' | 'VERIFICATION' = 'LOGIN'
  ): Promise<{
    success: boolean;
    message?: string;
    otpId?: string;
    errors?: GraphQLErrorResponse<AuthErrorCode>[];
  }> {
    try {
      const { data, errors } = await this.client.mutate<{
        sendEmailOTP: { success: boolean; message: string; otpId: string };
      }>({
        mutation: SEND_EMAIL_OTP_MUTATION,
        variables: { email, purpose },
      });

      const response = data?.sendEmailOTP;

      if (response?.success) {
        return {
          success: true,
          message: response.message,
          otpId: response.otpId,
        };
      }

      return {
        success: false,
        errors: errors as GraphQLErrorResponse<AuthErrorCode>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR' as AuthErrorCode,
              statusCode: error.statusCode || 500,
            },
          },
        ],
      };
    }
  }

  async otpLogin(input: OTPLoginInput): Promise<Response> {
    try {
      const { data, errors } = await this.client.mutate({
        mutation: LOGIN_WITH_OTP_MUTATION,
        variables: { email: input.email, password: input.code },
      });

      const response = data?.loginWithOTP;

      if (response?.token && response?.user) {
        const tokens = {
          accessToken: response.token,
          refreshToken: response.refreshToken,
          expiresIn: 3600,
        };

        this.saveTokens(tokens);
        this.saveUser(response.user);

        return {
          success: true,
          tokens,
          user: response.user,
        };
      }

      return {
        success: false,
        errors: errors as GraphQLErrorResponse<AuthErrorCode>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR' as AuthErrorCode,
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  async verifyEmailOTP(input: OTPVerificationInput): Promise<Response> {
    try {
      const { data, errors } = await this.client.mutate<{ verifyEmailOTP: OTPVerifyResponse }>({
        mutation: VERIFY_EMAIL_OTP_MUTATION,
        variables: { email: input.email, code: input.code },
      });

      const response = data?.verifyEmailOTP;

      if (response?.success) {
        // After successful OTP verification, fetch the current user to complete authentication
        try {
          const user = await this.getCurrentUser();
          if (user) {
            // Now save the user to complete authentication
            this.saveUser(user);
            return {
              success: true,
              user,
              requiresVerification: false,
              tokens: this.getTokens(), // Get tokens that were stored during registration
            };
          }
        } catch (error) {
          console.error('Failed to get user after OTP verification:', error);
        }

        return {
          success: true,
          user: null,
          requiresVerification: false,
          tokens: null,
        };
      }

      return {
        success: false,
        errors: errors as GraphQLErrorResponse<AuthErrorCode>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR' as AuthErrorCode,
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  // Passwordless Authentication
  async requestPasswordlessLogin(email: string): Promise<{
    success: boolean;
    message?: string;
    errors?: GraphQLErrorResponse<AuthErrorCode>[];
  }> {
    try {
      const { data, errors } = await this.client.mutate<{ requestPasswordlessLogin: Response }>({
        mutation: PASSWORDLESS_REQUEST_MUTATION,
        variables: { email },
      });

      if (data?.requestPasswordlessLogin?.success) {
        return data.requestPasswordlessLogin;
      }

      return {
        success: false,
        errors: errors as GraphQLErrorResponse<AuthErrorCode>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR' as AuthErrorCode,
              statusCode: error.statusCode || 500,
            },
          },
        ],
      };
    }
  }

  async verifyPasswordlessLogin(email: string, token: string): Promise<Response> {
    try {
      const { data, errors } = await this.client.mutate({
        mutation: PASSWORDLESS_VERIFY_MUTATION,
        variables: { email, token },
      });

      const response = data.verifyPasswordlessLogin;
      if (response.tokens && response.user) {
        this.saveTokens(response.tokens);
        this.saveUser(response.user);
        return {
          success: true,
          user: response.user,
          tokens: response.tokens,
          isNewUser: response.isNewUser,
        };
      }

      return {
        success: false,
        errors: errors as GraphQLErrorResponse<AuthErrorCode>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR' as AuthErrorCode,
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  // Social Authentication
  async socialLogin(input: SocialLoginInput): Promise<Response> {
    try {
      const { data, errors } = await this.client.mutate({
        mutation: SOCIAL_LOGIN_MUTATION,
        variables: { input },
      });

      const response = data.socialLogin;
      if (response.tokens && response.user) {
        this.saveTokens(response.tokens);
        this.saveUser(response.user);
        return {
          success: true,
          user: response.user,
          tokens: response.tokens,
          isNewUser: response.isNewUser,
        };
      }

      return {
        success: false,
        errors: errors as GraphQLErrorResponse<AuthErrorCode>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR' as AuthErrorCode,
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  // Verification
  async verifyAccount(
    input: VerificationInput
  ): Promise<{ success: boolean; user?: User; errors?: GraphQLErrorResponse<AuthErrorCode>[] }> {
    try {
      const { data, errors } = await this.client.mutate({
        mutation: VERIFY_ACCOUNT_MUTATION,
        variables: { input },
      });

      const response = data.verifyAccount;
      if (response.success) {
        this.saveUser(response.user);
        return response;
      }

      return {
        success: false,
        errors: errors as GraphQLErrorResponse<AuthErrorCode>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR' as AuthErrorCode,
              statusCode: error.statusCode || 500,
            },
          },
        ],
      };
    }
  }

  async resendVerification(
    type: 'email' | 'phone',
    identifier: string
  ): Promise<{
    success: boolean;
    message?: string;
    errors?: GraphQLErrorResponse<AuthErrorCode>[];
  }> {
    try {
      const { data, errors } = await this.client.mutate({
        mutation: RESEND_VERIFICATION_MUTATION,
        variables: { input: { type, identifier } },
      });

      if (data?.resendVerification?.success) {
        return data.resendVerification;
      }

      return {
        success: false,
        errors: errors as GraphQLErrorResponse<AuthErrorCode>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR' as AuthErrorCode,
              statusCode: error.statusCode || 500,
            },
          },
        ],
      };
    }
  }

  // Password Reset
  async requestPasswordReset(email: string): Promise<{
    success: boolean;
    message?: string;
    errors?: GraphQLErrorResponse<AuthErrorCode>[];
  }> {
    try {
      const { data, errors } = await this.client.mutate({
        mutation: REQUEST_PASSWORD_RESET_MUTATION,
        variables: { email },
      });

      if (data?.requestPasswordReset?.success) {
        return data.requestPasswordReset;
      }

      return {
        success: false,
        errors: errors as GraphQLErrorResponse<AuthErrorCode>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR' as AuthErrorCode,
              statusCode: error.statusCode || 500,
            },
          },
        ],
      };
    }
  }

  async resetPassword(input: ResetPasswordInput): Promise<{
    success: boolean;
    message?: string;
    errors?: GraphQLErrorResponse<AuthErrorCode>[];
  }> {
    try {
      const { data, errors } = await this.client.mutate({
        mutation: RESET_PASSWORD_MUTATION,
        variables: { input },
      });

      if (data?.resetPassword?.success) {
        return data.resetPassword;
      }

      return {
        success: false,
        errors: errors as GraphQLErrorResponse<AuthErrorCode>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR' as AuthErrorCode,
              statusCode: error.statusCode || 500,
            },
          },
        ],
      };
    }
  }

  // Account Linking
  async linkAccount(
    input: LinkAccountInput
  ): Promise<{ success: boolean; errors?: GraphQLErrorResponse<AuthErrorCode>[] }> {
    try {
      const { data, errors } = await this.client.mutate({
        mutation: LINK_ACCOUNT_MUTATION,
        variables: { input },
      });

      if (data?.linkAccount?.success) {
        return data.linkAccount;
      }

      return {
        success: false,
        errors: errors as GraphQLErrorResponse<AuthErrorCode>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR' as AuthErrorCode,
              statusCode: error.statusCode || 500,
            },
          },
        ],
      };
    }
  }

  // Token Refresh
  async refreshToken(): Promise<AuthTokens | null> {
    try {
      const currentTokens = await this.getTokens();
      if (!currentTokens?.refreshToken) {
        console.warn('No refresh token available');
        return null;
      }

      console.log('🔐 [RefreshToken] Auth token status: Token found', {
        hasToken: true,
        tokenLength: currentTokens.refreshToken.length,
        tokenPrefix: currentTokens.refreshToken.substring(0, 20) + '...',
      });

      const { data, errors } = await this.client.mutate({
        mutation: REFRESH_TOKEN_MUTATION,
        variables: { refreshToken: currentTokens.refreshToken },
        errorPolicy: 'all', // Return both data and errors
      });

      // Check for GraphQL errors first
      if (errors && errors.length > 0) {
        const error = errors[0];
        console.error('GraphQL error:', error);

        // Check if this is an invalid refresh token error
        if (error.extensions?.code === 'INVALID_REFRESH_TOKEN') {
          console.error('❌ Invalid refresh token - clearing auth state');
          // Clear tokens and user data
          this.clearTokens();
          this.clearUser();

          // Import and update auth store to trigger logout
          const { useAuthStore } = await import('../store/authStore');
          const authStore = useAuthStore.getState();
          authStore.clearAuth();

          // Return null to indicate auth failure
          return null;
        }

        // For other errors, throw to be handled by caller
        throw error;
      }

      const response = data?.refreshToken;
      if (!response) {
        throw new Error('No response from refresh token mutation');
      }

      // Handle the new response format
      const tokens = {
        accessToken: response.token,
        refreshToken: response.refreshToken,
        expiresIn: 3600, // Default since API doesn't return this
      };

      await this.saveTokens(tokens);

      // Update user data if provided
      if (response.user) {
        await this.saveUser(response.user);
      }

      return tokens;
    } catch (error: any) {
      // Only clear tokens for auth-related errors
      if (error.graphQLErrors || error.message?.includes('Unauthorized')) {
        await this.clearTokens();
        await this.clearUser();
      }
      throw this.handleError(error);
    }
  }

  // User Management
  async getCurrentUser(): Promise<User | null> {
    try {
      console.log('🔍 Executing getCurrentUser query...');
      const { data } = await this.client.query({
        query: GET_CURRENT_USER_QUERY,
        fetchPolicy: 'network-only',
      });

      console.log('📡 GraphQL response received:', data);
      const user = data.me;
      if (user) {
        console.log('👤 User data found, saving to storage');
        await this.saveUser(user);
        return user;
      } else {
        console.warn('⚠️ No user data in response');
        return null;
      }
    } catch (error: unknown) {
      console.error('❌ getCurrentUser failed:', error);
      if (error instanceof ApolloError) {
        console.error('GraphQL errors:', error.graphQLErrors);
      }
      return null;
    }
  }

  // Logout
  async logout(allDevices = false): Promise<void> {
    try {
      await this.client.mutate({
        mutation: LOGOUT_MUTATION,
        variables: { allDevices },
      });
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Always clear local auth data
      await this.clearTokens();
      await this.clearUser();
      // Clear Apollo cache
      await this.client.clearStore();
    }
  }

  // Error Handling
  private handleError(error: any): Error {
    // Preserve the original GraphQL error structure for better error handling
    if (error.graphQLErrors?.length > 0 || error.networkError) {
      // Create a structured error that preserves GraphQL error information
      const structuredError = new Error(error.message || 'GraphQL Error') as any;
      structuredError.graphQLErrors = error.graphQLErrors;
      structuredError.networkError = error.networkError;
      return structuredError;
    }

    if (error.networkError) {
      const networkError = new Error('Network error. Please check your connection.') as any;
      networkError.networkError = error.networkError;
      return networkError;
    }

    return error instanceof Error ? error : new Error('An unexpected error occurred');
  }
}

// Export singleton instance
export const authService = new AuthService(client);
