import { gql } from '@apollo/client';

/**
 * GraphQL Mutations for Authentication
 * Supports phone, email, passwordless, and social authentication
 */

// Common Fragments
export const USER_FRAGMENT = gql`
  fragment UserFields on User {
    id
    email
    phone
    name
    avatar
    emailVerified
    phoneVerified
  }
`;

export const AUTH_TOKENS_FRAGMENT = gql`
  fragment AuthTokenFields on AuthResponse {
    token
    refreshToken
  }
`;

export const USER_BASIC_FRAGMENT = gql`
  fragment UserBasicFields on User {
    id
    name
    email
    avatar
  }
`;

export const USER_VERIFICATION_FRAGMENT = gql`
  fragment UserVerificationFields on User {
    id
    emailVerified
    phoneVerified
  }
`;

// Phone Authentication Mutations
export const PHONE_REGISTER_MUTATION = gql`
  ${USER_FRAGMENT}
  mutation RegisterWithPhone($input: RegisterWithPhoneInput!) {
    registerWithPhone(input: $input) {
      user {
        ...UserFields
      }
      token
      refreshToken
    }
  }
`;

export const PHONE_LOGIN_MUTATION = gql`
  ${USER_FRAGMENT}
  mutation LoginWithPhone($input: LoginWithPhoneInput!) {
    loginWithPhone(input: $input) {
      user {
        ...UserFields
      }
      token
      refreshToken
    }
  }
`;

export const PHONE_VERIFY_OTP_MUTATION = gql`
  ${USER_BASIC_FRAGMENT}
  ${AUTH_TOKENS_FRAGMENT}
  mutation VerifyPhoneOTP($phone: String!, $code: String!) {
    verifyPhoneOTP(phone: $phone, code: $code) {
      user {
        ...UserBasicFields
        phone
        phoneVerified
      }
      tokens {
        ...AuthTokenFields
      }
    }
  }
`;

// Email Authentication Mutations
export const EMAIL_REGISTER_MUTATION = gql`
  mutation Register($input: RegisterInput!) {
    register(input: $input) {
      token
      refreshToken
    }
  }
`;

export const EMAIL_LOGIN_MUTATION = gql`
  mutation Login($input: LoginInput!) {
    login(input: $input) {
      token
      refreshToken
    }
  }
`;

// OTP Authentication Mutations
export const REGISTER_WITH_OTP_MUTATION = gql`
  mutation RegisterWithOTP($email: String!, $name: String!) {
    registerWithOTP(input: { otpType: "EMAIL", name: $name, email: $email, phoneNumber: null }) {
      token
      refreshToken
      user {
        id
        email
        name
      }
    }
  }
`;

export const SEND_EMAIL_OTP_MUTATION = gql`
  mutation SendEmailOTP($email: String!, $purpose: OTPPurpose!) {
    sendEmailOTP(input: { email: $email, purpose: $purpose }) {
      success
      message
      otpId
    }
  }
`;

export const LOGIN_WITH_OTP_MUTATION = gql`
  ${USER_BASIC_FRAGMENT}
  mutation LoginWithOTP($email: String!, $password: String!) {
    loginWithOTP(input: { input: { email: $email, password: $password }, otpType: "EMAIL" }) {
      token
      refreshToken
      user {
        ...UserBasicFields
        isEmailVerified
      }
    }
  }
`;

export const VERIFY_EMAIL_OTP_MUTATION = gql`
  mutation VerifyEmailOTP($email: String!, $code: String!) {
    verifyEmailOTP(input: { email: $email, code: $code }) {
      success
      message
      otpId
    }
  }
`;

// Passwordless Authentication Mutations
export const PASSWORDLESS_REQUEST_MUTATION = gql`
  mutation RequestPasswordlessLogin($email: String!) {
    requestPasswordlessLogin(email: $email) {
      success
      message
      expiresIn
    }
  }
`;

export const PASSWORDLESS_VERIFY_MUTATION = gql`
  ${USER_FRAGMENT}
  ${AUTH_TOKENS_FRAGMENT}
  mutation VerifyPasswordlessLogin($email: String!, $token: String!) {
    verifyPasswordlessLogin(email: $email, token: $token) {
      user {
        ...UserFields
      }
      tokens {
        ...AuthTokenFields
      }
      isNewUser
    }
  }
`;

// Social Authentication Mutations
export const SOCIAL_LOGIN_MUTATION = gql`
  ${USER_FRAGMENT}
  ${AUTH_TOKENS_FRAGMENT}
  mutation SocialLogin($input: SocialLoginInput!) {
    socialLogin(input: $input) {
      user {
        ...UserFields
      }
      tokens {
        ...AuthTokenFields
      }
      isNewUser
      linkedAccounts {
        id
        provider
        identifier
        isPrimary
      }
    }
  }
`;

// Verification Mutations
export const VERIFY_ACCOUNT_MUTATION = gql`
  mutation VerifyAccount($input: VerificationInput!) {
    verifyAccount(input: $input) {
      success
      user {
        id
        emailVerified
        phoneVerified
      }
    }
  }
`;

export const RESEND_VERIFICATION_MUTATION = gql`
  mutation ResendVerification($input: ResendVerificationInput!) {
    resendEmailVerification(input: $input) {
      success
      message
      nextAllowedAt
    }
  }
`;

// Token Management Mutations
export const REFRESH_TOKEN_MUTATION = gql`
  mutation RefreshToken($refreshToken: String!) {
    refreshToken(refreshToken: $refreshToken) {
      token
      refreshToken
      user {
        id
        name
        email
        avatar
      }
    }
  }
`;

export const LOGOUT_MUTATION = gql`
  mutation Logout($allDevices: Boolean) {
    logout(allDevices: $allDevices) {
      success
      message
    }
  }
`;

// Password Reset Mutations
export const REQUEST_PASSWORD_RESET_MUTATION = gql`
  mutation RequestPasswordReset($email: String!) {
    requestPasswordReset(email: $email) {
      success
      message
      expiresIn
    }
  }
`;

export const RESET_PASSWORD_MUTATION = gql`
  mutation ResetPassword($input: ResetPasswordInput!) {
    resetPassword(input: $input) {
      success
      message
    }
  }
`;

// Account Management Mutations
export const LINK_ACCOUNT_MUTATION = gql`
  mutation LinkAccount($input: LinkAccountInput!) {
    linkAccount(input: $input) {
      success
      linkedAccount {
        id
        provider
        identifier
        isPrimary
        verifiedAt
      }
    }
  }
`;

export const UNLINK_ACCOUNT_MUTATION = gql`
  mutation UnlinkAccount($accountId: String!) {
    unlinkAccount(accountId: $accountId) {
      success
      message
    }
  }
`;

// MFA Mutations
export const ENABLE_MFA_MUTATION = gql`
  mutation EnableMFA($input: EnableMFAInput!) {
    enableMFA(input: $input) {
      success
      qrCode
      secret
      backupCodes
    }
  }
`;

export const VERIFY_MFA_MUTATION = gql`
  mutation VerifyMFA($input: VerifyMFAInput!) {
    verifyMFA(input: $input) {
      success
      tokens {
        accessToken
        refreshToken
        expiresIn
      }
    }
  }
`;

export const DISABLE_MFA_MUTATION = gql`
  mutation DisableMFA($password: String!) {
    disableMFA(password: $password) {
      success
      message
    }
  }
`;

// Session Management Mutations
export const GET_SESSIONS_QUERY = gql`
  query GetSessions {
    sessions {
      id
      deviceId
      deviceName
      ipAddress
      userAgent
      createdAt
      lastActiveAt
    }
  }
`;

export const REVOKE_SESSION_MUTATION = gql`
  mutation RevokeSession($sessionId: String!) {
    revokeSession(sessionId: $sessionId) {
      success
      message
    }
  }
`;

// User Query
export const GET_CURRENT_USER_QUERY = gql`
  ${USER_BASIC_FRAGMENT}
  query Me {
    me {
      ...UserBasicFields
      profileImageURL
      coverImageURL
      isEmailVerified
      isPhoneVerified
      role
    }
  }
`;
