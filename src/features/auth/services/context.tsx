import React, { createContext, useContext, useEffect } from 'react';

import { useAuthStore } from '../store/authStore';
import { User } from '../types';
import { authService } from './auth.service';

// Simplified Auth Context - TanStack Query handles operations
interface AuthContextType {
  // State (from Zustand store)
  user: User | null;
  tokens: any | null;
  isAuthenticated: boolean;
  isInitializing: boolean;
  isSigningOut: boolean;
  isOnboardingComplete: boolean;

  // Simple actions
  signOut: () => Promise<void>;
  completeOnboarding: () => void;
  refreshSession: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | null>(null);

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  // Get state and actions from Zustand store
  const {
    user,
    tokens,
    isSigningOut,
    isAuthenticated,
    isOnboardingComplete,
    isInitializing,
    setUser,
    setTokens,
    setOnboardingComplete,
    signOut,
  } = useAuthStore();

  // Background user data fetching when we have tokens but no user
  useEffect(() => {
    const fetchUserData = async () => {
      if (tokens && !user && !isInitializing) {
        try {
          console.log('🔄 Fetching user data for authenticated session...');
          const currentUser = await authService.getCurrentUser();
          if (currentUser) {
            console.log('✅ User data fetched and stored');
            setUser(currentUser);
          }
        } catch (error) {
          console.error('❌ Failed to fetch user data:', error);
          // Don't clear tokens on user fetch failure
        }
      }
    };

    fetchUserData();
  }, [tokens, user, isInitializing, setUser]);

  // Essential methods only - TanStack Query handles auth operations
  const completeOnboarding = () => {
    setOnboardingComplete(true);
  };

  const refreshSession = async () => {
    try {
      const newTokens = await authService.refreshToken();

      if (!newTokens) {
        // Refresh returned null - auth is invalid
        console.warn('🔄 Auth sync: Detected auth mismatch - clearing store');
        await signOut();
        return;
      }

      setTokens(newTokens);
    } catch (error) {
      console.error('Token refresh failed:', error);
      // If refresh fails with network error, don't sign out
      if (error.networkError) {
        console.warn('⚠️ Network error during refresh - keeping user signed in');
        return;
      }
      // For other errors, sign out the user
      await signOut();
    }
  };

  // Context value
  const contextValue: AuthContextType = {
    // State
    user,
    tokens,
    isAuthenticated,
    isInitializing,
    isSigningOut,
    isOnboardingComplete,

    // Methods
    signOut,
    completeOnboarding,
    refreshSession,
  };

  return <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export type { User, AuthContextType };
