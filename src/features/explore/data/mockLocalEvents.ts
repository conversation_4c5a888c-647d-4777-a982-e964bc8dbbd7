import { FeedItem } from '../types/event';

// <PERSON>, PB mock locations
export const mockLocalEvents: FeedItem[] = [
  {
    id: '1',
    name: 'Underground vibes!',
    venueName: 'Skybar!',
    location: {
      lat: -7.1195,
      lng: -34.845,
      name: '<PERSON><PERSON><PERSON>',
    },
    distance: '500 m',
    startTime: 'Join the excitement now!',
    reactionsCount: 48,
    trending: true,
    engage: false,
    media: [],
  },
  {
    id: '2',
    name: 'Beach Party Sunset',
    venueName: 'Praia do Bessa',
    location: {
      lat: -7.0833,
      lng: -34.8333,
      name: '<PERSON><PERSON>',
    },
    distance: '2.3 km',
    startTime: 'Today at 6:00 PM',
    reactionsCount: 125,
    trending: false,
    engage: true,
    media: [],
  },
  {
    id: '3',
    name: 'Electronic Night',
    venueName: 'Club Manaíra',
    location: {
      lat: -7.1024,
      lng: -34.8447,
      name: 'Mana<PERSON><PERSON>',
    },
    distance: '1.8 km',
    startTime: 'Tonight at 11:00 PM',
    reactionsCount: 89,
    trending: true,
    engage: false,
    media: [],
  },
  {
    id: '4',
    name: 'Forró do Cabo Branco',
    venueName: 'Estação Cabo Branco',
    location: {
      lat: -7.1466,
      lng: -34.8297,
      name: 'Cabo Branco',
    },
    distance: '3.5 km',
    startTime: 'Friday at 8:00 PM',
    reactionsCount: 67,
    trending: false,
    engage: true,
    media: [],
  },
  {
    id: '5',
    name: 'Pool Party',
    venueName: 'Hotel Verdegreen',
    location: {
      lat: -7.1152,
      lng: -34.849,
      name: 'Tambaú',
    },
    distance: '800 m',
    startTime: 'Saturday at 2:00 PM',
    reactionsCount: 156,
    trending: true,
    engage: false,
    media: [],
  },
];

export const filterChips = [
  { id: 'all', label: 'All events', selected: true },
  { id: 'walking', label: 'Walking distance', selected: false },
  { id: 'after', label: 'After parties', selected: false },
  { id: 'beach', label: 'At the beach', selected: false },
  { id: 'electronic', label: 'Electronic', selected: false },
  { id: 'clubs', label: 'Clubs', selected: false },
];
