import { useApolloClient } from '@apollo/client';
import { UseQueryResult, useQuery } from '@tanstack/react-query';

import {
  GET_FRIENDS_ACTIVITY,
  GET_TRENDING_PARTIES,
  GET_TRENDING_VENUES,
} from '../graphql/queries';

interface Activity {
  id: string;
  user: {
    id: string;
    name: string;
    avatarUrl: string;
  };
  timestamp: string;
  type: string;
  message: string;
  event?: {
    id: string;
    title: string;
    imageUrl: string;
    date: string;
    time: string;
    venue: {
      id: string;
      name: string;
      address: string;
    };
    host: {
      id: string;
      name: string;
      avatarUrl: string;
      rating: number;
      isVerified: boolean;
    };
  };
}

interface TrendingEvent {
  id: string;
  title: string;
  imageUrl: string;
  date: string;
  time: string;
  status: string;
  description: string;
  trendingScore: number;
  venue: {
    id: string;
    name: string;
    address: string;
    latitude: number;
    longitude: number;
  };
  host: {
    id: string;
    name: string;
    avatarUrl: string;
    rating: number;
    isVerified: boolean;
  };
  ticketPrice: {
    min: number;
    max: number;
  };
  drinksPrice: {
    min: number;
    max: number;
  };
  tags: {
    id: string;
    label: string;
    color: string;
    textColor: string;
  }[];
  vibeRating: {
    hot: number;
    cold: number;
  };
  attendees: {
    count: number;
    friends: number;
  };
}

interface FriendsActivityResponse {
  friendsActivity: {
    edges: {
      node: Activity;
      cursor: string;
    }[];
    pageInfo: {
      hasNextPage: boolean;
      endCursor: string;
    };
  };
}

interface TrendingPartiesResponse {
  trendingParties: {
    edges: {
      node: TrendingEvent;
      cursor: string;
    }[];
    pageInfo: {
      hasNextPage: boolean;
      endCursor: string;
    };
  };
}

interface TrendingData {
  parties: any[];
  venues: any[];
  friendsActivities: any[];
}

type TimeFrame = 'daily' | 'weekly' | 'monthly';

export function useTrendingActivities(
  timeframe: TimeFrame = 'weekly'
): UseQueryResult<TrendingData, Error> {
  const apolloClient = useApolloClient();

  return useQuery({
    queryKey: ['trendingActivities', timeframe],
    queryFn: async () => {
      console.log('🔐 [GetTrendingActivities] Fetching with timeframe:', timeframe);

      try {
        // Fetch all trending data in parallel
        const [partiesResult, venuesResult, friendsResult] = await Promise.allSettled([
          apolloClient.query({
            query: GET_TRENDING_PARTIES,
            variables: {
              timeframe,
              limit: 10,
              offset: 0,
            },
          }),
          apolloClient.query({
            query: GET_TRENDING_VENUES,
            variables: {
              timeframe,
              limit: 10,
              offset: 0,
            },
          }),
          apolloClient.query({
            query: GET_FRIENDS_ACTIVITY,
            variables: {
              limit: 20,
              cursor: null,
            },
          }),
        ]);

        return {
          parties:
            partiesResult.status === 'fulfilled'
              ? partiesResult.value.data.trendingParties || []
              : [],
          venues:
            venuesResult.status === 'fulfilled' ? venuesResult.value.data.trendingVenues || [] : [],
          friendsActivities:
            friendsResult.status === 'fulfilled'
              ? friendsResult.value.data.friendsActivity || []
              : [],
        };
      } catch (error) {
        console.error('Error fetching trending activities:', error);
        // Return empty data structure on error
        return {
          parties: [],
          venues: [],
          friendsActivities: [],
        };
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
}
