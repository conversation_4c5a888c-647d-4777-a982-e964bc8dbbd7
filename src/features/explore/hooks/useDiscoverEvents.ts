import { useApolloClient } from '@apollo/client';
import { useInfiniteQuery } from '@tanstack/react-query';

import { GET_DISCOVERY_FEED } from '../graphql/queries';
import { FeedItem } from '../types/event';

interface FeedParamsInput {
  timeFilter?: string;
  categoryFilter?: string[];
  moodFilter?: string[];
  radius?: number;
  location?: {
    lat: number;
    lng: number;
  };
}

interface DiscoveryFeedResponse {
  discoveryFeed: {
    cursor: string | null;
    hasMore: boolean;
    items: FeedItem[];
  };
}

export function useDiscoverEvents(params?: FeedParamsInput) {
  const apolloClient = useApolloClient();

  return useInfiniteQuery({
    queryKey: ['discoveryFeed', params],
    queryFn: async ({ pageParam }) => {
      console.log('🔐 [GetDiscoveryFeed] Fetching with params:', { params, cursor: pageParam });

      const { data } = await apolloClient.query<DiscoveryFeedResponse>({
        query: GET_DISCOVERY_FEED,
        variables: {
          params: {
            ...params,
            cursor: pageParam,
          },
        },
        fetchPolicy: 'network-only',
      });

      return data.discoveryFeed;
    },
    initialPageParam: null as string | null,
    getNextPageParam: lastPage => (lastPage.hasMore ? lastPage.cursor : undefined),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}
