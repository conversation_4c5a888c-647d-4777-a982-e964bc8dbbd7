import { useApolloClient } from '@apollo/client';
import { useInfiniteQuery } from '@tanstack/react-query';

import { GET_NEARBY_EVENTS_FEED } from '../graphql/queries';
import { useLocationStore } from '../stores/locationStore';
import { FeedItem } from '../types/event';

interface EventFilters {
  walkingDistance?: boolean;
  categoryFilter?: string[];
}

interface NearbyEventsFeedResponse {
  nearbyEventsFeed: {
    cursor: string | null;
    hasMore: boolean;
    items: FeedItem[];
  };
}

export function useLocalEvents(radius: number = 10) {
  const apolloClient = useApolloClient();
  const { currentLocation } = useLocationStore();

  return useInfiniteQuery({
    queryKey: ['nearbyEventsFeed', currentLocation?.latitude, currentLocation?.longitude, radius],
    queryFn: async ({ pageParam }) => {
      if (!currentLocation) {
        return {
          cursor: null,
          hasMore: false,
          items: [],
        };
      }

      console.log('🔐 [GetNearbyEventsFeed] Fetching with location:', {
        latitude: currentLocation.latitude,
        longitude: currentLocation.longitude,
        radius,
        cursor: pageParam,
      });

      const { data } = await apolloClient.query<NearbyEventsFeedResponse>({
        query: GET_NEARBY_EVENTS_FEED,
        variables: {
          latitude: currentLocation.latitude,
          longitude: currentLocation.longitude,
          radius,
          cursor: pageParam,
          limit: 20,
        },
        fetchPolicy: 'network-only',
      });

      return data.nearbyEventsFeed;
    },
    initialPageParam: null as string | null,
    getNextPageParam: lastPage => (lastPage.hasMore ? lastPage.cursor : undefined),
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!currentLocation,
  });
}
