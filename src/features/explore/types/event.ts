// Feed Item Types
export interface FeedItem {
  id: string;
  itemType: 'party' | 'venue' | 'event' | 'user' | 'post';
  itemId: string;
  contextType: string;
  contextualNote?: string;
  connectionScore: number;
  geospatialScore: number;
  temporalScore: number;
  interestScore: number;
  overallScore: number;
  timeOfDay: string;
  dayOfWeek: string;
  mood?: string;
  groupSize?: number;
  compatibilityTags: string[];
  name: string;
  description?: string;
  imageUrl?: string;
  startDate?: string;
  tags: string[];
  detailsUrl?: string;
  distance?: number;
  engage?: boolean;
  location?: {
    lat: number;
    lng: number;
  };
}

// Party Types
export interface Party {
  id: string;
  title: string;
  description?: string;
  startDate: string;
  endDate?: string;
  imageUrl?: string;
  rules?: string[];
  venue: Venue;
  host: User;
  ticketTypes?: TicketType[];
  attendeeCount: number;
  maxAttendees?: number;
  tags: string[];
  vibeScore?: number;
  trendingScore?: number;
}

// Venue Types
export interface Venue {
  id: string;
  name: string;
  description?: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  latitude: number;
  longitude: number;
  imageUrl?: string;
  rating?: number;
  venueType?: {
    id: string;
    displayName: string;
    icon?: string;
  };
  trendingScore?: number;
  currentOccupancy?: number;
  maxOccupancy?: number;
}

// User Types
export interface User {
  id: string;
  username: string;
  avatar?: string;
  fullName?: string;
  rating?: number;
  isVerified?: boolean;
}

// Ticket Types
export interface TicketType {
  id: string;
  name: string;
  price: number;
  currency: string;
  available: number;
  maxQuantity: number;
}

// Activity Types
export interface FriendActivity {
  id: string;
  user: User;
  activityType: 'attending' | 'hosting' | 'interested' | 'checked_in';
  timestamp: string;
  partyId?: string;
  venueId?: string;
  description?: string;
  metadata?: any;
}

// Event Types (legacy - for compatibility)
export interface Event {
  id: string;
  title: string;
  imageUrl?: string;
  date: string;
  time: string;
  status: string;
  description?: string;
  venue: {
    id: string;
    name: string;
    address: string;
    latitude: number;
    longitude: number;
  };
  host: {
    id: string;
    name: string;
    avatarUrl?: string;
    rating?: number;
    isVerified?: boolean;
  };
  ticketPrice?: {
    min: number;
    max: number;
  };
  drinksPrice?: {
    min: number;
    max: number;
  };
  tags?: {
    id: string;
    label: string;
    color: string;
    textColor: string;
  }[];
  vibeRating?: {
    hot: number;
    cold: number;
  };
  attendees?: {
    count: number;
    friends: string[];
  };
  distance?: number;
  walkingDistance?: string;
}
