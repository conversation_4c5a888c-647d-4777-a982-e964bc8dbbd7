import React, { useState } from 'react';

import { ActivityIndicator, RefreshControl, ScrollView } from 'react-native';

import { Fire, MapPin, TrendUp, Users } from 'phosphor-react-native';
import { useTranslation } from 'react-i18next';

import { useTopTabBarHeight } from '@/src/core/navigation/ui/TopTabNavigation';
import { useTheme } from '@/src/core/theme';
import Box from '@/src/core/theme/Box';
import Pressable from '@/src/core/theme/Pressable';
import Text from '@/src/core/theme/Text';
import { useResponsive } from '@/src/core/theme/useResponsive';

import ActivityCard from '../components/ActivityCard';
import { useTrendingActivities } from '../hooks/useTrendingActivities';

type TimeFrame = 'daily' | 'weekly' | 'monthly';

const timeFrameOptions: { value: TimeFrame; label: string }[] = [
  { value: 'daily', label: 'Today' },
  { value: 'weekly', label: 'This Week' },
  { value: 'monthly', label: 'This Month' },
];

export default function TrendingScreen() {
  const { t } = useTranslation();
  const theme = useTheme();
  const { isTablet } = useResponsive();
  const [timeFrame, setTimeFrame] = useState<TimeFrame>('weekly');

  const { data, isLoading, isError, refetch, isRefetching } = useTrendingActivities(timeFrame);
  const { height: topTabHeight } = useTopTabBarHeight();

  if (isLoading) {
    return (
      <Box flex={1} justifyContent="center" alignItems="center" backgroundColor="mainBackground">
        <ActivityIndicator size="large" color={theme.colors.primary} />
      </Box>
    );
  }

  if (isError) {
    return (
      <Box
        flex={1}
        justifyContent="center"
        alignItems="center"
        padding="xl_32"
        backgroundColor="mainBackground">
        <Text variant="b_16Regular_input" color="errorMain" textAlign="center">
          {t('explore.error.loadingTrending')}
        </Text>
        <Pressable marginTop="md_16" onPress={() => refetch()}>
          <Text variant="b_16SemiBold_button" color="primary">
            {t('common.retry')}
          </Text>
        </Pressable>
      </Box>
    );
  }

  return (
    <ScrollView
      style={{ flex: 1, backgroundColor: theme.colors.mainBackground, paddingTop: topTabHeight }}
      refreshControl={
        <RefreshControl
          refreshing={isRefetching}
          onRefresh={refetch}
          tintColor={theme.colors.primary}
        />
      }
      showsVerticalScrollIndicator={false}>
      <Box padding="md_16">
        {/* Time Frame Selector */}
        <Box
          flexDirection="row"
          backgroundColor="elevatedBackground"
          borderRadius="sm_8"
          padding="xxs_4"
          marginBottom="lg_24">
          {timeFrameOptions.map(option => (
            <Pressable
              key={option.value}
              flex={1}
              paddingVertical="xs_8"
              paddingHorizontal="sm_12"
              backgroundColor={timeFrame === option.value ? 'cardBackground' : 'transparent'}
              borderRadius="xs_4"
              alignItems="center"
              onPress={() => setTimeFrame(option.value)}>
              <Text
                variant={
                  timeFrame === option.value ? 'b_14SemiBold_listTitle' : 'b_14Regular_content'
                }
                color={timeFrame === option.value ? 'mainText' : 'secondaryText'}>
                {option.label}
              </Text>
            </Pressable>
          ))}
        </Box>

        {/* Trending Parties Section */}
        {data?.parties && data.parties.length > 0 && (
          <Box marginBottom="xl_32">
            <Box flexDirection="row" alignItems="center" gap="xs_8" marginBottom="md_16">
              <Fire size={24} color={theme.colors.iconPrimary} weight="fill" />
              <Text variant="h_20Medium_subsection" color="mainText">
                {t('explore.trending.hotParties')}
              </Text>
            </Box>

            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <Box flexDirection="row" gap="sm_12">
                {data.parties.map(party => (
                  <Box key={party.id} width={280}>
                    <Pressable
                      backgroundColor="cardBackground"
                      borderRadius="lg_16"
                      borderWidth={1}
                      borderColor="mainBorder"
                      overflow="hidden">
                      <Box height={140} backgroundColor="elevatedBackground" />
                      <Box padding="md_16">
                        <Text
                          variant="h_16SemiBold_alertTitle"
                          numberOfLines={1}
                          marginBottom="xs_8">
                          {party.title}
                        </Text>
                        <Text
                          variant="b_14Regular_content"
                          color="secondaryText"
                          numberOfLines={2}
                          marginBottom="xs_8">
                          {party.description}
                        </Text>
                        <Box flexDirection="row" alignItems="center" justifyContent="space-between">
                          <Box flexDirection="row" alignItems="center" gap="xxs_4">
                            <Users size={16} color={theme.colors.iconMuted} />
                            <Text variant="l_12Regular_helperText" color="secondaryText">
                              {party.attendeeCount} going
                            </Text>
                          </Box>
                          <Box flexDirection="row" alignItems="center" gap="xxs_4">
                            <TrendUp size={16} color={theme.colors.successMain} />
                            <Text variant="l_12Bold_highlight" color="successMain">
                              {party.trendingScore}
                            </Text>
                          </Box>
                        </Box>
                      </Box>
                    </Pressable>
                  </Box>
                ))}
              </Box>
            </ScrollView>
          </Box>
        )}

        {/* Trending Venues Section */}
        {data?.venues && data.venues.length > 0 && (
          <Box marginBottom="xl_32">
            <Box flexDirection="row" alignItems="center" gap="xs_8" marginBottom="md_16">
              <MapPin size={24} color={theme.colors.iconSecondary} weight="fill" />
              <Text variant="h_20Medium_subsection" color="mainText">
                {t('explore.trending.hotVenues')}
              </Text>
            </Box>

            <Box gap="sm_12">
              {data.venues.slice(0, 5).map(venue => (
                <Pressable
                  key={venue.id}
                  backgroundColor="cardBackground"
                  borderRadius="lg_16"
                  borderWidth={1}
                  borderColor="mainBorder"
                  padding="md_16"
                  flexDirection="row"
                  alignItems="center"
                  gap="sm_12">
                  <Box
                    width={60}
                    height={60}
                    borderRadius="md_12"
                    backgroundColor="elevatedBackground"
                  />
                  <Box flex={1}>
                    <Text variant="h_16SemiBold_alertTitle" numberOfLines={1} marginBottom="xxs_4">
                      {venue.name}
                    </Text>
                    <Text variant="b_12Medium_CardSubtitle" color="secondaryText" numberOfLines={1}>
                      {venue.address}
                    </Text>
                  </Box>
                  <Box alignItems="flex-end">
                    <Box flexDirection="row" alignItems="center" gap="xxs_4">
                      <TrendUp size={16} color={theme.colors.warningMain} />
                      <Text variant="l_12Bold_highlight" color="warningMain">
                        {venue.trendingScore}
                      </Text>
                    </Box>
                    {venue.currentOccupancy && venue.maxOccupancy && (
                      <Text variant="l_10Medium_tooltip" color="secondaryText" marginTop="xxs_4">
                        {Math.round((venue.currentOccupancy / venue.maxOccupancy) * 100)}% full
                      </Text>
                    )}
                  </Box>
                </Pressable>
              ))}
            </Box>
          </Box>
        )}

        {/* Friends Activity Section */}
        {data?.friendsActivities && data.friendsActivities.length > 0 && (
          <Box marginBottom="xl_32">
            <Box flexDirection="row" alignItems="center" gap="xs_8" marginBottom="md_16">
              <Users size={24} color={theme.colors.iconAccent} weight="fill" />
              <Text variant="h_20Medium_subsection" color="mainText">
                {t('explore.trending.friendsActivity')}
              </Text>
            </Box>

            <Box gap="sm_12">
              {data.friendsActivities.map(activity => (
                <ActivityCard key={activity.id} activity={activity} />
              ))}
            </Box>
          </Box>
        )}

        {/* Empty State */}
        {!data?.parties?.length && !data?.venues?.length && !data?.friendsActivities?.length && (
          <Box paddingVertical="xxxl_48" alignItems="center">
            <Text variant="b_16Regular_input" color="secondaryText" textAlign="center">
              {t('explore.trending.noTrendingContent')}
            </Text>
          </Box>
        )}
      </Box>
    </ScrollView>
  );
}
