import React, { useCallback, useState } from 'react';

import { FlatList, ScrollView, useWindowDimensions } from 'react-native';

import { useBottomTabBarHeight } from '@react-navigation/bottom-tabs';
import { useNavigation } from '@react-navigation/native';
import { FlashList } from '@shopify/flash-list';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { EventsStackScreenProps } from '@/src/core/navigation/types';
import { useTopTabBarHeight } from '@/src/core/navigation/ui/TopTabNavigation';
import { Box, Text, useTheme } from '@/src/core/theme';
import {
  PublicPartyPost,
  PublicPartyPostData,
} from '@/src/features/shared/components/PublicPartyPost';

import { DiscoverFilter } from '../components/DiscoverFilter';
import { Event } from '../components/EventFeedCard';
import { EventsListing } from '../components/EventsListing';

// Mock data for events
const mockEvents: Event[] = [
  {
    id: '1',
    title: 'Midnight vibes After party',
    startTime: 'Starts at 1 AM',
    location: {
      name: 'Skybar',
      distance: '2.3mi',
    },
    description: 'Exclusive after hours set with dj maxwell after the main event at cloud nine',
    tags: ['Rooftop', 'House', 'Barbecue'],
    stats: {
      interested: 24,
      attending: 12,
    },
    isTrending: true,
    isOfficial: false,
    isFavorited: false,
    isBookmarked: false,
    requiresInvite: false,
    canJoin: true,
  },
  {
    id: '2',
    title: 'Summer Rooftop Session',
    startTime: 'Starts at 11 PM',
    location: {
      name: 'Skyline lounge',
      distance: '2.3 Km',
    },
    description: 'Join us for an unforgettable night under the stars with live DJs and cocktails',
    tags: ['Rooftop', 'Drinks', 'House', 'Electronic'],
    stats: {
      interested: 42,
      attending: 28,
    },
    isTrending: true,
    isOfficial: true,
    isFavorited: false,
    isBookmarked: false,
    requiresInvite: true,
    canJoin: false,
  },
  {
    id: '3',
    title: 'Beach Club Opening',
    startTime: 'Starts at 10 PM',
    location: {
      name: 'Paradise Beach Club',
      distance: '5.1 Km',
    },
    description: 'Grand opening of the newest beach club with international DJs',
    tags: ['Beach', 'Electronic', 'VIP', 'Drinks'],
    stats: {
      interested: 156,
      attending: 89,
    },
    isTrending: false,
    isOfficial: true,
    isFavorited: true,
    isBookmarked: false,
    requiresInvite: false,
    canJoin: true,
  },
];

// Mock data for public party posts
const mockPartyPosts: PublicPartyPostData[] = [
  {
    id: '1',
    imageUrl: 'https://picsum.photos/400/300',
    title: 'Churrasco do Menos é Mais',
    location: {
      name: 'Skyline lounge',
      distance: '1.2km',
    },
    pricing: {
      entry: '$15-30',
      drinks: '$3-10',
    },
    date: 'Saturday, Jan 15, 2025',
    time: '9:00PM',
    host: {
      id: '1',
      name: 'Lovina',
      avatar: 'https://picsum.photos/100/100',
      rating: 4.9,
      isVerified: true,
    },
    tags: [
      'Pagode',
      'Live Performance',
      'Muita MOVUCA',
      'Barbecue',
      'Drinks',
      'Outdoor',
      'Smoking',
    ],
    vibeRating: {
      cold: 53,
      hot: 47,
    },
    interactions: {
      fires: 24,
      ices: 12,
      hasTickets: true,
      hasGallery: true,
    },
  },
  {
    id: '2',
    imageUrl: 'https://picsum.photos/400/301',
    title: 'Sunset Beach Party',
    location: {
      name: 'Copacabana Beach Club',
      distance: '3.5km',
    },
    pricing: {
      entry: '$20-40',
      drinks: '$5-15',
    },
    date: 'Sunday, Jan 16, 2025',
    time: '6:00PM',
    host: {
      id: '2',
      name: 'DJ Max',
      avatar: 'https://picsum.photos/100/101',
      rating: 4.7,
      isVerified: true,
    },
    tags: ['Beach', 'Sunset', 'Electronic', 'Cocktails', 'Dancing'],
    vibeRating: {
      cold: 20,
      hot: 80,
    },
    interactions: {
      fires: 156,
      ices: 8,
      hasTickets: true,
      hasGallery: true,
    },
  },
  {
    id: '3',
    imageUrl: 'https://picsum.photos/400/302',
    title: 'Rooftop Jazz Night',
    location: {
      name: 'Sky Lounge 360',
      distance: '2.1km',
    },
    pricing: {
      entry: '$25-35',
      drinks: '$8-20',
    },
    date: 'Friday, Jan 14, 2025',
    time: '8:00PM',
    host: {
      id: '3',
      name: 'Maria Santos',
      avatar: 'https://picsum.photos/100/102',
      rating: 4.8,
      isVerified: false,
    },
    tags: ['Jazz', 'Live Music', 'Rooftop', 'Wine', 'Elegant'],
    vibeRating: {
      cold: 75,
      hot: 25,
    },
    interactions: {
      fires: 45,
      ices: 67,
      hasTickets: true,
      hasGallery: false,
    },
  },
];

function DiscoverScreen() {
  const { width } = useWindowDimensions();
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const { height } = useTopTabBarHeight();
  const bottomTabHeight = useBottomTabBarHeight();
  const [selectedTime, setSelectedTime] = useState('after11pm');
  const [selectedCategories, setSelectedCategories] = useState<string[]>(['all']);
  const [selectedEventId, setSelectedEventId] = useState<string | null>(null);
  const cardWidth = width - theme.spacing.xl_32 * 2;

  const navigation = useNavigation();

  const handleTimeChange = useCallback((time: string) => {
    setSelectedTime(time);
    // Filter events based on time
    console.log('Time filter changed:', time);
  }, []);

  const handleCategoriesChange = useCallback((categories: string[]) => {
    setSelectedCategories(categories);
    // Filter events based on categories
    console.log('Categories changed:', categories);
  }, []);

  const handleReset = useCallback(() => {
    setSelectedTime('after11pm');
    setSelectedCategories(['all']);
  }, []);

  const handleEventPress = useCallback((eventId: string) => {
    setSelectedEventId(eventId);
    // Navigate to event details
    navigation.navigate('Events', {
      screen: 'EventsDetailsScreen',
      params: { eventId },
    });
    console.log('Navigate to event:', eventId);
  }, []);

  const handleJoin = useCallback((eventId: string) => {
    console.log('Join event:', eventId);
  }, []);

  const handleRequestInvite = useCallback((eventId: string) => {
    console.log('Request invite for event:', eventId);
  }, []);

  const handleFavorite = useCallback((eventId: string) => {
    console.log('Toggle favorite for event:', eventId);
  }, []);

  const handleBookmark = useCallback((eventId: string) => {
    console.log('Toggle bookmark for event:', eventId);
  }, []);

  const handlePartyPostPress = useCallback((postId: string) => {
    console.log('Party post pressed:', postId);
  }, []);

  const handleHostPress = useCallback((hostId: string) => {
    console.log('Host pressed:', hostId);
  }, []);

  const handleLocationPress = useCallback(() => {
    console.log('Location pressed');
  }, []);

  const handleTicketPress = useCallback(() => {
    console.log('Tickets pressed');
  }, []);

  const handleGalleryPress = useCallback(() => {
    console.log('Gallery pressed');
  }, []);

  return (
    <Box flex={1} backgroundColor="mainBackground">
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: insets.bottom + bottomTabHeight }}
        stickyHeaderIndices={[0]}
        style={{ marginTop: height }}>
        {/* Filter Header - Sticky */}
        <DiscoverFilter
          selectedTime={selectedTime}
          selectedCategories={selectedCategories}
          onTimeChange={handleTimeChange}
          onCategoriesChange={handleCategoriesChange}
          onReset={handleReset}
        />

        {/* Content Sections */}
        <Box paddingTop="md_16">
          {/* Public Party Posts Section */}
          <Box marginBottom="lg_24">
            <FlatList
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={{ paddingHorizontal: 16 }}
              data={mockPartyPosts}
              ItemSeparatorComponent={() => <Box width={12} />}
              renderItem={({ item }) => (
                <Box key={item.id} width={cardWidth}>
                  <PublicPartyPost
                    cardWidth={cardWidth}
                    data={item}
                    onPress={() => handleEventPress(item.id)}
                    onHostPress={() => handleHostPress(item.host.id)}
                    onLocationPress={handleLocationPress}
                    onTicketPress={handleTicketPress}
                    onGalleryPress={handleGalleryPress}
                  />
                </Box>
              )}
            />
          </Box>

          {/* Events Listing Section */}
          <Box paddingHorizontal="md_16">
            <Box marginBottom="sm_12">
              <Text variant="h_24SemiBold_section" color="mainText">
                Upcoming Events
              </Text>
              <Text variant="l_14Medium_info" color="secondaryText" marginTop="xxs_4">
                {mockEvents.length} events near you
              </Text>
            </Box>

            <EventsListing
              events={mockEvents}
              variant="detailed"
              selectedEventId={selectedEventId}
              onEventPress={handleEventPress}
              onJoin={handleJoin}
              onRequestInvite={handleRequestInvite}
              onFavorite={handleFavorite}
              onBookmark={handleBookmark}
              onShowMore={id => console.log('Show more for:', id)}
              onViewTickets={id => console.log('View tickets for:', id)}
              onViewGallery={id => console.log('View gallery for:', id)}
            />
          </Box>
        </Box>
      </ScrollView>
    </Box>
  );
}

export default DiscoverScreen;

DiscoverScreen.displayName = 'DiscoverScreen';
