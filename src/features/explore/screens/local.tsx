import React, { useCallback, useState } from 'react';

import { ActivityIndicator, FlatList, Platform, RefreshControl } from 'react-native';

import { useBottomTabBarHeight } from '@react-navigation/bottom-tabs';
import { Funnel, MapPin } from 'phosphor-react-native';
import { useTranslation } from 'react-i18next';

import { useTopTabBarHeight } from '@/src/core/navigation/ui/TopTabNavigation';
import { useTheme } from '@/src/core/theme';
import Box from '@/src/core/theme/Box';
import Pressable from '@/src/core/theme/Pressable';
import Text from '@/src/core/theme/Text';
import SegmentedControl from '@/src/shared/components/SegmentedControl/index';
import { useLocation } from '@/src/shared/hooks';

import { EventMap, FilterChips, LocalEventCard, LocationHeader } from '../components';
import { filterChips as defaultFilterChips, mockLocalEvents } from '../data/mockLocalEvents';
import { useLocalEvents } from '../hooks/useLocalEvents';
import { FeedItem } from '../types/event';

export default function LocalScreen() {
  const { t } = useTranslation();
  const theme = useTheme();
  const topBarHeight = useTopTabBarHeight();
  const [viewIndex, setViewIndex] = useState(0); // 0: Map, 1: List
  const [radius, setRadius] = useState(3); // 3km default radius
  const [filterChips, setFilterChips] = useState(defaultFilterChips);
  const bottomTabHeight = useBottomTabBarHeight();

  const { location } = useLocation();
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    isError,
    refetch,
    isRefetching,
  } = useLocalEvents(radius);

  // Use mock data for now
  const items = mockLocalEvents;

  const handleItemPress = useCallback((item: FeedItem) => {
    console.log('Navigate to item details:', item);
  }, []);

  const handleChipPress = useCallback((chipId: string) => {
    setFilterChips(prev =>
      prev.map(chip => ({
        ...chip,
        selected: chip.id === chipId ? !chip.selected : chip.selected,
      }))
    );
  }, []);

  const handleLocationChange = useCallback(() => {
    console.log('Change location');
  }, []);

  const renderItem = useCallback(
    ({ item }: { item: FeedItem }) => (
      <LocalEventCard item={item} onPress={() => handleItemPress(item)} />
    ),
    [handleItemPress]
  );
  if (isLoading) {
    return (
      <Box flex={1} justifyContent="center" alignItems="center" backgroundColor="mainBackground">
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text variant="b_14Regular_content" color="secondaryText" marginTop="md_16">
          {t('explore.local.findingNearby')}
        </Text>
      </Box>
    );
  }

  return (
    <Box
      flex={1}
      backgroundColor="mainBackground"
      style={{
        paddingTop: topBarHeight.height,
        paddingBottom: Platform.OS === 'ios' ? bottomTabHeight : 0,
      }}>
      {/* Location Header and Controls */}
      <Box backgroundColor="cardBackground" borderBottomWidth={1} borderBottomColor="mainBorder">
        {/* Segmented Control and Filter */}
        <Box flexDirection="row" alignItems="center" paddingHorizontal="md_16" gap="sm_12">
          <Box flex={1}>
            <SegmentedControl
              items={[{ title: 'Map' }, { title: 'List' }]}
              onValueChange={setViewIndex}
              selected={viewIndex}
              initialValue={0}
            />
          </Box>
          <Pressable
            flexDirection="row"
            alignItems="center"
            gap="xxs_4"
            paddingVertical="xs_8"
            paddingHorizontal="sm_12">
            <Funnel size={16} color={theme.colors.iconSecondary} />
            <Text variant="h_16Medium_formLabel" color="iconSecondary">
              {t('explore.local.filter')}
            </Text>
          </Pressable>
        </Box>
        <LocationHeader
          locationName="Alagoa Grande"
          radius={`${radius} km radius`}
          onChangePress={handleLocationChange}
        />
      </Box>
      {/* Filter Chips */}
      {/* <Box backgroundColor="cardBackground" paddingVertical="xs_8">
        <FilterChips chips={filterChips} onChipPress={handleChipPress} />
      </Box> */}

      {/* Content Area */}
      <Box flex={1}>
        {viewIndex === 0 ? (
          // Map View
          <EventMap
            events={items}
            region={{
              latitude: location?.coords.latitude || -7.1195,
              longitude: location?.coords.longitude || -34.845,
              latitudeDelta: 0.05,
              longitudeDelta: 0.05,
            }}
            onEventPress={handleItemPress}
          />
        ) : (
          // List View
          <FlatList
            data={items}
            renderItem={renderItem}
            keyExtractor={item => item.id}
            contentContainerStyle={{
              paddingHorizontal: 16,
              paddingTop: 16,
            }}
            refreshControl={
              <RefreshControl
                refreshing={isRefetching}
                onRefresh={refetch}
                tintColor={theme.colors.primary}
              />
            }
            onEndReached={() => {
              if (hasNextPage && !isFetchingNextPage) {
                fetchNextPage();
              }
            }}
            onEndReachedThreshold={0.5}
            ItemSeparatorComponent={() => <Box height={12} />}
            ListFooterComponent={
              isFetchingNextPage ? (
                <Box paddingVertical="lg_24" alignItems="center">
                  <ActivityIndicator color={theme.colors.primary} />
                </Box>
              ) : (
                <Box height={16} />
              )
            }
            ListEmptyComponent={
              <Box paddingVertical="xxxl_48" alignItems="center">
                <Text variant="b_16Regular_input" color="secondaryText">
                  {t('explore.local.noNearbyEvents')}
                </Text>
              </Box>
            }
            showsVerticalScrollIndicator={false}
          />
        )}
      </Box>
    </Box>
  );
}
