// Types for the enhanced explore queries

export interface UserPreview {
  id: string;
  username: string;
  avatar?: string;
  fullName?: string;
  isVerified: boolean;
}

export interface EngagementMetrics {
  viewCount: number;
  likeCount: number;
  shareCount: number;
  commentCount: number;
  attendeeCount: number;
  interestCount: number;
  engagementScore: number;
  trendingScore: number;
}

export interface GeoPoint {
  lat: number;
  lng: number;
}

export interface SocialContext {
  friendsAttending: {
    count: number;
    friends: UserPreview[];
  };
  friendsInterested: {
    count: number;
  };
  mutualInterests: string[];
  reasonsToGo: string[];
}

export interface UrgencyIndicator {
  type: 'SELLING_FAST' | 'LAST_CHANCE' | 'EARLY_BIRD' | 'LIMITED_SPOTS';
  message: string;
  expiresAt?: string;
}

export interface LiveStatus {
  isHappeningNow: boolean;
  currentAttendance: number;
  vibeUpdate?: string;
  lastUpdated: string;
}

export interface EnhancedFeedItem {
  id: string;
  itemType: 'EVENT' | 'VENUE' | 'PERSON' | 'GROUP' | 'MOMENT';
  itemId: string;

  // Context
  contextType: string;
  contextualNote?: string;

  // Scores (dev only)
  connectionScore?: number;
  geospatialScore?: number;
  temporalScore?: number;
  interestScore?: number;
  overallScore?: number;

  // Atmosphere
  timeOfDay?: string;
  dayOfWeek?: string;
  mood?: string;
  groupSize?: number;
  compatibilityTags: string[];

  // Content
  name: string;
  description?: string;
  imageUrl?: string;
  startDate?: string;
  tags: string[];

  // Location
  location?: GeoPoint;
  distance?: string;
  walkingTime?: string;
  drivingTime?: string;
  transitTime?: string;

  // Navigation
  detailsUrl?: string;

  // Engagement
  engage: boolean;

  // Enhanced fields
  socialContext?: SocialContext;
  urgencyIndicator?: UrgencyIndicator;
  liveStatus?: LiveStatus;
  party?: PartyDetails;
}

export interface PartyImage {
  id: string;
  url: string;
  caption?: string;
  uploadedAt: string;
}

export interface PartyVideo {
  id: string;
  url: string;
  thumbnailUrl?: string;
  title?: string;
  duration?: number;
}

export interface PartyContent {
  images: PartyImage[];
  videos: PartyVideo[];
}

export interface Venue {
  id: string;
  name: string;
  address?: string;
  city?: string;
  imageUrl?: string;
  rating?: number;
  currentOccupancy?: number;
  maxOccupancy?: number;
}

export interface PartyHost {
  user: UserPreview & { rating?: number };
  role: 'OWNER' | 'CO_HOST' | 'ORGANIZER' | 'MODERATOR' | 'CONTRIBUTOR';
}

export interface PartyAttendee {
  user: UserPreview;
  status: 'GOING' | 'INTERESTED' | 'MAYBE' | 'NOT_GOING' | 'CHECKED_IN';
}

export interface PartyReview {
  id: string;
  rating: number;
  comment?: string;
  user: UserPreview;
  createdAt: string;
}

export interface PartyInsight {
  type: string;
  title: string;
  description: string;
  confidence: number;
}

export interface PartyDetails {
  id: string;
  name: string;
  title?: string;
  description?: string;
  type: string;
  eventType: string;
  status: string;
  visibility: string;

  // Timing
  startDate: string;
  endDate: string;
  startTime?: string;
  endTime?: string;

  // Capacity
  maxGuests?: number;
  attendeeCount: number;
  soldTickets: number;

  // Media
  coverImageId?: string;
  partyContent?: PartyContent;

  // Categories
  tags: string[];
  category?: string;
  musicGenres: string[];
  performers: string[];
  dressCode?: string;
  ageRestriction?: number;

  // Pricing
  isFree: boolean;
  ticketURL?: string;

  // Location
  venue?: Venue;
  location?: GeoPoint;
  address?: string;
  city?: string;
  placeType?: string;

  // Social
  hosts: PartyHost[];
  attendees: PartyAttendee[];

  // Analytics
  analytics?: EngagementMetrics;
  averageRating?: number;
  reviews: PartyReview[];

  // User relationship
  isUserAttending: boolean;
  isUserInvited: boolean;
  isUserHost: boolean;

  // Trending
  insights?: PartyInsight[];
}

export interface MoodMatch {
  score: number;
  explanation: string;
  suggestedActivities: string[];
  matchingElements: string[];
}

export interface WeekendHighlight {
  type: 'SATURDAY_NIGHT' | 'SUNDAY_BRUNCH' | 'FRIDAY_KICKOFF';
  bestTimeToArrive: string;
  expectedCrowd: 'BUSY' | 'MODERATE' | 'INTIMATE';
}

export interface GroupSuggestion {
  idealGroupSize: number;
  friendsWhoMightLike: (UserPreview & { reason: string })[];
  groupDiscount?: number;
  groupActivities: string[];
}

export interface FeedResult<T = EnhancedFeedItem> {
  items: T[];
  cursor?: string;
  hasMore: boolean;
}

export interface ActivityContent {
  message?: string;
  media?: {
    type: 'PHOTO' | 'VIDEO' | 'STORY';
    url: string;
    thumbnailUrl?: string;
  };
  mood?: string;
  rating?: number;
}

export interface Reaction {
  type: string;
  count: number;
  hasUserReacted: boolean;
}

export interface FriendActivity {
  id: string;
  user: UserPreview;
  activityType: 'ATTENDING' | 'HOSTING' | 'POSTED' | 'REVIEWED';
  timestamp: string;
  content?: ActivityContent;
  party?: PartyDetails;
  venue?: Venue;
  reactions: Reaction[];
  comments: {
    count: number;
    preview: {
      user: UserPreview;
      content: string;
    }[];
  };
}

export interface TrendingReason {
  primary: string;
  secondary?: string;
  metrics?: {
    percentageIncrease: number;
    comparedTo: string;
  };
}

export interface TrendingItem {
  id: string;
  type: string;
  trendingScore: number;
  engagementCount: number;
  trendingReason?: TrendingReason;
  party?: PartyDetails;
  venue?: Venue & {
    occupancyTrend?: 'INCREASING' | 'STABLE' | 'DECREASING';
    peakHours?: string[];
    currentEvents?: PartyDetails[];
    upcomingEvents?: PartyDetails[];
  };
}

export interface TicketType {
  id: string;
  name: string;
  price: number;
  currency: string;
  available: number;
  maxQuantity: number;
  perks?: string[];
  salesEndDate?: string;
}

export interface OperatingHours {
  dayOfWeek: string;
  openTime: string;
  closeTime: string;
  isOpen24Hours: boolean;
}

export interface VenueDetails {
  id: string;
  name: string;
  description?: string;
  address: string;
  city: string;
  state?: string;
  zipCode?: string;
  country: string;
  latitude: number;
  longitude: number;
  imageUrl?: string;
  images: {
    id: string;
    url: string;
    caption?: string;
    type: 'INTERIOR' | 'EXTERIOR' | 'MENU' | 'AMBIANCE';
  }[];
  venueType?: {
    id: string;
    displayName: string;
    icon?: string;
  };
  amenities: string[];
  capacity?: number;
  operatingHours: OperatingHours[];
  currentOccupancy?: number;
  maxOccupancy?: number;
  waitTime?: number;
  rating?: number;
  totalReviews: number;
  ratingDistribution: {
    stars: number;
    count: number;
  }[];
  currentEvents: PartyDetails[];
  upcomingEvents: PartyDetails[];
  friendsWhoVisited: (UserPreview & {
    lastVisit: string;
    favoriteEvent?: string;
  })[];
  isFavorite: boolean;
  userVisitCount: number;
}
