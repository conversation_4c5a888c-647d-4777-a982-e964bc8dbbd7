import { gql } from '@apollo/client';

// Fragment for user preview (social proof)
export const USER_PREVIEW_FRAGMENT = gql`
  fragment UserPreview on User {
    id
    username
    avatar
    fullName
    isVerified
  }
`;

// Fragment for engagement metrics
export const ENGAGEMENT_METRICS_FRAGMENT = gql`
  fragment EngagementMetrics on PartyEngagement {
    viewCount
    likeCount
    shareCount
    commentCount
    attendeeCount
    interestCount
    engagementScore
    trendingScore
  }
`;

// Enhanced feed item fragment with social and engagement data
export const ENHANCED_FEED_ITEM_FRAGMENT = gql`
  fragment EnhancedFeedItemFields on FeedItem {
    id
    itemType
    itemId

    # Contextual information for personalization
    contextType
    contextualNote # "Because you like <PERSON><PERSON>", "Your friends are going", etc.
    # Scoring for better recommendations (dev only)
    connectionScore
    geospatialScore
    temporalScore
    interestScore
    overallScore

    # Atmosphere and vibe
    timeOfDay
    dayOfWeek
    mood
    groupSize
    compatibilityTags

    # Core content
    name
    description
    imageUrl
    startDate
    tags

    # Location data
    location {
      lat
      lng
    }
    distance # "0.5 km away", "10 min walk"
    # Navigation
    detailsUrl

    # Real-time engagement
    engage
  }
`;

// Rich party fragment with all engaging details
export const PARTY_DETAILS_FRAGMENT = gql`
  ${USER_PREVIEW_FRAGMENT}
  ${ENGAGEMENT_METRICS_FRAGMENT}

  fragment PartyDetails on Party {
    id
    name
    title
    description
    type
    eventType
    status
    visibility

    # Timing
    startDate
    endDate
    startTime
    endTime

    # Capacity and social proof
    maxGuests
    attendeeCount
    soldTickets

    # Rich media
    coverImageId
    partyContent {
      images {
        id
        url
        caption
        uploadedAt
      }
      videos {
        id
        url
        thumbnailUrl
        title
        duration
      }
    }

    # Categorization and discovery
    tags
    category
    musicGenres
    performers
    dressCode
    ageRestriction

    # Pricing
    isFree
    ticketURL

    # Location
    venue {
      id
      name
      address
      city
      imageUrl
      rating
      currentOccupancy
      maxOccupancy
    }
    location {
      lat
      lng
    }
    address
    city
    placeType

    # Social proof - who's going
    hosts {
      user {
        ...UserPreview
        rating
      }
      role
    }

    # Friends attending (social proof)
    attendees(first: 5, filter: { onlyFriends: true }) {
      user {
        ...UserPreview
      }
      status
    }

    # Engagement metrics
    analytics {
      ...EngagementMetrics
    }

    # Reviews and ratings
    averageRating
    reviews(first: 3) {
      id
      rating
      comment
      user {
        ...UserPreview
      }
      createdAt
    }

    # User's relationship to the party
    isUserAttending(userId: $userId)
    isUserInvited(userId: $userId)
    isUserHost(userId: $userId)
  }
`;

// Enhanced discovery feed with social context
export const GET_DISCOVERY_FEED = gql`
  ${ENHANCED_FEED_ITEM_FRAGMENT}

  query GetDiscoveryFeed($params: FeedParamsInput, $userId: ID!) {
    discoveryFeed(params: $params) {
      cursor
      hasMore
      items {
        ...EnhancedFeedItemFields

        # Additional social context for feed items
        socialContext {
          friendsAttending {
            count
            friends(first: 3) {
              ...UserPreview
            }
          }
          friendsInterested {
            count
          }
          mutualInterests
          reasonsToGo
        }

        # Time sensitivity
        urgencyIndicator {
          type # "SELLING_FAST", "LAST_CHANCE", "EARLY_BIRD"
          message
          expiresAt
        }
      }
    }
  }
`;

// Nearby events with walking distance and live updates
export const GET_NEARBY_EVENTS_FEED = gql`
  ${ENHANCED_FEED_ITEM_FRAGMENT}
  ${PARTY_DETAILS_FRAGMENT}

  query GetNearbyEventsFeed(
    $latitude: Float!
    $longitude: Float!
    $radius: Int
    $cursor: String
    $limit: Int
    $userId: ID!
  ) {
    nearbyEventsFeed(
      latitude: $latitude
      longitude: $longitude
      radius: $radius
      cursor: $cursor
      limit: $limit
    ) {
      cursor
      hasMore
      items {
        ...EnhancedFeedItemFields

        # Location specifics
        walkingTime # "5 min walk"
        drivingTime # "10 min drive"
        transitTime # "15 min by transit"
        # Live status
        liveStatus {
          isHappeningNow
          currentAttendance
          vibeUpdate # "Getting packed!", "Great energy!"
          lastUpdated
        }

        # Detailed party info for nearby events
        party {
          ...PartyDetails
        }
      }
    }
  }
`;

// Enhanced friends activity with stories
export const GET_FRIENDS_ACTIVITY = gql`
  ${USER_PREVIEW_FRAGMENT}

  query GetFriendsActivity($limit: Int, $cursor: String, $userId: ID!) {
    friendsActivity(limit: $limit, cursor: $cursor) {
      edges {
        node {
          id
          user {
            ...UserPreview
          }
          activityType # "ATTENDING", "HOSTING", "POSTED", "REVIEWED"
          timestamp

          # Activity details
          content {
            message
            media {
              type # "PHOTO", "VIDEO", "STORY"
              url
              thumbnailUrl
            }
            mood # User's mood/vibe
            rating # If it's a review
          }

          # Related party/venue
          party {
            id
            name
            imageUrl
            startDate
            venue {
              name
              address
            }
            attendeeCount
            isUserAttending(userId: $userId)
          }

          venue {
            id
            name
            imageUrl
            currentEvent {
              id
              name
              startDate
            }
          }

          # Engagement on the activity
          reactions {
            type
            count
            hasUserReacted(userId: $userId)
          }
          comments {
            count
            preview(first: 2) {
              user {
                ...UserPreview
              }
              content
            }
          }
        }
        cursor
      }
      pageInfo {
        hasNextPage
        endCursor
      }
    }
  }
`;

// Trending with rich analytics and insights
export const GET_TRENDING_FEED = gql`
  ${PARTY_DETAILS_FRAGMENT}

  query GetTrendingFeed(
    $latitude: Float
    $longitude: Float
    $radius: Int
    $timeframe: TimeFrame!
    $limit: Int
    $cursor: String
    $userId: ID!
  ) {
    trendingFeed(
      latitude: $latitude
      longitude: $longitude
      radius: $radius
      cursor: $cursor
      limit: $limit
    ) {
      items {
        id
        type
        trendingScore
        engagementCount

        # Why it's trending
        trendingReason {
          primary # "Selling out fast"
          secondary # "90% of attendees rate it 5 stars"
          metrics {
            percentageIncrease
            comparedTo # "last week"
          }
        }

        # Rich content based on type
        party {
          ...PartyDetails

          # Trending specific insights
          insights {
            type # "PEAK_TIME", "DEMOGRAPHIC", "ENGAGEMENT"
            title
            description
            confidence
          }
        }

        venue {
          id
          name
          imageUrl
          rating
          venueType {
            displayName
            icon
          }

          # Live venue data
          currentOccupancy
          maxOccupancy
          occupancyTrend # "INCREASING", "STABLE", "DECREASING"
          peakHours

          # What's happening there
          currentEvents {
            ...PartyDetails
          }
          upcomingEvents(first: 3) {
            ...PartyDetails
          }
        }
      }
      cursor
      hasMore
    }
  }
`;

// Mood-based discovery
export const GET_MOOD_BASED_FEED = gql`
  ${ENHANCED_FEED_ITEM_FRAGMENT}

  query GetMoodBasedFeed($mood: String!, $cursor: String, $limit: Int, $userId: ID!) {
    moodBasedFeed(mood: $mood, cursor: $cursor, limit: $limit) {
      items {
        ...EnhancedFeedItemFields

        # Mood match explanation
        moodMatch {
          score
          explanation # "Perfect for a chill evening"
          suggestedActivities
          matchingElements # ["Relaxed atmosphere", "Acoustic music"]
        }
      }
      cursor
      hasMore
    }
  }
`;

// Weekend suggestions with group planning
export const GET_WEEKEND_SUGGESTIONS = gql`
  ${ENHANCED_FEED_ITEM_FRAGMENT}
  ${USER_PREVIEW_FRAGMENT}

  query GetWeekendSuggestions($cursor: String, $limit: Int, $userId: ID!, $groupSize: Int) {
    weekendSuggestions(cursor: $cursor, limit: $limit) {
      items {
        ...EnhancedFeedItemFields

        # Weekend specific
        weekendHighlight {
          type # "SATURDAY_NIGHT", "SUNDAY_BRUNCH", "FRIDAY_KICKOFF"
          bestTimeToArrive
          expectedCrowd # "BUSY", "MODERATE", "INTIMATE"
        }

        # Group suggestions
        groupSuggestion {
          idealGroupSize
          friendsWhoMightLike {
            ...UserPreview
            reason # "Also loves EDM"
          }
          groupDiscount
          groupActivities
        }
      }
      cursor
      hasMore
    }
  }
`;

// Detailed party view with everything
export const GET_PARTY_DETAILS = gql`
  ${PARTY_DETAILS_FRAGMENT}

  query GetPartyDetails($id: ID!, $userId: ID!) {
    party(id: $id) {
      ...PartyDetails

      # Additional details for full view
      rules

      # Rich content
      content
      mediaGallery

      # Ticket information
      ticketTypes {
        id
        name
        price
        currency
        available
        maxQuantity
        perks
        salesEndDate
      }

      # Similar events
      similarParties(first: 5) {
        ...PartyDetails
      }

      # User's friends' activity
      friendsActivity {
        attending {
          ...UserPreview
        }
        interested {
          ...UserPreview
        }
        posts {
          user {
            ...UserPreview
          }
          content
          createdAt
        }
      }
    }
  }
`;

// Venue details with live information
export const GET_VENUE_DETAILS = gql`
  query GetVenueDetails($id: ID!, $userId: ID!) {
    venue(id: $id) {
      id
      name
      description
      address
      city
      state
      zipCode
      country
      latitude
      longitude
      imageUrl
      images {
        id
        url
        caption
        type # "INTERIOR", "EXTERIOR", "MENU", "AMBIANCE"
      }

      # Venue characteristics
      venueType {
        id
        displayName
        icon
      }
      amenities
      capacity

      # Operating info
      operatingHours {
        dayOfWeek
        openTime
        closeTime
        isOpen24Hours
      }

      # Live status
      currentOccupancy
      maxOccupancy
      waitTime

      # Ratings and reviews
      rating
      totalReviews
      ratingDistribution {
        stars
        count
      }

      # Current and upcoming
      currentEvents {
        id
        name
        startDate
        endDate
        attendeeCount
      }
      upcomingEvents(first: 10) {
        id
        name
        startDate
        imageUrl
        ticketPrice {
          min
          max
          currency
        }
      }

      # Social
      friendsWhoVisited {
        ...UserPreview
        lastVisit
        favoriteEvent
      }

      # User relationship
      isFavorite(userId: $userId)
      userVisitCount(userId: $userId)
    }
  }
`;

// Mutations for engagement
export const ENGAGE_FEED_ITEM = gql`
  mutation EngageFeedItem($feedItemId: UUID!, $engagementType: FeedEngagementType!) {
    engageFeedItem(feedItemId: $feedItemId, engagementType: $engagementType)
  }
`;

export const UPDATE_PARTY_ATTENDANCE = gql`
  mutation UpdatePartyAttendance($partyId: UUID!, $status: PartyAttendanceStatus!) {
    updateAttendance(partyId: $partyId, status: $status) {
      id
      status
    }
  }
`;

export const SAVE_TO_FAVORITES = gql`
  mutation SaveToFavorites($itemId: UUID!, $itemType: FeedItemType!) {
    addToFavorites(itemId: $itemId, itemType: $itemType) {
      success
      message
    }
  }
`;
