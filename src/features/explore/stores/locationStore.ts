import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

import { appStorage, createZustandMMKVStorage } from '@/src/core';

export interface Location {
  id: string;
  name: string;
  latitude: number;
  longitude: number;
  radius: number; // in kilometers
}

interface LocationState {
  currentLocation: Location | null;
  savedLocations: Location[];
  setCurrentLocation: (location: Location) => void;
  addSavedLocation: (location: Location) => void;
  removeSavedLocation: (locationId: string) => void;
  clearCurrentLocation: () => void;
}

export const useLocationStore = create<LocationState>()(
  persist(
    set => ({
      currentLocation: null,
      savedLocations: [],

      setCurrentLocation: location => set({ currentLocation: location }),

      addSavedLocation: location =>
        set(state => ({
          savedLocations: [...state.savedLocations, location],
        })),

      removeSavedLocation: locationId =>
        set(state => ({
          savedLocations: state.savedLocations.filter(loc => loc.id !== locationId),
        })),

      clearCurrentLocation: () => set({ currentLocation: null }),
    }),
    {
      name: 'location-storage',
      storage: createJSONStorage(() => createZustandMMKVStorage(appStorage)),
    }
  )
);
