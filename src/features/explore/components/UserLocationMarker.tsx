import React from 'react';

import { CrosshairSimple } from 'phosphor-react-native';

import { useTheme } from '@/src/core/theme';
import Box from '@/src/core/theme/Box';

interface UserLocationMarkerProps {
  size?: number;
}

export default function UserLocationMarker({ size = 32 }: UserLocationMarkerProps) {
  const theme = useTheme();

  return (
    <Box
      width={size}
      height={size}
      borderRadius="circle_9999"
      backgroundColor="primary"
      alignItems="center"
      justifyContent="center"
      style={{
        shadowColor: theme.colors.primary,
        shadowOffset: { width: 0, height: 0 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 8,
      }}>
      <Box
        width={size * 0.5}
        height={size * 0.5}
        borderRadius="circle_9999"
        backgroundColor="white"
        alignItems="center"
        justifyContent="center">
        <CrosshairSimple size={size * 0.3} weight="bold" color={theme.colors.primary} />
      </Box>
    </Box>
  );
}
