import React from 'react';

import Image from '@d11/react-native-fast-image';
import { MapPin, Star, Users } from 'phosphor-react-native';

import { useTheme } from '@/src/core/theme';
import Box from '@/src/core/theme/Box';
import Pressable from '@/src/core/theme/Pressable';
import Text from '@/src/core/theme/Text';

import { FeedItem } from '../types/event';

interface FeedItemCardProps {
  item: FeedItem;
  onPress?: () => void;
}

export default function FeedItemCard({ item, onPress }: FeedItemCardProps) {
  const theme = useTheme();

  const getItemIcon = () => {
    switch (item.itemType) {
      case 'party':
      case 'event':
        return <Users size={16} color={theme.colors.iconMuted} />;
      case 'venue':
        return <MapPin size={16} color={theme.colors.iconMuted} />;
      default:
        return null;
    }
  };

  const formatDistance = (distance?: number) => {
    if (!distance) return '';
    if (distance < 1) return `${Math.round(distance * 1000)}m`;
    return `${distance.toFixed(1)}km`;
  };

  return (
    <Pressable onPress={onPress}>
      <Box
        backgroundColor="cardBackground"
        borderRadius="lg_16"
        overflow="hidden"
        borderWidth={1}
        borderColor="mainBorder">
        {/* Image */}
        {item.imageUrl && (
          <Box height={200} backgroundColor="elevatedBackground">
            <Image
              source={{ uri: item.imageUrl }}
              style={{ width: '100%', height: '100%' }}
              resizeMode="cover"
            />

            {/* Compatibility Score Badge */}
            {item.overallScore > 0.7 && (
              <Box
                position="absolute"
                top={12}
                right={12}
                backgroundColor="successMain"
                paddingHorizontal="sm_12"
                paddingVertical="xxs_4"
                borderRadius="sm_8"
                flexDirection="row"
                alignItems="center"
                gap="xxs_4">
                <Star size={12} color={theme.colors.white} weight="fill" />
                <Text variant="l_12SemiBold_button" color="white">
                  {Math.round(item.overallScore * 100)}% Match
                </Text>
              </Box>
            )}
          </Box>
        )}

        {/* Content */}
        <Box padding="md_16">
          {/* Header */}
          <Box
            flexDirection="row"
            alignItems="center"
            justifyContent="space-between"
            marginBottom="xs_8">
            <Box flexDirection="row" alignItems="center" gap="xs_8" flex={1}>
              {getItemIcon()}
              <Text variant="h_16SemiBold_alertTitle" numberOfLines={1} flex={1}>
                {item.name}
              </Text>
            </Box>

            {item.distance && (
              <Text variant="l_12Regular_helperText" color="secondaryText">
                {formatDistance(item.distance)}
              </Text>
            )}
          </Box>

          {/* Description */}
          {item.description && (
            <Text
              variant="b_14Regular_content"
              color="secondaryText"
              numberOfLines={2}
              marginBottom="sm_12">
              {item.description}
            </Text>
          )}

          {/* Tags */}
          {item.tags && item.tags.length > 0 && (
            <Box flexDirection="row" flexWrap="wrap" gap="xs_8" marginBottom="sm_12">
              {item.tags.slice(0, 3).map((tag, index) => (
                <Box
                  key={index}
                  backgroundColor="elevatedBackground"
                  paddingHorizontal="xs_8"
                  paddingVertical="xxs_4"
                  borderRadius="xs_4">
                  <Text variant="l_10SemiBold_chip" color="mainText">
                    {tag}
                  </Text>
                </Box>
              ))}
            </Box>
          )}

          {/* Contextual Note */}
          {item.contextualNote && (
            <Box
              backgroundColor="focusBackground"
              padding="xs_8"
              borderRadius="sm_8"
              marginTop="xs_8">
              <Text variant="l_12Regular_helperText" color="primaryText">
                {item.contextualNote}
              </Text>
            </Box>
          )}

          {/* Date/Time */}
          {item.startDate && (
            <Text variant="l_12Regular_helperText" color="secondaryText" marginTop="xs_8">
              {new Date(item.startDate).toLocaleDateString()} • {item.timeOfDay}
            </Text>
          )}
        </Box>
      </Box>
    </Pressable>
  );
}
