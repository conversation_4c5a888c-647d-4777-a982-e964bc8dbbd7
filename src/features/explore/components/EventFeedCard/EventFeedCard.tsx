import React, { memo } from 'react';

import {
  BookmarkSimple,
  CaretRight,
  Clock,
  DotsThreeVertical,
  Fire,
  Images,
  MapPin,
  SealCheck,
  Snowflake,
  Star,
  Ticket,
} from 'phosphor-react-native';

import { Box, Pressable, Text, useTheme } from '@/src/core/theme';
import { Divider } from '@/src/shared/components';

import { EventFeedCardProps } from './types';

// Pill component for trending badge
const Pill = ({
  size,
  variant,
  icon,
  label,
}: {
  size: 'small' | 'medium';
  variant: 'primary' | 'accent';
  icon?: React.ReactNode;
  label: string;
}) => {
  const backgroundColor = variant === 'accent' ? 'warningMain' : 'brandMain';
  const textColor = variant === 'accent' ? 'warningDark' : 'inverseText';

  return (
    <Box
      flexDirection="row"
      alignItems="center"
      gap="xxs_4"
      paddingHorizontal={size === 'small' ? 'xs_8' : 'sm_12'}
      paddingVertical={size === 'small' ? 'xxs_4' : 'xs_8'}
      backgroundColor={backgroundColor}
      borderRadius="xs_4">
      {icon}
      <Text
        variant={size === 'small' ? 'l_10SemiBold_tag' : 'l_12SemiBold_button'}
        color={textColor}>
        {label}
      </Text>
    </Box>
  );
};

// Chip component for tags
const Chip = ({
  size,
  variant,
  icon,
  label,
}: {
  size: 'small' | 'medium';
  variant: 'default' | 'secondary' | 'selected';
  icon?: React.ReactNode;
  label: string;
}) => {
  const getBackgroundColor = () => {
    switch (variant) {
      case 'secondary':
        return 'surfaceBackground';
      case 'selected':
        return 'buttonTintedBackground';
      default:
        return 'subtleBackground';
    }
  };

  const getTextColor = () => {
    switch (variant) {
      case 'secondary':
      case 'selected':
        return 'buttonTintedText';
      default:
        return 'mutedText';
    }
  };

  return (
    <Box
      flexDirection="row"
      alignItems="center"
      gap="xxs_4"
      paddingHorizontal={size === 'small' ? 'xs_8' : 'sm_12'}
      paddingVertical={size === 'small' ? 'xxs_4' : 'xs_8'}
      backgroundColor={getBackgroundColor()}
      borderRadius="xs_4">
      {icon}
      <Text
        variant={size === 'small' ? 'l_10SemiBold_tag' : 'l_12SemiBold_button'}
        color={getTextColor()}>
        {label}
      </Text>
    </Box>
  );
};

// Button component for actions
const Button = ({
  variant,
  size,
  onPress,
  disabled,
  children,
}: {
  variant: 'primary' | 'secondary';
  size: 'small' | 'medium';
  onPress?: () => void;
  disabled?: boolean;
  children: React.ReactNode;
}) => {
  const backgroundColor =
    variant === 'primary'
      ? disabled
        ? 'disabledBackground'
        : 'buttonFilledBackground'
      : 'buttonTintedBackground';

  return (
    <Pressable onPress={onPress} enabled={!disabled}>
      <Box
        paddingHorizontal={size === 'small' ? 'sm_12' : 'md_16'}
        paddingVertical={size === 'small' ? 'xs_8' : 'sm_12'}
        backgroundColor={backgroundColor}
        borderRadius="sm_8"
        opacity={disabled ? 0.6 : 1}>
        {children}
      </Box>
    </Pressable>
  );
};

const EventFeedCard = memo<EventFeedCardProps>(
  ({
    variant = 'minimal',
    event,
    onPress,
    onJoin,
    onRequestInvite,
    onFavorite,
    onBookmark,
    onShowMore,
    onViewTickets,
    onViewGallery,
    isSelected = false,
  }) => {
    const theme = useTheme();

    const renderHeader = () => (
      <Box flexDirection="row" justifyContent="space-between" alignItems="center" gap="sm_12">
        <Box flexDirection="row" gap="xs_8" flexShrink={1} flexWrap="wrap">
          {event.isTrending && (
            <Pill
              size="small"
              variant="accent"
              icon={<Fire size={12} color={theme.colors.buttonFilledText} />}
              label="Trending"
            />
          )}
          {event.isOfficial && (
            <Chip
              size="small"
              variant="secondary"
              icon={<SealCheck size={12} color={theme.colors.iconPrimary} />}
              label="Official event"
            />
          )}
        </Box>
        {event.startTime && (
          <Box flexDirection="row" alignItems="center" gap="xxs_4">
            <Clock size={12} color={theme.colors.iconMuted} />
            <Text variant="l_12Regular_helperText" color="mutedText">
              {event.startTime}
            </Text>
          </Box>
        )}
      </Box>
    );

    const renderTitle = () => (
      <Box>
        <Text variant="h_18SemiBold_cardTitle" color="mainText" numberOfLines={2}>
          {event.title}
        </Text>
      </Box>
    );

    const renderLocation = () => (
      <Box flexDirection="row" justifyContent="space-between" alignItems="center">
        <Box flexDirection="row" alignItems="center" gap="xxs_4" flex={1}>
          <MapPin size={14} color={theme.colors.iconDefault} />
          <Text variant="l_14Medium_info" color="secondaryText" numberOfLines={1}>
            {event.location.name}
          </Text>
        </Box>
        {event.location.distance && (
          <Text variant="l_14Medium_info" color="mutedText">
            {event.location.distance}
          </Text>
        )}
      </Box>
    );

    const renderTags = () => {
      if (!event.tags || event.tags.length === 0) return null;

      return (
        <Box flexDirection="row" gap="xs_8" flexWrap="wrap">
          {event.tags.map((tag, index) => (
            <Chip key={`${tag}-${index}`} size="small" variant="default" label={tag} />
          ))}
        </Box>
      );
    };

    const renderDescription = () => {
      if (!event.description) return null;

      return (
        <Box>
          <Text
            variant="b_14Regular_content"
            color="secondaryText"
            numberOfLines={variant === 'compact' ? 2 : 3}>
            {event.description}
          </Text>
        </Box>
      );
    };

    const renderStats = () => (
      <Box flexDirection="row" gap="md_16">
        <Box flexDirection="row" alignItems="center" gap="xxs_4">
          <Fire size={16} color={theme.colors.iconWarning} />
          <Text variant="l_12Regular_helperText" color="secondaryText">
            {event.stats.interested}
          </Text>
        </Box>
        <Box flexDirection="row" alignItems="center" gap="xxs_4">
          <Snowflake size={16} color={theme.colors.iconAccent} />
          <Text variant="l_12Regular_helperText" color="secondaryText">
            {event.stats.attending}
          </Text>
        </Box>
      </Box>
    );

    const renderActions = () => {
      const showMinimalActions = variant === 'minimal' || variant === 'withIcons';
      const showFullActions = variant === 'full' || variant === 'detailed';

      if (showMinimalActions) {
        return (
          <Box flexDirection="row" alignItems="center" gap="xs_8">
            {variant === 'withIcons' && (
              <>
                <Pressable onPress={onViewTickets}>
                  <Ticket size={20} color={theme.colors.iconDefault} />
                </Pressable>
                <Pressable onPress={onViewGallery}>
                  <Images size={20} color={theme.colors.iconDefault} />
                </Pressable>
              </>
            )}
            <CaretRight size={20} color={theme.colors.iconDefault} />
          </Box>
        );
      }

      if (showFullActions) {
        return (
          <Box flexDirection="row" alignItems="center" gap="sm_12">
            <Pressable onPress={onFavorite}>
              <Star
                size={20}
                color={event.isFavorited ? theme.colors.iconWarning : theme.colors.iconDefault}
              />
            </Pressable>
            <Pressable onPress={onBookmark}>
              <BookmarkSimple
                size={20}
                color={event.isBookmarked ? theme.colors.iconPrimary : theme.colors.iconDefault}
              />
            </Pressable>
            <Pressable onPress={onShowMore}>
              <DotsThreeVertical size={20} color={theme.colors.iconDefault} />
            </Pressable>
          </Box>
        );
      }

      return null;
    };

    const renderBottomActions = () => {
      if (variant !== 'detailed') return null;

      return (
        <Box gap="sm_12">
          <Divider />
          <Box flexDirection="row" justifyContent="space-between" alignItems="center">
            <Box flexDirection="row" gap="md_16">
              <Box flexDirection="row" alignItems="center" gap="xxs_4">
                <Fire size={16} color={theme.colors.iconWarning} />
                <Text variant="l_12Regular_helperText" color="secondaryText">
                  {event.stats.interested}
                </Text>
              </Box>
              <Box flexDirection="row" alignItems="center" gap="xxs_4">
                <Snowflake size={16} color={theme.colors.iconAccent} />
                <Text variant="l_12Regular_helperText" color="secondaryText">
                  {event.stats.attending}
                </Text>
              </Box>
            </Box>
            <Box flexDirection="row" gap="xs_8">
              {event.requiresInvite ? (
                <>
                  <Button variant="secondary" size="small" onPress={onRequestInvite}>
                    <Text variant="l_12SemiBold_button" color="buttonTintedText">
                      Request Invite
                    </Text>
                  </Button>
                  <Button variant="primary" size="small" onPress={onJoin} disabled={!event.canJoin}>
                    <Text variant="l_12SemiBold_button" color="buttonFilledText">
                      Join
                    </Text>
                  </Button>
                </>
              ) : (
                <Button variant="primary" size="small" onPress={onJoin}>
                  <Text variant="l_12SemiBold_button" color="buttonFilledText">
                    Join
                  </Text>
                </Button>
              )}
            </Box>
          </Box>
        </Box>
      );
    };

    const content = (
      <Box
        backgroundColor={isSelected ? 'focusBackground' : 'cardBackground'}
        padding="md_16"
        borderRadius="lg_16"
        borderWidth={1}
        borderColor={isSelected ? 'focusBorder' : 'mainBorder'}
        gap="sm_12">
        {renderHeader()}
        {renderTitle()}

        {variant !== 'minimal' && (
          <>
            <Box gap="xs_8">
              {renderLocation()}
              {variant === 'full' || variant === 'detailed' ? renderTags() : null}
            </Box>
          </>
        )}

        {(variant === 'full' || variant === 'detailed') && renderDescription()}

        {variant === 'compact' && (
          <Box flexDirection="row" justifyContent="space-between" alignItems="center">
            {renderStats()}
            {renderActions()}
          </Box>
        )}

        {variant === 'full' && (
          <>
            <Divider />
            <Box flexDirection="row" justifyContent="space-between" alignItems="center">
              {renderStats()}
              {renderActions()}
            </Box>
          </>
        )}

        {variant === 'minimal' && renderActions()}
        {variant === 'withIcons' && (
          <>
            <Divider />
            <Box flexDirection="row" justifyContent="space-between" alignItems="center">
              {renderStats()}
              {renderActions()}
            </Box>
          </>
        )}

        {renderBottomActions()}
      </Box>
    );

    if (onPress) {
      return <Pressable onPress={onPress}>{content}</Pressable>;
    }

    return content;
  }
);

EventFeedCard.displayName = 'EventFeedCard';

export { EventFeedCard };
