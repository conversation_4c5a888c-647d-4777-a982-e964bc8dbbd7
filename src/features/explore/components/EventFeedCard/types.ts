export type EventFeedVariant = 'minimal' | 'compact' | 'full' | 'detailed' | 'withIcons';

export interface EventLocation {
  name: string;
  distance?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

export interface EventStats {
  interested: number;
  attending: number;
}

export interface Event {
  id: string;
  title: string;
  startTime?: string;
  endTime?: string;
  location: EventLocation;
  description?: string;
  tags?: string[];
  stats: EventStats;
  isTrending?: boolean;
  isOfficial?: boolean;
  isFavorited?: boolean;
  isBookmarked?: boolean;
  requiresInvite?: boolean;
  canJoin?: boolean;
  coverImage?: string;
  hostName?: string;
  price?: number;
}

export interface EventFeedCardProps {
  variant?: EventFeedVariant;
  event: Event;
  onPress?: () => void;
  onJoin?: () => void;
  onRequestInvite?: () => void;
  onFavorite?: () => void;
  onBookmark?: () => void;
  onShowMore?: () => void;
  onViewTickets?: () => void;
  onViewGallery?: () => void;
  isSelected?: boolean;
}
