# EventFeedCard Component

A highly scalable and flexible component for displaying event information in the Movuca app's Discover feed. This component supports multiple variants to accommodate different UI needs while maintaining consistency with the design system.

## Component Variants

The `EventFeedCard` component supports 5 different variants:

### 1. `minimal` (Version 1)
- Shows trending/official badges and start time
- Event title
- Basic navigation arrow
- Engagement stats (interested/attending)

### 2. `compact` (Version 3)
- All minimal features
- Plus location information
- Inline stats and actions

### 3. `full` (Version 2)
- All compact features
- Location with distance
- Event tags
- Event description
- Action buttons (favorite, bookmark, more)

### 4. `detailed` (Version 4 & 5)
- All full features
- Join/Request Invite buttons
- Complete action bar with stats

### 5. `withIcons` (Version 1 extended)
- Minimal layout with additional action icons
- Tickets and gallery quick access

## Usage

```tsx
import { EventFeedCard } from '@/src/features/discover/components/EventFeedCard';

const event = {
  id: '1',
  title: 'Midnight vibes After party',
  startTime: 'Starts at 1 AM',
  location: {
    name: '<PERSON><PERSON>',
    distance: '2.3mi',
  },
  description: 'Exclusive after hours set...',
  tags: ['Rooftop', 'House', 'Barbecue'],
  stats: {
    interested: 24,
    attending: 12,
  },
  isTrending: true,
  isOfficial: false,
};

<EventFeedCard
  variant="compact"
  event={event}
  onPress={() => console.log('Navigate to event')}
  onJoin={() => console.log('Join event')}
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `variant` | `'minimal' \| 'compact' \| 'full' \| 'detailed' \| 'withIcons'` | `'minimal'` | Visual variant of the card |
| `event` | `Event` | Required | Event data object |
| `onPress` | `() => void` | - | Handler for card press |
| `onJoin` | `() => void` | - | Handler for join button |
| `onRequestInvite` | `() => void` | - | Handler for request invite button |
| `onFavorite` | `() => void` | - | Handler for favorite button |
| `onBookmark` | `() => void` | - | Handler for bookmark button |
| `onShowMore` | `() => void` | - | Handler for more options |
| `onViewTickets` | `() => void` | - | Handler for ticket viewing |
| `onViewGallery` | `() => void` | - | Handler for gallery viewing |
| `isSelected` | `boolean` | `false` | Shows selected state |

## Event Object Structure

```tsx
interface Event {
  id: string;
  title: string;
  startTime?: string;
  endTime?: string;
  location: {
    name: string;
    distance?: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  description?: string;
  tags?: string[];
  stats: {
    interested: number;
    attending: number;
  };
  isTrending?: boolean;
  isOfficial?: boolean;
  isFavorited?: boolean;
  isBookmarked?: boolean;
  requiresInvite?: boolean;
  canJoin?: boolean;
  coverImage?: string;
  hostName?: string;
  price?: number;
}
```

## Design Tokens

The component uses the following design tokens from the theme:

- **Colors**: `mainBackground`, `cardBackground`, `focusBackground`, `mainBorder`, `focusBorder`, etc.
- **Typography**: `h_18SemiBold_cardTitle`, `l_14Medium_info`, `b_14Regular_content`, etc.
- **Spacing**: `md_16`, `sm_12`, `xs_8`, `xxs_4`
- **Border Radius**: `lg_16`, `sm_8`, `xs_4`

## Customization

The component is designed to work seamlessly with the Movuca theme system. To customize:

1. Use the theme provider to override default values
2. Pass different variants based on your UI needs
3. Handle all callback props to integrate with your navigation and state management

## Icons Integration

Currently using placeholder icons. Replace the icon components with actual Phosphor icons from your icon library:

```tsx
import { 
  Fire, 
  Snowflake, 
  Clock, 
  MapPin, 
  Star, 
  BookmarkSimple, 
  DotsThreeVertical, 
  CaretRight, 
  Ticket, 
  Images, 
  SealCheck 
} from '@/src/shared/icons';
```

## Performance

- Component is memoized for optimal re-rendering
- Text components use `numberOfLines` for performance
- Conditional rendering prevents unnecessary DOM nodes

## Accessibility

- All interactive elements are wrapped in `Pressable` components
- Proper color contrast following the design system
- Support for disabled states on action buttons
