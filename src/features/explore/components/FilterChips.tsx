import React from 'react';

import { useTheme } from '@/src/core/theme';
import Box from '@/src/core/theme/Box';
import Pressable from '@/src/core/theme/Pressable';
import ScrollableContainer from '@/src/core/theme/ScrollableContainer';
import Text from '@/src/core/theme/Text';

interface FilterChip {
  id: string;
  label: string;
  selected?: boolean;
}

interface FilterChipsProps {
  chips: FilterChip[];
  onChipPress: (chipId: string) => void;
}

export default function FilterChips({ chips, onChipPress }: FilterChipsProps) {
  const theme = useTheme();

  return (
    <ScrollableContainer
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={{
        paddingHorizontal: 16,
        gap: 8,
      }}>
      {chips.map(chip => (
        <Pressable
          key={chip.id}
          onPress={() => onChipPress(chip.id)}
          paddingVertical="xs_8"
          paddingHorizontal="sm_12"
          backgroundColor={chip.selected ? 'buttonFilledBackground' : 'elevatedBackground'}
          borderRadius="sm_8"
          borderWidth={1}
          borderColor={chip.selected ? 'buttonFilledBackground' : 'mainBorder'}>
          <Text variant="l_12SemiBold_button" color={chip.selected ? 'inverseText' : 'mainText'}>
            {chip.label}
          </Text>
        </Pressable>
      ))}
    </ScrollableContainer>
  );
}
