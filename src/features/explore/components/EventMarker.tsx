import React from 'react';

import { MapPin } from 'phosphor-react-native';

import { useTheme } from '@/src/core/theme';
import Box from '@/src/core/theme/Box';
import { TokensThemeColors } from '@/src/core/theme/theme';

interface EventMarkerProps {
  selected?: boolean;
  trending?: boolean;
}

export default function EventMarker({ selected, trending }: EventMarkerProps) {
  const theme = useTheme();

  // Determine marker color based on state
  const getMarkerColor: () => TokensThemeColors = () => {
    if (selected) return 'primary';
    if (trending) return 'warningMain';
    return 'iconMuted';
  };

  return (
    <Box
      width={32}
      height={32}
      borderRadius="circle_9999"
      backgroundColor="cardBackground"
      borderWidth={2}
      borderColor={getMarkerColor()}
      alignItems="center"
      justifyContent="center"
      style={{
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
      }}>
      <MapPin size={18} weight="fill" color={getMarkerColor()} />
    </Box>
  );
}
