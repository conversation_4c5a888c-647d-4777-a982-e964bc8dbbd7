import React, { useCallback, useState } from 'react';

import { Modal, ScrollView, TouchableOpacity } from 'react-native';

import { CaretDown, Clock, Funnel, X } from 'phosphor-react-native';

import { haptics } from '@/src/core/libs';
import { Box, Pressable, Text } from '@/src/core/theme';

export type TimeFilterOption = {
  value: string;
  label: string;
  time: string;
};

export type CategoryFilter = {
  id: string;
  label: string;
  count?: number;
};

export interface DiscoverFilterProps {
  selectedTime: string;
  selectedCategories: string[];
  onTimeChange: (time: string) => void;
  onCategoriesChange: (categories: string[]) => void;
  onReset?: () => void;
}

const timeOptions: TimeFilterOption[] = [
  { value: 'all', label: 'All Times', time: '' },
  { value: 'after11pm', label: 'After 11 PM', time: '23:00' },
  { value: 'after1am', label: 'After 1 AM', time: '01:00' },
  { value: 'after3am', label: 'After 3 AM', time: '03:00' },
];

const categoryOptions: CategoryFilter[] = [
  { id: 'all', label: 'All' },
  { id: 'afterparties', label: 'After parties', count: 12 },
  { id: 'houseparties', label: 'House parties', count: 8 },
  { id: 'lounges', label: 'Lounges', count: 15 },
  { id: 'electronic', label: 'Electronic', count: 24 },
  { id: 'clubs', label: 'Clubs', count: 6 },
  { id: 'rooftop', label: 'Rooftop', count: 10 },
  { id: 'beach', label: 'Beach', count: 5 },
  { id: 'barbecue', label: 'Barbecue', count: 7 },
];

// Filter Chip Component
const FilterChip = ({
  label,
  isActive,
  onPress,
  count,
}: {
  label: string;
  isActive: boolean;
  onPress: () => void;
  count?: number;
}) => {
  return (
    <Pressable onPress={onPress}>
      <Box
        paddingHorizontal="sm_12"
        paddingVertical="xxs_4"
        backgroundColor={isActive ? 'buttonTintedBackground' : 'subtleBackground'}
        borderRadius="sm_8"
        flexDirection="row"
        alignItems="center"
        gap="xxs_4">
        <Text variant="l_10SemiBold_chip" color={isActive ? 'buttonTintedText' : 'mutedText'}>
          {label}
        </Text>
        {count && (
          <Text variant="l_8SemiBold_hint" color={isActive ? 'buttonTintedText' : 'mutedText'}>
            ({count})
          </Text>
        )}
      </Box>
    </Pressable>
  );
};

export function DiscoverFilter({
  selectedTime,
  selectedCategories,
  onTimeChange,
  onCategoriesChange,
  onReset,
}: DiscoverFilterProps) {
  const [showTimeModal, setShowTimeModal] = useState(false);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  const selectedTimeOption = timeOptions.find(opt => opt.value === selectedTime) || timeOptions[0];
  const hasActiveFilters = selectedCategories.length > 0 && !selectedCategories.includes('all');

  const handleTimePress = useCallback(async () => {
    await haptics.light();
    setShowTimeModal(true);
  }, []);

  const handleTimeSelect = useCallback(
    async (value: string) => {
      await haptics.selection();
      onTimeChange(value);
      setShowTimeModal(false);
    },
    [onTimeChange]
  );

  const handleFilterPress = useCallback(async () => {
    await haptics.light();
    setShowAdvancedFilters(!showAdvancedFilters);
  }, [showAdvancedFilters]);

  const handleCategoryToggle = useCallback(
    async (categoryId: string) => {
      await haptics.selection();

      if (categoryId === 'all') {
        onCategoriesChange(['all']);
      } else {
        const newCategories = selectedCategories.includes(categoryId)
          ? selectedCategories.filter(id => id !== categoryId)
          : [...selectedCategories.filter(id => id !== 'all'), categoryId];

        onCategoriesChange(newCategories.length === 0 ? ['all'] : newCategories);
      }
    },
    [selectedCategories, onCategoriesChange]
  );

  const handleReset = useCallback(async () => {
    await haptics.light();
    onReset?.();
  }, [onReset]);

  return (
    <>
      <Box
        backgroundColor="cardBackground"
        paddingHorizontal="md_16"
        paddingVertical="sm_12"
        borderBottomWidth={1}
        borderBottomColor="mainBorder">
        <Box gap="xs_8">
          {/* Header Row */}
          <Box flexDirection="row" justifyContent="space-between" alignItems="center">
            {/* Time Filter */}
            <Pressable onPress={handleTimePress}>
              <Box flexDirection="row" alignItems="center" gap="xxs_4">
                <Clock size={14} color="#424242" />
                <Box flexDirection="row" alignItems="center">
                  <Text variant="l_10SemiBold_chip" color="secondaryText">
                    {selectedTimeOption.label}
                  </Text>
                  <Box marginLeft="xxxs_2">
                    <CaretDown size={10} color="#424242" />
                  </Box>
                </Box>
              </Box>
            </Pressable>

            {/* Filter Button */}
            <Box flexDirection="row" alignItems="center" gap="xs_8">
              {hasActiveFilters && (
                <Pressable onPress={handleReset}>
                  <Text variant="l_12SemiBold_button" color="errorMain">
                    Clear
                  </Text>
                </Pressable>
              )}
              <Pressable onPress={handleFilterPress}>
                <Box flexDirection="row" alignItems="center" gap="xxs_4">
                  <Funnel size={14} color="#757575" />
                  <Text variant="l_14SemiBold_action" color="mutedText">
                    Filter
                  </Text>
                </Box>
              </Pressable>
            </Box>
          </Box>

          {/* Category Filters */}
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={{ marginHorizontal: -16 }}
            contentContainerStyle={{ paddingHorizontal: 16 }}>
            <Box flexDirection="row" gap="xs_8">
              {categoryOptions.slice(0, 6).map(filter => (
                <FilterChip
                  key={filter.id}
                  label={filter.label}
                  count={filter.count}
                  isActive={selectedCategories.includes(filter.id)}
                  onPress={() => handleCategoryToggle(filter.id)}
                />
              ))}
            </Box>
          </ScrollView>
        </Box>

        {/* Advanced Filters Panel */}
        {showAdvancedFilters && (
          <Box marginTop="sm_12" gap="sm_12">
            <Box>
              <Text variant="l_14SemiBold_action" color="mainText" marginBottom="xs_8">
                Event Types
              </Text>
              <Box flexDirection="row" flexWrap="wrap" gap="xs_8">
                {categoryOptions.map(filter => (
                  <FilterChip
                    key={filter.id}
                    label={filter.label}
                    count={filter.count}
                    isActive={selectedCategories.includes(filter.id)}
                    onPress={() => handleCategoryToggle(filter.id)}
                  />
                ))}
              </Box>
            </Box>

            <Box>
              <Text variant="l_14SemiBold_action" color="mainText" marginBottom="xs_8">
                Additional Filters
              </Text>
              <Text variant="l_12Regular_helperText" color="mutedText">
                More filter options coming soon!
              </Text>
            </Box>
          </Box>
        )}
      </Box>

      {/* Time Selection Modal */}
      <Modal
        visible={showTimeModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowTimeModal(false)}>
        <Pressable
          style={{ flex: 1, backgroundColor: 'rgba(0,0,0,0.5)' }}
          onPress={() => setShowTimeModal(false)}>
          <Box flex={1} justifyContent="flex-end">
            <Box
              backgroundColor="cardBackground"
              borderTopLeftRadius="xl_20"
              borderTopRightRadius="xl_20"
              paddingTop="lg_24"
              paddingBottom="xl_32"
              paddingHorizontal="md_16">
              <Box
                flexDirection="row"
                justifyContent="space-between"
                alignItems="center"
                marginBottom="lg_24">
                <Text variant="h_20Medium_subsection" color="mainText">
                  Select Time
                </Text>
                <Pressable onPress={() => setShowTimeModal(false)}>
                  <X size={24} color="#757575" />
                </Pressable>
              </Box>

              {timeOptions.map(option => (
                <TouchableOpacity
                  key={option.value}
                  onPress={() => handleTimeSelect(option.value)}
                  activeOpacity={0.7}>
                  <Box
                    paddingVertical="md_16"
                    flexDirection="row"
                    justifyContent="space-between"
                    alignItems="center">
                    <Text
                      variant="b_16Regular_input"
                      color={selectedTime === option.value ? 'brandMain' : 'mainText'}>
                      {option.label}
                    </Text>
                    {selectedTime === option.value && (
                      <Box
                        width={20}
                        height={20}
                        borderRadius="circle_9999"
                        backgroundColor="brandMain"
                      />
                    )}
                  </Box>
                </TouchableOpacity>
              ))}
            </Box>
          </Box>
        </Pressable>
      </Modal>
    </>
  );
}
