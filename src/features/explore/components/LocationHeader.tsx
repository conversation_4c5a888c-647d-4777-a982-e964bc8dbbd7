import React from 'react';

import { MapPin } from 'phosphor-react-native';
import { useTranslation } from 'react-i18next';

import { useTheme } from '@/src/core/theme';
import Box from '@/src/core/theme/Box';
import Pressable from '@/src/core/theme/Pressable';
import Text from '@/src/core/theme/Text';

interface LocationHeaderProps {
  locationName: string;
  radius: string;
  onChangePress: () => void;
}

export default function LocationHeader({
  locationName,
  radius,
  onChangePress,
}: LocationHeaderProps) {
  const theme = useTheme();
  const { t } = useTranslation();

  return (
    <Box
      flexDirection="row"
      alignItems="center"
      justifyContent="space-between"
      paddingHorizontal="md_16"
      paddingVertical="sm_12">
      <Box flexDirection="row" alignItems="center" gap="xs_8">
        <Box
          width={32}
          height={32}
          borderRadius="circle_9999"
          backgroundColor="focusBackground"
          alignItems="center"
          justifyContent="center">
          <MapPin size={18} weight="bold" color={theme.colors.primary} />
        </Box>
        <Box>
          <Text variant="h_18SemiBold_cardTitle" color="mainText">
            {locationName}
          </Text>
          <Text variant="b_12Medium_CardSubtitle" color="secondaryText">
            {radius}
          </Text>
        </Box>
      </Box>
      <Pressable
        onPress={onChangePress}
        paddingVertical="xxs_4"
        paddingHorizontal="sm_12"
        backgroundColor="subtleBackground"
        borderRadius="sm_8">
        <Text variant="l_12SemiBold_button" color="primary">
          {t('explore.local.change')}
        </Text>
      </Pressable>
    </Box>
  );
}
