import { ImageBackground } from 'react-native';

import {
  <PERSON><PERSON><PERSON>,
  Calendar,
  Clock,
  Fire,
  MapPin,
  Snowflake,
  Star,
  ThermometerSimple,
  Ticket,
} from 'phosphor-react-native';

import Box from '@/src/core/theme/Box';
import Pressable from '@/src/core/theme/Pressable';
import Text from '@/src/core/theme/Text';
import { useResponsive } from '@/src/core/theme/useResponsive';
import { Avatar } from '@/src/shared/components';

import { Event } from '../types/event';

interface PartyPostCardProps {
  event: Event;
  onPress: () => void;
  size?: 'default' | 'small';
}

export default function PartyPostCard({ event, onPress, size = 'default' }: PartyPostCardProps) {
  const { isTablet } = useResponsive();
  const isSmall = size === 'small';

  const vibePercentage = Math.round(
    (event.vibeRating.hot / (event.vibeRating.hot + event.vibeRating.cold)) * 100
  );

  return (
    <Pressable onPress={onPress}>
      <Box
        backgroundColor="cardBackground"
        borderRadius="md_12"
        borderWidth={1}
        borderColor="mainBorder"
        overflow="hidden"
        width={isSmall ? (isTablet ? 200 : 160) : undefined}>
        {/* Event Image */}
        <Box height={isSmall ? 120 : isTablet ? 200 : 180} position="relative">
          <ImageBackground
            source={{ uri: event.imageUrl }}
            style={{ width: '100%', height: '100%' }}
            resizeMode="cover">
            <Box
              position="absolute"
              bottom={0}
              left={0}
              right={0}
              height={60}
              // style={{
              //   background: 'linear-gradient(to top, rgba(0,0,0,0.8) 0%, transparent 100%)',
              // }}
            />
          </ImageBackground>
        </Box>

        {/* Event Info */}
        <Box padding={isSmall ? 'sm_12' : 'md_16'} gap={isSmall ? 'xs_8' : 'sm_12'}>
          {/* Title */}
          <Text
            variant={isSmall ? 'b_14Bold_CardTitle' : 'h_18SemiBold_cardTitle'}
            color="mainText"
            numberOfLines={2}>
            {event.title}
          </Text>

          {/* Location and Price Info */}
          <Box gap="xs_8">
            {/* Location */}
            <Box flexDirection="row" alignItems="center" gap="xxs_4">
              <MapPin size={isTablet ? 16 : 14} color="secondaryText" />
              <Text
                variant={isSmall ? 'l_10Medium_tooltip' : 'l_12Regular_helperText'}
                color="secondaryText"
                numberOfLines={1}>
                {event.venue.name}
              </Text>
            </Box>

            {/* Ticket Price */}
            <Box flexDirection="row" alignItems="center" gap="xxs_4">
              <Ticket size={isTablet ? 16 : 14} color="secondaryText" />
              <Text
                variant={isSmall ? 'l_10Medium_tooltip' : 'l_12Regular_helperText'}
                color="secondaryText">
                ${event.ticketPrice.min}-{event.ticketPrice.max}
              </Text>
            </Box>

            {/* Drinks Price */}
            {event.drinksPrice && (
              <Box flexDirection="row" alignItems="center" gap="xxs_4">
                <BeerStein size={isTablet ? 16 : 14} color="secondaryText" />
                <Text
                  variant={isSmall ? 'l_10Medium_tooltip' : 'l_12Regular_helperText'}
                  color="secondaryText">
                  ${event.drinksPrice.min}-{event.drinksPrice.max}
                </Text>
              </Box>
            )}
          </Box>

          {/* Date and Time */}
          <Box gap="xxs_4">
            <Box flexDirection="row" alignItems="center" gap="xxs_4">
              <Calendar size={isTablet ? 16 : 14} color="secondaryText" />
              <Text variant="l_12Regular_helperText" color="secondaryText">
                {event.date}
              </Text>
            </Box>
            <Box flexDirection="row" alignItems="center" gap="xxs_4">
              <Clock size={isTablet ? 16 : 14} color="secondaryText" />
              <Text variant="l_12Regular_helperText" color="secondaryText">
                {event.time}
              </Text>
            </Box>
          </Box>

          {/* Host Info */}
          <Box flexDirection="row" alignItems="center" justifyContent="space-between">
            <Box flexDirection="row" alignItems="center" gap="xs_8">
              <Avatar size="xs" source={{ uri: event.host.avatarUrl }} />
              <Box>
                <Box flexDirection="row" alignItems="center" gap="xxs_4">
                  <Text variant="l_12Bold_highlight" color="mainText">
                    {event.host.name}
                  </Text>
                  {event.host.isVerified && (
                    <Box
                      width={16}
                      height={16}
                      borderRadius="circle_9999"
                      backgroundColor="warningMain"
                      alignItems="center"
                      justifyContent="center">
                      <Text variant="l_8SemiBold_hint" color="white">
                        ✓
                      </Text>
                    </Box>
                  )}
                </Box>
                <Box flexDirection="row" alignItems="center" gap="xxs_4">
                  <Text variant="l_10Medium_tooltip" color="secondaryText">
                    {event.host.rating.toFixed(1)}
                  </Text>
                  <Star size={12} color="secondaryText" weight="fill" />
                </Box>
              </Box>
            </Box>

            {/* Vibe Rating */}
            {!isSmall && (
              <Box alignItems="flex-end">
                <Box flexDirection="row" alignItems="center" gap="xs_8">
                  <Box flexDirection="row" alignItems="center" gap="xxs_4">
                    <Text variant="l_10SemiBold_chip" color="primary">
                      {vibePercentage}%
                    </Text>
                    <ThermometerSimple size={14} color="primary" />
                  </Box>
                  <Box flexDirection="row" alignItems="center" gap="xxs_4">
                    <Text variant="l_10SemiBold_chip" color="errorMain">
                      {100 - vibePercentage}%
                    </Text>
                    <ThermometerSimple size={14} color="errorMain" />
                  </Box>
                </Box>
                <Box flexDirection="row" alignItems="center" gap="xs_8" marginTop="xxs_4">
                  <Box flexDirection="row" alignItems="center" gap="xxs_4">
                    <Box backgroundColor="primary" borderRadius="circle_9999" padding="xxs_4">
                      <Fire size={12} color="white" weight="bold" />
                    </Box>
                    <Text variant="l_10Medium_tooltip" color="primary">
                      Hot
                    </Text>
                  </Box>
                  <Box flexDirection="row" alignItems="center" gap="xxs_4">
                    <Box backgroundColor="errorMain" borderRadius="circle_9999" padding="xxs_4">
                      <Snowflake size={12} color="white" weight="bold" />
                    </Box>
                    <Text variant="l_10Medium_tooltip" color="errorMain">
                      Cold
                    </Text>
                  </Box>
                </Box>
              </Box>
            )}
          </Box>

          {/* Tags */}
          <Box flexDirection="row" flexWrap="wrap" gap="xxs_4">
            {event.tags.slice(0, isSmall ? 3 : 5).map(tag => (
              <Box
                key={tag.id}
                backgroundColor={tag.color || 'surfaceBackground'}
                paddingVertical="xxxs_2"
                paddingHorizontal="xs_8"
                borderRadius="xs_4">
                <Text variant="l_10SemiBold_tag" color={tag.textColor || 'mainText'}>
                  {tag.label}
                </Text>
              </Box>
            ))}
          </Box>
        </Box>
      </Box>
    </Pressable>
  );
}
