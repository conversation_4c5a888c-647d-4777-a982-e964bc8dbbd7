import React from 'react';

import { Clock, Fire, MapPin, Users } from 'phosphor-react-native';
import { useTranslation } from 'react-i18next';

import { useTheme } from '@/src/core/theme';
import Box from '@/src/core/theme/Box';
import Pressable from '@/src/core/theme/Pressable';
import Text from '@/src/core/theme/Text';
import { Button } from '@/src/shared/components';

import { FeedItem } from '../types/event';

interface LocalEventCardProps {
  item: FeedItem;
  onPress: () => void;
}

export default function LocalEventCard({ item, onPress }: LocalEventCardProps) {
  const theme = useTheme();
  const { t } = useTranslation();

  return (
    <Pressable
      onPress={onPress}
      backgroundColor="cardBackground"
      borderRadius="lg_16"
      borderWidth={1.5}
      borderColor="mainBorder"
      padding="sm_12"
      gap="xs_8">
      {/* Header */}
      <Box flexDirection="row" alignItems="center" justifyContent="space-between">
        <Text variant="h_18Bold_formTitle" color="mainText" flex={1} numberOfLines={1}>
          {item.name}
        </Text>
        {item.trending && (
          <Box
            flexDirection="row"
            alignItems="center"
            gap="xxs_4"
            backgroundColor="warningLight"
            paddingHorizontal="xxs_4"
            borderRadius="xs_4">
            <Fire size={12} weight="fill" color={theme.colors.warningMain} />
            <Text variant="l_10SemiBold_chip" color="warningMain">
              {t('explore.local.sellingOut')}
            </Text>
          </Box>
        )}
      </Box>

      {/* Content */}
      <Box gap="xxs_4">
        {/* Location */}
        <Box flexDirection="row" alignItems="center" gap="xxs_4">
          <MapPin size={14} color={theme.colors.mainText} />
          <Text variant="l_12Medium_message" color="mainText" flex={1}>
            {item.venueName || 'Venue'}
          </Text>
          <Box backgroundColor="successLight" paddingHorizontal="xxs_4" borderRadius="xs_4">
            <Text variant="l_10Medium_tooltip" color="successMain">
              {item.distance || '500 m'}
            </Text>
          </Box>
        </Box>
      </Box>
    </Pressable>
  );
}
//         {/* Time */}
//         <Box flexDirection="row" alignItems="center" gap="xxs_4">
//           <Clock size={14} color={theme.colors.textTertiary} />
//           <Text variant="l_10Medium_tooltip" color="textTertiary">
//             {item.startTime || t('explore.local.joinNow')}
//           </Text>
//         </Box>

//         {/* Users/Reactions */}
//         <Box flexDirection="row" alignItems="center" gap="xxs_4">
//           <Users size={14} weight="fill" color={theme.colors.primary} />
//           <Text variant="l_10Medium_tooltip" color="primary">
//             {item.reactionsCount
//               ? t('explore.local.reactionsCount', { count: item.reactionsCount })
//               : t('explore.local.noReactions')}
//           </Text>
//         </Box>
//       </Box>

//       {/* Action Button */}
//       <Button
//         variant="primary"
//         size="small"
//         tx="explore.local.discover"
//         onPress={onPress}
//         style={{ alignSelf: 'flex-end' }}
//       />
//     </Pressable>
//   );
// }
