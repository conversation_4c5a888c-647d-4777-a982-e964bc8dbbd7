import React from 'react';

import Image from '@d11/react-native-fast-image';
import { useNavigation } from '@react-navigation/native';
import { formatDistanceToNow } from 'date-fns';
import { Calendar, MapPin, UserCheck } from 'phosphor-react-native';

import { useTheme } from '@/src/core/theme';
import Box from '@/src/core/theme/Box';
import Pressable from '@/src/core/theme/Pressable';
import Text from '@/src/core/theme/Text';
import { useResponsive } from '@/src/core/theme/useResponsive';
import { Avatar } from '@/src/shared/components';

interface Activity {
  id: string;
  user: {
    id: string;
    name: string;
    avatarUrl: string;
  };
  timestamp: string;
  type: 'going' | 'interested' | 'checked_in' | 'hosting';
  message: string;
  event?: {
    id: string;
    title: string;
    imageUrl: string;
    date: string;
    time: string;
    venue: {
      id: string;
      name: string;
      address: string;
    };
    host: {
      id: string;
      name: string;
      avatarUrl: string;
      rating: number;
      isVerified: boolean;
    };
  };
}

interface ActivityCardProps {
  activity: Activity;
  onPress?: () => void;
}

export default function ActivityCard({ activity, onPress }: ActivityCardProps) {
  const theme = useTheme();
  const { isTablet } = useResponsive();
  const navigation = useNavigation();

  const getActivityIcon = () => {
    switch (activity.type) {
      case 'going':
        return (
          <Calendar size={isTablet ? 20 : 16} color={theme.colors.successMain} weight="fill" />
        );
      case 'interested':
        return <Calendar size={isTablet ? 20 : 16} color={theme.colors.warningMain} />;
      case 'checked_in':
        return <UserCheck size={isTablet ? 20 : 16} color={theme.colors.primary} weight="fill" />;
      case 'hosting':
        return <MapPin size={isTablet ? 20 : 16} color={theme.colors.errorMain} weight="fill" />;
      default:
        return null;
    }
  };

  const handleEventPress = () => {
    if (activity.event) {
      navigation.navigate('EventDetails' as any, { eventId: activity.event.id });
    }
  };

  return (
    <Pressable onPress={onPress}>
      <Box
        backgroundColor="cardBackground"
        borderRadius="lg_16"
        borderWidth={1}
        borderColor="mainBorder"
        padding="md_16"
        gap="sm_12">
        {/* User Info and Activity */}
        <Box flexDirection="row" alignItems="center" gap="sm_12">
          <Avatar
            source={{ uri: activity.user.avatarUrl }}
            size="m"
            fallbackText={activity.user.name}
          />

          <Box flex={1} gap="xxs_4">
            <Box flexDirection="row" alignItems="center" gap="xs_8" flexWrap="wrap">
              <Text variant="b_14SemiBold_listTitle" color="mainText">
                {activity.user.name}
              </Text>
              {getActivityIcon()}
              <Text variant="b_14Regular_content" color="secondaryText">
                {activity.message}
              </Text>
            </Box>

            <Text variant="l_12Regular_helperText" color="mutedText">
              {formatDistanceToNow(new Date(activity.timestamp), { addSuffix: true })}
            </Text>
          </Box>
        </Box>

        {/* Event Preview */}
        {activity.event && (
          <Pressable onPress={handleEventPress}>
            <Box
              backgroundColor="subtleBackground"
              borderRadius="md_12"
              overflow="hidden"
              borderWidth={1}
              borderColor="subtleBorder">
              <Box flexDirection="row" gap="sm_12">
                {activity.event.imageUrl && (
                  <Image
                    source={{ uri: activity.event.imageUrl }}
                    style={{
                      width: 80,
                      height: 80,
                    }}
                  />
                )}

                <Box flex={1} padding="sm_12" gap="xxs_4" justifyContent="center">
                  <Text variant="b_14Bold_CardTitle" color="mainText" numberOfLines={1}>
                    {activity.event.title}
                  </Text>

                  <Box flexDirection="row" alignItems="center" gap="xxs_4">
                    <Calendar size={12} color={theme.colors.secondaryText} />
                    <Text variant="l_12Regular_helperText" color="secondaryText">
                      {activity.event.date} • {activity.event.time}
                    </Text>
                  </Box>

                  <Box flexDirection="row" alignItems="center" gap="xxs_4">
                    <MapPin size={12} color={theme.colors.secondaryText} />
                    <Text variant="l_12Regular_helperText" color="secondaryText" numberOfLines={1}>
                      {activity.event.venue.name}
                    </Text>
                  </Box>
                </Box>
              </Box>
            </Box>
          </Pressable>
        )}
      </Box>
    </Pressable>
  );
}
