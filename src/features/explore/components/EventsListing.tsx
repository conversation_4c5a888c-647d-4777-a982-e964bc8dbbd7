import React from 'react';

import { FlatList, FlatListProps } from 'react-native';

import { Box } from '@/src/core/theme';

import { Event, EventFeedCard, EventFeedVariant } from './EventFeedCard';

interface EventsListingProps {
  events: Event[];
  variant?: EventFeedVariant;
  selectedEventId?: string | null;
  onEventPress?: (eventId: string) => void;
  onJoin?: (eventId: string) => void;
  onRequestInvite?: (eventId: string) => void;
  onFavorite?: (eventId: string) => void;
  onBookmark?: (eventId: string) => void;
  onShowMore?: (eventId: string) => void;
  onViewTickets?: (eventId: string) => void;
  onViewGallery?: (eventId: string) => void;
  flatListProps?: Partial<FlatListProps<Event>>;
}

export function EventsListing({
  events,
  variant = 'compact',
  selectedEventId,
  onEventPress,
  onJoin,
  onRequestInvite,
  onFavorite,
  onBookmark,
  onShowMore,
  onViewTickets,
  onViewGallery,
  flatListProps = {},
}: EventsListingProps) {
  const renderEvent = ({ item }: { item: Event }) => (
    <EventFeedCard
      variant={variant}
      event={item}
      isSelected={selectedEventId === item.id}
      onPress={() => onEventPress?.(item.id)}
      onJoin={() => onJoin?.(item.id)}
      onRequestInvite={() => onRequestInvite?.(item.id)}
      onFavorite={() => onFavorite?.(item.id)}
      onBookmark={() => onBookmark?.(item.id)}
      onShowMore={() => onShowMore?.(item.id)}
      onViewTickets={() => onViewTickets?.(item.id)}
      onViewGallery={() => onViewGallery?.(item.id)}
    />
  );

  return (
    <FlatList
      data={events}
      renderItem={renderEvent}
      keyExtractor={item => item.id}
      ItemSeparatorComponent={() => <Box height={12} />}
      scrollEnabled={false}
      {...flatListProps}
    />
  );
}
