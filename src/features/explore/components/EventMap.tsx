import React, { useRef, useState } from 'react';

import { Dimensions, Platform, ScrollView, StyleSheet } from 'react-native';

import { useBottomTabBarHeight } from '@react-navigation/bottom-tabs';
import MapView, { Marker, PROVIDER_DEFAULT, PROVIDER_GOOGLE } from 'react-native-maps';

import { useTheme } from '@/src/core/theme';
import Box from '@/src/core/theme/Box';

import { Event, FeedItem } from '../types/event';
import EventMarker from './EventMarker';
import LocalEventCard from './LocalEventCard';
import UserLocationMarker from './UserLocationMarker';

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const CARD_WIDTH = SCREEN_WIDTH - 32; // 16px padding on each side

interface EventMapProps {
  events: FeedItem[];
  region: {
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  };
  onEventPress?: (event: FeedItem) => void;
  onRegionChange?: (region: any) => void;
}

export default function EventMap({ events, region, onEventPress, onRegionChange }: EventMapProps) {
  const theme = useTheme();
  const mapRef = useRef<MapView>(null);
  const scrollRef = useRef<ScrollView>(null);
  const bottomTabHeight = useBottomTabBarHeight();
  const [selectedEventId, setSelectedEventId] = useState<string | null>(null);

  const handleMarkerPress = (event: FeedItem, index: number) => {
    setSelectedEventId(event.id);
    // Scroll to the selected card
    scrollRef.current?.scrollTo({
      x: index * (CARD_WIDTH + 12), // card width + gap
      animated: true,
    });
    onEventPress?.(event);
  };

  const handleCardPress = (event: FeedItem) => {
    setSelectedEventId(event.id);
    // Center map on the selected event
    mapRef.current?.animateToRegion(
      {
        latitude: event.location?.lat || region.latitude,
        longitude: event.location?.lng || region.longitude,
        latitudeDelta: 0.02,
        longitudeDelta: 0.02,
      },
      300
    );
    onEventPress?.(event);
  };

  const customMapStyle = [
    {
      elementType: 'geometry',
      stylers: [
        {
          color: theme.colors.subtleBackground,
        },
      ],
    },
    {
      elementType: 'labels.text.fill',
      stylers: [
        {
          color: theme.colors.mainText,
        },
      ],
    },
    {
      elementType: 'labels.text.stroke',
      stylers: [
        {
          color: theme.colors.mainBackground,
        },
      ],
    },
    {
      featureType: 'poi',
      elementType: 'labels',
      stylers: [
        {
          visibility: 'off',
        },
      ],
    },
    {
      featureType: 'road',
      elementType: 'geometry',
      stylers: [
        {
          color: theme.colors.mainBorder,
        },
      ],
    },
    {
      featureType: 'road',
      elementType: 'labels.text.fill',
      stylers: [
        {
          color: theme.colors.secondaryText,
        },
      ],
    },
    {
      featureType: 'water',
      elementType: 'geometry',
      stylers: [
        {
          color: theme.colors.primaryLight,
        },
      ],
    },
  ];

  return (
    <Box flex={1} style={StyleSheet.absoluteFillObject}>
      <MapView
        ref={mapRef}
        style={StyleSheet.absoluteFillObject}
        provider={Platform.OS === 'ios' ? PROVIDER_DEFAULT : PROVIDER_GOOGLE}
        initialRegion={region}
        region={region}
        onRegionChangeComplete={onRegionChange}
        customMapStyle={customMapStyle}
        showsUserLocation={false}
        showsMyLocationButton={false}
        showsCompass={false}>
        {/* User Location Marker */}
        <Marker
          coordinate={{
            latitude: region.latitude,
            longitude: region.longitude,
          }}
          anchor={{ x: 0.5, y: 0.5 }}>
          <UserLocationMarker />
        </Marker>
        {/* Event Markers */}
        {events.map((event, index) => (
          <Marker
            key={event.id}
            coordinate={{
              latitude: event.location?.lat || 0,
              longitude: event.location?.lng || 0,
            }}
            onPress={() => handleMarkerPress(event, index)}>
            <EventMarker selected={selectedEventId === event.id} trending={event.trending} />
          </Marker>
        ))}
      </MapView>

      {/* Floating Cards at Bottom */}
      <Box
        position="absolute"
        bottom={Platform.OS === 'ios' ? theme.spacing.xl_32 : undefined}
        left={0}
        right={0}
        pointerEvents="box-none">
        <ScrollView
          ref={scrollRef}
          horizontal
          showsHorizontalScrollIndicator={false}
          snapToInterval={CARD_WIDTH + 12}
          decelerationRate="fast"
          contentContainerStyle={{
            paddingHorizontal: 16,
            paddingTop: 8,
          }}
          style={{
            maxHeight: 180,
          }}>
          {events.map((event, index) => (
            <Box
              key={event.id}
              width={CARD_WIDTH}
              marginRight={index < events.length - 1 ? 'sm_12' : 'none_0'}
              style={{
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.15,
                shadowRadius: 8,
                elevation: 5,
              }}>
              <LocalEventCard item={event} onPress={() => handleCardPress(event)} />
            </Box>
          ))}
        </ScrollView>
      </Box>
    </Box>
  );
}
