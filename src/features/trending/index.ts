// i18n

/**
 * Trending Feature - Main exports
 */

// Components
export { TrendingPostCard } from './components/TrendingPostCard';
export { LocationBadge } from './components/LocationBadge';
export { PostInteractions } from './components/PostInteractions';
export { FilterChips } from './components/FilterChips';
export { FriendsActivityCard } from './components/FriendsActivityCard';
export { SecretEventCard } from './components/SecretEventCard';
export { VenueActivityCard } from './components/VenueActivityCard';
export { HostActivityCard } from './components/HostActivityCard';

// Screens
export { default as TrendingScreen } from './screens/TrendingScreen';

// Store
export { useTrendingStore, TrendingFilterType, TrendingSortType } from './store/trendingStore';

// Hooks
export { usePostInteractions } from './hooks/usePostInteractions';

// Types
export * from './types';

// GraphQL
export * from './graphql/queries';
export * from './graphql/mutations';
export * from './graphql/subscriptions';
