import { z } from 'zod';

import i18n from '@/src/core/i18n/config';
import { localizedValidation } from '@/src/shared/validation/localized';

export const createPostSchema = z.object({
  content: localizedValidation.custom(
    z
      .string()
      .min(1, i18n.t('validation:post.content.required', 'Post content is required'))
      .max(
        500,
        i18n.t(
          'validation:post.content.maxLength',
          'Post content must be less than {{max}} characters',
          { max: 500 }
        )
      ),
    content => content.trim().length > 0,
    'validation:post.content.notEmpty',
    'Post content cannot be empty or only whitespace'
  ),

  venueId: z.string().optional(),

  images: z
    .array(z.string().url(i18n.t('validation:post.images.invalidUrl', 'Invalid image URL')))
    .max(6, i18n.t('validation:post.images.maxCount', 'Maximum {{max}} images allowed', { max: 6 }))
    .optional()
    .default([]),

  hashtags: z
    .array(z.string())
    .max(
      10,
      i18n.t('validation:post.hashtags.maxCount', 'Maximum {{max}} hashtags allowed', { max: 10 })
    )
    .optional()
    .default([]),
});

export const commentSchema = z.object({
  content: localizedValidation.custom(
    z
      .string()
      .min(1, i18n.t('validation:comment.content.required', 'Comment content is required'))
      .max(
        300,
        i18n.t(
          'validation:comment.content.maxLength',
          'Comment must be less than {{max}} characters',
          { max: 300 }
        )
      ),
    content => content.trim().length > 0,
    'validation:comment.content.notEmpty',
    'Comment cannot be empty or only whitespace'
  ),

  parentId: z.string().optional(),
});

export const reportPostSchema = z.object({
  reason: z.enum(
    [
      'spam',
      'harassment',
      'hate_speech',
      'violence',
      'inappropriate_content',
      'copyright_violation',
    ],
    {
      required_error: i18n.t(
        'validation:report.reason.required',
        'Please select a reason for reporting'
      ),
    }
  ),

  description: z
    .string()
    .max(
      500,
      i18n.t(
        'validation:report.description.maxLength',
        'Description must be less than {{max}} characters',
        { max: 500 }
      )
    )
    .optional(),
});

export const editPostSchema = z.object({
  content: localizedValidation.custom(
    z
      .string()
      .min(1, i18n.t('validation:post.content.required', 'Post content is required'))
      .max(
        500,
        i18n.t(
          'validation:post.content.maxLength',
          'Post content must be less than {{max}} characters',
          { max: 500 }
        )
      ),
    content => content.trim().length > 0,
    'validation:post.content.notEmpty',
    'Post content cannot be empty or only whitespace'
  ),

  venueId: z.string().optional(),
});

export const filterSchema = z.object({
  filter: z.enum([
    'trending_now',
    'recent',
    'popular',
    'nearby',
    'following',
    'saved',
    'my_posts',
    'venue_posts',
  ]),

  sortOrder: z.enum(['trending', 'recent', 'popular', 'nearby']).optional(),

  category: z
    .enum([
      'restaurant',
      'bar',
      'cafe',
      'club',
      'hotel',
      'shop',
      'gym',
      'park',
      'museum',
      'theater',
      'hospital',
      'other',
    ])
    .optional(),

  venueId: z.string().optional(),

  location: z
    .object({
      latitude: z
        .number()
        .min(-90, i18n.t('validation:number.min', 'Value must be at least {{min}}', { min: -90 }))
        .max(90, i18n.t('validation:number.max', 'Value must be at most {{max}}', { max: 90 })),
      longitude: z
        .number()
        .min(-180, i18n.t('validation:number.min', 'Value must be at least {{min}}', { min: -180 }))
        .max(180, i18n.t('validation:number.max', 'Value must be at most {{max}}', { max: 180 })),
      radius: z
        .number()
        .min(0.1, i18n.t('validation:number.min', 'Value must be at least {{min}}', { min: 0.1 }))
        .max(50, i18n.t('validation:number.max', 'Value must be at most {{max}}', { max: 50 }))
        .default(5), // km
    })
    .optional(),
});

export type CreatePostInput = z.infer<typeof createPostSchema>;
export type CommentInput = z.infer<typeof commentSchema>;
export type ReportPostInput = z.infer<typeof reportPostSchema>;
export type EditPostInput = z.infer<typeof editPostSchema>;
export type FilterInput = z.infer<typeof filterSchema>;
