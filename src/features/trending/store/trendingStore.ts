import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

import { client } from '@/src/core/api/config';
import { appStorage, createZustandMMKVStorage } from '@/src/core/storage';

import {
  BOOKMARK_POST,
  LIKE_POST,
  SHARE_POST,
  UNBOOKMARK_POST,
  UNLIKE_POST,
} from '../graphql/mutations';
import { GET_TRENDING_DATA, GET_TRENDING_POSTS } from '../graphql/queries';
import { TrendingPost } from '../types';

// Default pagination values
const DEFAULT_LIMIT = 10;

type TrendingFilter = {
  categories: string[];
  venues: string[];
};

type PageInfo = {
  startCursor: string | null;
  endCursor: string | null;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  totalCount: number;
};

// Define the filter and sorting enums
export enum TrendingFilterType {
  ALL = 'ALL',
  FOLLOWING = 'FOLLOWING',
  NEARBY = 'NEARBY',
}

export enum TrendingSortType {
  RECENT = 'RECENT',
  POPULAR = 'POPULAR',
  TRENDING = 'TRENDING',
}

// Define the store state interface
interface TrendingState {
  // Posts data
  posts: Record<string, TrendingPost>;
  postIds: string[];

  // Pagination
  pageInfo: PageInfo;
  isLoading: boolean;
  isRefreshing: boolean;
  hasError: boolean;
  errorMessage: string | null;

  // Filters
  availableFilters: TrendingFilter;
  selectedCategories: string[];
  selectedVenues: string[];
  currentFilterType: TrendingFilterType;
  currentSortType: TrendingSortType;

  // Actions
  fetchTrendingPosts: (refresh?: boolean) => Promise<void>;
  fetchMorePosts: () => Promise<void>;
  fetchTrendingFilters: () => Promise<void>;
  setFilterType: (filterType: TrendingFilterType) => void;
  setSortType: (sortType: TrendingSortType) => void;
  toggleCategoryFilter: (categoryId: string) => void;
  toggleVenueFilter: (venueId: string) => void;
  resetFilters: () => void;

  // Post interaction methods
  likePost: (postId: string) => Promise<void>;
  unlikePost: (postId: string) => Promise<void>;
  savePost: (postId: string) => Promise<void>;
  unsavePost: (postId: string) => Promise<void>;
  sharePost: (postId: string) => Promise<void>;
}

// Create and export the trending store
export const useTrendingStore = create<TrendingState>()(
  persist(
    (set, get) => ({
      // Initial state
      posts: {},
      postIds: [],
      pageInfo: {
        hasNextPage: false,
        endCursor: null,
        totalCount: 0,
        startCursor: null,
        hasPreviousPage: false,
      },
      isLoading: false,
      isRefreshing: false,
      hasError: false,
      errorMessage: null,

      availableFilters: {
        categories: [],
        venues: [],
        popularTags: [],
      },
      selectedCategories: [],
      selectedVenues: [],
      currentFilterType: TrendingFilterType.ALL,
      currentSortType: TrendingSortType.TRENDING,

      // Fetch trending posts with optional refresh
      fetchTrendingPosts: async (refresh = false) => {
        const state = get();

        if (refresh) {
          set({ isRefreshing: true, hasError: false, errorMessage: null });
        } else {
          set({ isLoading: true, hasError: false, errorMessage: null });
        }

        try {
          // Build filter variables based on selected filters
          const filterVariables = {
            filterType: state.currentFilterType,
            sortBy: state.currentSortType,
            categories: state.selectedCategories.length > 0 ? state.selectedCategories : undefined,
            venueIds: state.selectedVenues.length > 0 ? state.selectedVenues : undefined,
          };

          const { data } = await client.query({
            query: GET_TRENDING_POSTS,
            variables: {
              limit: DEFAULT_LIMIT,
              cursor: null,
            },
            fetchPolicy: refresh ? 'network-only' : 'cache-first',
          });

          const newPosts: Record<string, any> = {};
          const newPostIds: string[] = [];

          // Process the trending items (not direct posts)
          data.trendingFeed.items.forEach((item: any) => {
            newPosts[item.id] = item;
            newPostIds.push(item.id);
          });

          set({
            posts: newPosts,
            postIds: newPostIds,
            pageInfo: {
              hasNextPage: data.trendingFeed.hasMore,
              endCursor: data.trendingFeed.cursor,
              totalCount: newPostIds.length,
              startCursor: null,
              hasPreviousPage: false,
            },
            isLoading: false,
            isRefreshing: false,
          });
        } catch (error) {
          console.error('Error fetching trending posts:', error);
          set({
            isLoading: false,
            isRefreshing: false,
            hasError: true,
            errorMessage: error instanceof Error ? error.message : 'Failed to fetch trending posts',
          });
        }
      },

      // Fetch more posts (pagination)
      fetchMorePosts: async () => {
        const state = get();

        // Don't fetch if we're already loading or there are no more pages
        if (state.isLoading || !state.pageInfo.hasNextPage) {
          return;
        }

        set({ isLoading: true });

        try {
          // Build filter variables based on selected filters
          const filterVariables = {
            filterType: state.currentFilterType,
            sortBy: state.currentSortType,
            categories: state.selectedCategories.length > 0 ? state.selectedCategories : undefined,
            venueIds: state.selectedVenues.length > 0 ? state.selectedVenues : undefined,
          };

          const { data } = await client.query({
            query: GET_TRENDING_POSTS,
            variables: {
              limit: DEFAULT_LIMIT,
              cursor: state.pageInfo.endCursor,
            },
            fetchPolicy: 'network-only',
          });

          const newPosts = { ...state.posts };
          const newPostIds = [...state.postIds];

          // Process the trending items
          data.trendingFeed.items.forEach((item: any) => {
            newPosts[item.id] = item;
            newPostIds.push(item.id);
          });

          set({
            posts: newPosts,
            postIds: newPostIds,
            pageInfo: {
              hasNextPage: data.trendingFeed.hasMore,
              endCursor: data.trendingFeed.cursor,
              totalCount: newPostIds.length,
              startCursor: state.pageInfo.startCursor,
              hasPreviousPage: state.pageInfo.hasPreviousPage,
            },
            isLoading: false,
          });
        } catch (error) {
          console.error('Error fetching more trending posts:', error);
          set({
            isLoading: false,
            hasError: true,
            errorMessage:
              error instanceof Error ? error.message : 'Failed to fetch more trending posts',
          });
        }
      },

      // Fetch available trending data
      fetchTrendingFilters: async () => {
        try {
          // Using default coordinates for now - should be user's location
          const { data } = await client.query({
            query: GET_TRENDING_DATA,
            variables: {
              latitude: 40.7128, // Default to NYC
              longitude: -74.006,
              radius: 10.0,
              limit: 20,
              timeframe: 'weekly',
            },
            fetchPolicy: 'network-only',
          });

          set({
            availableFilters: {
              categories: [],
              venues: data.trendingVenues || [],
            },
          });
        } catch (error) {
          console.error('Error fetching trending filters:', error);
        }
      },

      // Set filter type (All, Following, Nearby)
      setFilterType: (filterType: TrendingFilterType) => {
        set({ currentFilterType: filterType });
        get().fetchTrendingPosts(true);
      },

      // Set sort type (Recent, Popular, Trending)
      setSortType: (sortType: TrendingSortType) => {
        set({ currentSortType: sortType });
        get().fetchTrendingPosts(true);
      },

      // Toggle category filter selection
      toggleCategoryFilter: (categoryId: string) => {
        const { selectedCategories } = get();
        const isSelected = selectedCategories.includes(categoryId);

        if (isSelected) {
          set({
            selectedCategories: selectedCategories.filter(id => id !== categoryId),
          });
        } else {
          set({
            selectedCategories: [...selectedCategories, categoryId],
          });
        }

        get().fetchTrendingPosts(true);
      },

      // Toggle venue filter selection
      toggleVenueFilter: (venueId: string) => {
        const { selectedVenues } = get();
        const isSelected = selectedVenues.includes(venueId);

        if (isSelected) {
          set({
            selectedVenues: selectedVenues.filter(id => id !== venueId),
          });
        } else {
          set({
            selectedVenues: [...selectedVenues, venueId],
          });
        }

        get().fetchTrendingPosts(true);
      },

      // Reset all filters to default
      resetFilters: () => {
        set({
          selectedCategories: [],
          selectedVenues: [],
          currentFilterType: TrendingFilterType.ALL,
          currentSortType: TrendingSortType.TRENDING,
        });

        get().fetchTrendingPosts(true);
      },

      // Like a post
      likePost: async (postId: string) => {
        const state = get();
        const post = state.posts[postId];

        if (!post) return;

        // Optimistic update
        set({
          posts: {
            ...state.posts,
            [postId]: {
              ...post,
              userLiked: true,
              likesCount: post.likesCount + 1,
            },
          },
        });

        try {
          await client.mutate({
            mutation: LIKE_POST,
            variables: { postId },
          });
        } catch (error) {
          console.error('Error liking post:', error);

          // Revert on error
          set({
            posts: {
              ...state.posts,
              [postId]: post,
            },
          });
        }
      },

      // Unlike a post
      unlikePost: async (postId: string) => {
        const state = get();
        const post = state.posts[postId];

        if (!post) return;

        // Optimistic update
        set({
          posts: {
            ...state.posts,
            [postId]: {
              ...post,
              userLiked: false,
              likesCount: Math.max(0, post.likesCount - 1),
            },
          },
        });

        try {
          await client.mutate({
            mutation: UNLIKE_POST,
            variables: { postId },
          });
        } catch (error) {
          console.error('Error unliking post:', error);

          // Revert on error
          set({
            posts: {
              ...state.posts,
              [postId]: post,
            },
          });
        }
      },

      // Bookmark a post
      savePost: async (postId: string) => {
        const state = get();
        const post = state.posts[postId];

        if (!post) return;

        // Optimistic update
        set({
          posts: {
            ...state.posts,
            [postId]: {
              ...post,
              userBookmarked: true,
            },
          },
        });

        try {
          await client.mutate({
            mutation: BOOKMARK_POST,
            variables: { postId },
          });
        } catch (error) {
          console.error('Error bookmarking post:', error);

          // Revert on error
          set({
            posts: {
              ...state.posts,
              [postId]: post,
            },
          });
        }
      },

      // Unbookmark a post
      unsavePost: async (postId: string) => {
        const state = get();
        const post = state.posts[postId];

        if (!post) return;

        // Optimistic update
        set({
          posts: {
            ...state.posts,
            [postId]: {
              ...post,
              userBookmarked: false,
            },
          },
        });

        try {
          await client.mutate({
            mutation: UNBOOKMARK_POST,
            variables: { postId },
          });
        } catch (error) {
          console.error('Error unbookmarking post:', error);

          // Revert on error
          set({
            posts: {
              ...state.posts,
              [postId]: post,
            },
          });
        }
      },

      // Share a post
      sharePost: async (postId: string) => {
        const state = get();
        const post = state.posts[postId];

        if (!post) return;

        // Optimistic update
        set({
          posts: {
            ...state.posts,
            [postId]: {
              ...post,
              sharesCount: post.sharesCount + 1,
            },
          },
        });

        try {
          await client.mutate({
            mutation: SHARE_POST,
            variables: { postId },
          });
        } catch (error) {
          console.error('Error sharing post:', error);

          // Revert on error
          set({
            posts: {
              ...state.posts,
              [postId]: post,
            },
          });
        }
      },
    }),
    {
      name: 'trending-store',
      storage: createJSONStorage(() => createZustandMMKVStorage(appStorage)),
      partialize: state => ({
        // Only persist certain parts of the state
        posts: state.posts,
        postIds: state.postIds,
        selectedCategories: state.selectedCategories,
        selectedVenues: state.selectedVenues,
        currentFilterType: state.currentFilterType,
        currentSortType: state.currentSortType,
      }),
    }
  )
);
