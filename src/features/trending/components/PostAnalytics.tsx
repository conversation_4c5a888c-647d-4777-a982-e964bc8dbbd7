import React from 'react';

import { ScrollView } from 'react-native';

import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withSpring,
} from 'react-native-reanimated';

import { formatNumber } from '@/src/core';
import { Box, Text } from '@/src/core/theme';
import { Card } from '@/src/shared/components';

import type { TrendingPost } from '../types';

interface PostAnalyticsProps {
  post: TrendingPost;
  detailed?: boolean;
}

interface AnalyticsMetric {
  label: string;
  value: number;
  icon: string;
  color: 'primary' | 'secondary' | 'success' | 'warning';
  change?: number; // percentage change
}

const AnimatedBox = Animated.createAnimatedComponent(Box);

const MetricCard = ({
  metric,
  detailed,
  formatChange,
  getChangeColor,
  index,
}: {
  metric: AnalyticsMetric;
  detailed?: boolean;
  formatChange: (change: number) => string;
  getChangeColor: (change: number) => 'success' | 'error' | 'textSecondary';
  index: number;
}) => {
  const cardScale = useSharedValue(0.9);
  const cardOpacity = useSharedValue(0);

  React.useEffect(() => {
    cardOpacity.value = withDelay(200 + index * 100, withSpring(1, { duration: 400 }));
    cardScale.value = withDelay(200 + index * 100, withSpring(1, { duration: 400 }));
  }, [cardOpacity, cardScale, index]);

  const cardAnimatedStyle = useAnimatedStyle(() => ({
    opacity: cardOpacity.value,
    transform: [{ scale: cardScale.value }],
  }));

  return (
    <AnimatedBox style={cardAnimatedStyle} flex={1}>
      <Card variant="outlined" padding="md_16" marginHorizontal="xxxs_2">
        <Box alignItems="center">
          <Text variant="h_24SemiBold_section" marginBottom="xxs_4">
            {metric.icon}
          </Text>
          <Text variant="h_20Medium_subsection" color="text" marginBottom="xxs_4">
            {formatNumber(metric.value)}
          </Text>
          <Text
            variant="l_12Regular_helperText"
            color="textSecondary"
            textAlign="center"
            marginBottom="xs_8">
            {metric.label}
          </Text>
          {detailed && metric.change !== undefined && (
            <Text variant="l_10SemiBold_tag" color={getChangeColor(metric.change)}>
              {formatChange(metric.change)}
            </Text>
          )}
        </Box>
      </Card>
    </AnimatedBox>
  );
};

export function PostAnalytics({ post, detailed = false }: PostAnalyticsProps) {
  const scale = useSharedValue(0.8);
  const opacity = useSharedValue(0);

  React.useEffect(() => {
    opacity.value = withDelay(100, withSpring(1, { duration: 600 }));
    scale.value = withDelay(100, withSpring(1, { duration: 600 }));
  }, [opacity, scale]);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
    transform: [{ scale: scale.value }],
  }));

  // Calculate analytics metrics
  const metrics: AnalyticsMetric[] = [
    {
      label: 'Total Likes',
      value: post.likesCount,
      icon: '❤️',
      color: 'primary',
      change: Math.random() * 20 - 10, // Mock change percentage
    },
    {
      label: 'Comments',
      value: post.commentsCount,
      icon: '💬',
      color: 'secondary',
      change: Math.random() * 15 - 7,
    },
    {
      label: 'Shares',
      value: post.sharesCount || 0,
      icon: '🔗',
      color: 'success',
      change: Math.random() * 25 - 12,
    },
    {
      label: 'Saves',
      value: post.savesCount || 0,
      icon: '🔖',
      color: 'warning',
      change: Math.random() * 18 - 9,
    },
  ];

  const engagementRate = (() => {
    const totalEngagements =
      post.likesCount + post.commentsCount + (post.sharesCount || 0) + (post.savesCount || 0);
    const estimatedViews = totalEngagements * 10; // Mock calculation
    return Math.min((totalEngagements / estimatedViews) * 100, 100);
  })();

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  const formatChange = (change: number): string => {
    const sign = change >= 0 ? '+' : '';
    return `${sign}${change.toFixed(1)}%`;
  };

  const getChangeColor = (change: number) => {
    if (change > 0) return 'success';
    if (change < 0) return 'error';
    return 'textSecondary';
  };

  if (!detailed) {
    // Compact view - single row of metrics
    return (
      <AnimatedBox style={animatedStyle}>
        <Box flexDirection="row" gap="xs_8">
          {metrics.map((metric, index) => (
            <MetricCard
              key={metric.label}
              metric={metric}
              index={index}
              getChangeColor={getChangeColor}
              formatChange={formatChange}
              detailed={detailed}
            />
          ))}
        </Box>
      </AnimatedBox>
    );
  }

  // Detailed view with additional insights
  return (
    <ScrollView showsVerticalScrollIndicator={false}>
      <AnimatedBox style={animatedStyle} padding="lg_24">
        {/* Header */}
        <Box marginBottom="xl_32">
          <Text variant="h_24SemiBold_section" color="text" marginBottom="xs_8">
            📊 Post Analytics
          </Text>
          <Text variant="b_14Regular_content" color="textSecondary">
            Performance insights for your post
          </Text>
        </Box>

        {/* Main Metrics */}
        <Box marginBottom="xl_32">
          <Text variant="h_18SemiBold_cardTitle" color="text" marginBottom="md_16">
            Engagement Metrics
          </Text>
          <Box flexDirection="row" gap="xs_8" marginBottom="lg_24">
            {metrics.slice(0, 2).map((metric, index) => (
              <MetricCard
                key={metric.label}
                metric={metric}
                index={index}
                formatChange={formatChange}
                getChangeColor={getChangeColor}
                detailed={detailed}
              />
            ))}
          </Box>
          <Box flexDirection="row" gap="xs_8">
            {metrics.slice(2).map((metric, index) => (
              <MetricCard
                key={metric.label}
                metric={metric}
                index={index + 2}
                formatChange={formatChange}
                getChangeColor={getChangeColor}
                detailed={detailed}
              />
            ))}
          </Box>
        </Box>

        {/* Engagement Rate */}
        <Card variant="outlined" padding="lg_24" marginBottom="xl_32">
          <Text variant="h_18SemiBold_cardTitle" color="text" marginBottom="sm_12">
            📈 Engagement Rate
          </Text>
          <Box flexDirection="row" alignItems="center" marginBottom="sm_12">
            <Text variant="H_40Bold_title" color="primary">
              {engagementRate.toFixed(1)}%
            </Text>
            <Box
              marginLeft="sm_12"
              backgroundColor={
                engagementRate > 5 ? 'success' : engagementRate > 2 ? 'warning' : 'error'
              }
              paddingHorizontal="xs_8"
              paddingVertical="xxxs_2"
              borderRadius="sm_8"
              opacity={0.1}>
              <Text
                variant="l_10SemiBold_tag"
                color={engagementRate > 5 ? 'success' : engagementRate > 2 ? 'warning' : 'error'}>
                {engagementRate > 5 ? 'Excellent' : engagementRate > 2 ? 'Good' : 'Needs Work'}
              </Text>
            </Box>
          </Box>
          <Text variant="b_14Regular_content" color="textSecondary">
            This measures how much your audience interacts with your content
          </Text>
        </Card>

        {/* Time Performance */}
        <Card variant="outlined" padding="lg_24" marginBottom="xl_32">
          <Text variant="h_18SemiBold_cardTitle" color="text" marginBottom="sm_12">
            ⏰ Time Performance
          </Text>
          <Box flexDirection="row" justifyContent="space-between" marginBottom="md_16">
            <Box>
              <Text variant="b_16SemiBold_button" color="text">
                Peak Hour
              </Text>
              <Text variant="l_14Medium_formHelperText" color="textSecondary">
                2:00 PM - 3:00 PM
              </Text>
            </Box>
            <Box>
              <Text variant="b_16SemiBold_button" color="text">
                Posted
              </Text>
              <Text variant="l_14Medium_formHelperText" color="textSecondary">
                {new Date(post.createdAt).toLocaleDateString()}
              </Text>
            </Box>
          </Box>
          <Text variant="b_14Regular_content" color="textSecondary">
            Your post performed best during afternoon hours when your audience is most active
          </Text>
        </Card>

        {/* Audience Insights */}
        <Card variant="outlined" padding="lg_24">
          <Text variant="h_18SemiBold_cardTitle" color="text" marginBottom="sm_12">
            👥 Audience Insights
          </Text>
          <Box flexDirection="row" justifyContent="space-between" marginBottom="md_16">
            <Box flex={1}>
              <Text variant="b_16SemiBold_button" color="text">
                Top Location
              </Text>
              <Text variant="l_14Medium_formHelperText" color="textSecondary">
                {post.venue?.city || 'San Francisco, CA'}
              </Text>
            </Box>
            <Box flex={1}>
              <Text variant="b_16SemiBold_button" color="text">
                Age Group
              </Text>
              <Text variant="l_14Medium_formHelperText" color="textSecondary">
                25-34 years
              </Text>
            </Box>
          </Box>
          <Text variant="b_14Regular_content" color="textSecondary">
            Most engagement came from users in your local area and similar age group
          </Text>
        </Card>
      </AnimatedBox>
    </ScrollView>
  );
}
