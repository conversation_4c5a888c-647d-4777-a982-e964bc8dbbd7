import React from 'react';

import { Calendar, Clock, Fire, TrendUp, Users } from 'phosphor-react-native';

import { Box, Pressable, Text, useTheme } from '@/src/core/theme';
import { Card, CardToolbar, Divider } from '@/src/shared/components';

interface Venue {
  id: string;
  name: string;
  activity: 'High activity' | 'Moderate' | 'Low';
  currentGuests: number;
  peakTime: string;
  events: number;
  trending?: boolean;
}

interface VenueActivityCardProps {
  venues: Venue[];
  onViewVenue?: (venueId: string) => void;
  isExpanded?: boolean;
  onToggleExpand?: () => void;
  showViewMore?: boolean;
}

const ActivityIndicator = ({ level }: { level: 'High activity' | 'Moderate' | 'Low' }) => {
  const getColor = () => {
    switch (level) {
      case 'High activity':
        return 'errorMain';
      case 'Moderate':
        return 'warningMain';
      default:
        return 'successMain';
    }
  };

  const getIcon = () => {
    switch (level) {
      case 'High activity':
        return <Fire size={14} color="#fff" />;
      case 'Moderate':
        return <TrendUp size={14} color="#fff" />;
      default:
        return <Users size={14} color="#fff" />;
    }
  };

  return (
    <Box
      backgroundColor={getColor()}
      paddingHorizontal="xs_8"
      paddingVertical="xxxs_2"
      borderRadius="xs_4"
      flexDirection="row"
      alignItems="center"
      gap="xxs_4">
      {getIcon()}
      <Text variant="l_10SemiBold_chip" color="white">
        {level}
      </Text>
    </Box>
  );
};

const VenueItem = ({
  venue,
  onView,
  showDivider = true,
}: {
  venue: Venue;
  onView?: () => void;
  showDivider?: boolean;
}) => {
  const theme = useTheme();

  return (
    <>
      <Pressable onPress={onView}>
        <Box gap="sm_12">
          {/* Header */}
          <Box flexDirection="row" justifyContent="space-between" alignItems="center">
            <Box gap="xxs_4">
              <Text variant="h_16SemiBold_alertTitle" color="mainText">
                {venue.name}
              </Text>
              <ActivityIndicator level={venue.activity} />
            </Box>

            {venue.trending && (
              <Box
                backgroundColor="warningLight"
                paddingHorizontal="xs_8"
                paddingVertical="xxxs_2"
                borderRadius="xs_4"
                flexDirection="row"
                alignItems="center"
                gap="xxs_4">
                <Fire size={12} color={theme.colors.warningDark} />
                <Text variant="l_10SemiBold_chip" color="warningDark">
                  Trending
                </Text>
              </Box>
            )}
          </Box>

          {/* Stats */}
          <Box
            backgroundColor="subtleBackground"
            borderRadius="sm_8"
            padding="sm_12"
            flexDirection="row"
            justifyContent="space-between">
            <Box gap="xxs_4">
              <Box flexDirection="row" alignItems="center" gap="xxs_4">
                <Users size={14} color={theme.colors.iconDefault} />
                <Text variant="l_12Regular_helperText" color="mutedText">
                  Current guests
                </Text>
              </Box>
              <Text variant="h_20Medium_subsection" color="mainText">
                {venue.currentGuests}
              </Text>
            </Box>

            <Box gap="xxs_4">
              <Box flexDirection="row" alignItems="center" gap="xxs_4">
                <Clock size={14} color={theme.colors.iconMuted} />
                <Text variant="l_12Regular_helperText" color="mutedText">
                  Peak time
                </Text>
              </Box>
              <Text variant="l_14SemiBold_action" color="brandMain">
                {venue.peakTime}
              </Text>
            </Box>

            <Box gap="xxs_4">
              <Box flexDirection="row" alignItems="center" gap="xxs_4">
                <Calendar size={14} color={theme.colors.iconAccent} />
                <Text variant="l_12Regular_helperText" color="mutedText">
                  Events
                </Text>
              </Box>
              <Text variant="l_14SemiBold_action" color="mainText">
                {venue.events}
              </Text>
            </Box>
          </Box>
        </Box>
      </Pressable>

      {showDivider && <Divider marginVertical="sm_12" />}
    </>
  );
};

export function VenueActivityCard({
  venues,
  onViewVenue,
  isExpanded = false,
  onToggleExpand,
  showViewMore = false,
}: VenueActivityCardProps) {
  const displayVenues = isExpanded ? venues : venues.slice(0, 2);

  return (
    <Card
      showToolbar
      toolbarTitle="Venue Activity"
      toolbarActionText="Live"
      onToolbarPress={onToggleExpand}>
      {/* <CardToolbar title="Venue Activity" actionText="Live" onPress={onToggleExpand} /> */}

      <Box padding="md_16" gap="none_0">
        {displayVenues.map((venue, index) => (
          <VenueItem
            key={venue.id}
            venue={venue}
            onView={() => onViewVenue?.(venue.id)}
            showDivider={index < displayVenues.length - 1}
          />
        ))}

        {!isExpanded && showViewMore && venues.length > 2 && (
          <>
            <Divider marginVertical="sm_12" />
            <Pressable onPress={onToggleExpand} alignItems="center" paddingVertical="xs_8">
              <Text variant="l_14SemiBold_action" color="brandMain">
                View {venues.length - 2} more venues
              </Text>
            </Pressable>
          </>
        )}
      </Box>
    </Card>
  );
}
