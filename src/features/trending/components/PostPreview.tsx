import React from 'react';

import { <PERSON><PERSON>, <PERSON><PERSON>View } from 'react-native';

import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from 'react-native-reanimated';

import { useHaptics } from '@/src/core/libs';
import { Box, Text } from '@/src/core/theme';
import { Avatar, Button } from '@/src/shared/components';

import type { User, Venue } from '../types';
import { ImageGallery } from './ImageGallery';
import { LocationBadge } from './LocationBadge';

interface PostPreviewProps {
  content: string;
  images: string[];
  venue?: Venue | null;
  user: User;
  onClose: () => void;
  onPublish: () => void;
  isPublishing: boolean;
}

const AnimatedBox = Animated.createAnimatedComponent(Box);

export function PostPreview({
  content,
  images,
  venue,
  user,
  onClose,
  onPublish,
  isPublishing,
}: PostPreviewProps) {
  const { triggerHaptic } = useHaptics();
  const scale = useSharedValue(1);

  const buttonAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const handlePublish = () => {
    scale.value = withSpring(0.95, { duration: 100 }, () => {
      scale.value = withSpring(1, { duration: 100 });
    });
    runOnJS(triggerHaptic)('impact', 'heavy');
    onPublish();
  };

  const formatContent = (text: string) => {
    // Split text by hashtags and mentions for styling
    const parts = text.split(/(\#\w+|\@\w+)/g);

    return parts.map((part, index) => {
      if (part.startsWith('#')) {
        return (
          <Text key={index} variant="b_16Regular_input" color="primary">
            {part}
          </Text>
        );
      }
      if (part.startsWith('@')) {
        return (
          <Text key={index} variant="b_16Regular_input" color="secondary">
            {part}
          </Text>
        );
      }
      return (
        <Text key={index} variant="b_16Regular_input" color="text">
          {part}
        </Text>
      );
    });
  };

  return (
    <Modal
      visible={true}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}>
      <AnimatedBox flex={1} backgroundColor="background">
        {/* Header */}
        <Box
          flexDirection="row"
          justifyContent="space-between"
          alignItems="center"
          padding="lg_24"
          borderBottomWidth={1}
          borderBottomColor="border">
          <Button title="Back" variant="ghost" onPress={onClose} disabled={isPublishing} />
          <Text variant="h_18SemiBold_cardTitle" color="text">
            Preview Post
          </Text>
          <AnimatedBox style={buttonAnimatedStyle}>
            <Button
              title={isPublishing ? 'Publishing...' : 'Publish'}
              variant="primary"
              onPress={handlePublish}
              disabled={isPublishing}
              loading={isPublishing}
            />
          </AnimatedBox>
        </Box>

        {/* Preview Content */}
        <ScrollView contentContainerStyle={{ padding: 24 }} showsVerticalScrollIndicator={false}>
          {/* Post Card Preview */}
          <Box
            backgroundColor="card"
            borderRadius="md_12"
            padding="md_16"
            borderWidth={1}
            borderColor="border">
            {/* User Header */}
            <Box flexDirection="row" alignItems="center" marginBottom="sm_12">
              <Avatar size="m" source={{ uri: user.avatar }} />
              <Box marginLeft="sm_12" flex={1}>
                <Box flexDirection="row" alignItems="center">
                  <Text variant="b_16SemiBold_button" color="text">
                    {user.name}
                  </Text>
                  {user.isVerified && (
                    <Text variant="l_12Medium_message" color="primary" marginLeft="xxs_4">
                      ✓
                    </Text>
                  )}
                </Box>
                <Text variant="l_14Medium_formHelperText" color="textSecondary">
                  @{user.username} • now
                </Text>
              </Box>
            </Box>

            {/* Post Content */}
            <Box marginBottom="sm_12">{formatContent(content)}</Box>

            {/* Images */}
            {images.length > 0 && (
              <Box marginBottom="sm_12">
                <ImageGallery images={images} onRemove={() => {}} editable={false} />
              </Box>
            )}

            {/* Venue */}
            {venue && (
              <Box marginBottom="sm_12">
                <LocationBadge venue={venue} />
              </Box>
            )}

            {/* Interaction Buttons Preview */}
            <Box
              flexDirection="row"
              justifyContent="space-between"
              alignItems="center"
              borderTopWidth={1}
              borderTopColor="border"
              paddingTop="sm_12">
              <Box flexDirection="row" gap="lg_24">
                <Box flexDirection="row" alignItems="center">
                  <Text variant="b_14Regular_content" color="textSecondary">
                    🤍 0
                  </Text>
                </Box>
                <Box flexDirection="row" alignItems="center">
                  <Text variant="b_14Regular_content" color="textSecondary">
                    💬 0
                  </Text>
                </Box>
                <Box flexDirection="row" alignItems="center">
                  <Text variant="b_14Regular_content" color="textSecondary">
                    🔗
                  </Text>
                </Box>
              </Box>
              <Box flexDirection="row" alignItems="center">
                <Text variant="b_14Regular_content" color="textSecondary">
                  🔖
                </Text>
              </Box>
            </Box>
          </Box>

          {/* Preview Notes */}
          <Box backgroundColor="surface" borderRadius="sm_8" padding="md_16" marginTop="lg_24">
            <Text variant="h_18SemiBold_cardTitle" color="text" marginBottom="xs_8">
              📝 Preview Notes
            </Text>
            <Text variant="b_14Regular_content" color="textSecondary" marginBottom="sm_12">
              This is how your post will appear in the trending feed. Review everything carefully
              before publishing.
            </Text>

            <Box>
              <Text variant="l_12Medium_message" color="textTertiary">
                • Your post will be visible to all users
              </Text>
              {venue && (
                <Text variant="l_12Medium_message" color="textTertiary">
                  • Location will help others discover your post
                </Text>
              )}
              {images.length > 0 && (
                <Text variant="l_12Medium_message" color="textTertiary">
                  • {images.length} image{images.length > 1 ? 's' : ''} will be uploaded
                </Text>
              )}
              <Text variant="l_12Medium_message" color="textTertiary">
                • You can edit or delete your post after publishing
              </Text>
            </Box>
          </Box>
        </ScrollView>
      </AnimatedBox>
    </Modal>
  );
}
