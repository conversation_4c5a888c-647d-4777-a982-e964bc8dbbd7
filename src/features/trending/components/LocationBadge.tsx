import React from 'react';

import { useTranslation } from '@/src/core/i18n';
import { Box, Text } from '@/src/core/theme';
import { IllustrationPlaceholder, Illustrations } from '@/src/shared/components/Placeholders';

import { Venue, VenueCategory } from '../types';

interface LocationBadgeProps {
  venue: Venue;
  compact?: boolean;
}

const LocationBadge: React.FC<LocationBadgeProps> = ({ venue, compact = false }) => {
  const { t } = useTranslation();

  // Get category illustration based on venue category
  const getCategoryIllustration = (category: VenueCategory) => {
    const illustrationMap: Record<
      VenueCategory,
      (typeof Illustrations)[keyof typeof Illustrations]
    > = {
      [VenueCategory.RESTAURANT]: Illustrations.VENUE_RESTAURANT,
      [VenueCategory.CAFE]: Illustrations.VENUE_CAFE,
      [VenueCategory.BAR]: Illustrations.VENUE_BAR,
      [VenueCategory.CLUB]: Illustrations.VENUE_CLUB,
      [VenueCategory.EVENT_VENUE]: Illustrations.VENUE_EVENT,
      [VenueCategory.HOTEL]: Illustrations.VENUE_HOTEL,
      [VenueCategory.SHOPPING]: Illustrations.VENUE_SHOPPING,
      [VenueCategory.ENTERTAINMENT]: Illustrations.VENUE_ENTERTAINMENT,
      [VenueCategory.OUTDOORS]: Illustrations.VENUE_OUTDOORS,
      [VenueCategory.CULTURAL]: Illustrations.VENUE_CULTURAL,
      [VenueCategory.SPORTS]: Illustrations.VENUE_SPORTS,
      [VenueCategory.OTHER]: Illustrations.VENUE_OTHER,
    };

    return illustrationMap[category] || Illustrations.VENUE_OTHER;
  };

  // Format venue status (open/closed)
  const getStatusText = () => {
    // For MVP, we'll use a simple check. In real implementation, this would consider business hours
    const isOpen = venue.verified; // Placeholder logic
    return isOpen
      ? { text: t('venue.status.open'), color: 'successMain' as const }
      : { text: t('venue.status.closed'), color: 'errorMain' as const };
  };

  const status = getStatusText();
  const categoryIllustration = getCategoryIllustration(venue.category);

  return (
    <Box
      marginVertical="sm_12"
      paddingHorizontal="sm_12"
      paddingVertical="xs_8"
      backgroundColor="surfaceBackground"
      borderRadius="md_12"
      flexDirection="row"
      alignItems="center">
      <Box marginRight="xs_8">
        <IllustrationPlaceholder
          illustrationName={categoryIllustration.name}
          description={categoryIllustration.description}
          width={24}
          height={24}
          backgroundColor="primaryLight"
        />
      </Box>

      <Box flex={1}>
        <Text variant="h_16Medium_formLabel" numberOfLines={1}>
          {venue.name}
        </Text>

        {!compact && (
          <Box flexDirection="row" alignItems="center">
            <Text variant="l_12Regular_helperText" color="secondaryText" numberOfLines={1}>
              {venue.city}, {venue.country}
            </Text>

            <Box
              width={4}
              height={4}
              borderRadius={'xxs_2'}
              backgroundColor="mutedText"
              marginHorizontal="xs_8"
            />

            <Text variant="l_12Regular_helperText" color={status.color}>
              {status.text}
            </Text>
          </Box>
        )}
      </Box>

      {venue.rating && !compact && (
        <Box
          flexDirection="row"
          alignItems="center"
          backgroundColor="primary"
          paddingHorizontal="xs_8"
          paddingVertical="xxs_4"
          borderRadius="sm_8"
          marginLeft="sm_12">
          <Box marginRight="xxs_4">
            <IllustrationPlaceholder
              illustrationName={Illustrations.RATING_STAR.name}
              description={Illustrations.RATING_STAR.description}
              width={12}
              height={12}
              backgroundColor="transparent"
              showLabel={false}
            />
          </Box>
          <Text variant="l_12SemiBold_button" color="white">
            {venue.rating.toFixed(1)}
          </Text>
        </Box>
      )}
    </Box>
  );
};

export default LocationBadge;
