import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, CrownSimple, Detective, Lock, Users } from 'phosphor-react-native';

import { Box, Pressable, Text, useTheme } from '@/src/core/theme';
import { Card, CardToolbar } from '@/src/shared/components';

interface SecretEvent {
  id: string;
  name: string;
  description: string;
  isExclusive: boolean;
  isRevealed: boolean;
  location?: {
    name: string;
    distance?: string;
  };
  time?: string;
  host?: {
    name: string;
    avatar?: string;
  };
  attendees?: number;
  capacity?: number;
}

interface SecretEventCardProps {
  event: SecretEvent;
  onRSVP?: () => void;
  onRevealLocation?: () => void;
  onShare?: () => void;
}

export function SecretEventCard({
  event,
  onRSVP,
  onRevealLocation,
  onShare,
}: SecretEventCardProps) {
  const theme = useTheme();

  return (
    <Card>
      <CardToolbar title="Secret & Exclusive events" actionText="All" />

      <Box padding="md_16">
        <Box
          backgroundColor="cardBackground"
          borderRadius="lg_16"
          borderWidth={1}
          borderColor="mainBorder"
          overflow="hidden">
          {/* Header */}
          <Box
            backgroundColor={event.isExclusive ? 'brandMain' : 'subtleBackground'}
            padding="md_16"
            gap="xs_8">
            <Box flexDirection="row" justifyContent="space-between" alignItems="center">
              <Text
                variant="h_18SemiBold_cardTitle"
                color={event.isExclusive ? 'inverseText' : 'mainText'}>
                {event.name}
              </Text>
              {event.isExclusive && (
                <Box
                  style={{ backgroundColor: 'rgba(255,255,255,0.2)' }}
                  paddingHorizontal="xs_8"
                  paddingVertical="xxxs_2"
                  borderRadius="xs_4"
                  flexDirection="row"
                  alignItems="center"
                  gap="xxs_4">
                  <CrownSimple size={12} color={theme.colors.white} />
                  <Text variant="l_10SemiBold_chip" color="white">
                    Exclusive
                  </Text>
                </Box>
              )}
            </Box>

            <Text
              variant="b_14Regular_content"
              color={event.isExclusive ? 'inverseText' : 'secondaryText'}>
              {event.description}
            </Text>
          </Box>

          {/* Content */}
          <Box backgroundColor="subtleBackground" padding="md_16" gap="sm_12">
            {/* Location */}
            <Box flexDirection="row" alignItems="center" gap="sm_12">
              <Box flexDirection="row" alignItems="center" gap="xxs_4" flex={1}>
                {event.isRevealed ? (
                  <>
                    <Detective size={16} color={theme.colors.iconDefault} />
                    <Text variant="l_14Medium_info" color="secondaryText">
                      {event.location?.name || 'Location revealed'}
                    </Text>
                  </>
                ) : (
                  <>
                    <Lock size={16} color={theme.colors.iconMuted} />
                    <Text variant="l_14Medium_info" color="mutedText">
                      Hidden location
                    </Text>
                  </>
                )}
              </Box>

              {event.location?.distance && (
                <Box
                  backgroundColor="surfaceBackground"
                  paddingHorizontal="xs_8"
                  paddingVertical="xxxs_2"
                  borderRadius="xs_4">
                  <Text variant="l_10SemiBold_chip" color="mutedText">
                    {event.location.distance}
                  </Text>
                </Box>
              )}
            </Box>

            {/* Time & Attendees */}
            <Box flexDirection="row" justifyContent="space-between" alignItems="center">
              <Box flexDirection="row" gap="md_16">
                {event.time && (
                  <Box flexDirection="row" alignItems="center" gap="xxs_4">
                    <Clock size={16} color={theme.colors.iconMuted} />
                    <Text variant="l_12Regular_helperText" color="mutedText">
                      {event.time}
                    </Text>
                  </Box>
                )}

                {event.attendees !== undefined && (
                  <Box flexDirection="row" alignItems="center" gap="xxs_4">
                    <Users size={16} color={theme.colors.iconDefault} />
                    <Text variant="l_12Regular_helperText" color="secondaryText">
                      {event.attendees}/{event.capacity || '?'} attending
                    </Text>
                  </Box>
                )}
              </Box>

              {/* Status */}
              {event.isRevealed && (
                <Box flexDirection="row" alignItems="center" gap="xxs_4">
                  <CheckCircle size={16} color={theme.colors.successMain} />
                  <Text variant="l_12SemiBold_button" color="successMain">
                    RSVP&apos;d
                  </Text>
                </Box>
              )}
            </Box>

            {/* Actions */}
            <Box flexDirection="row" gap="xs_8">
              {!event.isRevealed ? (
                <>
                  <Pressable
                    flex={1}
                    onPress={onRSVP}
                    backgroundColor="buttonFilledBackground"
                    paddingVertical="sm_12"
                    borderRadius="sm_8"
                    alignItems="center">
                    <Text variant="l_14SemiBold_action" color="buttonFilledText">
                      RSVP to Reveal
                    </Text>
                  </Pressable>
                  <Pressable
                    onPress={onShare}
                    backgroundColor="buttonTintedBackground"
                    paddingHorizontal="md_16"
                    paddingVertical="sm_12"
                    borderRadius="sm_8">
                    <Text variant="l_14SemiBold_action" color="buttonTintedText">
                      Share
                    </Text>
                  </Pressable>
                </>
              ) : (
                <>
                  <Pressable
                    flex={1}
                    onPress={onRevealLocation}
                    backgroundColor="successMain"
                    paddingVertical="sm_12"
                    borderRadius="sm_8"
                    alignItems="center">
                    <Text variant="l_14SemiBold_action" color="white">
                      View Details
                    </Text>
                  </Pressable>
                  <Pressable
                    onPress={onShare}
                    backgroundColor="buttonTintedBackground"
                    paddingHorizontal="md_16"
                    paddingVertical="sm_12"
                    borderRadius="sm_8">
                    <Text variant="l_14SemiBold_action" color="buttonTintedText">
                      Share
                    </Text>
                  </Pressable>
                </>
              )}
            </Box>
          </Box>
        </Box>
      </Box>
    </Card>
  );
}
