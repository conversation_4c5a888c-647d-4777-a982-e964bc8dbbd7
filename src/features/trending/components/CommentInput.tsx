import React, { useRef, useState } from 'react';

import { Keyboard, TextInput as RNTextInput } from 'react-native';

import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';

import { useHaptics } from '@/src/core/libs';
import { Box, Pressable, Text, TextInput } from '@/src/core/theme';
import { useAuth } from '@/src/features/auth/services';
import { Avatar } from '@/src/shared/components';

interface CommentInputProps {
  onSubmit: (content: string, parentId?: string) => Promise<void>;
  replyingTo?: string | null;
  onCancelReply?: () => void;
  placeholder?: string;
}

const AnimatedBox = Animated.createAnimatedComponent(Box);

export function CommentInput({
  onSubmit,
  replyingTo,
  onCancelReply,
  placeholder = 'Add a comment...',
}: CommentInputProps) {
  const { user } = useAuth();
  const { triggerHaptic } = useHaptics();
  const [comment, setComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<RNTextInput>(null);

  const scale = useSharedValue(1);
  const opacity = useSharedValue(0.6);

  const buttonAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  const handleSubmit = async () => {
    if (!comment.trim() || isSubmitting) return;

    setIsSubmitting(true);
    scale.value = withSpring(0.95, { duration: 100 }, () => {
      scale.value = withSpring(1, { duration: 100 });
    });

    runOnJS(triggerHaptic)('impact', 'medium');

    try {
      await onSubmit(comment.trim(), replyingTo || undefined);
      setComment('');
      inputRef.current?.blur();
      Keyboard.dismiss();
    } catch (error) {
      console.error('Failed to submit comment:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancelReply = () => {
    runOnJS(triggerHaptic)('impact', 'light');
    onCancelReply?.();
  };

  // Update button opacity based on comment content
  React.useEffect(() => {
    opacity.value = withTiming(comment.trim().length > 0 ? 1 : 0.6, { duration: 200 });
  }, [comment, opacity]);

  if (!user) {
    return (
      <Box padding="md_16" backgroundColor="surface" borderTopWidth={1} borderTopColor="border">
        <Text variant="b_14Regular_content" color="textSecondary" textAlign="center">
          Sign in to comment
        </Text>
      </Box>
    );
  }

  return (
    <Box backgroundColor="surface" borderTopWidth={1} borderTopColor="border">
      {/* Reply Indicator */}
      {replyingTo && (
        <Box
          paddingHorizontal="lg_24"
          paddingTop="sm_12"
          flexDirection="row"
          justifyContent="space-between"
          alignItems="center">
          <Text variant="l_12Medium_message" color="textSecondary">
            Replying to comment
          </Text>
          <Pressable onPress={handleCancelReply}>
            <Text variant="l_12Medium_message" color="primary">
              Cancel
            </Text>
          </Pressable>
        </Box>
      )}

      {/* Input Area */}
      <Box
        paddingHorizontal="lg_24"
        paddingVertical="md_16"
        flexDirection="row"
        alignItems="flex-end"
        gap="sm_12">
        {/* User Avatar */}
        <Avatar size="s" source={{ uri: user.avatar }} />

        {/* Input Field */}
        <Box flex={1}>
          <TextInput
            ref={inputRef}
            value={comment}
            onChangeText={setComment}
            placeholder={placeholder}
            placeholderTextColor="textTertiary"
            multiline
            maxLength={500}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            style={{
              minHeight: 40,
              maxHeight: 120,
              paddingVertical: 8,
              paddingHorizontal: 12,
              borderRadius: 20,
              backgroundColor: isFocused ? 'rgba(0,0,0,0.05)' : 'rgba(0,0,0,0.03)',
              borderWidth: isFocused ? 1 : 0,
              borderColor: isFocused ? '#007AFF' : 'transparent',
            }}
            returnKeyType="send"
            onSubmitEditing={handleSubmit}
            blurOnSubmit={false}
            textAlignVertical="top"
          />

          {/* Character Counter */}
          {comment.length > 400 && (
            <Text
              variant="l_10SemiBold_tag"
              color={comment.length >= 500 ? 'error' : 'textTertiary'}
              marginTop="xxs_4"
              alignSelf="flex-end">
              {comment.length}/500
            </Text>
          )}
        </Box>

        {/* Send Button */}
        <AnimatedBox style={buttonAnimatedStyle}>
          <Pressable
            onPress={handleSubmit}
            disabled={!comment.trim() || isSubmitting}
            padding="xs_8"
            borderRadius="circle_9999"
            backgroundColor={comment.trim() ? 'primary' : 'disabled'}>
            <Text variant="l_12Medium_message" color="textInverted">
              {isSubmitting ? '⏳' : '➤'}
            </Text>
          </Pressable>
        </AnimatedBox>
      </Box>
    </Box>
  );
}
