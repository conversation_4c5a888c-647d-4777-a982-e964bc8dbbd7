import React from 'react';

import { useTranslation } from 'react-i18next';
import Animated, { useAnimatedStyle, withSpring } from 'react-native-reanimated';

import { Box, Text } from '@/src/core/theme';
import Pressable from '@/src/core/theme/Pressable';
import { RiveAnimationPlaceholder, RiveAnimations } from '@/src/shared/components/Placeholders';

import { TrendingPost } from '../types';

interface PostInteractionsProps {
  post: TrendingPost;
  onLikePress: () => void;
  onCommentPress: () => void;
  onSharePress: () => void;
  onSavePress: () => void;
}

const PostInteractions: React.FC<PostInteractionsProps> = ({
  post,
  onLikePress,
  onCommentPress,
  onSharePress,
  onSavePress,
}) => {
  const { t } = useTranslation();

  // Animation for like button
  const likeAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        {
          scale: post.isLiked
            ? withSpring(1.1, { damping: 10, stiffness: 100 }, () => {
                return withSpring(1);
              })
            : 1,
        },
      ],
    };
  }, [post.isLiked]);

  // Animation for save button
  const saveAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        {
          scale: post.isSaved
            ? withSpring(1.1, { damping: 10, stiffness: 100 }, () => {
                return withSpring(1);
              })
            : 1,
        },
      ],
    };
  }, [post.isSaved]);

  // Function to format numbers (e.g., 1000 -> 1K)
  const formatCount = (count: number): string => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    }
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  return (
    <Box
      flexDirection="row"
      justifyContent="space-between"
      paddingVertical="sm_12"
      marginTop="xs_8">
      {/* Like button */}
      <Animated.View style={likeAnimatedStyle}>
        <Pressable onPress={onLikePress} hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}>
          <Box flexDirection="row" alignItems="center">
            <Box marginRight="xxs_4">
              <RiveAnimationPlaceholder
                animationName={
                  post.isLiked ? RiveAnimations.HEART_LIKE.name : RiveAnimations.HEART_LIKE.name
                }
                description={
                  post.isLiked
                    ? 'Filled heart with pulse animation'
                    : 'Empty heart ready to be liked'
                }
                width={20}
                height={20}
                backgroundColor={post.isLiked ? 'error' : 'disabled'}
                showLabel={false}
              />
            </Box>
            <Text
              variant="l_12Regular_helperText"
              color={post.isLiked ? 'errorMain' : 'secondaryText'}>
              {formatCount(post.likesCount)}
            </Text>
          </Box>
        </Pressable>
      </Animated.View>

      {/* Comment button */}
      <Pressable onPress={onCommentPress} hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}>
        <Box flexDirection="row" alignItems="center">
          <Box marginRight="xxs_4">
            <RiveAnimationPlaceholder
              animationName={RiveAnimations.COMMENT_BUBBLE.name}
              description={RiveAnimations.COMMENT_BUBBLE.description}
              width={20}
              height={20}
              backgroundColor="secondaryLight"
              showLabel={false}
            />
          </Box>
          <Text variant="l_12Regular_helperText" color="secondaryText">
            {formatCount(post.commentsCount)}
          </Text>
        </Box>
      </Pressable>

      {/* Share button */}
      <Pressable onPress={onSharePress} hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}>
        <Box flexDirection="row" alignItems="center">
          <Box marginRight="xxs_4">
            <RiveAnimationPlaceholder
              animationName={RiveAnimations.SHARE_RIPPLE.name}
              description={RiveAnimations.SHARE_RIPPLE.description}
              width={20}
              height={20}
              backgroundColor="primaryLight"
              showLabel={false}
            />
          </Box>
          <Text variant="l_12Regular_helperText" color="secondaryText">
            {formatCount(post.sharesCount)}
          </Text>
        </Box>
      </Pressable>

      {/* Save button */}
      <Animated.View style={saveAnimatedStyle}>
        <Pressable onPress={onSavePress} hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}>
          <Box flexDirection="row" alignItems="center">
            <Box marginRight="xxs_4">
              <RiveAnimationPlaceholder
                animationName={RiveAnimations.BOOKMARK_FILL.name}
                description={
                  post.isSaved
                    ? 'Filled bookmark with bounce effect'
                    : 'Empty bookmark ready to save'
                }
                width={20}
                height={20}
                backgroundColor={post.isSaved ? 'primary' : 'disabled'}
                showLabel={false}
              />
            </Box>
            <Text
              variant="l_12Regular_helperText"
              color={post.isSaved ? 'primary' : 'secondaryText'}>
              {formatCount(post.savesCount)}
            </Text>
          </Box>
        </Pressable>
      </Animated.View>
    </Box>
  );
};

export default PostInteractions;
