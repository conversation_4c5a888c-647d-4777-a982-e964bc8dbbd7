import React, { useState } from 'react';

import { Pressable, ScrollView } from 'react-native';

import { useTranslation } from 'react-i18next';
import Animated, { useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated';

import { haptics } from '@/src/core/libs';
import { Box, Text } from '@/src/core/theme';

import { TrendingFilterType, TrendingSortType, useTrendingStore } from '../store/trendingStore';

const FilterChips: React.FC = () => {
  const { t } = useTranslation();

  // Access trending store state and actions
  const {
    selectedCategories,
    selectedVenues,
    currentFilterType,
    currentSortType,
    setFilterType,
    setSortType,
    resetFilters,
  } = useTrendingStore();

  // Animation for the filter menu
  const [showCategoryFilters, setShowCategoryFilters] = useState(false);
  const filterHeight = useSharedValue(0);

  const filterAnimatedStyle = useAnimatedStyle(() => {
    return {
      height: filterHeight.value,
      opacity: filterHeight.value > 0 ? 1 : 0,
      overflow: 'hidden',
    };
  });

  // Toggle category filters visibility
  const toggleCategoryFilters = async () => {
    await haptics.light();
    setShowCategoryFilters(!showCategoryFilters);
    filterHeight.set(
      withTiming(showCategoryFilters ? 0 : 120, {
        duration: 300,
      })
    );
  };

  // Filter type options for segmented control
  const filterTypeOptions = [
    { label: t('trending.filters.all'), value: TrendingFilterType.ALL },
    { label: t('trending.filters.following'), value: TrendingFilterType.FOLLOWING },
    { label: t('trending.filters.nearby'), value: TrendingFilterType.NEARBY },
  ];

  // Sort type options for segmented control
  const sortTypeOptions = [
    { label: t('trending.sort.trending'), value: TrendingSortType.TRENDING },
    { label: t('trending.sort.recent'), value: TrendingSortType.RECENT },
    { label: t('trending.sort.popular'), value: TrendingSortType.POPULAR },
  ];

  // Check if a category is selected

  // Check if a venue is selected

  // Handle filter type change
  const handleFilterTypeChange = async (value: string) => {
    await haptics.selection();
    setFilterType(value as TrendingFilterType);
  };

  // Handle sort type change
  const handleSortTypeChange = async (value: string) => {
    await haptics.selection();
    setSortType(value as TrendingSortType);
  };

  // Check if any filter is active
  const hasActiveFilters = selectedCategories.length > 0 || selectedVenues.length > 0;

  // Simple segmented control component
  const renderSegmentedControl = (
    options: any[],
    selectedValue: string,
    onChange: (value: string) => void
  ) => (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={{ paddingHorizontal: 4 }}>
      {options.map(option => (
        <Pressable
          key={option.value}
          onPress={() => onChange(option.value)}
          style={{ marginHorizontal: 4 }}>
          <Box
            backgroundColor={selectedValue === option.value ? 'primary' : 'buttonTintedBackground'}
            paddingHorizontal="md_16"
            paddingVertical="xs_8"
            borderRadius="md_12">
            <Text
              variant="l_14Medium_info"
              color={selectedValue === option.value ? 'white' : 'buttonTintedText'}>
              {option.label}
            </Text>
          </Box>
        </Pressable>
      ))}
    </ScrollView>
  );

  return (
    <Box marginBottom="md_16">
      {/* Main filter types */}
      <Box marginBottom="sm_12">
        {renderSegmentedControl(filterTypeOptions, currentFilterType, handleFilterTypeChange)}
      </Box>

      {/* Sort types */}
      <Box marginBottom="sm_12">
        {renderSegmentedControl(sortTypeOptions, currentSortType, handleSortTypeChange)}
      </Box>

      {/* Category filter toggle and reset button */}
      <Box
        flexDirection="row"
        justifyContent="space-between"
        alignItems="center"
        marginBottom="sm_12">
        <Pressable onPress={toggleCategoryFilters}>
          <Box
            flexDirection="row"
            alignItems="center"
            backgroundColor="buttonTintedBackground"
            paddingHorizontal="sm_12"
            paddingVertical="xs_8"
            borderRadius="sm_8">
            <Text variant="l_14Medium_info" color="buttonTintedText">
              {t('trending.filters.moreFilters', 'More Filters')}
            </Text>
          </Box>
        </Pressable>

        {hasActiveFilters && (
          <Pressable onPress={resetFilters}>
            <Box
              backgroundColor="errorMain"
              paddingHorizontal="sm_12"
              paddingVertical="xs_8"
              borderRadius="sm_8">
              <Text variant="l_12SemiBold_button" color="white">
                {t('trending.filters.reset', 'Reset')}
              </Text>
            </Box>
          </Pressable>
        )}
      </Box>

      {/* Expanded category and venue filters - Simplified for MVP */}
      <Animated.View style={filterAnimatedStyle}>
        <Box backgroundColor="subtleBackground" padding="md_16" borderRadius="md_12">
          <Text variant="l_14Medium_info" color="secondaryText" marginBottom="sm_12">
            {t('trending.filters.comingSoon', 'Advanced filters coming soon!')}
          </Text>
          <Text variant="l_12Regular_helperText" color="mutedText">
            {t(
              'trending.filters.comingSoonDesc',
              'Category and venue filters will be available in the next update.'
            )}
          </Text>
        </Box>
      </Animated.View>
    </Box>
  );
};

export default FilterChips;
