import React from 'react';

import { Dimensions, Image } from 'react-native';

import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';

import { useHaptics } from '@/src/core/libs';
import { Box, Pressable, Text } from '@/src/core/theme';

interface ImageGalleryProps {
  images: string[];
  onRemove: (index: number) => void;
  maxImages?: number;
  editable?: boolean;
}

const AnimatedBox = Animated.createAnimatedComponent(Box);
const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

const { width: screenWidth } = Dimensions.get('window');
const imageSize = (screenWidth - 72) / 3; // Account for padding and gaps

export function ImageGallery({
  images,
  onRemove,
  maxImages = 6,
  editable = true,
}: ImageGalleryProps) {
  const { triggerHaptic } = useHaptics();

  const getImageLayout = (index: number, total: number) => {
    if (total === 1) {
      return { width: screenWidth - 48, height: 200, aspectRatio: 16 / 9 };
    }
    if (total === 2) {
      return { width: (screenWidth - 60) / 2, height: 150, aspectRatio: 4 / 3 };
    }
    if (total === 3 && index === 0) {
      return { width: screenWidth - 48, height: 200, aspectRatio: 16 / 9 };
    }
    if (total === 3 && index > 0) {
      return { width: (screenWidth - 60) / 2, height: 150, aspectRatio: 4 / 3 };
    }
    if (total === 4 && index < 2) {
      return { width: (screenWidth - 60) / 2, height: 150, aspectRatio: 4 / 3 };
    }
    // Default grid layout
    return { width: imageSize, height: imageSize, aspectRatio: 1 };
  };

  const handleRemove = (index: number) => {
    triggerHaptic('impact', 'medium');
    onRemove(index);
  };

  const ImageItem = ({ uri, index, total }: { uri: string; index: number; total: number }) => {
    const scale = useSharedValue(1);
    const opacity = useSharedValue(1);

    const animatedStyle = useAnimatedStyle(() => ({
      transform: [{ scale: scale.value }],
      opacity: opacity.value,
    }));

    const handlePressIn = () => {
      scale.value = withSpring(0.95, { duration: 100 });
    };

    const handlePressOut = () => {
      scale.value = withSpring(1, { duration: 100 });
    };

    const layout = getImageLayout(index, total);

    return (
      <AnimatedBox
        style={[animatedStyle, layout]}
        borderRadius="sm_8"
        overflow="hidden"
        position="relative"
        marginBottom="xs_8"
        marginRight={index % 3 !== 2 ? 'xs_8' : 'none_0'}>
        <Pressable onPressIn={handlePressIn} onPressOut={handlePressOut} style={{ flex: 1 }}>
          <Image
            source={{ uri }}
            style={{
              width: '100%',
              height: '100%',
              backgroundColor: '#f0f0f0',
            }}
            resizeMode="cover"
          />

          {/* Overlay for more than maxImages */}
          {index === maxImages - 1 && total > maxImages && (
            <Box
              position="absolute"
              top={0}
              left={0}
              right={0}
              bottom={0}
              backgroundColor="rgba(0,0,0,0.7)"
              justifyContent="center"
              alignItems="center">
              <Text variant="h_24SemiBold_section" color="textInverted">
                +{total - maxImages + 1}
              </Text>
            </Box>
          )}

          {/* Remove Button */}
          {editable && (
            <AnimatedPressable
              onPress={() => handleRemove(index)}
              style={{
                position: 'absolute',
                top: 8,
                right: 8,
                width: 24,
                height: 24,
                borderRadius: 12,
                backgroundColor: 'rgba(0,0,0,0.8)',
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <Text variant="l_12Medium_message" color="textInverted">
                ×
              </Text>
            </AnimatedPressable>
          )}
        </Pressable>
      </AnimatedBox>
    );
  };

  if (images.length === 0) return null;

  const displayImages = images.slice(0, maxImages);

  return (
    <Box>
      {/* Header */}
      <Box
        flexDirection="row"
        justifyContent="space-between"
        alignItems="center"
        marginBottom="sm_12">
        <Text variant="b_16SemiBold_button" color="text">
          Photos ({images.length})
        </Text>
        {images.length > maxImages && (
          <Text variant="l_12Regular_helperText" color="textSecondary">
            Showing {maxImages} of {images.length}
          </Text>
        )}
      </Box>

      {/* Images Grid */}
      <Box flexDirection="row" flexWrap="wrap">
        {displayImages.map((uri, index) => (
          <ImageItem key={`${uri}-${index}`} uri={uri} index={index} total={displayImages.length} />
        ))}
      </Box>

      {/* Image Limit Warning */}
      {images.length >= maxImages && editable && (
        <Box
          padding="sm_12"
          backgroundColor="warning"
          borderRadius="sm_8"
          marginTop="xs_8"
          opacity={0.1}>
          <Text variant="l_12Medium_message" color="warning" textAlign="center">
            Maximum {maxImages} images allowed
          </Text>
        </Box>
      )}
    </Box>
  );
}
