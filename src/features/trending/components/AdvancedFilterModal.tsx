import React, { useState } from 'react';

import { <PERSON><PERSON>, <PERSON><PERSON>View } from 'react-native';

import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';

import { useHaptics } from '@/src/core/libs';
import { Box, Pressable, Text } from '@/src/core/theme';
import { Button, Chip, SegmentedControl } from '@/src/shared/components';

import type { PostFilter, SortOrder, VenueCategory } from '../types';

interface AdvancedFilterModalProps {
  visible: boolean;
  onClose: () => void;
  onApply: (filters: FilterConfig) => void;
  currentFilters: FilterConfig;
}

export interface FilterConfig {
  filter: PostFilter;
  sortOrder: SortOrder;
  categories: VenueCategory[];
  location?: {
    latitude: number;
    longitude: number;
    radius: number;
  };
  showOnlyFollowing: boolean;
  showOnlyVerified: boolean;
  timeRange: 'all' | 'today' | 'week' | 'month';
}

const AnimatedBox = Animated.createAnimatedComponent(Box);

const FILTER_OPTIONS: { label: string; value: PostFilter }[] = [
  { label: '🔥 Trending', value: 'trending_now' },
  { label: '⏰ Recent', value: 'recent' },
  { label: '⭐ Popular', value: 'popular' },
  { label: '📍 Nearby', value: 'nearby' },
  { label: '👥 Following', value: 'following' },
  { label: '🔖 Saved', value: 'saved' },
];

const SORT_OPTIONS: { label: string; value: SortOrder }[] = [
  { label: 'Trending', value: 'trending' },
  { label: 'Recent', value: 'recent' },
  { label: 'Popular', value: 'popular' },
  { label: 'Nearby', value: 'nearby' },
];

const CATEGORY_OPTIONS: { label: string; value: VenueCategory; emoji: string }[] = [
  { label: 'Restaurant', value: 'restaurant', emoji: '🍽️' },
  { label: 'Bar', value: 'bar', emoji: '🍻' },
  { label: 'Cafe', value: 'cafe', emoji: '☕' },
  { label: 'Club', value: 'nightclub', emoji: '🎉' },
  { label: 'Hotel', value: 'hotel', emoji: '🏨' },
  { label: 'Shop', value: 'shop', emoji: '🛍️' },
  { label: 'Gym', value: 'gym', emoji: '💪' },
  { label: 'Park', value: 'park', emoji: '🌳' },
  { label: 'Museum', value: 'museum', emoji: '🏛️' },
  { label: 'Theater', value: 'theater', emoji: '🎭' },
  { label: 'Hospital', value: 'hospital', emoji: '🏥' },
  { label: 'Other', value: 'other', emoji: '📍' },
];

const TIME_RANGE_OPTIONS = [
  { label: 'All Time', value: 'all' as const },
  { label: 'Today', value: 'today' as const },
  { label: 'This Week', value: 'week' as const },
  { label: 'This Month', value: 'month' as const },
];

export function AdvancedFilterModal({
  visible,
  onClose,
  onApply,
  currentFilters,
}: AdvancedFilterModalProps) {
  const { triggerHaptic } = useHaptics();
  const [filters, setFilters] = useState<FilterConfig>(currentFilters);

  const opacity = useSharedValue(0);
  const scale = useSharedValue(0.9);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
    transform: [{ scale: scale.value }],
  }));

  React.useEffect(() => {
    if (visible) {
      opacity.value = withTiming(1, { duration: 200 });
      scale.value = withSpring(1, { damping: 15, stiffness: 200 });
    } else {
      opacity.value = withTiming(0, { duration: 150 });
      scale.value = withTiming(0.9, { duration: 150 });
    }
  }, [visible, opacity, scale]);

  const handleFilterChange = (key: keyof FilterConfig, value: any) => {
    runOnJS(triggerHaptic)('impact', 'light');
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleCategoryToggle = (category: VenueCategory) => {
    runOnJS(triggerHaptic)('impact', 'light');
    setFilters(prev => ({
      ...prev,
      categories: prev.categories.includes(category)
        ? prev.categories.filter(c => c !== category)
        : [...prev.categories, category],
    }));
  };

  const handleApply = () => {
    runOnJS(triggerHaptic)('impact', 'medium');
    onApply(filters);
    onClose();
  };

  const handleReset = () => {
    runOnJS(triggerHaptic)('impact', 'light');
    setFilters({
      filter: 'trending_now',
      sortOrder: 'trending',
      categories: [],
      showOnlyFollowing: false,
      showOnlyVerified: false,
      timeRange: 'all',
    });
  };

  const hasActiveFilters = () => {
    return (
      filters.categories.length > 0 ||
      filters.showOnlyFollowing ||
      filters.showOnlyVerified ||
      filters.timeRange !== 'all' ||
      filters.filter !== 'trending_now' ||
      filters.sortOrder !== 'trending'
    );
  };

  return (
    <Modal visible={visible} animationType="none" transparent onRequestClose={onClose}>
      <Box
        flex={1}
        backgroundColor="rgba(0,0,0,0.5)"
        justifyContent="center"
        alignItems="center"
        padding="lg_24">
        <AnimatedBox
          style={[animatedStyle]}
          backgroundColor="background"
          borderRadius="lg_16"
          maxHeight="80%"
          width="100%"
          overflow="hidden">
          {/* Header */}
          <Box
            flexDirection="row"
            justifyContent="space-between"
            alignItems="center"
            padding="lg_24"
            borderBottomWidth={1}
            borderBottomColor="border">
            <Text variant="h_20Medium_subsection" color="text">
              Advanced Filters
            </Text>
            <Pressable onPress={onClose}>
              <Text variant="b_16SemiBold_button" color="textSecondary">
                ✕
              </Text>
            </Pressable>
          </Box>

          <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{ padding: 24 }}>
            {/* Main Filter */}
            <Box marginBottom="xl_32">
              <Text variant="h_18SemiBold_cardTitle" color="text" marginBottom="sm_12">
                Main Filter
              </Text>
              <Box flexDirection="row" flexWrap="wrap" gap="xs_8">
                {FILTER_OPTIONS.map(option => (
                  <Chip
                    key={option.value}
                    label={option.label}
                    selected={filters.filter === option.value}
                    onPress={() => handleFilterChange('filter', option.value)}
                  />
                ))}
              </Box>
            </Box>

            {/* Sort Order */}
            <Box marginBottom="xl_32">
              <Text variant="h_18SemiBold_cardTitle" color="text" marginBottom="sm_12">
                Sort Order
              </Text>
              <SegmentedControl
                options={SORT_OPTIONS}
                selectedValue={filters.sortOrder}
                onValueChange={value => handleFilterChange('sortOrder', value)}
              />
            </Box>

            {/* Time Range */}
            <Box marginBottom="xl_32">
              <Text variant="h_18SemiBold_cardTitle" color="text" marginBottom="sm_12">
                Time Range
              </Text>
              <SegmentedControl
                options={TIME_RANGE_OPTIONS}
                selectedValue={filters.timeRange}
                onValueChange={value => handleFilterChange('timeRange', value)}
              />
            </Box>

            {/* Categories */}
            <Box marginBottom="xl_32">
              <Text variant="h_18SemiBold_cardTitle" color="text" marginBottom="sm_12">
                Venue Categories
              </Text>
              <Box flexDirection="row" flexWrap="wrap" gap="xs_8">
                {CATEGORY_OPTIONS.map(category => (
                  <Chip
                    key={category.value}
                    label={`${category.emoji} ${category.label}`}
                    selected={filters.categories.includes(category.value)}
                    onPress={() => handleCategoryToggle(category.value)}
                  />
                ))}
              </Box>
            </Box>

            {/* Additional Options */}
            <Box marginBottom="lg_24">
              <Text variant="h_18SemiBold_cardTitle" color="text" marginBottom="sm_12">
                Additional Options
              </Text>

              <Pressable
                onPress={() => handleFilterChange('showOnlyFollowing', !filters.showOnlyFollowing)}
                marginBottom="md_16">
                <Box flexDirection="row" alignItems="center">
                  <Box
                    width={20}
                    height={20}
                    borderRadius="xxs_2"
                    borderWidth={2}
                    borderColor={filters.showOnlyFollowing ? 'primary' : 'border'}
                    backgroundColor={filters.showOnlyFollowing ? 'primary' : 'transparent'}
                    justifyContent="center"
                    alignItems="center"
                    marginRight="sm_12">
                    {filters.showOnlyFollowing && (
                      <Text variant="l_10SemiBold_tag" color="textInverted">
                        ✓
                      </Text>
                    )}
                  </Box>
                  <Text variant="b_16Regular_input" color="text">
                    Show only posts from people I follow
                  </Text>
                </Box>
              </Pressable>

              <Pressable
                onPress={() => handleFilterChange('showOnlyVerified', !filters.showOnlyVerified)}>
                <Box flexDirection="row" alignItems="center">
                  <Box
                    width={20}
                    height={20}
                    borderRadius="xxs_2"
                    borderWidth={2}
                    borderColor={filters.showOnlyVerified ? 'primary' : 'border'}
                    backgroundColor={filters.showOnlyVerified ? 'primary' : 'transparent'}
                    justifyContent="center"
                    alignItems="center"
                    marginRight="sm_12">
                    {filters.showOnlyVerified && (
                      <Text variant="l_10SemiBold_tag" color="textInverted">
                        ✓
                      </Text>
                    )}
                  </Box>
                  <Text variant="b_16Regular_input" color="text">
                    Show only verified users
                  </Text>
                </Box>
              </Pressable>
            </Box>
          </ScrollView>

          {/* Footer */}
          <Box
            flexDirection="row"
            padding="lg_24"
            borderTopWidth={1}
            borderTopColor="border"
            gap="sm_12">
            <Button
              title="Reset"
              variant="outline"
              onPress={handleReset}
              flex={1}
              disabled={!hasActiveFilters()}
            />
            <Button title="Apply Filters" variant="primary" onPress={handleApply} flex={2} />
          </Box>
        </AnimatedBox>
      </Box>
    </Modal>
  );
}
