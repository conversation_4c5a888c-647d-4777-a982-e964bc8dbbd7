import React from 'react';

import { Pressable, StyleSheet } from 'react-native';

import Image from '@d11/react-native-fast-image';
import { useNavigation } from '@react-navigation/native';
import { CheckCircle } from 'phosphor-react-native';
import { useTranslation } from 'react-i18next';

import { haptics } from '@/src/core/libs';
import { Box, Text, useTheme } from '@/src/core/theme';

import { useTrendingStore } from '../store/trendingStore';
import { TrendingPost } from '../types';
import LocationBadge from './LocationBadge';
import PostInteractions from './PostInteractions';

interface TrendingPostCardProps {
  post: TrendingPost;
  testID?: string;
}

const TrendingPostCard: React.FC<TrendingPostCardProps> = ({ post, testID }) => {
  const theme = useTheme();
  const { t } = useTranslation();
  const navigation = useNavigation();

  const { likePost, unlikePost, savePost, unsavePost, sharePost } = useTrendingStore();

  // Format the post creation date
  const formatDate = (date: Date): string => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    }).format(date);
  };

  const formattedDate = formatDate(new Date(post.createdAt));

  // Navigate to post details
  const handlePostPress = async () => {
    await haptics.light();
    // TODO: Navigate to post details screen when implemented
    console.log(`Navigate to post: ${post.id}`);
  };

  // Navigate to user profile
  const handleUserPress = async () => {
    await haptics.light();
    // TODO: Navigate to user profile screen when implemented
    console.log(`Navigate to profile: ${post.user.username}`);
  };

  // Navigate to venue details
  const handleVenuePress = async () => {
    await haptics.light();
    // TODO: Navigate to venue details screen when implemented
    console.log(`Navigate to venue: ${post.venue.id}`);
  };

  // Handle post interactions
  const handleLikePress = async () => {
    await haptics.light();
    post.isLiked ? unlikePost(post.id) : likePost(post.id);
  };

  const handleSavePress = async () => {
    await haptics.light();
    post.isSaved ? unsavePost(post.id) : savePost(post.id);
  };

  const handleSharePress = async () => {
    await haptics.medium();
    sharePost(post.id);
  };

  const handleCommentPress = async () => {
    await haptics.light();
    // TODO: Navigate to post comments when implemented
    console.log(`Navigate to comments for post: ${post.id}`);
  };

  return (
    <Pressable
      onPress={handlePostPress}
      testID={testID}
      style={({ pressed }) => [styles.container, { opacity: pressed ? 0.9 : 1 }]}>
      <Box
        padding="md_16"
        borderRadius="lg_16"
        backgroundColor="cardBackground"
        marginBottom="sm_12"
        style={styles.cardShadow}>
        {/* User info and date */}
        <Box flexDirection="row" alignItems="center" marginBottom="sm_12">
          <Pressable onPress={handleUserPress}>
            <Image
              source={{ uri: post.user.avatar }}
              style={styles.userAvatar}
              resizeMode="cover"
            />
          </Pressable>

          <Box flex={1} marginLeft="sm_12">
            <Pressable onPress={handleUserPress}>
              <Box flexDirection="row" alignItems="center">
                <Text variant="h_16Medium_formLabel">{post.user.displayName}</Text>
                {post.user.isVerified && (
                  <Box marginLeft="xs_8">
                    <CheckCircle />
                  </Box>
                )}
              </Box>

              <Text variant="l_12Regular_helperText" color="secondaryText">
                @{post.user.username} • {formattedDate}
              </Text>
            </Pressable>
          </Box>
        </Box>

        {/* Post content */}
        <Box marginBottom="sm_12">
          <Text variant="b_14Regular_content">{post.content}</Text>
        </Box>

        {/* Post images if available */}
        {post.images && post.images.length > 0 && (
          <Box height={200} borderRadius="md_12" overflow="hidden" marginBottom="sm_12">
            <Image
              source={{ uri: post.images[0] }}
              style={styles.postImage}
              contentFit="cover"
              transition={300}
            />
            {post.images.length > 1 && (
              <Box
                position="absolute"
                bottom={8}
                right={8}
                paddingHorizontal="sm_12"
                paddingVertical="xs_8"
                borderRadius="sm_8"
                backgroundColor="modalOverlayBackground">
                <Text variant="l_12Regular_helperText" color="white">
                  +{post.images.length - 1}
                </Text>
              </Box>
            )}
          </Box>
        )}

        {/* Venue information */}
        <Pressable onPress={handleVenuePress}>
          <LocationBadge venue={post.venue} />
        </Pressable>

        {/* Post interactions */}
        <PostInteractions
          post={post}
          onLikePress={handleLikePress}
          onCommentPress={handleCommentPress}
          onSharePress={handleSharePress}
          onSavePress={handleSavePress}
        />
      </Box>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  userAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  verifiedIcon: {
    width: 16,
    height: 16,
  },
  postImage: {
    flex: 1,
    width: '100%',
  },
  cardShadow: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
});

export default TrendingPostCard;
