import React from 'react';

import { <PERSON>, CheckCircle, Clock, MapPin, Star } from 'phosphor-react-native';

import { Box, Pressable, Text, useTheme } from '@/src/core/theme';
import { Avatar, Card, CardToolbar, Divider } from '@/src/shared/components';

interface HostEvent {
  name: string;
  time: string;
  venue: string;
}

interface Host {
  id: string;
  name: string;
  avatar?: string;
  rating: number;
  upcomingEvents: number;
  nextEvent?: HostEvent;
  isVerified?: boolean;
}

interface HostActivityCardProps {
  hosts: Host[];
  onViewHost?: (hostId: string) => void;
  onViewEvent?: (eventId: string) => void;
  isExpanded?: boolean;
  onToggleExpand?: () => void;
}

const HostItem = ({
  host,
  onViewHost,
  onViewEvent,
  showDivider = true,
}: {
  host: Host;
  onViewHost?: () => void;
  onViewEvent?: () => void;
  showDivider?: boolean;
}) => {
  const theme = useTheme();

  return (
    <>
      <Box gap="sm_12">
        {/* Host Info */}
        <Pressable onPress={onViewHost}>
          <Box flexDirection="row" gap="sm_12" alignItems="center">
            <Avatar
              size="m"
              source={host.avatar ? { uri: host.avatar } : undefined}
              fallbackText={host.name}
            />

            <Box flex={1} gap="xxs_4">
              <Box flexDirection="row" alignItems="center" gap="xs_8">
                <Text variant="h_16SemiBold_alertTitle" color="mainText">
                  {host.name}
                </Text>
                {host.isVerified && <CheckCircle size={16} color={theme.colors.brandMain} />}
              </Box>

              <Box flexDirection="row" gap="md_16">
                <Box flexDirection="row" alignItems="center" gap="xxs_4">
                  <Star size={14} color={theme.colors.iconWarning} />
                  <Text variant="l_12SemiBold_button" color="secondaryText">
                    {host.rating}
                  </Text>
                </Box>

                <Box flexDirection="row" alignItems="center" gap="xxs_4">
                  <Calendar size={14} color={theme.colors.iconAccent} />
                  <Text variant="l_12Regular_helperText" color="mutedText">
                    {host.upcomingEvents} upcoming
                  </Text>
                </Box>
              </Box>
            </Box>
          </Box>
        </Pressable>

        {/* Next Event */}
        {host.nextEvent && (
          <Pressable onPress={onViewEvent}>
            <Box backgroundColor="subtleBackground" borderRadius="md_12" padding="sm_12" gap="xs_8">
              <Text variant="l_10SemiBold_chip" color="mutedText">
                NEXT EVENT
              </Text>

              <Text variant="l_14SemiBold_action" color="mainText">
                {host.nextEvent.name}
              </Text>

              <Box flexDirection="row" gap="md_16">
                <Box flexDirection="row" alignItems="center" gap="xxs_4">
                  <Clock size={12} color={theme.colors.iconMuted} />
                  <Text variant="l_12Regular_helperText" color="mutedText">
                    {host.nextEvent.time}
                  </Text>
                </Box>

                <Box flexDirection="row" alignItems="center" gap="xxs_4">
                  <MapPin size={12} color={theme.colors.iconDefault} />
                  <Text variant="l_12Regular_helperText" color="secondaryText">
                    {host.nextEvent.venue}
                  </Text>
                </Box>
              </Box>
            </Box>
          </Pressable>
        )}
      </Box>

      {showDivider && <Divider marginVertical="sm_12" />}
    </>
  );
};

export function HostActivityCard({
  hosts,
  onViewHost,
  onViewEvent,
  isExpanded = false,
  onToggleExpand,
}: HostActivityCardProps) {
  const displayHosts = isExpanded ? hosts : hosts.slice(0, 2);

  return (
    <Card>
      <CardToolbar title="Top Hosts" actionText="This week" onPress={onToggleExpand} />

      <Box padding="md_16" gap="none_0">
        {displayHosts.map((host, index) => (
          <HostItem
            key={host.id}
            host={host}
            onViewHost={() => onViewHost?.(host.id)}
            onViewEvent={() => onViewEvent?.(host.id)}
            showDivider={index < displayHosts.length - 1}
          />
        ))}

        {!isExpanded && hosts.length > 2 && (
          <>
            <Divider marginVertical="sm_12" />
            <Pressable onPress={onToggleExpand} alignItems="center" paddingVertical="xs_8">
              <Text variant="l_14SemiBold_action" color="brandMain">
                View {hosts.length - 2} more hosts
              </Text>
            </Pressable>
          </>
        )}
      </Box>
    </Card>
  );
}
