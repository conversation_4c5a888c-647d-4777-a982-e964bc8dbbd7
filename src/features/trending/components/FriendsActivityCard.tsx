import React from 'react';

import {
  BookmarkSimple,
  Clock,
  Door,
  MapPin,
  ShareNetwork,
  SketchLogo,
} from 'phosphor-react-native';

import { Box, Pressable, Text, useTheme } from '@/src/core/theme';
import { Avatar, Card, CardToolbar, Divider } from '@/src/shared/components';
import { Pill } from '@/src/shared/components/Pill';

import {
  EventInfo,
  FriendActivity,
  FriendsActivityCardProps,
  VenueInfo,
} from '../types/friendsActivity';

const VenueDetail = ({ venue, onShare }: { venue: VenueInfo; onShare?: () => void }) => {
  const theme = useTheme();

  return (
    <Box
      flexDirection="row"
      backgroundColor="subtleBackground"
      borderRadius="lg_16"
      padding="sm_12"
      alignItems="center"
      justifyContent="space-between"
      gap="xs_8"
      borderWidth={1}
      borderColor="mainBorder">
      <Box gap="xxs_4">
        <Text variant="h_16SemiBold_alertTitle" color="mainText">
          {venue.name}
        </Text>

        <Box flexDirection="row" alignItems="center" gap="xxs_4">
          <Box flexDirection="row" alignItems="center" gap="xxs_4">
            <MapPin size={14} color={theme.colors.iconMuted} />
            <Text variant="l_10SemiBold_chip" color="secondaryText">
              {venue.location}
            </Text>
          </Box>
          <Pill
            text={venue.distance}
            variant="compact"
            backgroundColor="alertSuccessBackground"
            textColor="alertSuccessText"
            hideIcon
          />
        </Box>
      </Box>

      {(venue.openTime || venue.closeTime) && (
        <Box gap="xs_8">
          {venue.openTime && (
            <Box flexDirection="row" alignItems="center" gap="xxs_4">
              <Clock size={14} color={theme.colors.successMain} />
              <Text variant="l_10SemiBold_chip" color="successMain">
                {venue.openTime}
              </Text>
            </Box>
          )}
          {venue.closeTime && (
            <Box flexDirection="row" alignItems="center" gap="xxs_4">
              <Door size={14} color={theme.colors.secondaryMain} weight="fill" />
              <Text variant="l_10SemiBold_chip" color="secondaryMain">
                {venue.closeTime}
              </Text>
            </Box>
          )}
        </Box>
      )}
    </Box>
  );
};

const EventDetail = ({ event }: { event: EventInfo }) => {
  const theme = useTheme();
  const isPrivate = event.type === 'private';

  return (
    <Box
      backgroundColor="subtleBackground"
      borderRadius="lg_16"
      borderWidth={1}
      borderColor="mainBorder">
      <Box padding="sm_12" gap="xs_8" flexDirection="row" justifyContent="space-between">
        <Box>
          <Box flexDirection="row" justifyContent="space-between" alignItems="center">
            <Text variant="h_16SemiBold_alertTitle" color="mainText">
              {event.name}
            </Text>
            {isPrivate && (
              <Box
                backgroundColor="buttonFilledBackground"
                paddingHorizontal="xs_8"
                paddingVertical="xxxs_2"
                borderRadius="xs_4"
                flexDirection="row"
                alignItems="center"
                gap="xxs_4">
                <SketchLogo size={12} color={theme.colors.inverseText} />
                <Text variant="l_10SemiBold_chip" color="inverseText">
                  Invite only
                </Text>
              </Box>
            )}
          </Box>

          <Box flexDirection="row" alignItems="center" gap="xs_8">
            <Box flexDirection="row" alignItems="center" gap="xxs_4">
              <MapPin size={14} color={theme.colors.iconMuted} />
              <Text variant="l_10SemiBold_chip" color="secondaryText">
                {event.location}
              </Text>
            </Box>
            <Box flexDirection="row" alignItems="center" gap="xs_8">
              <Pill
                text={event.distance}
                variant="small"
                backgroundColor="alertSuccessBackground"
                textColor="alertSuccessText"
                hideIcon
                style={{ paddingVertical: 0 }}
              />
            </Box>
          </Box>
        </Box>

        {event.time && (
          <Box flexDirection="row" alignItems="center" gap="xxs_4">
            <Clock size={14} color={theme.colors.iconAccent} />
            <Text variant="l_10SemiBold_chip" color="accentMain">
              {event.time}
            </Text>
          </Box>
        )}
      </Box>
    </Box>
  );
};

const ActivityItem = ({
  activity,
  onShareVenue,
  onBookmarkEvent,
  onViewEvent,
  showDivider = true,
}: {
  activity: FriendActivity;
  onShareVenue?: (venueId: string) => void;
  onBookmarkEvent?: (eventId: string) => void;
  onViewEvent?: (eventId: string) => void;
  showDivider?: boolean;
}) => {
  const theme = useTheme();
  return (
    <>
      <Box flexDirection="row" gap="sm_12">
        <Avatar size="xs" fallbackText={activity.user.initials} />

        <Box flex={1} gap="xs_8">
          <Box gap="xxs_4">
            <Box flexDirection="row" alignItems="center" gap="xs_8" justifyContent="space-between">
              <Text variant="l_14SemiBold_action" color="mainText">
                {activity.user.name}
              </Text>
              <Text variant="l_12Regular_helperText" color="mutedText">
                {activity.timestamp}
              </Text>
            </Box>
            <Text variant="b_14Regular_content" color="mainText">
              {activity.message}
            </Text>
          </Box>

          {activity.venue && (
            <VenueDetail
              venue={activity.venue}
              onShare={() => onShareVenue?.(activity.venue!.id)}
            />
          )}

          {activity.event && <EventDetail event={activity.event} />}

          <Divider />

          <Box
            flexDirection="row"
            justifyContent="space-between"
            alignItems="center"
            padding="sm_12">
            <Box flexDirection="row" gap="sm_12">
              {activity.venue && (
                <Pressable onPress={() => onShareVenue?.(activity.venue!.id)}>
                  <ShareNetwork size={20} color={theme.colors.iconPrimary} />
                </Pressable>
              )}
              {activity.event && (
                <Pressable onPress={() => onBookmarkEvent?.(activity.event!.id)}>
                  <BookmarkSimple size={20} color={theme.colors.iconPrimary} />
                </Pressable>
              )}
            </Box>

            <Pressable
              onPress={() => onViewEvent?.(activity.event!.id)}
              backgroundColor="buttonFilledBackground"
              paddingHorizontal="sm_12"
              paddingVertical="xs_8"
              borderRadius="sm_8">
              <Text variant="l_12SemiBold_button" color="buttonFilledText">
                View more
              </Text>
            </Pressable>
          </Box>
        </Box>
      </Box>

      {showDivider && <Divider marginVertical="sm_12" />}
    </>
  );
};

export function FriendsActivityCard({
  activities,
  onViewMore,
  onShareVenue,
  onBookmarkEvent,
  onViewEvent,
  isExpanded = false,
  onToggleExpand,
}: FriendsActivityCardProps) {
  const displayActivities = isExpanded ? activities : activities.slice(0, 2);

  return (
    <Card
      px="none_0"
      showToolbar
      toolbarActionText="All"
      toolbarTitle="Friends activity"
      onToolbarPress={onToggleExpand}>
      <Box padding="sm_12" gap="none_0">
        {displayActivities.map((activity, index) => (
          <ActivityItem
            key={activity.id}
            activity={activity}
            onShareVenue={onShareVenue}
            onBookmarkEvent={onBookmarkEvent}
            onViewEvent={onViewEvent}
            showDivider={index < displayActivities.length - 1}
          />
        ))}

        {!isExpanded && activities.length > 2 && onViewMore && (
          <>
            <Divider marginVertical="sm_12" />
            <Pressable onPress={onViewMore} alignItems="center" paddingVertical="xs_8">
              <Text variant="l_14SemiBold_action" color="brandMain">
                View {activities.length - 2} more activities
              </Text>
            </Pressable>
          </>
        )}
      </Box>
    </Card>
  );
}

export default FriendsActivityCard;
