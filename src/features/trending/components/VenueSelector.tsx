import React, { useEffect, useState } from 'react';

import { FlatList, Modal } from 'react-native';

import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';

import { useHaptics } from '@/src/core/libs';
import { Box, Pressable, Text, TextInput } from '@/src/core/theme';

import type { Venue } from '../types';
import { LocationBadge } from './LocationBadge';

interface VenueSelectorProps {
  selectedVenue: Venue | null;
  onVenueSelect: (venue: Venue | null) => void;
}

const AnimatedBox = Animated.createAnimatedComponent(Box);

// Mock venue data - replace with actual API call
const mockVenues: Venue[] = [
  {
    id: '1',
    name: 'The Coffee House',
    category: 'cafe',
    address: '123 Main St, Downtown',
    city: 'San Francisco',
    country: 'United States',
    coordinates: { latitude: 37.7749, longitude: -122.4194 },
    rating: 4.5,
    isOpen: true,
    distance: 0.2,
  },
  {
    id: '2',
    name: 'Sunset Grill',
    category: 'restaurant',
    address: '456 Ocean Ave, Sunset District',
    city: 'San Francisco',
    country: 'United States',
    coordinates: { latitude: 37.7649, longitude: -122.4794 },
    rating: 4.2,
    isOpen: true,
    distance: 0.8,
  },
  {
    id: '3',
    name: 'The Night Club',
    category: 'nightclub',
    address: '789 Party St, SOMA',
    city: 'San Francisco',
    country: 'United States',
    coordinates: { latitude: 37.7849, longitude: -122.4094 },
    rating: 4.0,
    isOpen: false,
    distance: 1.2,
  },
];

export function VenueSelector({ selectedVenue, onVenueSelect }: VenueSelectorProps) {
  const { triggerHaptic } = useHaptics();
  const [showModal, setShowModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredVenues, setFilteredVenues] = useState<Venue[]>([]);

  const scale = useSharedValue(1);
  const opacity = useSharedValue(0);

  const buttonAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const modalAnimatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
  }));

  useEffect(() => {
    if (showModal) {
      opacity.value = withTiming(1, { duration: 200 });
    } else {
      opacity.value = withTiming(0, { duration: 200 });
    }
  }, [showModal, opacity]);

  useEffect(() => {
    const filtered = mockVenues.filter(
      venue =>
        venue.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        venue.address.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredVenues(filtered);
  }, [searchQuery]);

  const handleOpenModal = () => {
    scale.value = withSpring(0.95, { duration: 100 }, () => {
      scale.value = withSpring(1, { duration: 100 });
    });
    runOnJS(triggerHaptic)('impact', 'light');
    setShowModal(true);
    setFilteredVenues(mockVenues);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setSearchQuery('');
  };

  const handleVenueSelect = (venue: Venue) => {
    runOnJS(triggerHaptic)('impact', 'medium');
    onVenueSelect(venue);
    handleCloseModal();
  };

  const handleRemoveVenue = () => {
    runOnJS(triggerHaptic)('impact', 'light');
    onVenueSelect(null);
  };

  const renderVenueItem = ({ item }: { item: Venue }) => (
    <Pressable onPress={() => handleVenueSelect(item)}>
      <Box
        padding="md_16"
        borderBottomWidth={1}
        borderBottomColor="border"
        backgroundColor="background">
        <LocationBadge venue={item} showDistance />
      </Box>
    </Pressable>
  );

  return (
    <Box>
      {selectedVenue ? (
        <Box
          padding="md_16"
          backgroundColor="surface"
          borderRadius="md_12"
          borderWidth={1}
          borderColor="border">
          <Box flexDirection="row" justifyContent="space-between" alignItems="center">
            <Box flex={1}>
              <LocationBadge venue={selectedVenue} />
            </Box>
            <Pressable onPress={handleRemoveVenue} padding="xs_8">
              <Text variant="l_12Medium_message" color="error">
                Remove
              </Text>
            </Pressable>
          </Box>
        </Box>
      ) : (
        <Animated.View style={buttonAnimatedStyle}>
          <Pressable onPress={handleOpenModal}>
            <Box
              padding="md_16"
              backgroundColor="surface"
              borderRadius="md_12"
              borderWidth={1}
              borderColor="border"
              borderStyle="dashed"
              alignItems="center">
              <Text variant="b_14Medium_button" color="textSecondary">
                📍 Add Location
              </Text>
              <Text variant="l_12Regular_helperText" color="textTertiary" marginTop="xxs_4">
                Tag a venue to let people know where you are
              </Text>
            </Box>
          </Pressable>
        </Animated.View>
      )}

      {/* Venue Selection Modal */}
      <Modal
        visible={showModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={handleCloseModal}>
        <AnimatedBox style={[{ flex: 1 }, modalAnimatedStyle]} backgroundColor="background">
          {/* Header */}
          <Box
            flexDirection="row"
            justifyContent="space-between"
            alignItems="center"
            padding="lg_24"
            borderBottomWidth={1}
            borderBottomColor="border">
            <Text variant="h_20Medium_subsection" color="text">
              Choose Location
            </Text>
            <Pressable onPress={handleCloseModal}>
              <Text variant="b_16SemiBold_button" color="primary">
                Cancel
              </Text>
            </Pressable>
          </Box>

          {/* Search */}
          <Box padding="lg_24">
            <TextInput
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholder="Search venues..."
              autoFocus
            />
          </Box>

          {/* Venues List */}
          <FlatList
            data={filteredVenues}
            renderItem={renderVenueItem}
            keyExtractor={item => item.id}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={
              <Box padding="xl_32" alignItems="center">
                <Text variant="b_16Regular_input" color="textSecondary" textAlign="center">
                  {searchQuery ? 'No venues found' : 'Loading nearby venues...'}
                </Text>
              </Box>
            }
          />
        </AnimatedBox>
      </Modal>
    </Box>
  );
}
