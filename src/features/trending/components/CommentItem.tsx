import React from 'react';

import { Pressable } from 'react-native';

import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from 'react-native-reanimated';

import { useHaptics } from '@/src/core/libs';
import { Box, Text, Pressable as ThemedPressable } from '@/src/core/theme';
import { Avatar } from '@/src/shared/components';
import { formatTimeAgo } from '@/src/shared/utils';

import type { PostComment } from '../types';

interface CommentItemProps {
  comment: PostComment;
  onReply: () => void;
  onLike: () => void;
  depth?: number;
  maxDepth?: number;
}

const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

export function CommentItem({
  comment,
  onReply,
  onLike,
  depth = 0,
  maxDepth = 3,
}: CommentItemProps) {
  const { triggerHaptic } = useHaptics();
  const scale = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const handleLikePress = () => {
    scale.value = withSpring(0.95, { duration: 100 }, () => {
      scale.value = withSpring(1, { duration: 100 });
    });
    runOnJS(triggerHaptic)('impact', 'light');
    onLike();
  };

  const handleReplyPress = () => {
    runOnJS(triggerHaptic)('impact', 'medium');
    onReply();
  };

  return (
    <Box marginBottom="md_16">
      <Box
        flexDirection="row"
        paddingLeft={depth > 0 ? `${depth * 16}px` : 'none_0'}
        marginLeft={depth > 0 ? 'sm_12' : 'none_0'}
        borderLeftWidth={depth > 0 ? 2 : 0}
        borderLeftColor={depth > 0 ? 'border' : undefined}>
        {/* Avatar */}
        <Avatar size="s" source={{ uri: comment.author.avatar }} marginRight="sm_12" />

        {/* Comment Content */}
        <Box flex={1}>
          {/* Author & Time */}
          <Box flexDirection="row" alignItems="center" marginBottom="xxs_4">
            <Text variant="b_14Medium_button" color="text">
              {comment.author.name}
            </Text>
            {comment.author.isVerified && (
              <Text variant="l_12Medium_message" color="primary" marginLeft="xxs_4">
                ✓
              </Text>
            )}
            <Text variant="l_12Regular_helperText" color="textTertiary" marginLeft="xs_8">
              {formatTimeAgo(comment.createdAt)}
            </Text>
          </Box>

          {/* Comment Text */}
          <Text variant="b_14Regular_content" color="text" marginBottom="xs_8">
            {comment.content}
          </Text>

          {/* Actions */}
          <Box flexDirection="row" alignItems="center" gap="md_16">
            {/* Like Button */}
            <AnimatedPressable onPress={handleLikePress} style={animatedStyle}>
              <Box flexDirection="row" alignItems="center">
                <Text
                  variant="l_12Regular_helperText"
                  color={comment.isLiked ? 'error' : 'textSecondary'}>
                  {comment.isLiked ? '❤️' : '🤍'}
                </Text>
                {comment.likesCount > 0 && (
                  <Text variant="l_12Regular_helperText" color="textSecondary" marginLeft="xxs_4">
                    {comment.likesCount}
                  </Text>
                )}
              </Box>
            </AnimatedPressable>

            {/* Reply Button */}
            {depth < maxDepth && (
              <ThemedPressable onPress={handleReplyPress}>
                <Text variant="l_12Medium_message" color="textSecondary">
                  Reply
                </Text>
              </ThemedPressable>
            )}

            {/* Report Button */}
            <ThemedPressable
              onPress={() => {
                /* TODO: Implement report */
              }}>
              <Text variant="l_12Regular_helperText" color="textTertiary">
                Report
              </Text>
            </ThemedPressable>
          </Box>

          {/* Nested Replies */}
          {comment.replies && comment.replies.length > 0 && depth < maxDepth && (
            <Box marginTop="md_16">
              {comment.replies.map(reply => (
                <CommentItem
                  key={reply.id}
                  comment={reply}
                  onReply={onReply}
                  onLike={onLike}
                  depth={depth + 1}
                  maxDepth={maxDepth}
                />
              ))}
            </Box>
          )}

          {/* Show More Replies Link */}
          {comment.replies && comment.replies.length > 3 && depth < maxDepth && (
            <ThemedPressable marginTop="xs_8">
              <Text variant="l_12Medium_message" color="primary">
                View {comment.replies.length - 3} more replies
              </Text>
            </ThemedPressable>
          )}
        </Box>
      </Box>
    </Box>
  );
}
