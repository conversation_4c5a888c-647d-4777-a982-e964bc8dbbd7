import { gql } from '@apollo/client';

/**
 * Fragment for consistent trending post fields across queries
 */
export const TRENDING_POST_FRAGMENT = gql`
  fragment TrendingPostFields on Post {
    id
    content
    createdAt
    mediaUrls
    likesCount
    commentsCount
    sharesCount
    userLiked
    isEdited
    user {
      id
      name
      avatar
      isEmailVerified
      isPhoneVerified
    }
    event {
      id
      name
      title
      description
      address
      city
      type
      eventType
      status
      visibility
      startDate
      endDate
      isFree
      attendeeCount
      location {
        lat
        lng
      }
    }
    tags
    location {
      id
      name
      latitude
      longitude
    }
  }
`;

/**
 * Query to fetch trending posts with pagination
 */
export const GET_TRENDING_POSTS = gql`
  query GetTrendingPosts(
    $latitude: Float
    $longitude: Float
    $radius: Int
    $cursor: String
    $limit: Int
  ) {
    trendingFeed(
      latitude: $latitude
      longitude: $longitude
      radius: $radius
      cursor: $cursor
      limit: $limit
    ) {
      items {
        id
        type
        trendingScore
        engagementCount
        contextualNote
      }
      cursor
      hasMore
    }
  }
`;

/**
 * Query to fetch a single trending post by ID
 */
export const GET_TRENDING_POST_BY_ID = gql`
  query GetTrendingPostById($id: ID!) {
    post(id: $id) {
      ...TrendingPostFields
      comments {
        id
        content
        createdAt
        user {
          id
          name
          avatar
        }
        likesCount
        userLiked
      }
    }
  }
  ${TRENDING_POST_FRAGMENT}
`;

/**
 * Query to fetch trending venues and parties
 */
export const GET_TRENDING_DATA = gql`
  query GetTrendingData(
    $latitude: Float!
    $longitude: Float!
    $radius: Float
    $limit: Int
    $timeframe: String!
  ) {
    trendingVenues(latitude: $latitude, longitude: $longitude, radius: $radius, limit: $limit) {
      id
      name
      description
      address
      city
      state
      country
      latitude
      longitude
      rating
      reviewCount
      venueType {
        id
        displayName
      }
    }
    trendingParties(timeframe: $timeframe, limit: $limit) {
      id
      name
      title
      description
      type
      eventType
      status
      startDate
      endDate
      attendeeCount
      isFree
      address
      city
      location {
        lat
        lng
      }
    }
  }
`;

/**
 * Query to fetch posts for a specific party/event
 */
export const GET_PARTY_POSTS = gql`
  query GetPartyPosts($partyId: UUID!) {
    party(id: $partyId) {
      posts {
        id
        content
        createdAt
        mediaUrls
        likesCount
        commentsCount
        sharesCount
        userLiked
        user {
          id
          name
          avatar
        }
      }
    }
  }
`;
