import { gql } from '@apollo/client';

import { TRENDING_POST_FRAGMENT } from './queries';

/**
 * Mutation to like a trending post
 */
export const LIKE_POST = gql`
  mutation LikePost($postId: ID!) {
    likePost(postId: $postId) {
      ...TrendingPostFields
    }
  }
  ${TRENDING_POST_FRAGMENT}
`;

/**
 * Mutation to unlike a trending post
 */
export const UNLIKE_POST = gql`
  mutation UnlikePost($postId: ID!) {
    unlikePost(postId: $postId) {
      ...TrendingPostFields
    }
  }
  ${TRENDING_POST_FRAGMENT}
`;

/**
 * Mutation to bookmark a post
 */
export const BOOKMARK_POST = gql`
  mutation BookmarkPost($postId: ID!) {
    bookmarkPost(postId: $postId) {
      ...TrendingPostFields
    }
  }
  ${TRENDING_POST_FRAGMENT}
`;

/**
 * Mutation to unbookmark a post
 */
export const UNBOOKMARK_POST = gql`
  mutation UnbookmarkPost($postId: ID!) {
    unbookmarkPost(postId: $postId) {
      ...TrendingPostFields
    }
  }
  ${TRENDING_POST_FRAGMENT}
`;

/**
 * Mutation to share a trending post
 */
export const SHARE_POST = gql`
  mutation SharePost($postId: ID!) {
    sharePost(postId: $postId) {
      ...TrendingPostFields
    }
  }
  ${TRENDING_POST_FRAGMENT}
`;

/**
 * Mutation to add a comment to a trending post
 */
export const ADD_COMMENT = gql`
  mutation AddComment($postId: ID!, $content: String!) {
    addComment(postId: $postId, content: $content) {
      id
      content
      createdAt
      user {
        id
        name
        avatar
      }
      likesCount
      userLiked
    }
  }
`;

/**
 * Mutation to like a comment
 */
export const LIKE_COMMENT = gql`
  mutation LikeComment($commentId: ID!) {
    likeComment(commentId: $commentId) {
      id
      likesCount
      userLiked
    }
  }
`;

/**
 * Mutation to unlike a comment
 */
export const UNLIKE_COMMENT = gql`
  mutation UnlikeComment($commentId: ID!) {
    unlikeComment(commentId: $commentId) {
      id
      likesCount
      userLiked
    }
  }
`;

/**
 * Mutation to report a post for inappropriate content
 */
export const REPORT_POST = gql`
  mutation ReportPost($postId: ID!, $reason: String!, $details: String) {
    reportPost(postId: $postId, reason: $reason, details: $details) {
      success
      message
    }
  }
`;

/**
 * Mutation to follow a user from a trending post
 */
export const FOLLOW_USER = gql`
  mutation FollowUser($userId: ID!) {
    followUser(userId: $userId) {
      id
      name
      followersCount
      followingCount
    }
  }
`;

/**
 * Mutation to unfollow a user from a trending post
 */
export const UNFOLLOW_USER = gql`
  mutation UnfollowUser($userId: ID!) {
    unfollowUser(userId: $userId) {
      id
      name
      followersCount
      followingCount
    }
  }
`;

/**
 * Mutation to support a venue mentioned in a trending post
 */
export const SUPPORT_VENUE = gql`
  mutation SupportVenue($venueId: ID!) {
    supportVenue(venueId: $venueId) {
      id
      name
      supportCount
      isSupported
    }
  }
`;

/**
 * Mutation to unsupport a venue mentioned in a trending post
 */
export const UNSUPPORT_VENUE = gql`
  mutation UnsupportVenue($venueId: ID!) {
    unsupportVenue(venueId: $venueId) {
      id
      name
      supportCount
      isSupported
    }
  }
`;
