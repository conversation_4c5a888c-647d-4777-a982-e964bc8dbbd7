import { gql } from '@apollo/client';

import { TRENDING_POST_FRAGMENT } from './queries';

/**
 * Subscription for new trending posts
 */
export const NEW_TRENDING_POST_SUBSCRIPTION = gql`
  subscription OnNewTrendingPost($categories: [String], $venueIds: [ID]) {
    newTrendingPost(categories: $categories, venueIds: $venueIds) {
      ...TrendingPostFields
    }
  }
  ${TRENDING_POST_FRAGMENT}
`;

/**
 * Subscription for trending post updates (likes, comments, shares)
 */
export const TRENDING_POST_UPDATED_SUBSCRIPTION = gql`
  subscription OnTrendingPostUpdated($postId: ID!) {
    trendingPostUpdated(postId: $postId) {
      id
      likesCount
      commentsCount
      sharesCount
      savesCount
      isLiked
      isSaved
    }
  }
`;

/**
 * Subscription for new comments on a trending post
 */
export const NEW_COMMENT_SUBSCRIPTION = gql`
  subscription OnNewComment($postId: ID!) {
    newComment(postId: $postId) {
      id
      content
      createdAt
      user {
        id
        username
        displayName
        avatar
      }
      likesCount
      isLiked
    }
  }
`;

/**
 * Subscription for trending venue updates
 */
export const VENUE_UPDATES_SUBSCRIPTION = gql`
  subscription OnVenueUpdates($venueId: ID!) {
    venueUpdates(venueId: $venueId) {
      id
      name
      isOpen
      rating
      supportCount
      isSupported
    }
  }
`;

/**
 * Subscription for trending notifications
 */
export const TRENDING_NOTIFICATIONS_SUBSCRIPTION = gql`
  subscription OnTrendingNotifications {
    trendingNotifications {
      id
      type
      read
      createdAt
      actor {
        id
        username
        displayName
        avatar
      }
      post {
        id
        content
      }
      comment {
        id
        content
      }
    }
  }
`;
