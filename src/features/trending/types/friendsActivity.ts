interface VenueInfo {
  id: string;
  name: string;
  location: string;
  distance: string;
  openTime?: string;
  closeTime?: string;
}

interface EventInfo {
  id: string;
  name: string;
  location: string;
  distance: string;
  time: string;
  type: 'public' | 'private';
}

interface FriendActivity {
  id: string;
  user: {
    id: string;
    name: string;
    avatar?: string;
    initials: string;
  };
  timestamp: string;
  message: string;
  venue?: VenueInfo;
  event?: EventInfo;
}

interface FriendsActivityCardProps {
  activities: FriendActivity[];
  onViewMore?: () => void;
  onShareVenue?: (venueId: string) => void;
  onBookmarkEvent?: (eventId: string) => void;
  onViewEvent?: (eventId: string) => void;
  isExpanded?: boolean;
  onToggleExpand?: () => void;
}

export type { FriendActivity, FriendsActivityCardProps };

export type { VenueInfo, EventInfo };
