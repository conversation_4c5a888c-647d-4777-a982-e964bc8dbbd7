/**
 * Trending feature types and interfaces
 * Defines the data structures for posts, venues, and interactions
 */

export interface User {
  id: string;
  name: string;
  avatar?: string;
  isEmailVerified?: boolean;
  isPhoneVerified?: boolean;
}

export interface Venue {
  id: string;
  name: string;
  description?: string;
  address: string;
  city?: string;
  state?: string;
  country?: string;
  latitude?: number;
  longitude?: number;
  rating: number;
  reviewCount: number;
  venueType?: {
    id: string;
    name: string;
  };
}

export interface TrendingPost {
  id: string;
  user: User;
  content: string;
  type: string;
  event?: Party;
  mediaUrls?: string[];
  createdAt: string;
  isEdited?: boolean;

  // Interaction metrics
  likesCount: number;
  commentsCount: number;
  sharesCount: number;

  // User interaction state
  userLiked: boolean;
  userBookmarked?: boolean;
  isReported?: boolean;

  // Tags and location
  tags?: string[];
  location?: {
    id: string;
    name: string;
    latitude: number;
    longitude: number;
  };

  // Moderation
  isModerated?: boolean;
  moderationReason?: string;
}

// TrendingItem from the actual API
export interface TrendingItem {
  id: string;
  type: 'EVENT' | 'VENUE' | 'PERSON' | 'GROUP' | 'MOMENT';
  trendingScore: number;
  engagementCount: number;
  contextualNote?: string;
}

export interface Party {
  id: string;
  name: string;
  title?: string;
  description?: string;
  type: string;
  eventType: string;
  status: string;
  visibility: string;
  startDate: string;
  endDate: string;
  attendeeCount: number;
  isFree: boolean;
  address?: string;
  city?: string;
  location?: {
    lat: number;
    lng: number;
  };
}

export interface PostInteraction {
  id: string;
  postId: string;
  userId: string;
  type: InteractionType;
  createdAt: string;
}

export interface PostComment {
  id: string;
  postId: string;
  user: User;
  content: string;
  createdAt: string;
  likesCount: number;
  userLiked: boolean;
  parentCommentId?: string;
  replies?: PostComment[];
}

export interface PostReport {
  id: string;
  postId: string;
  userId: string;
  reason: ReportReason;
  description?: string;
  createdAt: string;
  status: ReportStatus;
}

// Enums
export enum InteractionType {
  LIKE = 'like',
  SAVE = 'save',
  SHARE = 'share',
  COMMENT = 'comment',
  REPORT = 'report',
}

export enum VenueCategory {
  RESTAURANT = 'restaurant',
  BAR = 'bar',
  CLUB = 'club',
  CAFE = 'cafe',
  HOTEL = 'hotel',
  EVENT_VENUE = 'event_venue',
  SHOPPING = 'shopping',
  ENTERTAINMENT = 'entertainment',
  OUTDOORS = 'outdoors',
  CULTURAL = 'cultural',
  SPORTS = 'sports',
  OTHER = 'other',
}

export enum PostFilter {
  ALL = 'all',
  RESTAURANTS = 'restaurants',
  NIGHTLIFE = 'nightlife',
  EVENTS = 'events',
  SHOPPING = 'shopping',
  OUTDOORS = 'outdoors',
  TRENDING_NOW = 'trending_now',
  NEARBY = 'nearby',
}

export enum SortOrder {
  TRENDING = 'trending',
  RECENT = 'recent',
  POPULAR = 'popular',
  NEARBY = 'nearby',
}

export enum ReportReason {
  SPAM = 'spam',
  INAPPROPRIATE_CONTENT = 'inappropriate_content',
  HARASSMENT = 'harassment',
  FALSE_INFORMATION = 'false_information',
  COPYRIGHT = 'copyright',
  OTHER = 'other',
}

export enum ReportStatus {
  PENDING = 'pending',
  REVIEWED = 'reviewed',
  RESOLVED = 'resolved',
  DISMISSED = 'dismissed',
}

// Query/Filter types
export interface TrendingFeedQuery {
  filter: PostFilter;
  sortOrder: SortOrder;
  location?: {
    latitude: number;
    longitude: number;
    radius: number; // in kilometers
  };
  cursor?: string;
  limit: number;
  timeRange?: {
    start: string;
    end: string;
  };
}

export interface TrendingFeedResponse {
  posts: TrendingPost[];
  hasNextPage: boolean;
  nextCursor?: string;
  totalCount: number;
}

// Store state types
export interface TrendingState {
  posts: TrendingPost[];
  filteredPosts: TrendingPost[];
  currentFilter: PostFilter;
  currentSort: SortOrder;
  isLoading: boolean;
  isRefreshing: boolean;
  isLoadingMore: boolean;
  hasNextPage: boolean;
  nextCursor?: string;
  error: string | null;
  lastRefresh: number | null;
}

// Action types for optimistic updates
export interface OptimisticAction {
  type: 'like' | 'unlike' | 'save' | 'unsave' | 'share';
  postId: string;
  userId: string;
  timestamp: number;
}

// Analytics types
export interface PostAnalytics {
  postId: string;
  impressions: number;
  clicks: number;
  engagementRate: number;
  topLocations: string[];
  peakHours: number[];
  demographics: {
    ageGroups: Record<string, number>;
    genders: Record<string, number>;
  };
}

// Notification types
export interface TrendingNotification {
  id: string;
  type: 'post_trending' | 'venue_popular' | 'user_mention';
  postId?: string;
  venueId?: string;
  message: string;
  createdAt: string;
  isRead: boolean;
}

// Cache types
export interface CachedPost extends TrendingPost {
  cachedAt: number;
  cacheKey: string;
}

export interface CacheMetadata {
  filter: PostFilter;
  location?: string;
  expiresAt: number;
  itemCount: number;
}
