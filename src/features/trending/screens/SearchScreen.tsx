import React, { useEffect, useRef, useState } from 'react';

import { TextInput as RNTextInput, ScrollView } from 'react-native';

import { FlashList } from '@shopify/flash-list';
import { Stack, router } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { useHaptics } from '@/src/core/libs';
import { Box, Pressable, Text, TextInput } from '@/src/core/theme';
import { useAuth } from '@/src/features/auth/services';
import { Avatar, Chip, HeaderButton } from '@/src/shared/components';

import { LocationBadge } from '../components/LocationBadge';
import { TrendingPostCard } from '../components/TrendingPostCard';
import { useTrendingStore } from '../store/trendingStore';
import type { TrendingPost, User, Venue } from '../types';

type SearchResultType = 'posts' | 'users' | 'venues' | 'hashtags';

interface SearchResult {
  type: SearchResultType;
  data: TrendingPost | User | Venue | string;
}

const RECENT_SEARCHES_KEY = 'trending_recent_searches';
const MAX_RECENT_SEARCHES = 10;

export default function SearchScreen() {
  const { user } = useAuth();
  const { posts, searchPosts, loading } = useTrendingStore();
  const { triggerHaptic } = useHaptics();
  const insets = useSafeAreaInsets();

  const [query, setQuery] = useState('');
  const [activeTab, setActiveTab] = useState<SearchResultType>('posts');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  const inputRef = useRef<RNTextInput>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout>();

  const tabs: { label: string; value: SearchResultType; icon: string }[] = [
    { label: 'Posts', value: 'posts', icon: '📝' },
    { label: 'People', value: 'users', icon: '👥' },
    { label: 'Places', value: 'venues', icon: '📍' },
    { label: 'Tags', value: 'hashtags', icon: '#️⃣' },
  ];

  useEffect(() => {
    // Load recent searches from storage
    const loadRecentSearches = async () => {
      try {
        // TODO: Load from MMKV storage
        setRecentSearches([]);
      } catch (error) {
        console.error('Failed to load recent searches:', error);
      }
    };

    loadRecentSearches();

    // Focus input on mount
    setTimeout(() => {
      inputRef.current?.focus();
    }, 100);
  }, []);

  useEffect(() => {
    if (query.trim().length >= 2) {
      // Debounce search
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }

      searchTimeoutRef.current = setTimeout(() => {
        performSearch(query.trim());
      }, 300);
    } else {
      setSearchResults([]);
      setIsSearching(false);
    }

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [query]);

  const performSearch = async (searchQuery: string) => {
    setIsSearching(true);

    try {
      // TODO: Replace with actual API calls
      const mockResults: SearchResult[] = [];

      // Mock post search
      if (activeTab === 'posts') {
        const filteredPosts = posts.filter(
          post =>
            post.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
            post.hashtags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
        );
        mockResults.push(...filteredPosts.map(post => ({ type: 'posts' as const, data: post })));
      }

      // Mock user search
      if (activeTab === 'users') {
        const mockUsers: User[] = [
          {
            id: '1',
            username: 'john_doe',
            name: 'John Doe',
            avatar:
              'https://images.unsplash.com/photo-1599566150163-29194dcaad36?w=100&h=100&fit=crop&crop=face',
            isVerified: true,
          },
          {
            id: '2',
            username: 'jane_smith',
            name: 'Jane Smith',
            avatar:
              'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
            isVerified: false,
          },
        ];

        const filteredUsers = mockUsers.filter(
          user =>
            user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            user.username.toLowerCase().includes(searchQuery.toLowerCase())
        );
        mockResults.push(...filteredUsers.map(user => ({ type: 'users' as const, data: user })));
      }

      // Mock venue search
      if (activeTab === 'venues') {
        const mockVenues: Venue[] = [
          {
            id: '1',
            name: 'The Coffee House',
            category: 'cafe',
            address: '123 Main St, Downtown',
            city: 'San Francisco',
            country: 'United States',
            coordinates: { latitude: 37.7749, longitude: -122.4194 },
            rating: 4.5,
            isOpen: true,
            distance: 0.2,
          },
        ];

        const filteredVenues = mockVenues.filter(
          venue =>
            venue.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            venue.address.toLowerCase().includes(searchQuery.toLowerCase())
        );
        mockResults.push(
          ...filteredVenues.map(venue => ({ type: 'venues' as const, data: venue }))
        );
      }

      // Mock hashtag search
      if (activeTab === 'hashtags') {
        const mockHashtags = ['trending', 'foodie', 'travel', 'coffee', 'sunset', 'friends'];
        const filteredHashtags = mockHashtags.filter(tag =>
          tag.toLowerCase().includes(searchQuery.toLowerCase())
        );
        mockResults.push(
          ...filteredHashtags.map(hashtag => ({ type: 'hashtags' as const, data: hashtag }))
        );
      }

      setSearchResults(mockResults);
    } catch (error) {
      console.error('Search failed:', error);
    } finally {
      setIsSearching(false);
    }
  };

  const handleSearch = (searchQuery: string) => {
    if (searchQuery.trim()) {
      // Add to recent searches
      const newRecentSearches = [
        searchQuery,
        ...recentSearches.filter(s => s !== searchQuery),
      ].slice(0, MAX_RECENT_SEARCHES);

      setRecentSearches(newRecentSearches);
      // TODO: Save to MMKV storage

      triggerHaptic('impact', 'light');
    }
  };

  const handleRecentSearchPress = (searchQuery: string) => {
    setQuery(searchQuery);
    triggerHaptic('impact', 'light');
  };

  const clearRecentSearches = () => {
    setRecentSearches([]);
    triggerHaptic('impact', 'medium');
    // TODO: Clear from MMKV storage
  };

  const renderSearchResult = ({ item }: { item: SearchResult }) => {
    switch (item.type) {
      case 'posts':
        return (
          <TrendingPostCard
            post={item.data as TrendingPost}
            onPress={() => {
              router.push(`/trending/post/${(item.data as TrendingPost).id}`);
            }}
          />
        );

      case 'users':
        const userData = item.data as User;
        return (
          <Pressable
            onPress={() => {
              router.push(`/profile/${userData.id}`);
              triggerHaptic('impact', 'light');
            }}>
            <Box
              flexDirection="row"
              alignItems="center"
              padding="md_16"
              borderBottomWidth={1}
              borderBottomColor="border">
              <Avatar size="m" source={{ uri: userData.avatar }} />
              <Box marginLeft="sm_12" flex={1}>
                <Box flexDirection="row" alignItems="center">
                  <Text variant="b_16SemiBold_button" color="text">
                    {userData.name}
                  </Text>
                  {userData.isVerified && (
                    <Text variant="l_12Medium_message" color="primary" marginLeft="xxs_4">
                      ✓
                    </Text>
                  )}
                </Box>
                <Text variant="l_14Medium_formHelperText" color="textSecondary">
                  @{userData.username}
                </Text>
              </Box>
            </Box>
          </Pressable>
        );

      case 'venues':
        const venueData = item.data as Venue;
        return (
          <Pressable
            onPress={() => {
              router.push(`/venue/${venueData.id}`);
              triggerHaptic('impact', 'light');
            }}>
            <Box padding="md_16" borderBottomWidth={1} borderBottomColor="border">
              <LocationBadge venue={venueData} showDistance />
            </Box>
          </Pressable>
        );

      case 'hashtags':
        const hashtag = item.data as string;
        return (
          <Pressable
            onPress={() => {
              setQuery(`#${hashtag}`);
              setActiveTab('posts');
              triggerHaptic('impact', 'light');
            }}>
            <Box
              flexDirection="row"
              alignItems="center"
              padding="md_16"
              borderBottomWidth={1}
              borderBottomColor="border">
              <Text variant="h_18SemiBold_cardTitle" color="primary" marginRight="sm_12">
                #
              </Text>
              <Text variant="b_16Regular_input" color="text">
                {hashtag}
              </Text>
            </Box>
          </Pressable>
        );

      default:
        return null;
    }
  };

  const showRecentSearches = query.trim().length === 0 && !isSearching;
  const showResults = query.trim().length >= 2 && searchResults.length > 0;
  const showNoResults = query.trim().length >= 2 && searchResults.length === 0 && !isSearching;

  return (
    <Box flex={1} backgroundColor="background">
      <Stack.Screen
        options={{
          title: 'Search',
          headerLeft: () => (
            <HeaderButton
              icon="arrow-back"
              onPress={() => router.back()}
              accessibilityLabel="Go back"
            />
          ),
        }}
      />

      {/* Search Input */}
      <Box padding="lg_24" borderBottomWidth={1} borderBottomColor="border">
        <TextInput
          ref={inputRef}
          value={query}
          onChangeText={setQuery}
          placeholder="Search posts, people, places..."
          onSubmitEditing={() => handleSearch(query)}
          returnKeyType="search"
          clearButtonMode="while-editing"
          autoCapitalize="none"
          autoCorrect={false}
        />
      </Box>

      {/* Tabs */}
      <Box flexDirection="row" paddingHorizontal="lg_24" paddingVertical="sm_12">
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <Box flexDirection="row" gap="xs_8">
            {tabs.map(tab => (
              <Chip
                key={tab.value}
                label={`${tab.icon} ${tab.label}`}
                selected={activeTab === tab.value}
                onPress={() => {
                  setActiveTab(tab.value);
                  triggerHaptic('impact', 'light');
                  if (query.trim().length >= 2) {
                    performSearch(query.trim());
                  }
                }}
              />
            ))}
          </Box>
        </ScrollView>
      </Box>

      {/* Content */}
      <Box flex={1}>
        {showRecentSearches && (
          <Box padding="lg_24">
            <Box
              flexDirection="row"
              justifyContent="space-between"
              alignItems="center"
              marginBottom="md_16">
              <Text variant="h_18SemiBold_cardTitle" color="text">
                Recent Searches
              </Text>
              {recentSearches.length > 0 && (
                <Pressable onPress={clearRecentSearches}>
                  <Text variant="l_12Medium_message" color="textSecondary">
                    Clear
                  </Text>
                </Pressable>
              )}
            </Box>

            {recentSearches.length > 0 ? (
              <Box flexDirection="row" flexWrap="wrap" gap="xs_8">
                {recentSearches.map((search, index) => (
                  <Chip
                    key={index}
                    label={search}
                    onPress={() => handleRecentSearchPress(search)}
                  />
                ))}
              </Box>
            ) : (
              <Text variant="b_14Regular_content" color="textSecondary">
                No recent searches
              </Text>
            )}
          </Box>
        )}

        {showResults && (
          <FlashList
            data={searchResults}
            renderItem={renderSearchResult}
            keyExtractor={(item, index) => `${item.type}-${index}`}
            estimatedItemSize={activeTab === 'posts' ? 200 : 80}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: Math.max(insets.bottom, 24) }}
          />
        )}

        {showNoResults && (
          <Box flex={1} justifyContent="center" alignItems="center" padding="xl_32">
            <Text variant="h_20Medium_subsection" color="text" marginBottom="xs_8">
              No results found
            </Text>
            <Text variant="b_14Regular_content" color="textSecondary" textAlign="center">
              Try searching for something else or check your spelling
            </Text>
          </Box>
        )}

        {isSearching && (
          <Box flex={1} justifyContent="center" alignItems="center">
            <Text variant="b_16Regular_input" color="textSecondary">
              Searching...
            </Text>
          </Box>
        )}
      </Box>
    </Box>
  );
}
