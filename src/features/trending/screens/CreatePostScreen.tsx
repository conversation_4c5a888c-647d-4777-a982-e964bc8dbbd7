import React, { useRef, useState } from 'react';

import { Alert, KeyboardAvoidingView, Platform, ScrollView } from 'react-native';

import * as ImagePicker from 'expo-image-picker';
import { Stack, router } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { useHaptics, useImagePicker } from '@/src/core/libs';
import { Box, Text, TextInput } from '@/src/core/theme';
import { useAuth } from '@/src/features/auth/services';
import { Avatar, Button, HeaderButton } from '@/src/shared/components';
import { useZodForm } from '@/src/shared/forms';

import { ImageGallery } from '../components/ImageGallery';
import { PostPreview } from '../components/PostPreview';
import { VenueSelector } from '../components/VenueSelector';
import { useTrendingStore } from '../store/trendingStore';
import type { Venue } from '../types';
import { createPostSchema } from '../validation/schemas';

interface CreatePostForm {
  content: string;
  venueId?: string;
  images: string[];
  hashtags: string[];
}

export default function CreatePostScreen() {
  const { user } = useAuth();
  const { createPost, loading } = useTrendingStore();
  const { triggerHaptic } = useHaptics();
  const { pickImage, pickImages } = useImagePicker();
  const insets = useSafeAreaInsets();

  const [selectedVenue, setSelectedVenue] = useState<Venue | null>(null);
  const [images, setImages] = useState<string[]>([]);
  const [showPreview, setShowPreview] = useState(false);

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isValid },
  } = useZodForm({
    schema: createPostSchema,
    defaultValues: {
      content: '',
      images: [],
      hashtags: [],
    },
  });

  const content = watch('content');
  const contentLength = content?.length || 0;
  const maxLength = 500;

  const handleImagePick = async () => {
    try {
      triggerHaptic('impact', 'light');

      const result = await pickImages({
        mediaTypes: ['images'],
        allowsMultipleSelection: true,
        quality: 0.8,
        maxImages: 6 - images.length,
      });

      if (result && !result.canceled) {
        const newImages = result.assets.map(asset => asset.uri);
        const updatedImages = [...images, ...newImages].slice(0, 6);
        setImages(updatedImages);
        setValue('images', updatedImages);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to pick images. Please try again.');
    }
  };

  const handleRemoveImage = (index: number) => {
    triggerHaptic('impact', 'medium');
    const updatedImages = images.filter((_, i) => i !== index);
    setImages(updatedImages);
    setValue('images', updatedImages);
  };

  const handleVenueSelect = (venue: Venue | null) => {
    setSelectedVenue(venue);
    setValue('venueId', venue?.id);
    triggerHaptic('impact', 'light');
  };

  const extractHashtags = (text: string): string[] => {
    const hashtags = text.match(/#\w+/g) || [];
    return hashtags.map(tag => tag.slice(1).toLowerCase());
  };

  const onSubmit = async (data: CreatePostForm) => {
    if (!user) return;

    try {
      triggerHaptic('impact', 'medium');

      const hashtags = extractHashtags(data.content);

      await createPost({
        content: data.content,
        venueId: selectedVenue?.id,
        images: data.images,
        hashtags,
      });

      triggerHaptic('notification', 'success');
      router.back();
    } catch (error) {
      triggerHaptic('notification', 'error');
      Alert.alert('Error', 'Failed to create post. Please try again.');
    }
  };

  const handlePreview = () => {
    setShowPreview(true);
    triggerHaptic('impact', 'light');
  };

  const handleClosePreview = () => {
    setShowPreview(false);
  };

  const handleDiscard = () => {
    Alert.alert(
      'Discard Post',
      'Are you sure you want to discard this post? Your changes will be lost.',
      [
        { text: 'Keep Editing', style: 'cancel' },
        {
          text: 'Discard',
          style: 'destructive',
          onPress: () => {
            triggerHaptic('impact', 'medium');
            router.back();
          },
        },
      ]
    );
  };

  if (!user) {
    return (
      <Box flex={1} backgroundColor="background" justifyContent="center" alignItems="center">
        <Text variant="h_20Medium_subsection" color="text">
          Please sign in to create posts
        </Text>
      </Box>
    );
  }

  if (showPreview) {
    return (
      <PostPreview
        content={content}
        images={images}
        venue={selectedVenue}
        user={user}
        onClose={handleClosePreview}
        onPublish={handleSubmit(onSubmit)}
        isPublishing={loading}
      />
    );
  }

  return (
    <KeyboardAvoidingView
      flex={1}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      backgroundColor="background">
      <Stack.Screen
        options={{
          title: 'Create Post',
          headerLeft: () => (
            <HeaderButton icon="close" onPress={handleDiscard} accessibilityLabel="Close" />
          ),
          headerRight: () => (
            <Button
              title="Preview"
              variant="ghost"
              onPress={handlePreview}
              disabled={!isValid || contentLength === 0}
            />
          ),
        }}
      />

      <ScrollView
        contentContainerStyle={{ flexGrow: 1 }}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}>
        {/* User Info */}
        <Box
          flexDirection="row"
          alignItems="center"
          paddingHorizontal="lg_24"
          paddingVertical="md_16"
          borderBottomWidth={1}
          borderBottomColor="border">
          <Avatar size="m" source={{ uri: user.avatar }} />
          <Box marginLeft="sm_12">
            <Text variant="b_16SemiBold_button" color="text">
              {user.name}
            </Text>
            <Text variant="l_14Medium_formHelperText" color="textSecondary">
              @{user.username}
            </Text>
          </Box>
        </Box>

        {/* Content Input */}
        <Box flex={1} padding="lg_24">
          <TextInput
            control={control}
            name="content"
            placeholder="What's happening?"
            multiline
            autoFocus
            style={{
              minHeight: 120,
              textAlignVertical: 'top',
              fontSize: 18,
              lineHeight: 24,
            }}
            maxLength={maxLength}
          />

          {/* Character Counter */}
          <Box alignItems="flex-end" marginTop="xs_8">
            <Text
              variant="l_12Regular_helperText"
              color={contentLength > maxLength * 0.9 ? 'warning' : 'textTertiary'}>
              {contentLength}/{maxLength}
            </Text>
          </Box>

          {errors.content && (
            <Text variant="l_12Medium_message" color="error" marginTop="xs_8">
              {errors.content.message}
            </Text>
          )}
        </Box>

        {/* Image Gallery */}
        {images.length > 0 && (
          <Box paddingHorizontal="lg_24" marginBottom="md_16">
            <ImageGallery images={images} onRemove={handleRemoveImage} maxImages={6} />
          </Box>
        )}

        {/* Venue Selection */}
        <Box paddingHorizontal="lg_24" marginBottom="md_16">
          <VenueSelector selectedVenue={selectedVenue} onVenueSelect={handleVenueSelect} />
        </Box>

        {/* Action Buttons */}
        <Box
          flexDirection="row"
          justifyContent="space-between"
          paddingHorizontal="lg_24"
          paddingVertical="md_16"
          borderTopWidth={1}
          borderTopColor="border"
          style={{ paddingBottom: Math.max(insets.bottom, 16) }}>
          <Button
            title="📷 Photos"
            variant="outline"
            onPress={handleImagePick}
            disabled={images.length >= 6}
            flex={1}
            marginRight="sm_12"
          />

          <Button
            title="📍 Add Location"
            variant="outline"
            onPress={() => {
              /* TODO: Implement location picker */
            }}
            flex={1}
            marginLeft="sm_12"
          />
        </Box>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}
