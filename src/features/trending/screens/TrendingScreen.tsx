import React, { useCallback, useState } from 'react';

import { RefreshControl, ScrollView } from 'react-native';

import { useBottomTabBarHeight } from '@react-navigation/bottom-tabs';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { useTopTabBarHeight } from '@/src/core/navigation/ui/TopTabNavigation';
import { Box, Pressable, Text, useTheme } from '@/src/core/theme';

import { FriendsActivityCard } from '../components/FriendsActivityCard';
import { HostActivityCard } from '../components/HostActivityCard';
import { SecretEventCard } from '../components/SecretEventCard';
import { VenueActivityCard } from '../components/VenueActivityCard';

// Mock data types
interface FriendActivity {
  id: string;
  user: {
    id: string;
    name: string;
    avatar?: string;
    initials: string;
  };
  timestamp: string;
  message: string;
  venue?: {
    id: string;
    name: string;
    location: string;
    distance: string;
    openTime?: string;
    closeTime?: string;
  };
  event?: {
    id: string;
    name: string;
    location: string;
    distance: string;
    time: string;
    type: 'public' | 'private';
  };
}

interface SecretEvent {
  id: string;
  name: string;
  description: string;
  isExclusive: boolean;
  isRevealed: boolean;
  location?: {
    name: string;
    distance?: string;
  };
  time?: string;
  host?: {
    name: string;
    avatar?: string;
  };
  attendees?: number;
  capacity?: number;
}

// Mock data
const mockFriendsActivities: FriendActivity[] = [
  {
    id: '1',
    user: {
      id: '1',
      name: 'Alex',
      initials: 'JS',
    },
    timestamp: '12m ago',
    message: 'Goin to River bar after the concert',
    venue: {
      id: '1',
      name: 'River bar basement',
      location: 'Skyline lounge',
      distance: '2.3 Km',
      openTime: 'Open at 1 AM',
      closeTime: 'Close at 5 AM',
    },
  },
  {
    id: '2',
    user: {
      id: '2',
      name: 'Sarah',
      initials: 'SM',
    },
    timestamp: '25m ago',
    message: 'Just added a special after-hours set with Dj Maxwell. Starting at 1 AM.',
    event: {
      id: '1',
      name: 'Midnight after party',
      location: "Pandeiro Hall's",
      distance: '2.3 Km',
      time: '1AM - 4AM',
      type: 'public',
    },
  },
  {
    id: '3',
    user: {
      id: '3',
      name: 'Mike',
      initials: 'MJ',
    },
    timestamp: '45m ago',
    message: 'Private rooftop gathering tonight. Limited spots available!',
    event: {
      id: '2',
      name: 'Skyline Exclusive',
      location: 'Skyline lounge',
      distance: '2.3 Km',
      time: 'Invite only',
      type: 'private',
    },
  },
];

const mockSecretEvents: SecretEvent[] = [
  {
    id: '1',
    name: 'The secret garden',
    description:
      "You've been added to the guest list for this exclusive event. RSVP to reveal the location ASAP.",
    isExclusive: true,
    isRevealed: false,
    time: '11 PM',
    attendees: 24,
    capacity: 50,
  },
  {
    id: '2',
    name: 'Underground Sessions',
    description: 'Exclusive underground party. Location revealed 2 hours before start.',
    isExclusive: true,
    isRevealed: true,
    location: {
      name: 'Warehouse District',
      distance: '4.2 Km',
    },
    time: '1 AM - 6 AM',
    host: {
      name: 'DJ Shadow',
    },
    attendees: 89,
    capacity: 150,
  },
];

type FilterType = 'all' | 'friends' | 'venue' | 'private' | 'hosts';

export default function TrendingScreen() {
  const insets = useSafeAreaInsets();
  const bottomTabHeight = useBottomTabBarHeight();
  const topBarHeight = useTopTabBarHeight();
  const theme = useTheme();
  const [selectedFilter, setSelectedFilter] = useState<FilterType>('all');
  const [refreshing, setRefreshing] = useState(false);
  const [expandedCards, setExpandedCards] = useState<Set<string>>(new Set());

  const handleFilterChange = useCallback((filter: FilterType) => {
    setSelectedFilter(filter);
  }, []);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    setRefreshing(false);
  }, []);

  const toggleCardExpansion = useCallback((cardId: string) => {
    setExpandedCards(prev => {
      const newSet = new Set(prev);
      if (newSet.has(cardId)) {
        newSet.delete(cardId);
      } else {
        newSet.add(cardId);
      }
      return newSet;
    });
  }, []);

  const renderContent = () => {
    switch (selectedFilter) {
      case 'friends':
        return (
          <FriendsActivityCard
            activities={mockFriendsActivities}
            onViewMore={() => console.log('View more friends activity')}
            onShareVenue={venueId => console.log('Share venue:', venueId)}
            onBookmarkEvent={eventId => console.log('Bookmark event:', eventId)}
            onViewEvent={eventId => console.log('View event:', eventId)}
            isExpanded={expandedCards.has('friends')}
            onToggleExpand={() => toggleCardExpansion('friends')}
          />
        );

      case 'private':
        return (
          <>
            {mockSecretEvents.map(event => (
              <Box key={event.id} marginBottom="md_16">
                <SecretEventCard
                  event={event}
                  onRSVP={() => console.log('RSVP to event:', event.id)}
                  onRevealLocation={() => console.log('Reveal location for:', event.id)}
                  onShare={() => console.log('Share event:', event.id)}
                />
              </Box>
            ))}
          </>
        );

      case 'venue':
        return (
          <VenueActivityCard
            venues={[
              {
                id: '1',
                name: 'River Bar',
                activity: 'High activity',
                currentGuests: 156,
                peakTime: '1-3 AM',
                events: 3,
              },
              {
                id: '2',
                name: 'Skyline Lounge',
                activity: 'Moderate',
                currentGuests: 78,
                peakTime: '11 PM - 1 AM',
                events: 2,
              },
            ]}
            onViewVenue={venueId => console.log('View venue:', venueId)}
            isExpanded={expandedCards.has('venues')}
            onToggleExpand={() => toggleCardExpansion('venues')}
          />
        );

      case 'hosts':
        return (
          <HostActivityCard
            hosts={[
              {
                id: '1',
                name: 'DJ Maxwell',
                avatar: 'https://picsum.photos/100/100',
                rating: 4.9,
                upcomingEvents: 3,
                nextEvent: {
                  name: 'After Hours Set',
                  time: '1 AM',
                  venue: 'River Bar',
                },
              },
              {
                id: '2',
                name: 'Sarah Events',
                avatar: 'https://picsum.photos/100/101',
                rating: 4.7,
                upcomingEvents: 2,
                nextEvent: {
                  name: 'Rooftop Session',
                  time: '11 PM',
                  venue: 'Skyline',
                },
              },
            ]}
            onViewHost={hostId => console.log('View host:', hostId)}
            onViewEvent={eventId => console.log('View host event:', eventId)}
            isExpanded={expandedCards.has('hosts')}
            onToggleExpand={() => toggleCardExpansion('hosts')}
          />
        );

      default: // 'all'
        return (
          <>
            <Box marginBottom="lg_24">
              <FriendsActivityCard
                activities={mockFriendsActivities.slice(0, 2)}
                onViewMore={() => console.log('View more friends activity')}
                onShareVenue={venueId => console.log('Share venue:', venueId)}
                onBookmarkEvent={eventId => console.log('Bookmark event:', eventId)}
                onViewEvent={eventId => console.log('View event:', eventId)}
                isExpanded={expandedCards.has('friends')}
                onToggleExpand={() => toggleCardExpansion('friends')}
              />
            </Box>

            <Box marginBottom="lg_24">
              <SecretEventCard
                event={mockSecretEvents[0]}
                onRSVP={() => console.log('RSVP to event')}
                onRevealLocation={() => console.log('Reveal location')}
                onShare={() => console.log('Share event')}
              />
            </Box>

            <Box marginBottom="lg_24">
              <VenueActivityCard
                venues={[
                  {
                    id: '1',
                    name: 'River Bar',
                    activity: 'High activity',
                    currentGuests: 156,
                    peakTime: '1-3 AM',
                    events: 3,
                  },
                ]}
                onViewVenue={venueId => console.log('View venue:', venueId)}
                isExpanded={false}
                onToggleExpand={() => {}}
                showViewMore
              />
            </Box>
          </>
        );
    }
  };

  return (
    <Box flex={1} backgroundColor="mainBackground">
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          paddingBottom: insets.bottom + bottomTabHeight,
          paddingTop: topBarHeight.height + theme.spacing.md_16,
        }}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />}>
        {/* Filter Section */}
        <Box paddingHorizontal="md_16" marginBottom="md_16">
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={{ marginHorizontal: -16 }}
            contentContainerStyle={{ paddingHorizontal: 16 }}>
            <Box flexDirection="row" gap="xs_8">
              {[
                { value: 'all' as FilterType, label: 'All' },
                { value: 'friends' as FilterType, label: 'Friends' },
                { value: 'venue' as FilterType, label: 'Venue' },
                { value: 'private' as FilterType, label: 'Private' },
                { value: 'hosts' as FilterType, label: 'Hosts' },
              ].map(filter => (
                <FilterChip
                  key={filter.value}
                  label={filter.label}
                  isActive={selectedFilter === filter.value}
                  onPress={() => handleFilterChange(filter.value)}
                />
              ))}
            </Box>
          </ScrollView>
        </Box>

        {/* Content */}
        <Box paddingHorizontal="md_16">{renderContent()}</Box>
      </ScrollView>
    </Box>
  );
}

// Filter Chip Component
const FilterChip = ({
  label,
  isActive,
  onPress,
}: {
  label: string;
  isActive: boolean;
  onPress: () => void;
}) => {
  return (
    <Pressable onPress={onPress}>
      <Box
        paddingHorizontal="sm_12"
        paddingVertical="xxs_4"
        backgroundColor={isActive ? 'buttonTintedBackground' : 'subtleBackground'}
        borderRadius="sm_8">
        <Text variant="l_14Medium_info" color={isActive ? 'buttonTintedText' : 'mutedText'}>
          {label}
        </Text>
      </Box>
    </Pressable>
  );
};
