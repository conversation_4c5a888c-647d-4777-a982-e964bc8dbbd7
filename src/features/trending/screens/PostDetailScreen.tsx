import React, { useCallback, useEffect, useState } from 'react';

import { KeyboardAvoidingView, Platform, RefreshControl, View } from 'react-native';

import { useNavigation, useRoute } from '@react-navigation/native';
import { FlashList } from '@shopify/flash-list';
import { useTranslation } from 'react-i18next';

import { haptics } from '@/src/core/libs';
import { useTheme } from '@/src/core/theme';
import Typography from '@/src/core/theme/Text';

import { CommentInput } from '../components/CommentInput';
import { CommentItem } from '../components/CommentItem';
import { TrendingPostCard } from '../components/TrendingPostCard';
import { usePostInteractions } from '../hooks/usePostInteractions';
import { useAddComment } from '../hooks/usePostMutations';
import { usePost, usePostComments } from '../hooks/usePostQueries';
import { Comment } from '../types';

export function PostDetailScreen() {
  const { t } = useTranslation();
  const { colors } = useTheme();
  const navigation = useNavigation();
  const route = useRoute<any>();
  const insets = useSafeAreaInsets();
  const [replyTo, setReplyTo] = useState<{ id: string; username: string } | null>(null);

  // Extract the post ID from route params
  const postId = route.params?.postId;

  // Query hooks for fetching data
  const {
    data: post,
    isLoading: isLoadingPost,
    isError: isPostError,
    error: postError,
    refetch: refetchPost,
    isRefetching: isRefetchingPost,
  } = usePost(postId);

  const {
    data: commentsData,
    isLoading: isLoadingComments,
    isError: isCommentsError,
    error: commentsError,
    fetchNextPage: fetchNextComments,
    hasNextPage: hasMoreComments,
    isFetchingNextPage: isFetchingNextComments,
    refetch: refetchComments,
    isRefetching: isRefetchingComments,
  } = usePostComments(postId);

  // Mutation hook for adding comments
  const addCommentMutation = useAddComment(postId);

  // Get interactions and handlers
  const { handleLike, handleSave, handleShare, handleReport } = usePostInteractions({ post });

  // Handle refresh - refetch both post and comments
  const handleRefresh = useCallback(async () => {
    await Promise.all([refetchPost(), refetchComments()]);
  }, [refetchPost, refetchComments]);

  // Handle reply to comment
  const handleReplyToComment = useCallback((commentId: string, username: string) => {
    haptics.light();
    setReplyTo({ id: commentId, username });
  }, []);

  // Handle cancel reply
  const handleCancelReply = useCallback(() => {
    setReplyTo(null);
  }, []);

  // Handle adding a comment
  const handleAddComment = useCallback(
    (content: string) => {
      if (!content.trim()) return;

      addCommentMutation.mutate(
        {
          content,
          parentCommentId: replyTo?.id,
        },
        {
          onSuccess: () => {
            // Clear reply state after successful submission
            setReplyTo(null);
          },
        }
      );
    },
    [addCommentMutation, replyTo]
  );

  // Handle loading more comments
  const handleLoadMoreComments = useCallback(() => {
    if (hasMoreComments && !isFetchingNextComments) {
      fetchNextComments();
    }
  }, [fetchNextComments, hasMoreComments, isFetchingNextComments]);

  // Render comment item
  const renderCommentItem = useCallback(
    ({ item }: { item: Comment }) => (
      <CommentItem
        comment={item}
        onReply={handleReplyToComment}
        onLike={() => console.log('Like comment:', item.id)}
      />
    ),
    [handleReplyToComment]
  );

  // Loading state
  if (isLoadingPost && !isRefetchingPost) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <LoadingIndicator size="large" />
      </View>
    );
  }

  // Error state
  if (isPostError) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 }}>
        <EmptyState
          title={t('postDetail.error', 'Error loading post')}
          description={
            postError instanceof Error
              ? postError.message
              : t('postDetail.genericError', 'Something went wrong')
          }
          actionLabel={t('common.tryAgain', 'Try Again')}
          onAction={refetchPost}
        />
      </View>
    );
  }

  // Ensure post data is available
  if (!post) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Typography variant="h2">{t('postDetail.notFound', 'Post not found')}</Typography>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={{ flex: 1, backgroundColor: colors.background }}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}>
      <FlashList
        data={commentsData?.comments || []}
        renderItem={renderCommentItem}
        estimatedItemSize={100}
        ItemSeparatorComponent={() => (
          <View style={{ height: 1, backgroundColor: colors.border }} />
        )}
        ListHeaderComponent={() => (
          <View style={{ padding: 16 }}>
            <TrendingPostCard
              post={post}
              onLike={handleLike}
              onSave={handleSave}
              onShare={handleShare}
              onComment={() => {}}
              onReport={handleReport}
              hideBottomBorder
              disableNavigation
              showFullContent
            />
            <View
              style={{
                height: 8,
                backgroundColor: colors.card,
                marginVertical: 16,
                marginHorizontal: -16,
              }}
            />
            <Typography variant="h3" style={{ marginBottom: 12 }}>
              {t('postDetail.comments', { count: commentsData?.totalCount || 0 })}
            </Typography>
          </View>
        )}
        ListEmptyComponent={() => (
          <View style={{ padding: 16, alignItems: 'center' }}>
            {isLoadingComments && !isRefetchingComments ? (
              <LoadingIndicator size="small" />
            ) : isCommentsError ? (
              <Typography variant="body" color="error">
                {commentsError instanceof Error
                  ? commentsError.message
                  : t('postDetail.commentsError')}
              </Typography>
            ) : (
              <Typography variant="body" color="textSecondary">
                {t('postDetail.noComments')}
              </Typography>
            )}
          </View>
        )}
        onEndReached={handleLoadMoreComments}
        onEndReachedThreshold={0.5}
        ListFooterComponent={() =>
          isFetchingNextComments ? (
            <View style={{ padding: 16, alignItems: 'center' }}>
              <LoadingIndicator size="small" />
            </View>
          ) : null
        }
        refreshControl={
          <RefreshControl
            refreshing={isRefetchingPost || isRefetchingComments}
            onRefresh={handleRefresh}
            tintColor={colors.primary}
            colors={[colors.primary]}
          />
        }
      />
      <CommentInput
        onSubmit={handleAddComment}
        replyTo={replyTo ? `@${replyTo.username}` : undefined}
        onCancelReply={handleCancelReply}
        style={{ paddingBottom: Math.max(insets.bottom, 16) }}
        isSubmitting={addCommentMutation.isPending}
        disabled={addCommentMutation.isPending}
      />
    </KeyboardAvoidingView>
  );
}
