import { useInfiniteQuery, useQuery } from '@tanstack/react-query';

import { extractErrorMessage } from '@/src/shared/utils/errors';

import { postService } from '../services/postService';
import { Comment, Post } from '../types';

/**
 * Hook to fetch a single post by ID
 */
export const usePost = (postId: string, enabled = true) => {
  return useQuery({
    queryKey: ['post', postId],
    queryFn: () => postService.getPost(postId),
    enabled: enabled && !!postId,
    select: response => {
      if (response.success) {
        return response.data;
      }
      throw new Error(extractErrorMessage(response.errors, 'Failed to fetch post'));
    },
  });
};

/**
 * Hook to fetch comments for a post with infinite pagination
 */
export const usePostComments = (postId: string, enabled = true) => {
  return useInfiniteQuery({
    queryKey: ['post-comments', postId],
    queryFn: ({ pageParam }) => postService.getPostComments(postId, pageParam as string | null),
    initialPageParam: null as string | null,
    enabled: enabled && !!postId,
    getNextPageParam: lastPage => {
      if (lastPage.success && lastPage.data.comments.hasMore) {
        return lastPage.data.comments.cursor;
      }
      return undefined;
    },
    select: data => {
      // Flatten and transform the pages data
      const comments: Comment[] = [];
      let totalCount = 0;

      data.pages.forEach(page => {
        if (page.success && page.data.comments) {
          comments.push(...page.data.comments.items);
          totalCount = page.data.comments.totalCount;
        }
      });

      return {
        comments,
        totalCount,
        hasNextPage: data.hasNextPage,
      };
    },
  });
};
