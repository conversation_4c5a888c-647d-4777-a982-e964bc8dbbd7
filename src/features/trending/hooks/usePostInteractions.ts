import { useCallback, useState } from 'react';

import { Platform, Share } from 'react-native';

import { useTranslation } from 'react-i18next';

import { fileDownload, haptics, sharing } from '@/src/core/libs';
import { useAlert, useToast } from '@/src/shared/hooks';

import { useTrendingStore } from '../store/trendingStore';
import { TrendingPost } from '../types';

interface UsePostInteractionsProps {
  post?: TrendingPost;
}

export const usePostInteractions = ({ post }: UsePostInteractionsProps = {}) => {
  const { t } = useTranslation();
  const toast = useToast();
  const alert = useAlert();
  const [isSharing, setIsSharing] = useState(false);

  const {
    likePost,
    unlikePost,
    savePost,
    unsavePost,
    sharePost: incrementShareCount,
  } = useTrendingStore();

  /**
   * Handles toggling like on a post
   */
  const handleLike = useCallback(
    (postId: string, isLiked: boolean) => {
      haptics.light();
      isLiked ? unlikePost(postId) : likePost(postId);
    },
    [likePost, unlikePost]
  );

  /**
   * Handles toggling save on a post
   */
  const handleSave = useCallback(
    (postId: string, isSaved: boolean) => {
      haptics.light();
      isSaved ? unsavePost(postId) : savePost(postId);

      toast.successWithHaptic(
        isSaved ? t('trending.interactions.unsaved') : t('trending.interactions.saved')
      );
    },
    [savePost, unsavePost, toast, t]
  );

  /**
   * Handles the native share functionality
   */
  const handleShare = useCallback(
    async (postId: string, postContent: string, username: string, venueInfo?: string) => {
      if (isSharing) return;

      try {
        setIsSharing(true);
        haptics.medium();

        // Prepare share content
        const shareMessage = venueInfo
          ? `${postContent}\n\n📍 ${venueInfo}\n\nShared from @${username} on Movuca App`
          : `${postContent}\n\nShared from @${username} on Movuca App`;

        const shareUrl = `https://movuca.app/post/${postId}`;

        // If there's an image, download it first for sharing
        let localImageUrl = null;
        if (post?.images && post.images.length > 0) {
          const imageUrl = post.images[0];

          try {
            const downloadResult = await fileDownload.downloadImage(imageUrl);
            if (downloadResult.success && downloadResult.localUri) {
              localImageUrl = downloadResult.localUri;
            }
          } catch (error) {
            console.error('Error downloading image for sharing:', error);
          }
        }

        // Share the content
        const shareOptions = {
          message: shareMessage,
          url: localImageUrl,
          title: t('trending.share.title'),
        };

        // Check if sharing is available and use the appropriate API
        if (Platform.OS === 'ios' || Platform.OS === 'android') {
          const result = await Share.share(shareOptions);

          if (result.action === Share.sharedAction) {
            incrementShareCount(postId);
          }
        } else if (await sharing.isAvailable()) {
          if (localImageUrl) {
            await sharing.shareFile(localImageUrl, { dialogTitle: shareMessage });
            incrementShareCount(postId);
          } else {
            await sharing.shareFile(shareUrl, { dialogTitle: shareMessage });
            incrementShareCount(postId);
          }
        } else {
          // Fallback for web or other platforms
          await alert.info(
            t('trending.share.notAvailable', 'Sharing not available'),
            t('trending.share.notAvailableMessage', 'Sharing is not supported on this platform')
          );
        }
      } catch (error) {
        console.error('Error sharing post:', error);
        toast.errorWithHaptic(t('trending.interactions.shareError'));
      } finally {
        setIsSharing(false);
      }
    },
    [isSharing, post, incrementShareCount, t, toast]
  );

  /**
   * Opens the post's comment section
   */
  const navigateToComments = useCallback((postId: string) => {
    haptics.light();
    // This will be handled by the component using the router.push
  }, []);

  /**
   * Reports inappropriate content
   */
  const handleReport = useCallback(
    async (postId: string) => {
      await haptics.medium();
      const confirmed = await alert.confirmDestructive({
        title: t('trending.report.title', 'Report Post'),
        message: t(
          'trending.report.message',
          'Are you sure you want to report this content as inappropriate?'
        ),
        confirmText: t('trending.report.confirm', 'Report'),
        cancelText: t('common.cancel', 'Cancel'),
        onConfirm: () => {
          // TODO: Implement report functionality
          toast.successWithHaptic(t('trending.report.submitted', 'Report submitted successfully'));
        },
        haptic: 'warning',
      });
    },
    [t, toast, alert]
  );

  /**
   * Follow or unfollow a user
   */
  const handleFollowUser = useCallback(
    (userId: string, isFollowing: boolean) => {
      toast.successWithHaptic(isFollowing ? t('user.unfollowed') : t('user.followed'));
    },
    [t, toast]
  );

  /**
   * Support or unsupport a venue
   */
  const handleSupportVenue = useCallback(
    (venueId: string, isSupported: boolean) => {
      toast.successWithHaptic(isSupported ? t('venue.unsupported') : t('venue.supported'));
    },
    [t, toast]
  );

  return {
    isSharing,
    handleLike,
    handleSave,
    handleShare,
    navigateToComments,
    handleReport,
    handleFollowUser,
    handleSupportVenue,
  };
};
