import { useNavigation } from '@react-navigation/native';
import { useMutation, useQueryClient } from '@tanstack/react-query';

import { haptics } from '@/src/core/libs';
import { useToast } from '@/src/shared/hooks';
import { extractErrorMessage } from '@/src/shared/utils/errors';

import { postService } from '../services/postService';

/**
 * Hook for adding a comment to a post
 */
export const useAddComment = (postId: string) => {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: ({ content, parentCommentId }: { content: string; parentCommentId?: string }) =>
      postService.addComment(postId, content, parentCommentId),
    onSuccess: response => {
      if (response.success) {
        haptics.success();
        // Invalidate comments query to refresh the comments list
        queryClient.invalidateQueries({ queryKey: ['post-comments', postId] });
        // Update post comment count in the post detail
        queryClient.invalidateQueries({ queryKey: ['post', postId] });
      } else {
        const errorMessage = extractErrorMessage(response.errors, 'Failed to add comment');
        toast.errorWithHaptic(errorMessage);
      }
    },
    onError: error => {
      console.error('Add comment error:', error);
      const errorMessage = extractErrorMessage(error, 'An unexpected error occurred');
      toast.errorWithHaptic(errorMessage);
    },
  });
};

/**
 * Hook for liking a comment
 */
export const useLikeComment = (postId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (commentId: string) => postService.likeComment(commentId),
    onMutate: async commentId => {
      // Optimistic update logic could be added here
      // This would update the UI before the server responds
      haptics.light();
    },
    onSuccess: response => {
      if (response.success) {
        // Invalidate to refresh comments with updated like status
        queryClient.invalidateQueries({ queryKey: ['post-comments', postId] });
      }
    },
    onError: (error, commentId) => {
      console.error('Like comment error:', error);
      // Revert optimistic update if needed
    },
  });
};

/**
 * Hook for unliking a comment
 */
export const useUnlikeComment = (postId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (commentId: string) => postService.unlikeComment(commentId),
    onMutate: async commentId => {
      // Optimistic update logic could be added here
      haptics.light();
    },
    onSuccess: response => {
      if (response.success) {
        // Invalidate to refresh comments with updated like status
        queryClient.invalidateQueries({ queryKey: ['post-comments', postId] });
      }
    },
    onError: (error, commentId) => {
      console.error('Unlike comment error:', error);
      // Revert optimistic update if needed
    },
  });
};

/**
 * Hook for liking a post
 */
export const useLikePost = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (postId: string) => postService.likePost(postId),
    onMutate: async postId => {
      // Optimistic update logic
      haptics.light();

      // Update post in cache optimistically
      await queryClient.cancelQueries({ queryKey: ['post', postId] });
      const previousPost = queryClient.getQueryData(['post', postId]);

      queryClient.setQueryData(['post', postId], (oldData: any) => {
        if (oldData && oldData.success && oldData.data) {
          return {
            ...oldData,
            data: {
              ...oldData.data,
              userLiked: true,
              likesCount: oldData.data.likesCount + 1,
            },
          };
        }
        return oldData;
      });

      return { previousPost };
    },
    onError: (error, postId, context) => {
      console.error('Like post error:', error);
      // Revert the optimistic update
      if (context?.previousPost) {
        queryClient.setQueryData(['post', postId], context.previousPost);
      }
    },
    onSettled: (data, error, postId) => {
      // Always refetch to ensure consistency
      queryClient.invalidateQueries({ queryKey: ['post', postId] });
    },
  });
};

/**
 * Hook for unliking a post
 */
export const useUnlikePost = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (postId: string) => postService.unlikePost(postId),
    onMutate: async postId => {
      // Optimistic update logic
      haptics.light();

      // Update post in cache optimistically
      await queryClient.cancelQueries({ queryKey: ['post', postId] });
      const previousPost = queryClient.getQueryData(['post', postId]);

      queryClient.setQueryData(['post', postId], (oldData: any) => {
        if (oldData && oldData.success && oldData.data) {
          return {
            ...oldData,
            data: {
              ...oldData.data,
              userLiked: false,
              likesCount: Math.max(0, oldData.data.likesCount - 1),
            },
          };
        }
        return oldData;
      });

      return { previousPost };
    },
    onError: (error, postId, context) => {
      console.error('Unlike post error:', error);
      // Revert the optimistic update
      if (context?.previousPost) {
        queryClient.setQueryData(['post', postId], context.previousPost);
      }
    },
    onSettled: (data, error, postId) => {
      // Always refetch to ensure consistency
      queryClient.invalidateQueries({ queryKey: ['post', postId] });
    },
  });
};

/**
 * Hook for sharing a post
 */
export const useSharePost = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (postId: string) => postService.sharePost(postId),
    onMutate: async postId => {
      haptics.medium();

      // Update post in cache optimistically
      await queryClient.cancelQueries({ queryKey: ['post', postId] });
      const previousPost = queryClient.getQueryData(['post', postId]);

      queryClient.setQueryData(['post', postId], (oldData: any) => {
        if (oldData && oldData.success && oldData.data) {
          return {
            ...oldData,
            data: {
              ...oldData.data,
              sharesCount: oldData.data.sharesCount + 1,
            },
          };
        }
        return oldData;
      });

      return { previousPost };
    },
    onError: (error, postId, context) => {
      console.error('Share post error:', error);
      // Revert the optimistic update
      if (context?.previousPost) {
        queryClient.setQueryData(['post', postId], context.previousPost);
      }
    },
    onSettled: (data, error, postId) => {
      // Always refetch to ensure consistency
      queryClient.invalidateQueries({ queryKey: ['post', postId] });
    },
  });
};
