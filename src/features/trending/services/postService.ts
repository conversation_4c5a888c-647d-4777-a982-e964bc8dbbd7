import { apolloClient } from '@/src/core/api/config';

import {
  GraphQLErrorResponse,
  ServiceResponse,
} from '../../event-registration/services/eventService';
import {
  ADD_COMMENT_MUTATION,
  LIKE_COMMENT_MUTATION,
  LIKE_POST,
  SHARE_POST,
  UNLIKE_COMMENT_MUTATION,
  UNLIKE_POST,
} from '../graphql/mutations';
import { GET_POST_COMMENTS_QUERY, GET_POST_QUERY } from '../graphql/queries';
import { Comment, Post, TrendingPost } from '../types';

// Types for service responses
export interface PostResponse {
  post: Post;
}

export interface CommentsResponse {
  comments: {
    items: Comment[];
    cursor: string | null;
    hasMore: boolean;
    totalCount: number;
  };
}

export interface AddCommentResponse {
  addComment: Comment;
}

export interface CommentActionResponse {
  success: boolean;
  commentId: string;
}

// Post Service Class
class PostService {
  /**
   * Fetch a post by ID
   */
  async getPost(postId: string): Promise<ServiceResponse<Post>> {
    try {
      const { data, errors } = await apolloClient.query({
        query: GET_POST_QUERY,
        variables: { postId },
        fetchPolicy: 'network-only',
      });

      if (data?.post) {
        return {
          success: true,
          data: data.post,
        };
      }

      return {
        success: false,
        errors: errors?.map(error => ({
          message: error.message,
          extensions: {
            code: error.extensions?.code || 'UNKNOWN_ERROR',
            statusCode: error.extensions?.statusCode || 500,
          },
        })) as GraphQLErrorResponse<any>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  /**
   * Fetch comments for a post
   */
  async getPostComments(
    postId: string,
    cursor?: string | null,
    limit = 20
  ): Promise<ServiceResponse<CommentsResponse>> {
    try {
      const { data, errors } = await apolloClient.query({
        query: GET_POST_COMMENTS_QUERY,
        variables: { postId, cursor, limit },
        fetchPolicy: 'network-only',
      });

      if (data?.comments) {
        return {
          success: true,
          data: data,
        };
      }

      return {
        success: false,
        errors: errors?.map(error => ({
          message: error.message,
          extensions: {
            code: error.extensions?.code || 'UNKNOWN_ERROR',
            statusCode: error.extensions?.statusCode || 500,
          },
        })) as GraphQLErrorResponse<any>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  /**
   * Add a comment to a post
   */
  async addComment(
    postId: string,
    content: string,
    parentCommentId?: string
  ): Promise<ServiceResponse<Comment>> {
    try {
      const { data, errors } = await apolloClient.mutate({
        mutation: ADD_COMMENT_MUTATION,
        variables: { postId, content, parentCommentId },
      });

      if (data?.addComment) {
        return {
          success: true,
          data: data.addComment,
        };
      }

      return {
        success: false,
        errors: errors?.map(error => ({
          message: error.message,
          extensions: {
            code: error.extensions?.code || 'UNKNOWN_ERROR',
            statusCode: error.extensions?.statusCode || 500,
          },
        })) as GraphQLErrorResponse<any>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  /**
   * Like a comment
   */
  async likeComment(commentId: string): Promise<ServiceResponse<CommentActionResponse>> {
    try {
      const { data, errors } = await apolloClient.mutate({
        mutation: LIKE_COMMENT_MUTATION,
        variables: { commentId },
      });

      if (data?.likeComment?.success) {
        return {
          success: true,
          data: data.likeComment,
        };
      }

      return {
        success: false,
        errors: errors?.map(error => ({
          message: error.message,
          extensions: {
            code: error.extensions?.code || 'UNKNOWN_ERROR',
            statusCode: error.extensions?.statusCode || 500,
          },
        })) as GraphQLErrorResponse<any>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  /**
   * Unlike a comment
   */
  async unlikeComment(commentId: string): Promise<ServiceResponse<CommentActionResponse>> {
    try {
      const { data, errors } = await apolloClient.mutate({
        mutation: UNLIKE_COMMENT_MUTATION,
        variables: { commentId },
      });

      if (data?.unlikeComment?.success) {
        return {
          success: true,
          data: data.unlikeComment,
        };
      }

      return {
        success: false,
        errors: errors?.map(error => ({
          message: error.message,
          extensions: {
            code: error.extensions?.code || 'UNKNOWN_ERROR',
            statusCode: error.extensions?.statusCode || 500,
          },
        })) as GraphQLErrorResponse<any>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  /**
   * Like a post
   */
  async likePost(postId: string): Promise<ServiceResponse<boolean>> {
    try {
      const { data, errors } = await apolloClient.mutate({
        mutation: LIKE_POST,
        variables: { postId },
      });

      if (data?.likePost) {
        return {
          success: true,
          data: data.likePost,
        };
      }

      return {
        success: false,
        errors: errors?.map(error => ({
          message: error.message,
          extensions: {
            code: error.extensions?.code || 'UNKNOWN_ERROR',
            statusCode: error.extensions?.statusCode || 500,
          },
        })) as GraphQLErrorResponse<any>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  /**
   * Unlike a post
   */
  async unlikePost(postId: string): Promise<ServiceResponse<boolean>> {
    try {
      const { data, errors } = await apolloClient.mutate({
        mutation: UNLIKE_POST,
        variables: { postId },
      });

      if (data?.unlikePost) {
        return {
          success: true,
          data: data.unlikePost,
        };
      }

      return {
        success: false,
        errors: errors?.map(error => ({
          message: error.message,
          extensions: {
            code: error.extensions?.code || 'UNKNOWN_ERROR',
            statusCode: error.extensions?.statusCode || 500,
          },
        })) as GraphQLErrorResponse<any>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  /**
   * Share a post
   */
  async sharePost(postId: string): Promise<ServiceResponse<boolean>> {
    try {
      const { data, errors } = await apolloClient.mutate({
        mutation: SHARE_POST,
        variables: { postId },
      });

      if (data?.sharePost) {
        return {
          success: true,
          data: data.sharePost,
        };
      }

      return {
        success: false,
        errors: errors?.map(error => ({
          message: error.message,
          extensions: {
            code: error.extensions?.code || 'UNKNOWN_ERROR',
            statusCode: error.extensions?.statusCode || 500,
          },
        })) as GraphQLErrorResponse<any>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }
}

// Export singleton instance
export const postService = new PostService();
