import { apolloClient } from '@/src/core/api/config';

import {
  GraphQLErrorResponse,
  ServiceResponse,
} from '../../event-registration/services/eventService';
import {
  ADD_PARTY_HOST_MUTATION,
  ADD_PARTY_IMAGE_MUTATION,
  ATTEND_PARTY_MUTATION,
  COMPLETE_PARTY_IMAGE_UPLOAD_MUTATION,
  CREATE_PARTY_MUTATION,
  DELETE_PARTY_MUTATION,
  GENERATE_PARTY_IMAGE_PRESIGNED_URL_MUTATION,
  INVITE_TO_PARTY_MUTATION,
  REMOVE_PARTY_HOST_MUTATION,
  REMOVE_PARTY_IMAGE_MUTATION,
  SET_PRIMARY_PARTY_IMAGE_MUTATION,
  TRACK_PARTY_INTERACTION_MUTATION,
  UPDATE_PARTY_CONTENT_MUTATION,
  UPDATE_PARTY_HOST_ROLE_MUTATION,
  UPDATE_PARTY_IMAGE_ORDER_MUTATION,
  UPDATE_PARTY_MUTATION,
  UPDATE_PARTY_STATUS_MUTATION,
} from '../graphql/mutations';
import {
  GET_MY_PARTIES_QUERY,
  GET_MY_PARTY_INVITATIONS_QUERY,
  GET_PARTIES_BY_CATEGORY_QUERY,
  GET_PARTIES_BY_DATE_QUERY,
  GET_PARTIES_BY_LOCATION_QUERY,
  GET_PARTIES_QUERY,
  GET_PARTY_ANALYTICS_QUERY,
  GET_PARTY_ATTENDEES_QUERY,
  GET_PARTY_COMMENTS_QUERY,
  GET_PARTY_ENGAGEMENT_QUERY,
  GET_PARTY_HOSTS_QUERY,
  GET_PARTY_IMAGES_QUERY,
  GET_PARTY_POSTS_QUERY,
  GET_PARTY_QUERY,
  GET_PARTY_REVIEWS_QUERY,
  GET_PRIMARY_PARTY_IMAGE_QUERY,
  SEARCH_PARTIES_QUERY,
} from '../graphql/queries';
import type {
  AttendanceStatus,
  CreatePartyInput,
  InvitationStatus,
  PaginationInput,
  Party,
  PartyAnalytics,
  PartyAttendee,
  PartyFiltersInput,
  PartyFormData,
  PartyHostRole,
  PartyInvitation,
  PartyListResponse,
  PartySearchInput,
  PartySearchResponse,
  PartyStatus,
  UpdatePartyInput,
} from '../types/party.types';

// Party Service Class
class PartyService {
  async createParty(data: PartyFormData): Promise<ServiceResponse<Party>> {
    try {
      const input: CreatePartyInput = {
        name: data.name,
        title: data.title,
        description: data.description,
        type: data.type,
        eventType: data.eventType,
        startDate: data.startDate,
        endDate: data.endDate,
        startTime: data.startTime,
        endTime: data.endTime,
        maxGuests: data.maxGuests,
        venueId: data.venueId,
        location: data.location,
        isPrivate: data.isPrivate,
        ageRestriction: data.ageRestriction,
        tags: data.tags,
        category: data.category,
        status: data.status,
        visibility: data.visibility,
        isFree: data.isFree,
        ticketURL: data.ticketURL,
        musicGenres: data.musicGenres,
        dressCodes: data.dressCodes,
      };

      const { data: responseData, errors } = await apolloClient.mutate({
        mutation: CREATE_PARTY_MUTATION,
        variables: { input },
      });

      if (responseData?.createParty) {
        return {
          success: true,
          data: responseData.createParty,
        };
      }

      return {
        success: false,
        errors: errors?.map(error => ({
          message: error.message,
          extensions: {
            code: error.extensions?.code || 'UNKNOWN_ERROR',
            statusCode: error.extensions?.statusCode || 500,
          },
        })) as GraphQLErrorResponse<any>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  async updateParty(
    partyId: string,
    data: Partial<PartyFormData>
  ): Promise<ServiceResponse<Party>> {
    try {
      const input: UpdatePartyInput = {
        ...(data.name && { name: data.name }),
        ...(data.title && { title: data.title }),
        ...(data.description && { description: data.description }),
        ...(data.type && { type: data.type }),
        ...(data.eventType && { eventType: data.eventType }),
        ...(data.startDate && { startDate: data.startDate }),
        ...(data.endDate && { endDate: data.endDate }),
        ...(data.startTime && { startTime: data.startTime }),
        ...(data.endTime && { endTime: data.endTime }),
        ...(data.maxGuests !== undefined && { maxGuests: data.maxGuests }),
        ...(data.venueId && { venueId: data.venueId }),
        ...(data.location && { location: data.location }),
        ...(data.isPrivate !== undefined && { isPrivate: data.isPrivate }),
        ...(data.ageRestriction !== undefined && { ageRestriction: data.ageRestriction }),
        ...(data.tags && { tags: data.tags }),
        ...(data.category && { category: data.category }),
        ...(data.status && { status: data.status }),
        ...(data.visibility && { visibility: data.visibility }),
        ...(data.isFree !== undefined && { isFree: data.isFree }),
        ...(data.ticketURL && { ticketURL: data.ticketURL }),
        ...(data.musicGenres && { musicGenres: data.musicGenres }),
        ...(data.dressCodes && { dressCodes: data.dressCodes }),
      };

      const { data: responseData, errors } = await apolloClient.mutate({
        mutation: UPDATE_PARTY_MUTATION,
        variables: { id: partyId, input },
      });

      if (responseData?.updateParty) {
        return {
          success: true,
          data: responseData.updateParty,
        };
      }

      return {
        success: false,
        errors: errors?.map(error => ({
          message: error.message,
          extensions: {
            code: error.extensions?.code || 'UNKNOWN_ERROR',
            statusCode: error.extensions?.statusCode || 500,
          },
        })) as GraphQLErrorResponse<any>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  async getParty(partyId: string): Promise<ServiceResponse<Party>> {
    try {
      const { data: responseData, errors } = await apolloClient.query({
        query: GET_PARTY_QUERY,
        variables: { id: partyId },
        fetchPolicy: 'cache-first',
      });

      if (responseData?.party) {
        return {
          success: true,
          data: responseData.party,
        };
      }

      return {
        success: false,
        errors: [
          {
            message: 'Party not found',
            extensions: {
              code: 'PARTY_NOT_FOUND',
              statusCode: 404,
            },
          },
        ],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  async deleteParty(partyId: string): Promise<ServiceResponse<boolean>> {
    try {
      const { data: responseData, errors } = await apolloClient.mutate({
        mutation: DELETE_PARTY_MUTATION,
        variables: { id: partyId },
      });

      if (responseData?.deleteParty) {
        return {
          success: true,
          data: responseData.deleteParty,
        };
      }

      return {
        success: false,
        errors: errors?.map(error => ({
          message: error.message,
          extensions: {
            code: error.extensions?.code || 'UNKNOWN_ERROR',
            statusCode: error.extensions?.statusCode || 500,
          },
        })) as GraphQLErrorResponse<any>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  async getParties(
    filters?: PartyFiltersInput,
    pagination?: PaginationInput
  ): Promise<ServiceResponse<PartyListResponse>> {
    try {
      const { data: responseData, errors } = await apolloClient.query({
        query: GET_PARTIES_QUERY,
        variables: { filters, pagination },
        fetchPolicy: 'cache-first',
      });

      if (responseData?.parties) {
        return {
          success: true,
          data: responseData.parties,
        };
      }

      return {
        success: false,
        errors: [
          {
            message: 'Failed to fetch parties',
            extensions: {
              code: 'PARTIES_FETCH_ERROR',
              statusCode: 500,
            },
          },
        ],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  async searchParties(input: PartySearchInput): Promise<ServiceResponse<PartySearchResponse>> {
    try {
      const { data: responseData, errors } = await apolloClient.query({
        query: SEARCH_PARTIES_QUERY,
        variables: { input },
        fetchPolicy: 'cache-first',
      });

      if (responseData?.searchParties) {
        return {
          success: true,
          data: responseData.searchParties,
        };
      }

      return {
        success: false,
        errors: [
          {
            message: 'Failed to search parties',
            extensions: {
              code: 'PARTIES_SEARCH_ERROR',
              statusCode: 500,
            },
          },
        ],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  async getMyParties(
    status?: PartyStatus,
    pagination?: PaginationInput
  ): Promise<ServiceResponse<PartyListResponse>> {
    try {
      const { data: responseData, errors } = await apolloClient.query({
        query: GET_MY_PARTIES_QUERY,
        variables: { status, pagination },
        fetchPolicy: 'cache-first',
      });

      if (responseData?.myParties) {
        return {
          success: true,
          data: responseData.myParties,
        };
      }

      return {
        success: false,
        errors: [
          {
            message: 'Failed to fetch your parties',
            extensions: {
              code: 'MY_PARTIES_FETCH_ERROR',
              statusCode: 500,
            },
          },
        ],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  async attendParty(
    partyId: string,
    status: AttendanceStatus,
    note?: string
  ): Promise<ServiceResponse<PartyAttendee>> {
    try {
      const { data: responseData, errors } = await apolloClient.mutate({
        mutation: ATTEND_PARTY_MUTATION,
        variables: { partyId, status, note },
      });

      if (responseData?.attendParty) {
        return {
          success: true,
          data: responseData.attendParty,
        };
      }

      return {
        success: false,
        errors: errors?.map(error => ({
          message: error.message,
          extensions: {
            code: error.extensions?.code || 'UNKNOWN_ERROR',
            statusCode: error.extensions?.statusCode || 500,
          },
        })) as GraphQLErrorResponse<any>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  async inviteToParty(
    partyId: string,
    userIds: string[],
    message?: string
  ): Promise<ServiceResponse<PartyInvitation[]>> {
    try {
      const { data: responseData, errors } = await apolloClient.mutate({
        mutation: INVITE_TO_PARTY_MUTATION,
        variables: { partyId, userIds, message },
      });

      if (responseData?.inviteToParty) {
        return {
          success: true,
          data: responseData.inviteToParty,
        };
      }

      return {
        success: false,
        errors: errors?.map(error => ({
          message: error.message,
          extensions: {
            code: error.extensions?.code || 'UNKNOWN_ERROR',
            statusCode: error.extensions?.statusCode || 500,
          },
        })) as GraphQLErrorResponse<any>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  async addPartyHost(
    partyId: string,
    userId: string,
    role: PartyHostRole
  ): Promise<ServiceResponse<Party>> {
    try {
      const { data: responseData, errors } = await apolloClient.mutate({
        mutation: ADD_PARTY_HOST_MUTATION,
        variables: { partyId, userId, role },
      });

      if (responseData?.addPartyHost) {
        return {
          success: true,
          data: responseData.addPartyHost,
        };
      }

      return {
        success: false,
        errors: errors?.map(error => ({
          message: error.message,
          extensions: {
            code: error.extensions?.code || 'UNKNOWN_ERROR',
            statusCode: error.extensions?.statusCode || 500,
          },
        })) as GraphQLErrorResponse<any>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  async updatePartyStatus(partyId: string, status: PartyStatus): Promise<ServiceResponse<Party>> {
    try {
      const { data: responseData, errors } = await apolloClient.mutate({
        mutation: UPDATE_PARTY_STATUS_MUTATION,
        variables: { id: partyId, status },
      });

      if (responseData?.updatePartyStatus) {
        return {
          success: true,
          data: responseData.updatePartyStatus,
        };
      }

      return {
        success: false,
        errors: errors?.map(error => ({
          message: error.message,
          extensions: {
            code: error.extensions?.code || 'UNKNOWN_ERROR',
            statusCode: error.extensions?.statusCode || 500,
          },
        })) as GraphQLErrorResponse<any>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  async getPartyAttendees(
    partyId: string,
    status?: AttendanceStatus,
    pagination?: PaginationInput
  ): Promise<
    ServiceResponse<{ attendees: PartyAttendee[]; totalCount: number; hasNextPage: boolean }>
  > {
    try {
      const { data: responseData, errors } = await apolloClient.query({
        query: GET_PARTY_ATTENDEES_QUERY,
        variables: { partyId, status, pagination },
        fetchPolicy: 'cache-first',
      });

      if (responseData?.partyAttendees) {
        return {
          success: true,
          data: responseData.partyAttendees,
        };
      }

      return {
        success: false,
        errors: [
          {
            message: 'Failed to fetch party attendees',
            extensions: {
              code: 'PARTY_ATTENDEES_FETCH_ERROR',
              statusCode: 500,
            },
          },
        ],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  async getPartyAnalytics(
    partyId: string,
    timeRange?: string
  ): Promise<ServiceResponse<PartyAnalytics>> {
    try {
      const { data: responseData, errors } = await apolloClient.query({
        query: GET_PARTY_ANALYTICS_QUERY,
        variables: { partyId, timeRange },
        fetchPolicy: 'cache-first',
      });

      if (responseData?.partyAnalytics) {
        return {
          success: true,
          data: responseData.partyAnalytics,
        };
      }

      return {
        success: false,
        errors: [
          {
            message: 'Failed to fetch party analytics',
            extensions: {
              code: 'PARTY_ANALYTICS_FETCH_ERROR',
              statusCode: 500,
            },
          },
        ],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  async addPartyImage(
    partyId: string,
    imageUrl: string,
    isPrimary?: boolean
  ): Promise<ServiceResponse<any>> {
    try {
      const { data: responseData, errors } = await apolloClient.mutate({
        mutation: ADD_PARTY_IMAGE_MUTATION,
        variables: { partyId, imageUrl, isPrimary },
      });

      if (responseData?.addPartyImage) {
        return {
          success: true,
          data: responseData.addPartyImage,
        };
      }

      return {
        success: false,
        errors: errors?.map(error => ({
          message: error.message,
          extensions: {
            code: error.extensions?.code || 'UNKNOWN_ERROR',
            statusCode: error.extensions?.statusCode || 500,
          },
        })) as GraphQLErrorResponse<any>[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'An unexpected error occurred',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }
}

// Export service instance
export const partyService = new PartyService();

// Export individual functions for backward compatibility
export const createParty = (data: PartyFormData) => partyService.createParty(data);
export const updateParty = (partyId: string, data: Partial<PartyFormData>) =>
  partyService.updateParty(partyId, data);
export const getParty = (partyId: string) => partyService.getParty(partyId);
export const deleteParty = (partyId: string) => partyService.deleteParty(partyId);
