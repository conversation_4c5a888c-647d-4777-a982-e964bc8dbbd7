// API Types based on actual GraphQL schema
export interface Party {
  id: string;
  name: string;
  title?: string;
  description?: string;
  type: PartyType;
  eventType: EventType;
  status: PartyStatus;
  visibility: PartyVisibility;
  startDate: string;
  endDate: string;
  startTime?: string;
  endTime?: string;
  maxGuests?: number;
  venueId?: string;
  venue?: Venue;
  location?: Location;
  isPrivate?: boolean;
  ageRestriction?: number;
  tags?: string[];
  category?: string;
  isFree?: boolean;
  ticketURL?: string;
  musicGenres?: string[];
  dressCodes?: string[];
  hostId: string;
  hostName?: string;
  hosts?: PartyHost[];
  attendeesCount?: {
    interested: number;
    going: number;
    total: number;
  };
  images?: string[];
  primaryImage?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Location {
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  latitude?: number;
  longitude?: number;
  formattedAddress?: string;
}

export interface LocationInput {
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  latitude?: number;
  longitude?: number;
  formattedAddress?: string;
}

export interface Venue {
  id: string;
  name: string;
  address: string;
  city?: string;
  state?: string;
  country?: string;
  latitude?: number;
  longitude?: number;
}

export interface PartyHost {
  id: string;
  userId: string;
  userName: string;
  role: PartyHostRole;
  permissions: string[];
}

export interface CreatePartyInput {
  name: string;
  title?: string;
  description?: string;
  type: PartyType;
  eventType?: EventType;
  startDate: string;
  endDate: string;
  startTime?: string;
  endTime?: string;
  maxGuests?: number;
  venueId?: string;
  location?: LocationInput;
  isPrivate?: boolean;
  ageRestriction?: number;
  tags?: string[];
  category?: string;
  status: PartyStatus;
  visibility: PartyVisibility;
  isFree?: boolean;
  ticketURL?: string;
  musicGenres?: string[];
  dressCodes?: string[];
}

export interface UpdatePartyInput {
  name?: string;
  title?: string;
  description?: string;
  type?: PartyType;
  eventType?: EventType;
  startDate?: string;
  endDate?: string;
  startTime?: string;
  endTime?: string;
  maxGuests?: number;
  venueId?: string;
  location?: LocationInput;
  isPrivate?: boolean;
  ageRestriction?: number;
  tags?: string[];
  category?: string;
  status?: PartyStatus;
  visibility?: PartyVisibility;
  isFree?: boolean;
  ticketURL?: string;
  musicGenres?: string[];
  dressCodes?: string[];
}

export interface PartyFiltersInput {
  type?: PartyType[];
  eventType?: EventType[];
  status?: PartyStatus[];
  visibility?: PartyVisibility[];
  startDateFrom?: string;
  startDateTo?: string;
  location?: LocationInput;
  radius?: number;
  tags?: string[];
  category?: string;
  isFree?: boolean;
  ageRestriction?: number;
  musicGenres?: string[];
}

export interface PartySearchInput {
  query?: string;
  filters?: PartyFiltersInput;
  pagination?: PaginationInput;
}

export interface PaginationInput {
  limit?: number;
  offset?: number;
  cursor?: string;
}

// Enums
export enum PartyType {
  PRIVATE = 'PRIVATE',
  PUBLIC = 'PUBLIC',
  BUSINESS = 'BUSINESS',
  TICKETED = 'TICKETED',
}

export enum EventType {
  PARTY = 'PARTY',
  CONCERT = 'CONCERT',
  FESTIVAL = 'FESTIVAL',
  CLUB_NIGHT = 'CLUB_NIGHT',
  BAR_CRAWL = 'BAR_CRAWL',
  SOCIAL = 'SOCIAL',
  NETWORKING = 'NETWORKING',
  OTHER = 'OTHER',
}

export enum PartyStatus {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
  CANCELLED = 'CANCELLED',
  COMPLETED = 'COMPLETED',
  SUSPENDED = 'SUSPENDED',
}

export enum PartyVisibility {
  PUBLIC = 'PUBLIC',
  PRIVATE = 'PRIVATE',
  FRIENDS_ONLY = 'FRIENDS_ONLY',
  INVITED_ONLY = 'INVITED_ONLY',
}

export enum PartyHostRole {
  OWNER = 'OWNER',
  CO_HOST = 'CO_HOST',
  MODERATOR = 'MODERATOR',
}

export enum AttendanceStatus {
  INTERESTED = 'INTERESTED',
  GOING = 'GOING',
  NOT_GOING = 'NOT_GOING',
  MAYBE = 'MAYBE',
}

// Form Data Types
export interface PartyFormData {
  // Basic Details
  name: string;
  title?: string;
  description?: string;
  type: PartyType;
  eventType?: EventType;

  // Date & Time
  startDate: string;
  endDate: string;
  startTime?: string;
  endTime?: string;

  // Location
  venueId?: string;
  location?: LocationInput;

  // Settings
  maxGuests?: number;
  isPrivate?: boolean;
  ageRestriction?: number;
  visibility: PartyVisibility;
  status: PartyStatus;

  // Content
  tags?: string[];
  category?: string;
  musicGenres?: string[];
  dressCodes?: string[];

  // Tickets
  isFree?: boolean;
  ticketURL?: string;

  // Media
  images?: string[];
  primaryImage?: string;
}

// Navigation Types
export type PartyManagementStackParamList = {
  PartyBasicDetails: undefined;
  PartyDateTime: undefined;
  PartyLocation: undefined;
  PartySettings: undefined;
  PartyContent: undefined;
  PartyPreview: undefined;
  PartySuccess: { partyId: string };
  PartyList: undefined;
  PartyDetails: { partyId: string };
  EditParty: { partyId: string };
};

// Store Types
export interface PartyManagementState {
  // Form state
  currentDraft: PartyFormData | null;
  currentStep: number;
  totalSteps: number;
  isSubmitting: boolean;

  // Cache
  recentParties: Party[];
  drafts: PartyFormData[];

  // UI state
  isLocationModalOpen: boolean;
  selectedVenue: Venue | null;

  // Actions
  setCurrentDraft: (draft: PartyFormData | null) => void;
  updateDraft: (updates: Partial<PartyFormData>) => void;
  setCurrentStep: (step: number) => void;
  saveDraft: () => void;
  clearDraft: () => void;
  resetForm: () => void;
}

// Response Types
export interface PartyListResponse {
  parties: Party[];
  totalCount: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  cursor?: string;
}

export interface PartySearchResponse {
  parties: Party[];
  totalCount: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  filters: PartyFiltersInput;
  suggestions?: string[];
}

export interface PartyAttendee {
  id: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  status: AttendanceStatus;
  joinedAt: string;
  note?: string;
}

export interface PartyInvitation {
  id: string;
  partyId: string;
  fromUserId: string;
  toUserId: string;
  status: InvitationStatus;
  message?: string;
  sentAt: string;
  respondedAt?: string;
}

export enum InvitationStatus {
  PENDING = 'PENDING',
  ACCEPTED = 'ACCEPTED',
  DECLINED = 'DECLINED',
  CANCELLED = 'CANCELLED',
}

export interface PartyAnalytics {
  partyId: string;
  views: number;
  interests: number;
  attendees: number;
  shares: number;
  clickThroughs: number;
  conversionRate: number;
  topReferrers: string[];
  demographics: {
    ageGroups: Record<string, number>;
    locations: Record<string, number>;
    interests: Record<string, number>;
  };
}
