import { MMKV } from 'react-native-mmkv';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

import {
  Party,
  PartyFormData,
  PartyManagementState,
  PartyStatus,
  PartyType,
  PartyVisibility,
  Venue,
} from '../types/party.types';

// Create MMKV instance for party management
const partyStorage = new MMKV({
  id: 'party-management-storage',
  encryptionKey: 'party-management-encryption-key',
});

// MMKV storage interface for Zustand
const storage = {
  getItem: (name: string) => {
    const value = partyStorage.getString(name);
    return value ? JSON.parse(value) : null;
  },
  setItem: (name: string, value: any) => {
    partyStorage.set(name, JSON.stringify(value));
  },
  removeItem: (name: string) => {
    partyStorage.delete(name);
  },
};

// Default party form data
const getDefaultFormData = (): PartyFormData => ({
  name: '',
  type: PartyType.PRIVATE,
  startDate: '',
  endDate: '',
  status: PartyStatus.DRAFT,
  visibility: PartyVisibility.PRIVATE,
});

export const usePartyManagementStore = create<PartyManagementState>()(
  persist(
    (set, get) => ({
      // Form state
      currentDraft: null,
      currentStep: 0,
      totalSteps: 6, // BasicDetails, DateTime, Location, Settings, Content, Preview
      isSubmitting: false,

      // Cache
      recentParties: [],
      drafts: [],

      // UI state
      isLocationModalOpen: false,
      selectedVenue: null,

      // Actions
      setCurrentDraft: draft => {
        set({ currentDraft: draft });
      },

      updateDraft: updates => {
        const { currentDraft } = get();
        const updatedDraft = currentDraft
          ? { ...currentDraft, ...updates }
          : { ...getDefaultFormData(), ...updates };

        set({ currentDraft: updatedDraft });
      },

      setCurrentStep: step => {
        set({ currentStep: Math.max(0, Math.min(step, get().totalSteps - 1)) });
      },

      saveDraft: () => {
        const { currentDraft, drafts } = get();
        if (!currentDraft) return;

        const existingDraftIndex = drafts.findIndex(draft => draft.name === currentDraft.name);

        if (existingDraftIndex >= 0) {
          // Update existing draft
          const updatedDrafts = [...drafts];
          updatedDrafts[existingDraftIndex] = currentDraft;
          set({ drafts: updatedDrafts });
        } else {
          // Add new draft
          set({ drafts: [...drafts, currentDraft] });
        }
      },

      clearDraft: () => {
        set({
          currentDraft: null,
          currentStep: 0,
          isSubmitting: false,
        });
      },

      resetForm: () => {
        set({
          currentDraft: getDefaultFormData(),
          currentStep: 0,
          isSubmitting: false,
          selectedVenue: null,
        });
      },
    }),
    {
      name: 'party-management-store',
      storage,
      partialize: state => ({
        // Only persist certain parts of the state
        currentDraft: state.currentDraft,
        currentStep: state.currentStep,
        recentParties: state.recentParties.slice(0, 10), // Limit recent parties
        drafts: state.drafts.slice(0, 5), // Limit drafts
      }),
    }
  )
);

// Selectors for common use cases
export const usePartyFormData = () => usePartyManagementStore(state => state.currentDraft);
export const usePartyFormStep = () => usePartyManagementStore(state => state.currentStep);
export const usePartyDrafts = () => usePartyManagementStore(state => state.drafts);
export const useRecentParties = () => usePartyManagementStore(state => state.recentParties);
export const useSelectedVenue = () => usePartyManagementStore(state => state.selectedVenue);

// Action selectors
export const usePartyFormActions = () =>
  usePartyManagementStore(state => ({
    updateDraft: state.updateDraft,
    setCurrentStep: state.setCurrentStep,
    saveDraft: state.saveDraft,
    clearDraft: state.clearDraft,
    resetForm: state.resetForm,
  }));

// Helper hooks
export const useIsPartyFormComplete = () => {
  return usePartyManagementStore(state => {
    const draft = state.currentDraft;
    if (!draft) return false;

    return !!(
      draft.name &&
      draft.type &&
      draft.startDate &&
      draft.endDate &&
      draft.status &&
      draft.visibility
    );
  });
};

export const usePartyFormProgress = () => {
  return usePartyManagementStore(state => {
    const { currentStep, totalSteps } = state;
    return {
      currentStep,
      totalSteps,
      progress: (currentStep / (totalSteps - 1)) * 100,
      isFirstStep: currentStep === 0,
      isLastStep: currentStep === totalSteps - 1,
    };
  });
};
