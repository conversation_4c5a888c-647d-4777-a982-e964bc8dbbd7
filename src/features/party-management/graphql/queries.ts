import { gql } from '@apollo/client';

import {
  PARTY_ANALYTICS_FRAGMENT,
  PARTY_ATTENDEE_FRAGMENT,
  PARTY_BASIC_FRAGMENT,
  PARTY_FULL_FRAGMENT,
  PARTY_INVITATION_FRAGMENT,
} from './fragments';

export const GET_PARTY_QUERY = gql`
  ${PARTY_FULL_FRAGMENT}

  query GetParty($id: UUID!) {
    party(id: $id) {
      ...PartyFullFields
    }
  }
`;

export const GET_PARTIES_QUERY = gql`
  ${PARTY_BASIC_FRAGMENT}

  query GetParties($filters: PartyFiltersInput, $pagination: PaginationInput) {
    parties(filters: $filters, pagination: $pagination) {
      parties {
        ...PartyBasicFields
      }
      totalCount
      hasNextPage
      hasPreviousPage
      cursor
    }
  }
`;

export const SEARCH_PARTIES_QUERY = gql`
  ${PARTY_BASIC_FRAGMENT}

  query SearchParties($input: PartySearchInput!) {
    searchParties(input: $input) {
      parties {
        ...PartyBasicFields
      }
      totalCount
      hasNextPage
      hasPreviousPage
      filters {
        type
        eventType
        status
        visibility
        startDateFrom
        startDateTo
        tags
        category
        isFree
        ageRestriction
        musicGenres
      }
      suggestions
    }
  }
`;

export const GET_PARTIES_BY_LOCATION_QUERY = gql`
  ${PARTY_BASIC_FRAGMENT}

  query GetPartiesByLocation(
    $location: LocationFilterInput!
    $filters: PartyFiltersInput
    $pagination: PaginationInput
  ) {
    partiesByLocation(location: $location, filters: $filters, pagination: $pagination) {
      parties {
        ...PartyBasicFields
        distance
      }
      totalCount
      hasNextPage
      hasPreviousPage
    }
  }
`;

export const GET_PARTIES_BY_CATEGORY_QUERY = gql`
  ${PARTY_BASIC_FRAGMENT}

  query GetPartiesByCategory($category: String!, $pagination: PaginationInput) {
    partiesByCategory(category: $category, pagination: $pagination) {
      parties {
        ...PartyBasicFields
      }
      totalCount
      hasNextPage
      hasPreviousPage
    }
  }
`;

export const GET_PARTIES_BY_DATE_QUERY = gql`
  ${PARTY_BASIC_FRAGMENT}

  query GetPartiesByDate(
    $startDate: Time!
    $endDate: Time
    $filters: PartyFiltersInput
    $pagination: PaginationInput
  ) {
    partiesByDate(
      startDate: $startDate
      endDate: $endDate
      filters: $filters
      pagination: $pagination
    ) {
      parties {
        ...PartyBasicFields
      }
      totalCount
      hasNextPage
      hasPreviousPage
    }
  }
`;

export const GET_MY_PARTIES_QUERY = gql`
  ${PARTY_BASIC_FRAGMENT}

  query GetMyParties($status: PartyStatus, $pagination: PaginationInput) {
    myParties(status: $status, pagination: $pagination) {
      parties {
        ...PartyBasicFields
      }
      totalCount
      hasNextPage
      hasPreviousPage
    }
  }
`;

export const GET_MY_PARTY_INVITATIONS_QUERY = gql`
  ${PARTY_INVITATION_FRAGMENT}

  query GetMyPartyInvitations($status: InvitationStatus, $pagination: PaginationInput) {
    myPartyInvitations(status: $status, pagination: $pagination) {
      invitations {
        ...PartyInvitationFields
        party {
          id
          name
          startDate
          primaryImage
        }
      }
      totalCount
      hasNextPage
      hasPreviousPage
    }
  }
`;

export const GET_PARTY_ATTENDEES_QUERY = gql`
  ${PARTY_ATTENDEE_FRAGMENT}

  query GetPartyAttendees(
    $partyId: UUID!
    $status: AttendanceStatus
    $pagination: PaginationInput
  ) {
    partyAttendees(partyId: $partyId, status: $status, pagination: $pagination) {
      attendees {
        ...PartyAttendeeFields
      }
      totalCount
      hasNextPage
      hasPreviousPage
    }
  }
`;

export const GET_PARTY_HOSTS_QUERY = gql`
  query GetPartyHosts($partyId: UUID!) {
    partyHosts(partyId: $partyId) {
      id
      userId
      userName
      userAvatar
      role
      permissions
      addedAt
    }
  }
`;

export const GET_PARTY_ANALYTICS_QUERY = gql`
  ${PARTY_ANALYTICS_FRAGMENT}

  query GetPartyAnalytics($partyId: UUID!, $timeRange: AnalyticsTimeRange) {
    partyAnalytics(partyId: $partyId, timeRange: $timeRange) {
      ...PartyAnalyticsFields
    }
  }
`;

export const GET_PARTY_ENGAGEMENT_QUERY = gql`
  query GetPartyEngagement($partyId: UUID!) {
    partyEngagement(partyId: $partyId) {
      partyId
      views
      likes
      shares
      comments
      interests
      attendees
      viewsOverTime {
        date
        count
      }
      topInteractions {
        type
        count
      }
    }
  }
`;

export const GET_PARTY_POSTS_QUERY = gql`
  query GetPartyPosts($partyId: UUID!, $pagination: PaginationInput) {
    partyPosts(partyId: $partyId, pagination: $pagination) {
      posts {
        id
        content
        imageUrl
        authorId
        authorName
        authorAvatar
        createdAt
        likesCount
        commentsCount
        isLiked
      }
      totalCount
      hasNextPage
      hasPreviousPage
    }
  }
`;

export const GET_PARTY_POST_QUERY = gql`
  query GetPartyPost($postId: UUID!) {
    partyPost(id: $postId) {
      id
      content
      imageUrl
      authorId
      authorName
      authorAvatar
      partyId
      createdAt
      updatedAt
      likesCount
      commentsCount
      sharesCount
      isLiked
      isBookmarked
    }
  }
`;

export const GET_PARTY_COMMENTS_QUERY = gql`
  query GetPartyComments($partyId: UUID!, $pagination: PaginationInput) {
    partyComments(partyId: $partyId, pagination: $pagination) {
      comments {
        id
        content
        authorId
        authorName
        authorAvatar
        partyId
        parentId
        createdAt
        likesCount
        repliesCount
        isLiked
      }
      totalCount
      hasNextPage
      hasPreviousPage
    }
  }
`;

export const GET_PARTY_REVIEWS_QUERY = gql`
  query GetPartyReviews($partyId: UUID!, $pagination: PaginationInput) {
    partyReviews(partyId: $partyId, pagination: $pagination) {
      reviews {
        id
        rating
        comment
        authorId
        authorName
        authorAvatar
        partyId
        createdAt
        helpfulCount
        isHelpful
      }
      totalCount
      hasNextPage
      hasPreviousPage
      averageRating
    }
  }
`;

export const GET_PARTY_IMAGES_QUERY = gql`
  query GetPartyImages($partyId: UUID!) {
    partyImages(partyId: $partyId) {
      id
      partyId
      imageUrl
      isPrimary
      order
      uploadedBy
      createdAt
    }
  }
`;

export const GET_PARTY_IMAGE_QUERY = gql`
  query GetPartyImage($partyId: UUID!, $imageId: UUID!) {
    partyImage(partyId: $partyId, imageId: $imageId) {
      id
      partyId
      imageUrl
      isPrimary
      order
      uploadedBy
      createdAt
      metadata {
        width
        height
        size
        format
      }
    }
  }
`;

export const GET_PRIMARY_PARTY_IMAGE_QUERY = gql`
  query GetPrimaryPartyImage($partyId: UUID!) {
    primaryPartyImage(partyId: $partyId) {
      id
      partyId
      imageUrl
      isPrimary
      order
      uploadedBy
      createdAt
    }
  }
`;

export const GET_PARTY_IMAGE_STATS_QUERY = gql`
  query GetPartyImageStats($partyId: UUID!) {
    partyImageStats(partyId: $partyId) {
      totalImages
      primaryImageUrl
      averageSize
      totalSize
      formats {
        format
        count
      }
    }
  }
`;
