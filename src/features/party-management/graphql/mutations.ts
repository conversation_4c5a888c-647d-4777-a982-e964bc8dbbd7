import { gql } from '@apollo/client';

import {
  PARTY_ATTENDEE_FRAGMENT,
  PARTY_FULL_FRAGMENT,
  PARTY_INVITATION_FRAGMENT,
} from './fragments';

export const CREATE_PARTY_MUTATION = gql`
  ${PARTY_FULL_FRAGMENT}

  mutation CreateParty($input: CreatePartyInput!) {
    createParty(input: $input) {
      ...PartyFullFields
    }
  }
`;

export const UPDATE_PARTY_MUTATION = gql`
  ${PARTY_FULL_FRAGMENT}

  mutation UpdateParty($id: UUID!, $input: UpdatePartyInput!) {
    updateParty(id: $id, input: $input) {
      ...PartyFullFields
    }
  }
`;

export const DELETE_PARTY_MUTATION = gql`
  mutation DeleteParty($id: UUID!) {
    deleteParty(id: $id)
  }
`;

export const UPDATE_PARTY_STATUS_MUTATION = gql`
  ${PARTY_FULL_FRAGMENT}

  mutation UpdatePartyStatus($id: UUID!, $status: PartyStatus!) {
    updatePartyStatus(id: $id, status: $status) {
      ...PartyFullFields
    }
  }
`;

export const ATTEND_PARTY_MUTATION = gql`
  mutation AttendParty($partyId: UUID!, $status: AttendanceStatus!, $note: String) {
    attendParty(partyId: $partyId, status: $status, note: $note) {
      id
      status
      note
      joinedAt
    }
  }
`;

export const INVITE_TO_PARTY_MUTATION = gql`
  ${PARTY_INVITATION_FRAGMENT}

  mutation InviteToParty($partyId: UUID!, $userIds: [UUID!]!, $message: String) {
    inviteToParty(partyId: $partyId, userIds: $userIds, message: $message) {
      ...PartyInvitationFields
    }
  }
`;

export const ADD_PARTY_HOST_MUTATION = gql`
  ${PARTY_FULL_FRAGMENT}

  mutation AddPartyHost($partyId: UUID!, $userId: UUID!, $role: PartyHostRole!) {
    addPartyHost(partyId: $partyId, userId: $userId, role: $role) {
      ...PartyFullFields
    }
  }
`;

export const REMOVE_PARTY_HOST_MUTATION = gql`
  ${PARTY_FULL_FRAGMENT}

  mutation RemovePartyHost($partyId: UUID!, $userId: UUID!) {
    removePartyHost(partyId: $partyId, userId: $userId) {
      ...PartyFullFields
    }
  }
`;

export const UPDATE_PARTY_HOST_ROLE_MUTATION = gql`
  ${PARTY_FULL_FRAGMENT}

  mutation UpdatePartyHostRole($partyId: UUID!, $userId: UUID!, $role: PartyHostRole!) {
    updatePartyHostRole(partyId: $partyId, userId: $userId, role: $role) {
      ...PartyFullFields
    }
  }
`;

export const ADD_PARTY_IMAGE_MUTATION = gql`
  mutation AddPartyImage($partyId: UUID!, $imageUrl: String!, $isPrimary: Boolean) {
    addPartyImage(partyId: $partyId, imageUrl: $imageUrl, isPrimary: $isPrimary) {
      id
      partyId
      imageUrl
      isPrimary
      order
      createdAt
    }
  }
`;

export const REMOVE_PARTY_IMAGE_MUTATION = gql`
  mutation RemovePartyImage($partyId: UUID!, $imageUrl: String!) {
    removePartyImage(partyId: $partyId, imageUrl: $imageUrl)
  }
`;

export const SET_PRIMARY_PARTY_IMAGE_MUTATION = gql`
  mutation SetPrimaryPartyImage($partyId: UUID!, $imageUrl: String!) {
    setPrimaryPartyImage(partyId: $partyId, imageUrl: $imageUrl) {
      id
      partyId
      imageUrl
      isPrimary
      order
      createdAt
    }
  }
`;

export const UPDATE_PARTY_IMAGE_ORDER_MUTATION = gql`
  mutation UpdatePartyImageOrder($partyId: UUID!, $imageOrder: [String!]!) {
    updatePartyImageOrder(partyId: $partyId, imageOrder: $imageOrder) {
      id
      partyId
      imageUrl
      isPrimary
      order
      createdAt
    }
  }
`;

export const GENERATE_PARTY_IMAGE_PRESIGNED_URL_MUTATION = gql`
  mutation GeneratePartyImagePresignedURL(
    $partyId: UUID!
    $fileName: String!
    $contentType: String!
  ) {
    generatePartyImagePresignedURL(
      partyId: $partyId
      fileName: $fileName
      contentType: $contentType
    ) {
      uploadUrl
      imageUrl
      fields
    }
  }
`;

export const GENERATE_PARTY_IMAGES_PRESIGNED_URL_MUTATION = gql`
  mutation GeneratePartyImagesPresignedURL($partyId: UUID!, $files: [FileInput!]!) {
    generatePartyImagesPresignedURL(partyId: $partyId, files: $files) {
      uploadUrl
      imageUrl
      fields
    }
  }
`;

export const COMPLETE_PARTY_IMAGE_UPLOAD_MUTATION = gql`
  mutation CompletePartyImageUpload($partyId: UUID!, $imageUrl: String!) {
    completePartyImageUpload(partyId: $partyId, imageUrl: $imageUrl) {
      id
      partyId
      imageUrl
      isPrimary
      order
      createdAt
    }
  }
`;

export const UPDATE_PARTY_CONTENT_MUTATION = gql`
  ${PARTY_FULL_FRAGMENT}

  mutation UpdatePartyContent($partyId: UUID!, $input: UpdatePartyContentInput!) {
    updatePartyContent(partyId: $partyId, input: $input) {
      ...PartyFullFields
    }
  }
`;

export const TRACK_PARTY_INTERACTION_MUTATION = gql`
  mutation TrackPartyInteraction($partyId: UUID!, $type: InteractionType!, $metadata: JSON) {
    trackPartyInteraction(partyId: $partyId, type: $type, metadata: $metadata) {
      success
      interactionId
    }
  }
`;
