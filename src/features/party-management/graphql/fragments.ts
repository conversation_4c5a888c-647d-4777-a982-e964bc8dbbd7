import { gql } from '@apollo/client';

export const LOCATION_FRAGMENT = gql`
  fragment LocationFields on Location {
    address
    city
    state
    country
    postalCode
    latitude
    longitude
    formattedAddress
  }
`;

export const VENUE_FRAGMENT = gql`
  fragment VenueFields on Venue {
    id
    name
    address
    city
    state
    country
    latitude
    longitude
  }
`;

export const PARTY_HOST_FRAGMENT = gql`
  fragment PartyHostFields on PartyHost {
    id
    userId
    userName
    role
    permissions
  }
`;

export const PARTY_ATTENDEES_COUNT_FRAGMENT = gql`
  fragment PartyAttendeesCountFields on PartyAttendeesCount {
    interested
    going
    total
  }
`;

export const PARTY_BASIC_FRAGMENT = gql`
  fragment PartyBasicFields on Party {
    id
    name
    title
    description
    type
    eventType
    status
    visibility
    startDate
    endDate
    startTime
    endTime
    maxGuests
    isPrivate
    ageRestriction
    tags
    category
    isFree
    ticketURL
    musicGenres
    dressCodes
    hostId
    hostName
    primaryImage
    createdAt
    updatedAt
  }
`;

export const PARTY_FULL_FRAGMENT = gql`
  ${PARTY_BASIC_FRAGMENT}
  ${LOCATION_FRAGMENT}
  ${VENUE_FRAGMENT}
  ${PARTY_HOST_FRAGMENT}
  ${PARTY_ATTENDEES_COUNT_FRAGMENT}

  fragment PartyFullFields on Party {
    ...PartyBasicFields
    location {
      ...LocationFields
    }
    venue {
      ...VenueFields
    }
    hosts {
      ...PartyHostFields
    }
    attendeesCount {
      ...PartyAttendeesCountFields
    }
    images
    venueId
  }
`;

export const PARTY_ATTENDEE_FRAGMENT = gql`
  fragment PartyAttendeeFields on PartyAttendee {
    id
    userId
    userName
    userAvatar
    status
    joinedAt
    note
  }
`;

export const PARTY_INVITATION_FRAGMENT = gql`
  fragment PartyInvitationFields on PartyInvitation {
    id
    partyId
    fromUserId
    toUserId
    status
    message
    sentAt
    respondedAt
  }
`;

export const PARTY_ANALYTICS_FRAGMENT = gql`
  fragment PartyAnalyticsFields on PartyAnalytics {
    partyId
    views
    interests
    attendees
    shares
    clickThroughs
    conversionRate
    topReferrers
    demographics {
      ageGroups
      locations
      interests
    }
  }
`;
