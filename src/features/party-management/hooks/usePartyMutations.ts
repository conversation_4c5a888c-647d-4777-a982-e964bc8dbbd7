import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useMutation, useQueryClient } from '@tanstack/react-query';

import { toast } from '@/src/core/libs';
import { extractErrorMessage } from '@/src/shared/utils/errorUtils';

import { type Party, partyService } from '../services/partyService';
import type {
  AttendanceStatus,
  PartyFormData,
  PartyHostRole,
  PartyManagementStackParamList,
  PartyStatus,
} from '../types/party.types';

type NavigationProp = StackNavigationProp<PartyManagementStackParamList>;

export const useCreateParty = () => {
  const navigation = useNavigation<NavigationProp>();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: PartyFormData) => partyService.createParty(data),
    onSuccess: response => {
      if (response.success && response.data) {
        toast.success('Party created successfully!');

        // Invalidate related queries
        queryClient.invalidateQueries({ queryKey: ['myParties'] });
        queryClient.invalidateQueries({ queryKey: ['parties'] });

        navigation.navigate('PartySuccess', { partyId: response.data.id });
      } else {
        const errorMessage = extractErrorMessage(response.errors, 'Failed to create party');
        toast.error(errorMessage);
      }
    },
    onError: error => {
      console.error('Create party error:', error);
      const errorMessage = extractErrorMessage(error, 'An unexpected error occurred');
      toast.error(errorMessage);
    },
  });
};

export const useUpdateParty = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ partyId, data }: { partyId: string; data: Partial<PartyFormData> }) =>
      partyService.updateParty(partyId, data),
    onSuccess: (response, variables) => {
      if (response.success) {
        toast.success('Party updated successfully!');

        // Invalidate specific party and list queries
        queryClient.invalidateQueries({ queryKey: ['party', variables.partyId] });
        queryClient.invalidateQueries({ queryKey: ['myParties'] });
        queryClient.invalidateQueries({ queryKey: ['parties'] });
      } else {
        const errorMessage = extractErrorMessage(response.errors, 'Failed to update party');
        toast.error(errorMessage);
      }
    },
    onError: error => {
      console.error('Update party error:', error);
      const errorMessage = extractErrorMessage(error, 'An unexpected error occurred');
      toast.error(errorMessage);
    },
  });
};

export const useDeleteParty = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (partyId: string) => partyService.deleteParty(partyId),
    onSuccess: response => {
      if (response.success) {
        toast.success('Party deleted successfully!');

        // Invalidate party list queries
        queryClient.invalidateQueries({ queryKey: ['myParties'] });
        queryClient.invalidateQueries({ queryKey: ['parties'] });
      } else {
        const errorMessage = extractErrorMessage(response.errors, 'Failed to delete party');
        toast.error(errorMessage);
      }
    },
    onError: error => {
      console.error('Delete party error:', error);
      const errorMessage = extractErrorMessage(error, 'An unexpected error occurred');
      toast.error(errorMessage);
    },
  });
};

export const useUpdatePartyStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ partyId, status }: { partyId: string; status: PartyStatus }) =>
      partyService.updatePartyStatus(partyId, status),
    onSuccess: (response, variables) => {
      if (response.success) {
        const statusMessage = {
          DRAFT: 'Party saved as draft',
          PUBLISHED: 'Party published successfully!',
          CANCELLED: 'Party cancelled',
          COMPLETED: 'Party marked as completed',
          SUSPENDED: 'Party suspended',
        }[variables.status];

        toast.success(statusMessage);

        // Invalidate related queries
        queryClient.invalidateQueries({ queryKey: ['party', variables.partyId] });
        queryClient.invalidateQueries({ queryKey: ['myParties'] });
        queryClient.invalidateQueries({ queryKey: ['parties'] });
      } else {
        const errorMessage = extractErrorMessage(response.errors, 'Failed to update party status');
        toast.error(errorMessage);
      }
    },
    onError: error => {
      console.error('Update party status error:', error);
      const errorMessage = extractErrorMessage(error, 'An unexpected error occurred');
      toast.error(errorMessage);
    },
  });
};

export const useAttendParty = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      partyId,
      status,
      note,
    }: {
      partyId: string;
      status: AttendanceStatus;
      note?: string;
    }) => partyService.attendParty(partyId, status, note),
    onSuccess: (response, variables) => {
      if (response.success) {
        const statusMessage = {
          INTERESTED: 'Marked as interested!',
          GOING: "You're going to this party!",
          NOT_GOING: 'Marked as not going',
          MAYBE: 'Marked as maybe',
        }[variables.status];

        toast.success(statusMessage);

        // Invalidate party and attendees queries
        queryClient.invalidateQueries({ queryKey: ['party', variables.partyId] });
        queryClient.invalidateQueries({ queryKey: ['partyAttendees', variables.partyId] });
        queryClient.invalidateQueries({ queryKey: ['myParties'] });
      } else {
        const errorMessage = extractErrorMessage(response.errors, 'Failed to update attendance');
        toast.error(errorMessage);
      }
    },
    onError: error => {
      console.error('Attend party error:', error);
      const errorMessage = extractErrorMessage(error, 'An unexpected error occurred');
      toast.error(errorMessage);
    },
  });
};

export const useInviteToParty = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      partyId,
      userIds,
      message,
    }: {
      partyId: string;
      userIds: string[];
      message?: string;
    }) => partyService.inviteToParty(partyId, userIds, message),
    onSuccess: (response, variables) => {
      if (response.success) {
        const inviteCount = variables.userIds.length;
        toast.success(`${inviteCount} invitation${inviteCount > 1 ? 's' : ''} sent!`);

        // Invalidate party and invitations queries
        queryClient.invalidateQueries({ queryKey: ['party', variables.partyId] });
        queryClient.invalidateQueries({ queryKey: ['partyInvitations'] });
      } else {
        const errorMessage = extractErrorMessage(response.errors, 'Failed to send invitations');
        toast.error(errorMessage);
      }
    },
    onError: error => {
      console.error('Invite to party error:', error);
      const errorMessage = extractErrorMessage(error, 'An unexpected error occurred');
      toast.error(errorMessage);
    },
  });
};

export const useAddPartyHost = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      partyId,
      userId,
      role,
    }: {
      partyId: string;
      userId: string;
      role: PartyHostRole;
    }) => partyService.addPartyHost(partyId, userId, role),
    onSuccess: (response, variables) => {
      if (response.success) {
        const roleMessage = {
          OWNER: 'Added as owner',
          CO_HOST: 'Added as co-host',
          MODERATOR: 'Added as moderator',
        }[variables.role];

        toast.success(`${roleMessage}!`);

        // Invalidate party and hosts queries
        queryClient.invalidateQueries({ queryKey: ['party', variables.partyId] });
        queryClient.invalidateQueries({ queryKey: ['partyHosts', variables.partyId] });
      } else {
        const errorMessage = extractErrorMessage(response.errors, 'Failed to add host');
        toast.error(errorMessage);
      }
    },
    onError: error => {
      console.error('Add party host error:', error);
      const errorMessage = extractErrorMessage(error, 'An unexpected error occurred');
      toast.error(errorMessage);
    },
  });
};

export const useUploadPartyImage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      partyId,
      imageUrl,
      isPrimary,
    }: {
      partyId: string;
      imageUrl: string;
      isPrimary?: boolean;
    }) => partyService.addPartyImage(partyId, imageUrl, isPrimary),
    onSuccess: (response, variables) => {
      if (response.success) {
        toast.success('Image uploaded successfully!');

        // Invalidate party and images queries
        queryClient.invalidateQueries({ queryKey: ['party', variables.partyId] });
        queryClient.invalidateQueries({ queryKey: ['partyImages', variables.partyId] });
      } else {
        const errorMessage = extractErrorMessage(response.errors, 'Failed to upload image');
        toast.error(errorMessage);
      }
    },
    onError: error => {
      console.error('Upload party image error:', error);
      const errorMessage = extractErrorMessage(error, 'An unexpected error occurred');
      toast.error(errorMessage);
    },
  });
};
