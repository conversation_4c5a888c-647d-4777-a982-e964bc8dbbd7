import { useInfiniteQuery, useQuery } from '@tanstack/react-query';

import {
  type AttendanceStatus,
  type PaginationInput,
  type Party,
  type PartyFiltersInput,
  type PartySearchInput,
  type PartyStatus,
  partyService,
} from '../services/partyService';

export const useParty = (partyId: string, enabled = true) => {
  return useQuery({
    queryKey: ['party', partyId],
    queryFn: () => partyService.getParty(partyId),
    enabled: enabled && !!partyId,
    select: response => {
      if (response.success) {
        return response.data;
      }
      throw new Error(response.errors?.[0]?.message || 'Failed to fetch party');
    },
  });
};

export const useParties = (filters?: PartyFiltersInput, pagination?: PaginationInput) => {
  return useInfiniteQuery({
    queryKey: ['parties', filters],
    queryFn: ({ pageParam }) =>
      partyService.getParties(filters, {
        ...pagination,
        cursor: pageParam as string,
      }),
    initialPageParam: undefined,
    getNextPageParam: lastPage => {
      if (lastPage.success && lastPage.data?.hasNextPage) {
        return lastPage.data.cursor;
      }
      return undefined;
    },
    select: data => {
      const pages = data.pages.map(page => {
        if (page.success) {
          return page.data;
        }
        throw new Error(page.errors?.[0]?.message || 'Failed to fetch parties');
      });
      return {
        pages,
        pageParams: data.pageParams,
      };
    },
  });
};

export const useSearchParties = (input: PartySearchInput, enabled = true) => {
  return useInfiniteQuery({
    queryKey: ['searchParties', input],
    queryFn: ({ pageParam }) =>
      partyService.searchParties({
        ...input,
        pagination: {
          ...input.pagination,
          cursor: pageParam as string,
        },
      }),
    initialPageParam: undefined,
    enabled: enabled && (!!input.query || !!input.filters),
    getNextPageParam: lastPage => {
      if (lastPage.success && lastPage.data?.hasNextPage) {
        return lastPage.data.cursor;
      }
      return undefined;
    },
    select: data => {
      const pages = data.pages.map(page => {
        if (page.success) {
          return page.data;
        }
        throw new Error(page.errors?.[0]?.message || 'Failed to search parties');
      });
      return {
        pages,
        pageParams: data.pageParams,
      };
    },
  });
};

export const useMyParties = (status?: PartyStatus, pagination?: PaginationInput) => {
  return useInfiniteQuery({
    queryKey: ['myParties', status],
    queryFn: ({ pageParam }) =>
      partyService.getMyParties(status, {
        ...pagination,
        cursor: pageParam as string,
      }),
    initialPageParam: undefined,
    getNextPageParam: lastPage => {
      if (lastPage.success && lastPage.data?.hasNextPage) {
        return lastPage.data.cursor;
      }
      return undefined;
    },
    select: data => {
      const pages = data.pages.map(page => {
        if (page.success) {
          return page.data;
        }
        throw new Error(page.errors?.[0]?.message || 'Failed to fetch your parties');
      });
      return {
        pages,
        pageParams: data.pageParams,
      };
    },
  });
};

export const usePartyAttendees = (
  partyId: string,
  status?: AttendanceStatus,
  pagination?: PaginationInput,
  enabled = true
) => {
  return useInfiniteQuery({
    queryKey: ['partyAttendees', partyId, status],
    queryFn: ({ pageParam }) =>
      partyService.getPartyAttendees(partyId, status, {
        ...pagination,
        cursor: pageParam as string,
      }),
    initialPageParam: undefined,
    enabled: enabled && !!partyId,
    getNextPageParam: lastPage => {
      if (lastPage.success && lastPage.data?.hasNextPage) {
        return lastPage.data.cursor;
      }
      return undefined;
    },
    select: data => {
      const pages = data.pages.map(page => {
        if (page.success) {
          return page.data;
        }
        throw new Error(page.errors?.[0]?.message || 'Failed to fetch party attendees');
      });
      return {
        pages,
        pageParams: data.pageParams,
      };
    },
  });
};

export const usePartyAnalytics = (partyId: string, timeRange?: string, enabled = true) => {
  return useQuery({
    queryKey: ['partyAnalytics', partyId, timeRange],
    queryFn: () => partyService.getPartyAnalytics(partyId, timeRange),
    enabled: enabled && !!partyId,
    select: response => {
      if (response.success) {
        return response.data;
      }
      throw new Error(response.errors?.[0]?.message || 'Failed to fetch party analytics');
    },
  });
};

// Convenience hooks for specific party lists
export const useUpcomingParties = (filters?: Omit<PartyFiltersInput, 'startDateFrom'>) => {
  return useParties({
    ...filters,
    startDateFrom: new Date().toISOString(),
  });
};

export const usePastParties = (filters?: Omit<PartyFiltersInput, 'startDateTo'>) => {
  return useParties({
    ...filters,
    startDateTo: new Date().toISOString(),
  });
};

export const useNearbyParties = (
  latitude: number,
  longitude: number,
  radius = 10,
  filters?: PartyFiltersInput,
  enabled = true
) => {
  return useParties({
    ...filters,
    location: {
      latitude,
      longitude,
    },
    radius,
  });
};

export const usePartiesByCategory = (category: string, filters?: PartyFiltersInput) => {
  return useParties({
    ...filters,
    category,
  });
};

export const useMyDraftParties = () => {
  return useMyParties(PartyStatus.DRAFT);
};

export const useMyPublishedParties = () => {
  return useMyParties(PartyStatus.PUBLISHED);
};

export const usePartyGoingAttendees = (partyId: string, enabled = true) => {
  return usePartyAttendees(partyId, AttendanceStatus.GOING, undefined, enabled);
};

export const usePartyInterestedAttendees = (partyId: string, enabled = true) => {
  return usePartyAttendees(partyId, AttendanceStatus.INTERESTED, undefined, enabled);
};
