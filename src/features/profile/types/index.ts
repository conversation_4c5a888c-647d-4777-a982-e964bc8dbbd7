export interface UserProfile {
  id: string;
  name: string;
  username?: string;
  email: string;
  avatar?: string;
  bio?: string;
  location?: {
    from?: string;
    current?: string;
  };
  stats: ProfileStats;
  isVerified?: boolean;
  isVip?: boolean;
  followsYou?: boolean;
  isFollowing?: boolean;
  createdAt: string;
  preferences?: UserPreferences;
}

export interface ProfileStats {
  followersCount: number;
  followingCount: number;
  eventsHostedCount: number;
  eventsAttendedCount: number;
  reviewsCount: number;
  photosCount: number;
  averageRating?: number;
}

export interface UserPreferences {
  isProfilePublic: boolean;
  showLocation: boolean;
  showEmail: boolean;
  allowMessages: boolean;
  allowEventInvites: boolean;
}

export type ProfileTab = 'events' | 'favorites' | 'reviews' | 'photos';

export type EventFilter = 'attending' | 'hosting' | 'attended' | 'hosted';

export interface ProfileScreenParams {
  userId?: string;
  isOwnProfile?: boolean;
}

export interface UserProfileModalParams {
  userId: string;
  fromEvent?: boolean;
  eventId?: string;
}

export interface ProfileAction {
  type: 'follow' | 'unfollow' | 'message' | 'invite' | 'report' | 'block';
  userId: string;
}

export interface ProfileReview {
  id: string;
  reviewer: {
    id: string;
    name: string;
    avatar?: string;
  };
  rating: number;
  comment: string;
  eventId: string;
  eventName: string;
  createdAt: string;
  type: 'host' | 'attendee';
}

export interface ProfileEvent {
  id: string;
  title: string;
  description: string;
  coverImage?: string;
  date: string;
  location: string;
  attendees: number;
  capacity?: number;
  price?: number;
  status: 'upcoming' | 'ongoing' | 'past' | 'cancelled';
  userRole: 'host' | 'attendee' | 'interested';
}

// Profile Types based on GraphQL schema

export enum UserRole {
  USER = 'USER',
  ADMIN = 'ADMIN',
  MODERATOR = 'MODERATOR',
  ARTIST = 'ARTIST',
  VENUE_OWNER = 'VENUE_OWNER',
}

export enum OnlineStatus {
  ONLINE = 'ONLINE',
  OFFLINE = 'OFFLINE',
  BUSY = 'BUSY',
  AWAY = 'AWAY',
}

export enum ActivityVisibility {
  PUBLIC = 'PUBLIC',
  FRIENDS_ONLY = 'FRIENDS_ONLY',
  PRIVATE = 'PRIVATE',
}

export enum ConnectionType {
  FRIEND = 'FRIEND',
  FOLLOWER = 'FOLLOWER',
  FOLLOWING = 'FOLLOWING',
  BLOCKED = 'BLOCKED',
}

export interface User {
  id: string;
  name: string;
  email: string;
  phoneNumber?: string;
  avatar?: string;
  profileImageURL?: string;
  coverImageURL?: string;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  role: UserRole;
  createdAt: string;
  updatedAt: string;
  onlineStatus: OnlineStatus;
  lastActiveAt?: string;
  activityVisibility: ActivityVisibility;
  locationSharingEnabled: boolean;
  currentEventId?: string;
  profileCompleteness: number;
  loginCount: number;
  hostedParties?: Party[];
  attendedParties?: PartyAttendance[];
  friends?: User[];
  preferences?: UserPreferencesEntity;
  images?: UserImage[];
  artistProfile?: ArtistProfile;
}

export interface Party {
  id: string;
  name: string;
  title: string;
  description?: string;
  type: string;
  eventType: string;
  status: string;
  visibility: string;
  startDate: string;
  endDate?: string;
  startTime: string;
  endTime?: string;
  maxGuests?: number;
  currentStep?: number;
  lastSaved?: string;
  isComplete: boolean;
  creatorId: string;
  venueId?: string;
  soldTickets?: number;
  coverImageId?: string;
  isPrivate: boolean;
  ageRestriction?: number;
  tags?: string[];
  category?: string;
  createdAt: string;
  updatedAt: string;
  address?: string;
  city?: string;
  placeType?: string;
  isFree: boolean;
  ticketURL?: string;
  musicGenres?: string[];
  performers?: string[];
  dressCode?: string;
  isCancelled: boolean;
  content?: any;
  mediaGallery?: any;
  engagementData?: any;
  analyticsData?: any;
  recommendationData?: any;
  searchIndex?: string;
  attendeeCount: number;
  averageRating?: number;
  isUserAttending?: boolean;
  isUserInvited?: boolean;
  isUserHost?: boolean;
  location?: {
    lat: number;
    lng: number;
  };
  venue?: Venue;
  hosts?: PartyHost[];
}

export interface Venue {
  id: string;
  name: string;
  description?: string;
  address: string;
  city: string;
  state: string;
  country: string;
  postalCode?: string;
  longitude: number;
  latitude: number;
  phone?: string;
  email?: string;
  website?: string;
  distance?: number;
  venueTypeId?: string;
  capacity?: number;
  rating?: number;
  reviewCount?: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface PartyHost {
  id: string;
  partyId: string;
  userId: string;
  role: string;
  createdAt: string;
  updatedAt: string;
}

export interface PartyAttendance {
  id: string;
  partyId: string;
  userId: string;
  status: string;
  userAttendanceOrder?: number;
  partyAttendanceOrder?: number;
  createdAt: string;
  updatedAt: string;
  party?: Party;
  user?: User;
}

export interface UserPreferencesEntity {
  id: string;
  userId: string;
  language?: string;
  timezone?: string;
  notifications?: any;
  privacy?: any;
  theme?: string;
  createdAt: string;
  updatedAt: string;
}

export interface UserImage {
  id: string;
  userId: string;
  url: string;
  type: string;
  isPrimary: boolean;
  order?: number;
  createdAt: string;
  updatedAt: string;
}

export interface ArtistProfile {
  id: string;
  userId: string;
  stageName?: string;
  bio?: string;
  genres?: string[];
  socialLinks?: any;
  verified: boolean;
  status: string;
  createdAt: string;
  updatedAt: string;
}

export interface Connection {
  id: string;
  followerId: string;
  followingId: string;
  connectionType: ConnectionType;
  createdAt: string;
  interactionScore?: number;
  lastInteractionAt?: string;
  follower?: User;
  following?: User;
}

export interface UserUpdateInput {
  name?: string;
  phoneNumber?: string;
  avatar?: File;
  profileImage?: File;
  coverImage?: File;
  onlineStatus?: OnlineStatus;
  activityVisibility?: ActivityVisibility;
  locationSharingEnabled?: boolean;
}

export interface FollowUserInput {
  userId: string;
}

export interface FollowUserResponse {
  success: boolean;
  message?: string;
  friendship?: any;
  friendRequest?: any;
  connection?: Connection;
}

export interface UserFollowersResponse {
  totalCount: number;
  hasNextPage: boolean;
  endCursor?: string;
  followers: Connection[];
}

export interface UserFollowingResponse {
  totalCount: number;
  hasNextPage: boolean;
  endCursor?: string;
  following: Connection[];
}

export interface PaginationInput {
  limit?: number;
  cursor?: string;
}

// Profile Statistics - using the ProfileStats interface defined above

export interface EditProfileScreenParams {
  user: User;
}

export interface FollowersScreenParams {
  userId: string;
  userName: string;
}

export interface FollowingScreenParams {
  userId: string;
  userName: string;
}

export interface UserProfileModalParams {
  userId: string;
}

export interface HostProfileModalParams {
  userId: string;
  hostName: string;
}

// Additional types for profile management
export interface ProfileSettings {
  language?: string;
  timezone?: string;
  notifications?: any;
  privacy?: any;
  theme?: string;
}

export interface ProfileUpdateData {
  name?: string;
  phoneNumber?: string;
  avatar?: File;
  profileImage?: File;
  coverImage?: File;
  onlineStatus?: OnlineStatus;
  activityVisibility?: ActivityVisibility;
  locationSharingEnabled?: boolean;
  bio?: string;
  preferences?: UserPreferences;
}
