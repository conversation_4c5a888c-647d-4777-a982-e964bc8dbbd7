import React from 'react';

import { RouteProp, useRoute } from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { RootStackParamList } from '@/src/core/navigation/types';
import { Box, ScrollableContainer, Text } from '@/src/core/theme';
import { ProfileHeader, ProfileStats } from '@/src/features/profile/components';
import { useUserProfile } from '@/src/features/profile/hooks/useUserProfile';
import { NavigationTopBar } from '@/src/shared/components';
import { Divider } from '@/src/shared/components/Divider';

type UserProfileModalRouteProp = RouteProp<RootStackParamList, 'UserProfile'>;

const UserProfileModal: React.FC = () => {
  const route = useRoute<UserProfileModalRouteProp>();
  const { userId, fromEvent } = route.params;

  const { profile, isLoading, error, handleProfileAction } = useUserProfile(userId);

  if (isLoading) {
    return (
      <SafeAreaView style={{ flex: 1 }}>
        <NavigationTopBar title="" />
        <Box flex={1} justifyContent="center" alignItems="center">
          <Text>Loading...</Text>
        </Box>
      </SafeAreaView>
    );
  }

  if (error || !profile) {
    return (
      <SafeAreaView style={{ flex: 1 }}>
        <NavigationTopBar title="" />
        <Box flex={1} justifyContent="center" alignItems="center">
          <Text>{error || 'Profile not found'}</Text>
        </Box>
      </SafeAreaView>
    );
  }

  const handleFollowPress = () => {
    handleProfileAction({
      type: profile.isFollowing ? 'unfollow' : 'follow',
      userId: profile.id,
    });
  };

  console.log(profile);

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <NavigationTopBar title={profile.name} />

      <ScrollableContainer backgroundColor="mainBackground">
        {/* Profile Header */}
        <Box paddingHorizontal="lg_24" paddingTop="md_16">
          <ProfileHeader profile={profile} isOwnProfile={false} onFollowPress={handleFollowPress} />
        </Box>

        <Divider />

        {/* Profile Stats */}
        <Box paddingHorizontal="lg_24">
          <ProfileStats stats={profile.stats} userId={profile.id} />
        </Box>

        <Divider />

        {/* Quick Info Section for Modal View */}
        <Box padding="lg_24">
          <Text variant="h_16SemiBold_alertTitle" color="mainText" marginBottom="sm_12">
            About {profile.name}
          </Text>

          {/* Recent Events if from event */}
          {fromEvent && (
            <Box marginBottom="md_16">
              <Text variant="b_14Medium_button" color="mainText" marginBottom="xs_8">
                Host Rating
              </Text>
              <Box flexDirection="row" alignItems="center" gap="xs_8">
                <Text variant="h_20Medium_subsection" color="primary">
                  {profile.stats.averageRating || 0}
                </Text>
                <Text variant="b_14Regular_content" color="mutedText">
                  ({profile.stats.reviews} reviews)
                </Text>
              </Box>
            </Box>
          )}

          {/* Bio */}
          {profile.bio && (
            <Box>
              <Text variant="b_14Medium_button" color="mainText" marginBottom="xs_8">
                Bio
              </Text>
              <Text variant="b_14Regular_content" color="secondaryText">
                {profile.bio}
              </Text>
            </Box>
          )}
        </Box>
      </ScrollableContainer>
    </SafeAreaView>
  );
};

export default UserProfileModal;
