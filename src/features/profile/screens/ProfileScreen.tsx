import React, { useState } from 'react';

import { Platform } from 'react-native';

import { useBottomTabBarHeight } from '@react-navigation/bottom-tabs';
import { RouteProp, useRoute } from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { SettingsStackParamList } from '@/src/core/navigation/types';
import { Box, ScrollableContainer, Text } from '@/src/core/theme';
import { NavigationTopBar } from '@/src/shared/components';
import { Divider } from '@/src/shared/components/Divider';

import { ProfileHeader, ProfileStats, ProfileTabs } from '../components';
import { useUserProfile } from '../hooks/useUserProfile';
import type { EventFilter, ProfileTab } from '../types';

// Placeholder components for tab content
const EventsContent = ({ filter }: { filter: EventFilter }) => (
  <Box padding="md_16">
    <Text>Events - {filter}</Text>
  </Box>
);

const FavoritesContent = () => (
  <Box padding="md_16">
    <Text>Favorites</Text>
  </Box>
);

const ReviewsContent = () => (
  <Box padding="md_16">
    <Text>Reviews</Text>
  </Box>
);

const PhotosContent = () => (
  <Box padding="md_16">
    <Text>Photos</Text>
  </Box>
);

type ProfileScreenRouteProp = RouteProp<SettingsStackParamList, 'Profile'>;

const ProfileScreen: React.FC = () => {
  const route = useRoute<ProfileScreenRouteProp>();
  const tabBarHeight = useBottomTabBarHeight();
  const { userId } = route.params || {};

  const { profile, isLoading, error, handleProfileAction, isOwnProfile } = useUserProfile(userId);

  const [activeTab, setActiveTab] = useState<ProfileTab>('events');
  const [eventFilter, setEventFilter] = useState<EventFilter>('attending');

  if (isLoading) {
    return (
      <SafeAreaView style={{ flex: 1 }}>
        <NavigationTopBar title="Profile" />
        <Box flex={1} justifyContent="center" alignItems="center">
          <Text>Loading...</Text>
        </Box>
      </SafeAreaView>
    );
  }

  if (error || !profile) {
    return (
      <SafeAreaView style={{ flex: 1 }}>
        <NavigationTopBar title="Profile" />
        <Box flex={1} justifyContent="center" alignItems="center">
          <Text>{error || 'Profile not found'}</Text>
        </Box>
      </SafeAreaView>
    );
  }

  const handleFollowPress = () => {
    handleProfileAction({
      type: profile.isFollowing ? 'unfollow' : 'follow',
      userId: profile.id,
    });
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'events':
        return <EventsContent filter={eventFilter} />;
      case 'favorites':
        return <FavoritesContent />;
      case 'reviews':
        return <ReviewsContent />;
      case 'photos':
        return <PhotosContent />;
    }
  };

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <NavigationTopBar title={isOwnProfile ? 'My Profile' : profile.name} />

      <ScrollableContainer
        backgroundColor="mainBackground"
        contentContainerStyle={{
          paddingBottom: Platform.OS === 'ios' ? tabBarHeight : 0,
        }}>
        {/* Profile Header */}
        <Box paddingHorizontal="lg_24" paddingTop="md_16">
          <ProfileHeader
            profile={profile}
            isOwnProfile={isOwnProfile}
            onFollowPress={handleFollowPress}
          />
        </Box>

        <Divider />

        {/* Profile Stats */}
        <Box paddingHorizontal="lg_24">
          <ProfileStats stats={profile.stats} userId={profile.id} />
        </Box>

        <Divider />

        {/* Profile Tabs */}
        <ProfileTabs
          activeTab={activeTab}
          onTabChange={setActiveTab}
          eventFilter={activeTab === 'events' ? eventFilter : undefined}
          onEventFilterChange={setEventFilter}
        />

        <Divider />

        {/* Tab Content */}
        {renderTabContent()}
      </ScrollableContainer>
    </SafeAreaView>
  );
};

export default ProfileScreen;
