import React from 'react';

import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { Calendar, MapPin, Star } from 'phosphor-react-native';

import { RootStackParamList } from '@/src/core/navigation/types';
import { Box, Pressable, Text, useTheme } from '@/src/core/theme';
import { useUserProfile } from '@/src/features/profile/hooks/useUserProfile';
import { Avatar, Button } from '@/src/shared/components';

import { navigateToProfile } from '../utils/navigation';

type HostProfileModalRouteProp = RouteProp<RootStackParamList, 'HostProfileModal'>;

const HostProfileModal: React.FC = () => {
  const route = useRoute<HostProfileModalRouteProp>();
  const navigation = useNavigation();
  const theme = useTheme();
  const { userId, eventId } = route.params;

  const { profile, isLoading, error, handleProfileAction } = useUserProfile(userId);

  if (isLoading || error || !profile) {
    return (
      <Box flex={1} backgroundColor="mainBackground" padding="lg_24">
        <Text>{isLoading ? 'Loading...' : error || 'Profile not found'}</Text>
      </Box>
    );
  }

  const handleFollowPress = () => {
    handleProfileAction({
      type: profile.isFollowing ? 'unfollow' : 'follow',
      userId: profile.id,
    });
  };

  const handleViewFullProfile = () => {
    navigation.goBack(); // Close modal first
    setTimeout(() => {
      navigateToProfile(navigation as any, userId, { fromEvent: true, eventId });
    }, 100);
  };

  return (
    <Box flex={1} backgroundColor="mainBackground" padding="lg_24">
      {/* Header Section */}
      <Box flexDirection="row" gap="md_16" marginBottom="lg_24">
        <Avatar
          size="l"
          source={profile.avatar ? { uri: profile.avatar } : undefined}
          fallbackText={profile.name.charAt(0)}
        />

        <Box flex={1} gap="xs_8">
          <Text variant="h_20Medium_subsection" color="mainText">
            {profile.name}
          </Text>

          {profile.location?.current && (
            <Box flexDirection="row" alignItems="center" gap="xxs_4">
              <MapPin size={14} color={theme.colors.mutedText} />
              <Text variant="b_14Regular_content" color="mutedText">
                {profile.location.current}
              </Text>
            </Box>
          )}

          <Box flexDirection="row" alignItems="center" gap="sm_12">
            <Box flexDirection="row" alignItems="center" gap="xxs_4">
              <Star size={16} color={theme.colors.warning} weight="fill" />
              <Text variant="b_14Bold_CardTitle" color="mainText">
                {profile.stats.averageRating || 0}
              </Text>
              <Text variant="b_12Medium_CardSubtitle" color="mutedText">
                ({profile.stats.reviews})
              </Text>
            </Box>

            <Box flexDirection="row" alignItems="center" gap="xxs_4">
              <Calendar size={16} color={theme.colors.mutedText} />
              <Text variant="b_14Regular_content" color="mutedText">
                {profile.stats.eventsHosted} events
              </Text>
            </Box>
          </Box>
        </Box>
      </Box>

      {/* Bio Section */}
      {profile.bio && (
        <Box marginBottom="lg_24">
          <Text variant="b_14Regular_content" color="secondaryText" numberOfLines={3}>
            {profile.bio}
          </Text>
        </Box>
      )}

      {/* Quick Stats */}
      <Box
        flexDirection="row"
        backgroundColor="cardBackground"
        borderRadius="md_12"
        padding="md_16"
        marginBottom="lg_24">
        <Box flex={1} alignItems="center">
          <Text variant="h_20Medium_subsection" color="mainText">
            {profile.stats.followers}
          </Text>
          <Text variant="b_12Medium_CardSubtitle" color="mutedText">
            Followers
          </Text>
        </Box>

        <Box width={1} backgroundColor="mainBorder" marginHorizontal="md_16" />

        <Box flex={1} alignItems="center">
          <Text variant="h_20Medium_subsection" color="mainText">
            {profile.stats.eventsHosted}
          </Text>
          <Text variant="b_12Medium_CardSubtitle" color="mutedText">
            Events Hosted
          </Text>
        </Box>

        <Box width={1} backgroundColor="mainBorder" marginHorizontal="md_16" />

        <Box flex={1} alignItems="center">
          <Text variant="h_20Medium_subsection" color="mainText">
            {profile.stats.eventsAttended}
          </Text>
          <Text variant="b_12Medium_CardSubtitle" color="mutedText">
            Attended
          </Text>
        </Box>
      </Box>

      {/* Action Buttons */}
      <Box gap="sm_12">
        <Button
          title={profile.isFollowing ? 'Following' : 'Follow'}
          onPress={handleFollowPress}
          variant={profile.isFollowing ? 'secondary' : 'primary'}
        />

        <Button title="View Full Profile" onPress={handleViewFullProfile} variant="outline" />
      </Box>
    </Box>
  );
};

export default HostProfileModal;
