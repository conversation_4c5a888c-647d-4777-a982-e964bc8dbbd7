import React, { useState } from 'react';

import { Platform } from 'react-native';

import { useNavigation } from '@react-navigation/native';
import { Camera } from 'phosphor-react-native';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import { SafeAreaView } from 'react-native-safe-area-context';

import { toast } from '@/src/core';
import { Box, Pressable, ScrollableContainer, Text, TextInput } from '@/src/core/theme';
import { Avatar, Button, NavigationTopBar } from '@/src/shared/components';

import { useUserProfile } from '../hooks/useUserProfile';

const EditProfileScreen: React.FC = () => {
  const navigation = useNavigation();
  const { profile, updateProfile } = useUserProfile();

  const [formData, setFormData] = useState({
    name: profile?.name || '',
    username: profile?.username || '',
    bio: profile?.bio || '',
    fromLocation: profile?.location?.from || '',
    currentLocation: profile?.location?.current || '',
  });

  const [isSaving, setIsSaving] = useState(false);

  const handleSave = async () => {
    setIsSaving(true);

    const success = await updateProfile({
      name: formData.name,
      username: formData.username,
      bio: formData.bio,
      location: {
        from: formData.fromLocation,
        current: formData.currentLocation,
      },
    });

    setIsSaving(false);

    if (success) {
      toast.success('Profile updated successfully');
      navigation.goBack();
    }
  };

  const handleAvatarPress = () => {
    // TODO: Implement image picker
    console.log('Change avatar');
  };

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <NavigationTopBar
        title="Edit Profile"
        trailingActions={[
          <Button
            key="save"
            title="Save"
            onPress={handleSave}
            enabled={!isSaving}
            loading={isSaving}
          />,
        ]}
      />

      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        <ScrollableContainer backgroundColor="mainBackground" padding="lg_24">
          {/* Avatar Section */}
          <Box alignItems="center" marginBottom="xl_32">
            <Pressable onPress={handleAvatarPress}>
              <Box>
                <Avatar
                  size="2xl"
                  source={profile?.avatar ? { uri: profile.avatar } : undefined}
                  fallbackText={profile?.name.charAt(0) || 'U'}
                />
                <Box
                  position="absolute"
                  bottom={0}
                  right={0}
                  backgroundColor="primary"
                  borderRadius="circle_9999"
                  padding="xs_8">
                  <Camera size={20} color="white" />
                </Box>
              </Box>
            </Pressable>
            <Text variant="b_14Medium_link" color="primary" marginTop="sm_12">
              Change Photo
            </Text>
          </Box>

          {/* Form Fields */}
          <Box gap="lg_24">
            <Box>
              <Text variant="l_14Medium_formHelperText" color="mainText" marginBottom="xs_8">
                Name
              </Text>
              <TextInput
                value={formData.name}
                onChangeText={text => setFormData({ ...formData, name: text })}
                placeholder="Your name"
              />
            </Box>

            <Box>
              <Text variant="l_14Medium_formHelperText" color="mainText" marginBottom="xs_8">
                Username
              </Text>
              <TextInput
                value={formData.username}
                onChangeText={text => setFormData({ ...formData, username: text })}
                placeholder="@username"
                autoCapitalize="none"
              />
            </Box>

            <Box>
              <Text variant="l_14Medium_formHelperText" color="mainText" marginBottom="xs_8">
                Bio
              </Text>
              <TextInput
                value={formData.bio}
                onChangeText={text => setFormData({ ...formData, bio: text })}
                placeholder="Tell us about yourself"
                multiline
                numberOfLines={4}
                textAlignVertical="top"
              />
            </Box>

            <Box>
              <Text variant="l_14Medium_formHelperText" color="mainText" marginBottom="xs_8">
                From
              </Text>
              <TextInput
                value={formData.fromLocation}
                onChangeText={text => setFormData({ ...formData, fromLocation: text })}
                placeholder="Where you're from"
              />
            </Box>

            <Box>
              <Text variant="l_14Medium_formHelperText" color="mainText" marginBottom="xs_8">
                Current Location
              </Text>
              <TextInput
                value={formData.currentLocation}
                onChangeText={text => setFormData({ ...formData, currentLocation: text })}
                placeholder="Where you live now"
              />
            </Box>
          </Box>

          <Box height={50} />
        </ScrollableContainer>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default EditProfileScreen;
