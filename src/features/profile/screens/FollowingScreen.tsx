import React from 'react';

import { RouteProp, useRoute } from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { SettingsStackParamList } from '@/src/core/navigation/types';
import { Box, Text } from '@/src/core/theme';
import { NavigationTopBar } from '@/src/shared/components';

type FollowingScreenRouteProp = RouteProp<SettingsStackParamList, 'Following'>;

const FollowingScreen: React.FC = () => {
  const route = useRoute<FollowingScreenRouteProp>();
  const { userId } = route.params;

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <NavigationTopBar title="Following" />
      <Box flex={1} justifyContent="center" alignItems="center">
        <Text>Following list for user: {userId}</Text>
      </Box>
    </SafeAreaView>
  );
};

export default FollowingScreen;
