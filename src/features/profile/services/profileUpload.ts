/**
 * Profile upload service - handles file uploads with progress tracking
 * Provides image processing and upload functionality
 */
import { imagePicker, network } from '@/src/core/libs';
import type { UploadProgress } from '@/src/core/libs';

interface ProcessedImage {
  uri: string;
  width: number;
  height: number;
  size: number;
}

class ProfileUpload {
  private static readonly MAX_AVATAR_SIZE = 2 * 1024 * 1024; // 2MB
  private static readonly MAX_COVER_SIZE = 5 * 1024 * 1024; // 5MB
  private static readonly AVATAR_SIZE = { width: 400, height: 400 };
  private static readonly COVER_SIZE = { width: 1200, height: 400 };

  /**
   * Pick and process avatar image
   */
  async pickAvatar(): Promise<ProcessedImage | null> {
    try {
      // Request permission
      const hasPermission = await imagePicker.requestPermissions();

      if (!hasPermission) {
        throw new Error('Permission to access media library is required');
      }

      // Pick image
      const result = await imagePicker.pickFromLibrary({
        allowsEditing: true,
        aspect: [1, 1], // Square aspect ratio for avatar
        quality: 0.8,
        mediaTypes: 'images',
      });

      if (!result || result.cancelled) return null;

      // Process image for avatar
      return await this.processAvatarImage(result.uri);
    } catch (error) {
      console.error('Failed to pick avatar:', error);
      throw error;
    }
  }

  /**
   * Pick and process cover photo
   */
  async pickCoverPhoto(): Promise<ProcessedImage | null> {
    try {
      // Request permission
      const hasPermission = await imagePicker.requestPermissions();

      if (!hasPermission) {
        throw new Error('Permission to access media library is required');
      }

      // Pick image
      const result = await imagePicker.pickFromLibrary({
        allowsEditing: true,
        aspect: [3, 1], // Wide aspect ratio for cover photo
        quality: 0.9,
        mediaTypes: 'images',
      });

      if (!result || result.cancelled) return null;

      // Process image for cover photo
      return await this.processCoverImage(result.uri);
    } catch (error) {
      console.error('Failed to pick cover photo:', error);
      throw error;
    }
  }

  /**
   * Take photo with camera for avatar
   */
  async takeAvatarPhoto(): Promise<ProcessedImage | null> {
    try {
      // Request permission
      const hasPermission = await imagePicker.requestPermissions();

      if (!hasPermission) {
        throw new Error('Permission to access camera is required');
      }

      // Take photo
      const result = await imagePicker.takePhoto({
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result || result.cancelled) return null;

      // Process image for avatar
      return await this.processAvatarImage(result.uri);
    } catch (error) {
      console.error('Failed to take avatar photo:', error);
      throw error;
    }
  }

  /**
   * Process image for avatar use
   */
  private async processAvatarImage(uri: string): Promise<ProcessedImage> {
    try {
      const result = await imagePicker.processImage(uri, {
        resize: ProfileUpload.AVATAR_SIZE,
        compress: 0.8,
        format: 'jpeg',
      });

      if (result.size > ProfileUpload.MAX_AVATAR_SIZE) {
        throw new Error(
          `Avatar image is too large. Maximum size is ${ProfileUpload.MAX_AVATAR_SIZE / 1024 / 1024}MB`
        );
      }

      return {
        uri: result.uri,
        width: result.width,
        height: result.height,
        size: result.size,
      };
    } catch (error) {
      console.error('Failed to process avatar image:', error);
      throw error;
    }
  }

  /**
   * Process image for cover photo use
   */
  private async processCoverImage(uri: string): Promise<ProcessedImage> {
    try {
      const result = await imagePicker.processImage(uri, {
        resize: ProfileUpload.COVER_SIZE,
        compress: 0.9,
        format: 'jpeg',
      });

      if (result.size > ProfileUpload.MAX_COVER_SIZE) {
        throw new Error(
          `Cover photo is too large. Maximum size is ${ProfileUpload.MAX_COVER_SIZE / 1024 / 1024}MB`
        );
      }

      return {
        uri: result.uri,
        width: result.width,
        height: result.height,
        size: result.size,
      };
    } catch (error) {
      console.error('Failed to process cover image:', error);
      throw error;
    }
  }

  /**
   * Upload image with progress tracking
   */
  async uploadImageWithProgress(
    imageUri: string,
    uploadUrl: string,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<string> {
    try {
      const result = await network.uploadFile(uploadUrl, imageUri, {
        onProgress,
      });

      return result.data;
    } catch (error) {
      console.error('Failed to upload image:', error);
      throw error;
    }
  }

  /**
   * Get image dimensions
   */
  async getImageDimensions(uri: string): Promise<{ width: number; height: number }> {
    try {
      return await imagePicker.getImageDimensions(uri);
    } catch (error) {
      console.error('Failed to get image dimensions:', error);
      throw error;
    }
  }

  /**
   * Validate image file
   */
  async validateImageFile(uri: string, maxSize: number): Promise<boolean> {
    try {
      const isValid = await imagePicker.validateImage(uri, {
        maxSize,
        allowedTypes: ['image/jpeg', 'image/png', 'image/webp'],
      });

      if (!isValid) {
        throw new Error(`File is too large or invalid. Maximum size is ${maxSize / 1024 / 1024}MB`);
      }

      return true;
    } catch (error) {
      console.error('Failed to validate image file:', error);
      throw error;
    }
  }
}

export const profileUpload = new ProfileUpload();
export type { ProcessedImage, UploadProgress };
