/**
 * Profile cache service - handles caching and data persistence
 * Optimizes performance by reducing API calls
 */
import { cacheStorage } from '@/src/core/storage';

import type { UserProfile } from '../types';

class ProfileCache {
  private static readonly CACHE_PREFIX = 'profile_cache_';
  private static readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
  private static readonly PROFILES_CACHE_KEY = 'cached_profiles';

  /**
   * Cache a user profile
   */
  async cacheProfile(profile: UserProfile): Promise<void> {
    try {
      const cacheData = {
        profile,
        timestamp: Date.now(),
      };

      cacheStorage.setObject(`${ProfileCache.CACHE_PREFIX}${profile.id}`, cacheData);

      // Update cached profiles list
      await this.updateCachedProfilesList(profile.id);
    } catch (error) {
      console.warn('Failed to cache profile:', error);
    }
  }

  /**
   * Get cached profile if valid
   */
  async getCachedProfile(userId: string): Promise<UserProfile | null> {
    try {
      const cached = cacheStorage.getObject<{ profile: UserProfile; timestamp: number }>(
        `${ProfileCache.CACHE_PREFIX}${userId}`
      );

      if (!cached) return null;

      const { profile, timestamp } = cached;

      // Check if cache is still valid
      if (Date.now() - timestamp > ProfileCache.CACHE_DURATION) {
        await this.removeCachedProfile(userId);
        return null;
      }

      return profile;
    } catch (error) {
      console.warn('Failed to get cached profile:', error);
      return null;
    }
  }

  /**
   * Remove cached profile
   */
  async removeCachedProfile(userId: string): Promise<void> {
    try {
      cacheStorage.delete(`${ProfileCache.CACHE_PREFIX}${userId}`);
      await this.removeFromCachedProfilesList(userId);
    } catch (error) {
      console.warn('Failed to remove cached profile:', error);
    }
  }

  /**
   * Clear all cached profiles
   */
  async clearAllCachedProfiles(): Promise<void> {
    try {
      const cachedProfileIds = await this.getCachedProfilesList();

      // Remove all cached profiles
      cachedProfileIds.forEach(id => cacheStorage.delete(`${ProfileCache.CACHE_PREFIX}${id}`));

      // Clear the cached profiles list
      cacheStorage.delete(ProfileCache.PROFILES_CACHE_KEY);
    } catch (error) {
      console.warn('Failed to clear cached profiles:', error);
    }
  }

  /**
   * Get list of cached profile IDs
   */
  private async getCachedProfilesList(): Promise<string[]> {
    try {
      return cacheStorage.getObject<string[]>(ProfileCache.PROFILES_CACHE_KEY) || [];
    } catch (error) {
      console.warn('Failed to get cached profiles list:', error);
      return [];
    }
  }

  /**
   * Update cached profiles list
   */
  private async updateCachedProfilesList(userId: string): Promise<void> {
    try {
      const cachedIds = await this.getCachedProfilesList();

      if (!cachedIds.includes(userId)) {
        cachedIds.push(userId);
        cacheStorage.setObject(ProfileCache.PROFILES_CACHE_KEY, cachedIds);
      }
    } catch (error) {
      console.warn('Failed to update cached profiles list:', error);
    }
  }

  /**
   * Remove from cached profiles list
   */
  private async removeFromCachedProfilesList(userId: string): Promise<void> {
    try {
      const cachedIds = await this.getCachedProfilesList();
      const filteredIds = cachedIds.filter(id => id !== userId);

      cacheStorage.setObject(ProfileCache.PROFILES_CACHE_KEY, filteredIds);
    } catch (error) {
      console.warn('Failed to remove from cached profiles list:', error);
    }
  }

  /**
   * Check if profile is cached and valid
   */
  async isCacheValid(userId: string): Promise<boolean> {
    const cached = await this.getCachedProfile(userId);
    return cached !== null;
  }

  /**
   * Get cache statistics
   */
  async getCacheStats(): Promise<{ cachedCount: number; totalSize: string }> {
    try {
      const cachedIds = await this.getCachedProfilesList();

      // Calculate approximate cache size
      let totalSize = 0;
      for (const id of cachedIds) {
        const cached = cacheStorage.getObject(`${ProfileCache.CACHE_PREFIX}${id}`);
        if (cached) {
          // Approximate size calculation since MMKV doesn't provide direct size info
          totalSize += JSON.stringify(cached).length;
        }
      }

      return {
        cachedCount: cachedIds.length,
        totalSize: `${(totalSize / 1024).toFixed(1)} KB`,
      };
    } catch (error) {
      console.warn('Failed to get cache stats:', error);
      return { cachedCount: 0, totalSize: '0 KB' };
    }
  }

  /**
   * Cleanup expired cache entries
   */
  async cleanupExpiredCache(): Promise<void> {
    try {
      const cachedIds = await this.getCachedProfilesList();
      const validIds: string[] = [];

      for (const id of cachedIds) {
        const profile = await this.getCachedProfile(id);
        if (profile) {
          validIds.push(id);
        }
      }

      // Update the list with only valid entries
      cacheStorage.setObject(ProfileCache.PROFILES_CACHE_KEY, validIds);
    } catch (error) {
      console.warn('Failed to cleanup expired cache:', error);
    }
  }
}

export const profileCache = new ProfileCache();
