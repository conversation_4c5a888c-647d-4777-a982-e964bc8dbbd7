import { ApolloClient } from '@apollo/client';

import { client } from '@/src/core/api';

import {
  BLOCK_USER,
  CHECK_FOLLOWING_STATUS,
  FOLLOW_USER,
  GET_CONNECTION_STATUS,
  GET_USER_FOLLOWERS,
  GET_USER_FOLLOWING,
  GET_USER_PROFILE,
  GET_USER_STATS,
  REPORT_USER,
  UNBLOCK_USER,
  UNFOLLOW_USER,
  UPDATE_USER_PROFILE,
  UPLOAD_COVER_IMAGE,
  UPLOAD_PROFILE_PICTURE,
} from '../graphql/queries';
import type {
  FollowUserInput,
  FollowUserResponse,
  PaginationInput,
  User,
  UserFollowersResponse,
  UserFollowingResponse,
  UserUpdateInput,
} from '../types';

export interface ErrorResponse {
  message: string;
  extensions?: {
    code: string;
    statusCode?: number;
    [key: string]: any;
  };
}

export type ServiceResponse<T> =
  | { success: true; data: T; meta?: any }
  | { success: false; errors: ErrorResponse[]; networkError?: any };

class ProfileService {
  private client: ApolloClient<any>;

  constructor() {
    this.client = client;
  }

  // Get user profile
  async getUserProfile(userId: string): Promise<ServiceResponse<User>> {
    try {
      const { data, errors } = await this.client.query({
        query: GET_USER_PROFILE,
        variables: { id: userId },
        fetchPolicy: 'cache-first',
      });

      if (data?.user) {
        return {
          success: true,
          data: data.user,
        };
      }

      return {
        success: false,
        errors: errors as ErrorResponse[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'Failed to fetch user profile',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  // Get user stats
  async getUserStats(userId: string): Promise<
    ServiceResponse<{
      followersCount: number;
      followingCount: number;
      eventsHostedCount: number;
      eventsAttendedCount: number;
    }>
  > {
    try {
      const { data, errors } = await this.client.query({
        query: GET_USER_STATS,
        variables: { userId },
        fetchPolicy: 'cache-first',
      });

      if (data?.socialStats && data?.userAttendanceStats) {
        return {
          success: true,
          data: {
            followersCount: data.socialStats.followersCount || 0,
            followingCount: data.socialStats.followingCount || 0,
            eventsHostedCount: data.userAttendanceStats.totalEventsHosted || 0,
            eventsAttendedCount: data.userAttendanceStats.totalEventsAttended || 0,
          },
        };
      }

      return {
        success: false,
        errors: errors as ErrorResponse[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'Failed to fetch user stats',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  // Get user followers
  async getUserFollowers(
    userId: string,
    pagination?: PaginationInput
  ): Promise<ServiceResponse<UserFollowersResponse>> {
    try {
      const { data, errors } = await this.client.query({
        query: GET_USER_FOLLOWERS,
        variables: { userId, pagination },
        fetchPolicy: 'network-only',
      });

      if (data?.userFollowers) {
        return {
          success: true,
          data: data.userFollowers,
        };
      }

      return {
        success: false,
        errors: errors as ErrorResponse[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'Failed to fetch followers',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  // Get user following
  async getUserFollowing(
    userId: string,
    pagination?: PaginationInput
  ): Promise<ServiceResponse<UserFollowingResponse>> {
    try {
      const { data, errors } = await this.client.query({
        query: GET_USER_FOLLOWING,
        variables: { userId, pagination },
        fetchPolicy: 'network-only',
      });

      if (data?.userFollowing) {
        return {
          success: true,
          data: data.userFollowing,
        };
      }

      return {
        success: false,
        errors: errors as ErrorResponse[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'Failed to fetch following',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  // Check if following user
  async checkFollowingStatus(userId: string): Promise<ServiceResponse<boolean>> {
    try {
      const { data, errors } = await this.client.query({
        query: CHECK_FOLLOWING_STATUS,
        variables: { userId },
        fetchPolicy: 'network-only',
      });

      if (data?.isFollowing !== undefined) {
        return {
          success: true,
          data: data.isFollowing,
        };
      }

      return {
        success: false,
        errors: errors as ErrorResponse[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'Failed to check following status',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  // Get connection status
  async getConnectionStatus(userId: string): Promise<
    ServiceResponse<{
      isFollowing: boolean;
      isFollowedBy: boolean;
      isFriend: boolean;
      isBlocked: boolean;
      isBlockedBy: boolean;
      connectionType?: string;
      connectionId?: string;
    }>
  > {
    try {
      const { data, errors } = await this.client.query({
        query: GET_CONNECTION_STATUS,
        variables: { userId },
        fetchPolicy: 'network-only',
      });

      if (data?.connectionStatus) {
        return {
          success: true,
          data: data.connectionStatus,
        };
      }

      return {
        success: false,
        errors: errors as ErrorResponse[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'Failed to check connection status',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  // Follow user
  async followUser(input: FollowUserInput): Promise<ServiceResponse<FollowUserResponse>> {
    try {
      const { data, errors } = await this.client.mutate({
        mutation: FOLLOW_USER,
        variables: { input },
      });

      if (data?.followUser) {
        return {
          success: true,
          data: data.followUser,
        };
      }

      return {
        success: false,
        errors: errors as ErrorResponse[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'Failed to follow user',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  // Unfollow user
  async unfollowUser(
    userId: string
  ): Promise<ServiceResponse<{ success: boolean; message?: string }>> {
    try {
      const { data, errors } = await this.client.mutate({
        mutation: UNFOLLOW_USER,
        variables: { input: { userId } },
      });

      if (data?.unfollowUser) {
        return {
          success: true,
          data: data.unfollowUser,
        };
      }

      return {
        success: false,
        errors: errors as ErrorResponse[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'Failed to unfollow user',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  // Update user profile
  async updateUserProfile(input: UserUpdateInput): Promise<ServiceResponse<User>> {
    try {
      const { data, errors } = await this.client.mutate({
        mutation: UPDATE_USER_PROFILE,
        variables: { input },
      });

      if (data?.updateUser) {
        return {
          success: true,
          data: data.updateUser,
        };
      }

      return {
        success: false,
        errors: errors as ErrorResponse[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'Failed to update profile',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  // Upload profile picture
  async uploadProfilePicture(
    file: File
  ): Promise<ServiceResponse<{ success: boolean; message?: string; imageUrl?: string }>> {
    try {
      const { data, errors } = await this.client.mutate({
        mutation: UPLOAD_PROFILE_PICTURE,
        variables: { file },
      });

      if (data?.uploadProfilePicture) {
        return {
          success: true,
          data: data.uploadProfilePicture,
        };
      }

      return {
        success: false,
        errors: errors as ErrorResponse[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'Failed to upload profile picture',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  // Upload cover image
  async uploadCoverImage(
    file: File
  ): Promise<ServiceResponse<{ id: string; url: string; type: string; isPrimary: boolean }>> {
    try {
      const { data, errors } = await this.client.mutate({
        mutation: UPLOAD_COVER_IMAGE,
        variables: { file },
      });

      if (data?.uploadUserImage) {
        return {
          success: true,
          data: data.uploadUserImage,
        };
      }

      return {
        success: false,
        errors: errors as ErrorResponse[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'Failed to upload cover image',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  // Block user
  async blockUser(
    userId: string
  ): Promise<ServiceResponse<{ success: boolean; message?: string }>> {
    try {
      const { data, errors } = await this.client.mutate({
        mutation: BLOCK_USER,
        variables: { input: { userId } },
      });

      if (data?.blockUser) {
        return {
          success: true,
          data: data.blockUser,
        };
      }

      return {
        success: false,
        errors: errors as ErrorResponse[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'Failed to block user',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  // Unblock user
  async unblockUser(
    userId: string
  ): Promise<ServiceResponse<{ success: boolean; message?: string }>> {
    try {
      const { data, errors } = await this.client.mutate({
        mutation: UNBLOCK_USER,
        variables: { input: { userId } },
      });

      if (data?.unblockUser) {
        return {
          success: true,
          data: data.unblockUser,
        };
      }

      return {
        success: false,
        errors: errors as ErrorResponse[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'Failed to unblock user',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }

  // Report user
  async reportUser(
    userId: string,
    reason: string
  ): Promise<ServiceResponse<{ success: boolean; message?: string }>> {
    try {
      const { data, errors } = await this.client.mutate({
        mutation: REPORT_USER,
        variables: { input: { userId, reason } },
      });

      if (data?.reportUser) {
        return {
          success: true,
          data: data.reportUser,
        };
      }

      return {
        success: false,
        errors: errors as ErrorResponse[],
      };
    } catch (error: any) {
      return {
        success: false,
        errors: [
          {
            message: error.message || 'Failed to report user',
            extensions: {
              code: 'NETWORK_ERROR',
              statusCode: error.statusCode || 500,
            },
          },
        ],
        networkError: error.networkError,
      };
    }
  }
}

export const profileService = new ProfileService();
