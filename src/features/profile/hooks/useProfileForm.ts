import { useCallback } from 'react';

import { Alert } from 'react-native';

import { useAsyncFormSubmit, useZodForm } from '@/src/shared/forms/useZodForm';

import { profileService } from '../services';
import { useProfileStore } from '../store/profileStore';
import { ProfileUpdateInput, profileUpdateSchema } from '../validation';

/**
 * Hook for managing profile edit form with validation and submission
 */
export const useProfileForm = (initialData?: Partial<ProfileUpdateInput>) => {
  const { updateProfile, isUpdating } = useProfileStore();

  // Form with Zod validation
  const form = useZodForm(profileUpdateSchema, {
    defaultValues: {
      name: initialData?.name || '',
      username: initialData?.username || '',
      bio: initialData?.bio || '',
      location: initialData?.location || '',
      website: initialData?.website || '',
      dateOfBirth: initialData?.dateOfBirth || '',
    },
  });

  // Async submission handler
  const {
    isLoading: isSubmitting,
    error,
    handleSubmit,
    clearError,
  } = useAsyncFormSubmit(
    async (data: ProfileUpdateInput) => {
      await updateProfile(data);
    },
    {
      onSuccess: () => {
        Alert.alert('Success', 'Profile updated successfully!');
      },
      onError: error => {
        Alert.alert('Error', error.message);
      },
    }
  );

  // Field-specific handlers
  const handleAvatarUpload = useCallback(async (imageUri: string) => {
    try {
      await profileService.uploadAvatar(imageUri);
      Alert.alert('Success', 'Avatar updated successfully!');
    } catch (error) {
      Alert.alert('Error', 'Failed to upload avatar');
    }
  }, []);

  const handleCoverPhotoUpload = useCallback(async (imageUri: string) => {
    try {
      await profileService.uploadCoverPhoto(imageUri);
      Alert.alert('Success', 'Cover photo updated successfully!');
    } catch (error) {
      Alert.alert('Error', 'Failed to upload cover photo');
    }
  }, []);

  return {
    // Form state and methods
    ...form,

    // Submission
    onSubmit: handleSubmit,
    isSubmitting: isSubmitting || isUpdating,
    submitError: error,
    clearSubmitError: clearError,

    // File uploads
    handleAvatarUpload,
    handleCoverPhotoUpload,

    // Utility methods
    resetForm: () => form.reset(),
    isDirty: form.formState.isDirty,
    isValid: form.formState.isValid,
  };
};
