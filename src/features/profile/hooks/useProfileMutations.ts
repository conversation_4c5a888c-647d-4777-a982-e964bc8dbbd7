import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { useToast } from '@/src/core/libs';
import { extractErrorMessage } from '@/src/shared/utils/error';

import { profileService } from '../services/profileService';
import type {
  FollowUserInput,
  PaginationInput,
  User,
  UserFollowersResponse,
  UserFollowingResponse,
  UserUpdateInput,
} from '../types';

// Query Keys
export const profileQueryKeys = {
  all: ['profiles'] as const,
  profile: (userId: string) => [...profileQueryKeys.all, 'profile', userId] as const,
  stats: (userId: string) => [...profileQueryKeys.all, 'stats', userId] as const,
  followers: (userId: string, pagination?: PaginationInput) =>
    [...profileQueryKeys.all, 'followers', userId, pagination] as const,
  following: (userId: string, pagination?: PaginationInput) =>
    [...profileQueryKeys.all, 'following', userId, pagination] as const,
  followingStatus: (userId: string) =>
    [...profileQueryKeys.all, 'followingStatus', userId] as const,
  connectionStatus: (userId: string) =>
    [...profileQueryKeys.all, 'connectionStatus', userId] as const,
};

// Get User Profile
export const useUserProfile = (userId: string) => {
  return useQuery({
    queryKey: profileQueryKeys.profile(userId),
    queryFn: async () => {
      const response = await profileService.getUserProfile(userId);
      if (response.success) {
        return response.data;
      }
      throw new Error(extractErrorMessage(response.errors));
    },
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get User Stats
export const useUserStats = (userId: string) => {
  return useQuery({
    queryKey: profileQueryKeys.stats(userId),
    queryFn: async () => {
      const response = await profileService.getUserStats(userId);
      if (response.success) {
        return response.data;
      }
      throw new Error(extractErrorMessage(response.errors));
    },
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get User Followers
export const useUserFollowers = (userId: string, pagination?: PaginationInput) => {
  return useQuery({
    queryKey: profileQueryKeys.followers(userId, pagination),
    queryFn: async () => {
      const response = await profileService.getUserFollowers(userId, pagination);
      if (response.success) {
        return response.data;
      }
      throw new Error(extractErrorMessage(response.errors));
    },
    enabled: !!userId,
  });
};

// Get User Following
export const useUserFollowing = (userId: string, pagination?: PaginationInput) => {
  return useQuery({
    queryKey: profileQueryKeys.following(userId, pagination),
    queryFn: async () => {
      const response = await profileService.getUserFollowing(userId, pagination);
      if (response.success) {
        return response.data;
      }
      throw new Error(extractErrorMessage(response.errors));
    },
    enabled: !!userId,
  });
};

// Check Following Status
export const useFollowingStatus = (userId: string) => {
  return useQuery({
    queryKey: profileQueryKeys.followingStatus(userId),
    queryFn: async () => {
      const response = await profileService.checkFollowingStatus(userId);
      if (response.success) {
        return response.data;
      }
      throw new Error(extractErrorMessage(response.errors));
    },
    enabled: !!userId,
  });
};

// Get Connection Status
export const useConnectionStatus = (userId: string) => {
  return useQuery({
    queryKey: profileQueryKeys.connectionStatus(userId),
    queryFn: async () => {
      const response = await profileService.getConnectionStatus(userId);
      if (response.success) {
        return response.data;
      }
      throw new Error(extractErrorMessage(response.errors));
    },
    enabled: !!userId,
  });
};

// Follow User Mutation
export const useFollowUser = () => {
  const toast = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (input: FollowUserInput) => profileService.followUser(input),
    onSuccess: (response, variables) => {
      if (response.success) {
        toast.show({
          type: 'success',
          text1: 'Following user',
        });

        // Invalidate related queries
        queryClient.invalidateQueries({
          queryKey: profileQueryKeys.followingStatus(variables.userId),
        });
        queryClient.invalidateQueries({
          queryKey: profileQueryKeys.connectionStatus(variables.userId),
        });
        queryClient.invalidateQueries({
          queryKey: profileQueryKeys.stats(variables.userId),
        });
      } else {
        toast.show({
          type: 'error',
          text1: 'Failed to follow user',
          text2: extractErrorMessage(response.errors),
        });
      }
    },
    onError: error => {
      toast.show({
        type: 'error',
        text1: 'Network error',
        text2: error.message,
      });
    },
  });
};

// Unfollow User Mutation
export const useUnfollowUser = () => {
  const toast = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (userId: string) => profileService.unfollowUser(userId),
    onSuccess: (response, userId) => {
      if (response.success) {
        toast.show({
          type: 'success',
          text1: 'Unfollowed user',
        });

        // Invalidate related queries
        queryClient.invalidateQueries({
          queryKey: profileQueryKeys.followingStatus(userId),
        });
        queryClient.invalidateQueries({
          queryKey: profileQueryKeys.connectionStatus(userId),
        });
        queryClient.invalidateQueries({
          queryKey: profileQueryKeys.stats(userId),
        });
      } else {
        toast.show({
          type: 'error',
          text1: 'Failed to unfollow user',
          text2: extractErrorMessage(response.errors),
        });
      }
    },
    onError: error => {
      toast.show({
        type: 'error',
        text1: 'Network error',
        text2: error.message,
      });
    },
  });
};

// Update User Profile Mutation
export const useUpdateUserProfile = () => {
  const toast = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (input: UserUpdateInput) => profileService.updateUserProfile(input),
    onSuccess: response => {
      if (response.success) {
        toast.show({
          type: 'success',
          text1: 'Profile updated successfully',
        });

        // Update the cache with new user data
        if (response.data) {
          queryClient.setQueryData(profileQueryKeys.profile(response.data.id), response.data);
        }

        // Invalidate all profile queries to ensure consistency
        queryClient.invalidateQueries({
          queryKey: profileQueryKeys.all,
        });
      } else {
        toast.show({
          type: 'error',
          text1: 'Failed to update profile',
          text2: extractErrorMessage(response.errors),
        });
      }
    },
    onError: error => {
      toast.show({
        type: 'error',
        text1: 'Network error',
        text2: error.message,
      });
    },
  });
};

// Upload Profile Picture Mutation
export const useUploadProfilePicture = () => {
  const toast = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (file: File) => profileService.uploadProfilePicture(file),
    onSuccess: response => {
      if (response.success) {
        toast.show({
          type: 'success',
          text1: 'Profile picture updated',
        });

        // Invalidate profile queries to refresh avatar
        queryClient.invalidateQueries({
          queryKey: profileQueryKeys.all,
        });
      } else {
        toast.show({
          type: 'error',
          text1: 'Failed to upload profile picture',
          text2: extractErrorMessage(response.errors),
        });
      }
    },
    onError: error => {
      toast.show({
        type: 'error',
        text1: 'Upload failed',
        text2: error.message,
      });
    },
  });
};

// Upload Cover Image Mutation
export const useUploadCoverImage = () => {
  const toast = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (file: File) => profileService.uploadCoverImage(file),
    onSuccess: response => {
      if (response.success) {
        toast.show({
          type: 'success',
          text1: 'Cover image updated',
        });

        // Invalidate profile queries to refresh cover
        queryClient.invalidateQueries({
          queryKey: profileQueryKeys.all,
        });
      } else {
        toast.show({
          type: 'error',
          text1: 'Failed to upload cover image',
          text2: extractErrorMessage(response.errors),
        });
      }
    },
    onError: error => {
      toast.show({
        type: 'error',
        text1: 'Upload failed',
        text2: error.message,
      });
    },
  });
};

// Block User Mutation
export const useBlockUser = () => {
  const toast = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (userId: string) => profileService.blockUser(userId),
    onSuccess: (response, userId) => {
      if (response.success) {
        toast.show({
          type: 'success',
          text1: 'User blocked',
        });

        // Invalidate connection status
        queryClient.invalidateQueries({
          queryKey: profileQueryKeys.connectionStatus(userId),
        });
      } else {
        toast.show({
          type: 'error',
          text1: 'Failed to block user',
          text2: extractErrorMessage(response.errors),
        });
      }
    },
    onError: error => {
      toast.show({
        type: 'error',
        text1: 'Network error',
        text2: error.message,
      });
    },
  });
};

// Unblock User Mutation
export const useUnblockUser = () => {
  const toast = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (userId: string) => profileService.unblockUser(userId),
    onSuccess: (response, userId) => {
      if (response.success) {
        toast.show({
          type: 'success',
          text1: 'User unblocked',
        });

        // Invalidate connection status
        queryClient.invalidateQueries({
          queryKey: profileQueryKeys.connectionStatus(userId),
        });
      } else {
        toast.show({
          type: 'error',
          text1: 'Failed to unblock user',
          text2: extractErrorMessage(response.errors),
        });
      }
    },
    onError: error => {
      toast.show({
        type: 'error',
        text1: 'Network error',
        text2: error.message,
      });
    },
  });
};

// Report User Mutation
export const useReportUser = () => {
  const toast = useToast();

  return useMutation({
    mutationFn: ({ userId, reason }: { userId: string; reason: string }) =>
      profileService.reportUser(userId, reason),
    onSuccess: response => {
      if (response.success) {
        toast.show({
          type: 'success',
          text1: 'User reported',
          text2: 'Thank you for your report',
        });
      } else {
        toast.show({
          type: 'error',
          text1: 'Failed to report user',
          text2: extractErrorMessage(response.errors),
        });
      }
    },
    onError: error => {
      toast.show({
        type: 'error',
        text1: 'Network error',
        text2: error.message,
      });
    },
  });
};
