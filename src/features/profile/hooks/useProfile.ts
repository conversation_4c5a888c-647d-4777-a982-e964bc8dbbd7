import { useEffect } from 'react';

import { useProfileStore } from '../store';

/**
 * Profile ViewModel Hook - Manages profile state and operations
 * Follows MVVM pattern: connects Profile Store (Model) to Profile Screens (View)
 * Now uses Zustand store for state management
 */
export const useProfile = (userId?: string) => {
  const store = useProfileStore();

  // Load profile on mount or when userId changes
  useEffect(() => {
    store.loadProfile(userId);
  }, [userId, store.loadProfile]);

  return {
    // Profile data
    profile: store.profile,
    profileSettings: store.profileSettings,

    // Loading states
    isLoading: store.isLoading,
    isUpdating: store.isUpdating,
    isUploading: store.isUploading,

    // Error handling
    error: store.error,
    clearError: store.clearError,

    // Actions
    loadProfile: store.loadProfile,
    updateProfile: store.updateProfile,
    refreshProfile: store.refreshProfile,
    uploadAvatar: store.uploadAvatar,
    uploadCoverPhoto: store.uploadCoverPhoto,
    deleteAccount: store.deleteAccount,

    // Settings
    updateSettings: store.updateSettings,

    // UI state
    isEditing: store.isEditing,
    setEditing: store.setEditing,

    // Utility
    refetch: store.refreshProfile,
  };
};
