import React, { useCallback, useState } from 'react';

import { useAuthStore } from '@/src/features/auth/store/authStore';

import type { ProfileAction, UserProfile } from '../types';

// Mock data - replace with actual API calls
const mockProfile: UserProfile = {
  id: '1',
  name: '<PERSON>',
  username: 'joh<PERSON><PERSON>',
  email: '<EMAIL>',
  avatar: undefined,
  bio: 'Software engineer who loves to party and meet new people',
  location: {
    from: 'Paraiba, BR',
    current: 'Dublin, IE',
  },
  stats: {
    followers: 2,
    following: 12,
    eventsHosted: 15,
    eventsAttended: 17,
    reviews: 12,
    photos: 4,
    averageRating: 4.5,
  },
  isVerified: true,
  isVip: false,
  followsYou: false,
  isFollowing: false,
  createdAt: '2024-01-01',
  preferences: {
    isProfilePublic: true,
    showLocation: true,
    showEmail: false,
    allowMessages: true,
    allowEventInvites: true,
  },
};

export const useUserProfile = (userId?: string) => {
  const currentUser = useAuthStore(state => state.user);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Simulate API call
  React.useEffect(() => {
    const fetchProfile = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));

        // If no userId or userId matches current user, return own profile
        if (!userId || userId === currentUser?.id) {
          setProfile({
            ...mockProfile,
            id: currentUser?.id || '1',
            name: currentUser?.name || mockProfile.name,
            email: currentUser?.email || mockProfile.email,
          });
        } else {
          // Return other user's profile
          setProfile({
            ...mockProfile,
            id: userId,
            name: 'Other User',
            followsYou: true,
            isFollowing: false,
          });
        }
      } catch (err) {
        setError('Failed to load profile');
      } finally {
        setIsLoading(false);
      }
    };

    fetchProfile();
  }, [userId, currentUser]);

  const handleProfileAction = useCallback(async (action: ProfileAction) => {
    try {
      switch (action.type) {
        case 'follow':
        case 'unfollow':
          setProfile(prev =>
            prev
              ? {
                  ...prev,
                  isFollowing: action.type === 'follow',
                  stats: {
                    ...prev.stats,
                    followers: prev.stats.followers + (action.type === 'follow' ? 1 : -1),
                  },
                }
              : null
          );
          break;
        // Handle other actions
      }
    } catch (err) {
      console.error('Profile action failed:', err);
    }
  }, []);

  const updateProfile = useCallback(async (updates: Partial<UserProfile>) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      setProfile(prev => (prev ? { ...prev, ...updates } : null));
      return true;
    } catch (err) {
      console.error('Profile update failed:', err);
      return false;
    }
  }, []);

  return {
    profile,
    isLoading,
    error,
    handleProfileAction,
    updateProfile,
    isOwnProfile: !userId || userId === currentUser?.id,
  };
};
