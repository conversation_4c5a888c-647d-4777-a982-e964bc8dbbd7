import { z } from 'zod';

import {
  birthDateSchema,
  emailSchema,
  imageFileSchema,
  longTextSchema,
  optionalString,
  requiredString,
  shortTextSchema,
  urlSchema,
  usernameSchema,
} from '@/src/shared/validation/common';

/**
 * Profile-specific validation schemas
 */

// Basic profile information
export const profileUpdateSchema = z.object({
  name: requiredString
    .min(2, 'Name must be at least 2 characters')
    .max(50, 'Name must be less than 50 characters'),
  username: usernameSchema.optional(),
  bio: longTextSchema(500).optional().or(z.literal('')),
  location: shortTextSchema(100).optional().or(z.literal('')),
  website: urlSchema,
  dateOfBirth: birthDateSchema.optional().or(z.literal('')),
});

// Profile settings
export const profileSettingsSchema = z.object({
  isPrivate: z.boolean(),
  allowNotifications: z.boolean(),
  allowDirectMessages: z.boolean(),
  showEmail: z.boolean(),
  showLocation: z.boolean(),
  showBirthDate: z.boolean(),
});

// Avatar upload
export const avatarUploadSchema = z.object({
  image: imageFileSchema,
});

// Cover photo upload
export const coverPhotoUploadSchema = z.object({
  image: imageFileSchema,
});

// Account verification
export const verificationRequestSchema = z.object({
  type: z.enum(['identity', 'business', 'creator']),
  documents: z.array(imageFileSchema).min(1, 'At least one document is required'),
  additionalInfo: longTextSchema(1000).optional(),
});

// Privacy settings
export const privacySettingsSchema = z.object({
  profileVisibility: z.enum(['public', 'followers', 'private']),
  contactVisibility: z.enum(['everyone', 'followers', 'nobody']),
  searchable: z.boolean(),
  allowTagging: z.boolean(),
  allowMentions: z.boolean(),
});

// Notification preferences
export const notificationPreferencesSchema = z.object({
  pushNotifications: z.boolean(),
  emailNotifications: z.boolean(),
  smsNotifications: z.boolean(),
  likes: z.boolean(),
  comments: z.boolean(),
  follows: z.boolean(),
  mentions: z.boolean(),
  directMessages: z.boolean(),
  marketing: z.boolean(),
});

// Account deletion
export const accountDeletionSchema = z.object({
  reason: z.enum(['privacy_concerns', 'too_much_time', 'not_useful', 'technical_issues', 'other']),
  feedback: longTextSchema(500).optional(),
  confirmPassword: requiredString,
});

// Export types inferred from schemas
export type ProfileUpdateInput = z.infer<typeof profileUpdateSchema>;
export type ProfileSettingsInput = z.infer<typeof profileSettingsSchema>;
export type AvatarUploadInput = z.infer<typeof avatarUploadSchema>;
export type CoverPhotoUploadInput = z.infer<typeof coverPhotoUploadSchema>;
export type VerificationRequestInput = z.infer<typeof verificationRequestSchema>;
export type PrivacySettingsInput = z.infer<typeof privacySettingsSchema>;
export type NotificationPreferencesInput = z.infer<typeof notificationPreferencesSchema>;
export type AccountDeletionInput = z.infer<typeof accountDeletionSchema>;
