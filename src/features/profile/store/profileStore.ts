import { devtools } from '@csark0812/zustand-expo-devtools';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

import { createZustandMMKVStorage, userStorage } from '@/src/core/storage';

import { profileService } from '../services';
import type { ProfileSettings, ProfileUpdateData, UserProfile } from '../types';
import { NotificationPreferencesInput, PrivacySettingsInput } from '../validation';

/**
 * Profile feature state management with Zustand
 * Follows the feature-driven architecture pattern
 */

interface ProfileState {
  // Current profile data
  profile: UserProfile | null;
  profileSettings: ProfileSettings | null;

  // Loading states
  isLoading: boolean;
  isUpdating: boolean;
  isUploading: boolean;

  // Error handling
  error: string | null;

  // Cache management
  lastFetched: number | null;

  // View states
  isEditing: boolean;
}

interface ProfileActions {
  // Profile data actions
  loadProfile: (userId?: string) => Promise<void>;
  updateProfile: (data: ProfileUpdateData) => Promise<void>;
  refreshProfile: () => Promise<void>;

  // Settings actions
  updateSettings: (settings: Partial<ProfileSettings>) => Promise<void>;
  updatePrivacySettings: (settings: PrivacySettingsInput) => Promise<void>;
  updateNotificationPreferences: (preferences: NotificationPreferencesInput) => Promise<void>;

  // File upload actions
  uploadAvatar: (imageUri: string) => Promise<void>;
  uploadCoverPhoto: (imageUri: string) => Promise<void>;

  // Account management
  deleteAccount: () => Promise<void>;

  // UI state actions
  setEditing: (isEditing: boolean) => void;
  clearError: () => void;

  // Cache management
  invalidateCache: () => void;

  // Reset actions
  resetProfile: () => void;
}

type ProfileStore = ProfileState & ProfileActions;

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

export const useProfileStore = create<ProfileStore>()(
  devtools(
    persist(
      immer((set, get) => ({
        // Initial state
        profile: null,
        profileSettings: null,
        isLoading: false,
        isUpdating: false,
        isUploading: false,
        error: null,
        lastFetched: null,
        isEditing: false,

        // Profile data actions
        loadProfile: async (userId?: string) => {
          const state = get();

          // Check cache validity
          if (
            state.profile &&
            state.lastFetched &&
            Date.now() - state.lastFetched < CACHE_DURATION &&
            !userId // Only use cache for current user
          ) {
            return;
          }

          set(state => {
            state.isLoading = true;
            state.error = null;
          });

          try {
            const profile = await profileService.getProfile(userId);
            set(state => {
              state.profile = profile;
              state.isLoading = false;
              state.lastFetched = Date.now();
            });
          } catch (error) {
            set(state => {
              state.isLoading = false;
              state.error = error instanceof Error ? error.message : 'Failed to load profile';
            });
          }
        },

        updateProfile: async (data: ProfileUpdateData) => {
          set(state => {
            state.isUpdating = true;
            state.error = null;
          });

          try {
            const updatedProfile = await profileService.updateProfile(data);
            set(state => {
              state.profile = updatedProfile;
              state.isUpdating = false;
              state.lastFetched = Date.now();
            });
          } catch (error) {
            set(state => {
              state.isUpdating = false;
              state.error = error instanceof Error ? error.message : 'Failed to update profile';
            });
            throw error; // Re-throw for form handling
          }
        },

        refreshProfile: async () => {
          set(state => {
            state.lastFetched = null; // Force refresh
          });
          await get().loadProfile();
        },

        // Settings actions
        updateSettings: async (settings: Partial<ProfileSettings>) => {
          set(state => {
            state.isUpdating = true;
            state.error = null;
          });

          try {
            // TODO: Implement settings API call
            await new Promise(resolve => setTimeout(resolve, 1000));

            set(state => {
              if (state.profileSettings) {
                Object.assign(state.profileSettings, settings);
              }
              state.isUpdating = false;
            });
          } catch (error) {
            set(state => {
              state.isUpdating = false;
              state.error = error instanceof Error ? error.message : 'Failed to update settings';
            });
          }
        },

        updatePrivacySettings: async (settings: PrivacySettingsInput) => {
          // Implementation for privacy settings
          await get().updateSettings(settings as any);
        },

        updateNotificationPreferences: async (preferences: NotificationPreferencesInput) => {
          // Implementation for notification preferences
          await get().updateSettings(preferences as any);
        },

        // File upload actions
        uploadAvatar: async (imageUri: string) => {
          set(state => {
            state.isUploading = true;
            state.error = null;
          });

          try {
            const avatarUrl = await profileService.uploadAvatar(imageUri);

            // Update profile with new avatar
            set(state => {
              if (state.profile) {
                state.profile.avatar = avatarUrl;
              }
              state.isUploading = false;
            });
          } catch (error) {
            set(state => {
              state.isUploading = false;
              state.error = error instanceof Error ? error.message : 'Failed to upload avatar';
            });
            throw error;
          }
        },

        uploadCoverPhoto: async (imageUri: string) => {
          set(state => {
            state.isUploading = true;
            state.error = null;
          });

          try {
            const coverUrl = await profileService.uploadCoverPhoto(imageUri);

            // Update profile with new cover photo
            set(state => {
              if (state.profile) {
                state.profile.coverPhoto = coverUrl;
              }
              state.isUploading = false;
            });
          } catch (error) {
            set(state => {
              state.isUploading = false;
              state.error = error instanceof Error ? error.message : 'Failed to upload cover photo';
            });
            throw error;
          }
        },

        // Account management
        deleteAccount: async () => {
          set(state => {
            state.isUpdating = true;
            state.error = null;
          });

          try {
            await profileService.deleteAccount();
            set(state => {
              state.profile = null;
              state.profileSettings = null;
              state.isUpdating = false;
              state.lastFetched = null;
            });
          } catch (error) {
            set(state => {
              state.isUpdating = false;
              state.error = error instanceof Error ? error.message : 'Failed to delete account';
            });
            throw error;
          }
        },

        // UI state actions
        setEditing: (isEditing: boolean) => {
          set(state => {
            state.isEditing = isEditing;
          });
        },

        clearError: () => {
          set(state => {
            state.error = null;
          });
        },

        // Cache management
        invalidateCache: () => {
          set(state => {
            state.lastFetched = null;
          });
        },

        // Reset actions
        resetProfile: () => {
          set(state => {
            state.profile = null;
            state.profileSettings = null;
            state.isLoading = false;
            state.isUpdating = false;
            state.isUploading = false;
            state.error = null;
            state.lastFetched = null;
            state.isEditing = false;
          });
        },
      })),
      {
        name: 'profile-store',
        storage: createJSONStorage(() => createZustandMMKVStorage(userStorage)),
        partialize: state => ({
          profile: state.profile,
          profileSettings: state.profileSettings,
          lastFetched: state.lastFetched,
        }),
      }
    ),
    { name: 'ProfileStore' }
  )
);
