import React from 'react';

import { ScrollView } from 'react-native';

import { Box, Pressable, Text } from '@/src/core/theme';

import type { EventFilter, ProfileTab } from '../types';

interface ProfileTabsProps {
  activeTab: ProfileTab;
  onTabChange: (tab: ProfileTab) => void;
  eventFilter?: EventFilter;
  onEventFilterChange?: (filter: EventFilter) => void;
}

const tabs: { key: ProfileTab; label: string }[] = [
  { key: 'events', label: 'Events' },
  { key: 'favorites', label: 'Favorites' },
  { key: 'reviews', label: 'Reviews' },
  { key: 'photos', label: 'Photos' },
];

const eventFilters: { key: EventFilter; label: string }[] = [
  { key: 'attending', label: 'Attending' },
  { key: 'hosting', label: 'Hosting' },
  { key: 'attended', label: 'Attended' },
  { key: 'hosted', label: 'Hosted' },
];

export const ProfileTabs: React.FC<ProfileTabsProps> = ({
  activeTab,
  onTabChange,
  eventFilter,
  onEventFilterChange,
}) => {
  return (
    <Box>
      {/* Main Tabs */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{ paddingHorizontal: 16 }}>
        <Box flexDirection="row" gap="lg_24">
          {tabs.map(tab => (
            <Pressable key={tab.key} onPress={() => onTabChange(tab.key)} paddingVertical="sm_12">
              <Text
                variant="h_16Medium_formLabel"
                color={activeTab === tab.key ? 'primary' : 'textSecondary'}>
                {tab.label}
              </Text>
              {activeTab === tab.key && (
                <Box
                  position="absolute"
                  bottom={0}
                  left={0}
                  right={0}
                  height={2}
                  backgroundColor="primary"
                  borderRadius="circle_9999"
                />
              )}
            </Pressable>
          ))}
        </Box>
      </ScrollView>

      {/* Event Filters - Show only when events tab is active */}
      {activeTab === 'events' && eventFilter && onEventFilterChange && (
        <Box marginTop="sm_12">
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{ paddingHorizontal: 16 }}>
            <Box flexDirection="row" gap="xs_8">
              {eventFilters.map(filter => (
                <Pressable
                  key={filter.key}
                  onPress={() => onEventFilterChange(filter.key)}
                  backgroundColor={eventFilter === filter.key ? 'primary' : 'transparent'}
                  borderWidth={1}
                  borderColor={eventFilter === filter.key ? 'primary' : 'border'}
                  borderRadius="sm_8"
                  paddingHorizontal="sm_12"
                  paddingVertical="xs_8">
                  <Text
                    variant="b_14Medium_button"
                    color={eventFilter === filter.key ? 'white' : 'primary'}>
                    {filter.label}
                  </Text>
                </Pressable>
              ))}
            </Box>
          </ScrollView>
        </Box>
      )}
    </Box>
  );
};
