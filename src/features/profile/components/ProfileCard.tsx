import React from 'react';

import { User } from 'phosphor-react-native';

import { Box, Text } from '@/src/core/theme';
import { Avatar, Button } from '@/src/shared/components';

import type { UserProfile } from '../types';

interface ProfileCardProps {
  profile: UserProfile;
  showFollowButton?: boolean;
  onPress?: () => void;
  onFollowPress?: () => void;
}

export const ProfileCard: React.FC<ProfileCardProps> = ({
  profile,
  showFollowButton = false,
  onPress,
  onFollowPress,
}) => {
  return (
    <Box backgroundColor="surfaceBackground" borderRadius="md_12" padding="md_16" onPress={onPress}>
      <Box flexDirection="row" alignItems="center" gap="sm_12">
        {/* Avatar */}
        <Avatar user={profile} size="m" />

        {/* Profile Info */}
        <Box flex={1}>
          <Box flexDirection="row" alignItems="center" gap="xxs_4">
            <Text variant="b_16SemiBold_button" color="mainText" numberOfLines={1}>
              {profile.name}
            </Text>
            {profile.isVerified && (
              <Box
                width={16}
                height={16}
                borderRadius="sm_8"
                backgroundColor="primary"
                alignItems="center"
                justifyContent="center">
                <Text variant="l_10SemiBold_tag" color="white">
                  ✓
                </Text>
              </Box>
            )}
          </Box>

          {profile.username && (
            <Text variant="b_14Regular_content" color="textSecondary" numberOfLines={1}>
              @{profile.username}
            </Text>
          )}

          {profile.bio && (
            <Text
              variant="b_14Regular_content"
              color="mainText"
              numberOfLines={2}
              marginTop="xxs_4">
              {profile.bio}
            </Text>
          )}

          {/* Stats */}
          <Box flexDirection="row" gap="md_16" marginTop="xs_8">
            <Text variant="l_12Regular_helperText" color="textSecondary">
              {profile.followersCount.toLocaleString()} followers
            </Text>
            <Text variant="l_12Regular_helperText" color="textSecondary">
              {profile.postsCount.toLocaleString()} posts
            </Text>
          </Box>
        </Box>

        {/* Follow Button */}
        {showFollowButton && <Button title="Follow" variant="secondary" onPress={onFollowPress} />}
      </Box>
    </Box>
  );
};

export default ProfileCard;
