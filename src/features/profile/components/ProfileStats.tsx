import React from 'react';

import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

import { SettingsStackParamList } from '@/src/core/navigation/types';
import { Box, Pressable, Text } from '@/src/core/theme';
import { Divider } from '@/src/shared/components/Divider';

import type { ProfileStats as ProfileStatsType } from '../types';

interface ProfileStatsProps {
  stats: ProfileStatsType;
  userId: string;
}

type NavigationProp = NativeStackNavigationProp<SettingsStackParamList>;

export const ProfileStats: React.FC<ProfileStatsProps> = ({ stats, userId }) => {
  const navigation = useNavigation<NavigationProp>();

  const statItems = [
    {
      count: stats.followers,
      label: 'Followers',
      onPress: () => navigation.navigate('Followers', { userId }),
    },
    {
      count: stats.following,
      label: 'Following',
      onPress: () => navigation.navigate('Following', { userId }),
    },
    {
      count: stats.eventsHosted + stats.eventsAttended,
      label: 'Events',
      onPress: () => navigation.navigate('UserEvents', { userId }),
    },
    {
      count: stats.reviews,
      label: 'Reviews',
      onPress: () => navigation.navigate('UserReviews', { userId }),
    },
    {
      count: stats.photos,
      label: 'Photos',
      onPress: () => navigation.navigate('UserPhotos', { userId }),
    },
  ];

  return (
    <Box flexDirection="row" paddingVertical="md_16">
      {statItems.map((item, index) => (
        <React.Fragment key={item.label}>
          <Pressable flex={1} alignItems="center" gap="xxs_4" onPress={item.onPress}>
            <Text variant="h_20Medium_subsection" color="mainText">
              {item.count}
            </Text>
            <Text variant="b_12Medium_CardSubtitle" color="mutedText">
              {item.label}
            </Text>
          </Pressable>
          {index < statItems.length - 1 && (
            <Box paddingHorizontal="xs_8">
              <Divider orientation="vertical" height={40} />
            </Box>
          )}
        </React.Fragment>
      ))}
    </Box>
  );
};
