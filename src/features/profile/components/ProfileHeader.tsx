import React from 'react';

import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { DotsThree, Pencil, UserPlus } from 'phosphor-react-native';

import { SettingsStackParamList } from '@/src/core/navigation/types';
import { Box, Pressable, Text, useTheme } from '@/src/core/theme';
import { Avatar } from '@/src/shared/components';

import type { UserProfile } from '../types';

interface ProfileHeaderProps {
  profile: UserProfile;
  isOwnProfile: boolean;
  onFollowPress?: () => void;
  onMessagePress?: () => void;
  onMorePress?: () => void;
}

type NavigationProp = NativeStackNavigationProp<SettingsStackParamList>;

export const ProfileHeader: React.FC<ProfileHeaderProps> = ({
  profile,
  isOwnProfile,
  onFollowPress,
  onMessagePress,
  onMorePress,
}) => {
  const theme = useTheme();
  const navigation = useNavigation<NavigationProp>();

  const handleEditPress = () => {
    navigation.navigate('EditProfile');
  };

  return (
    <Box>
      {/* Profile Info Section */}
      <Box flexDirection="row" gap="md_16" marginBottom="md_16">
        <Box borderRadius="sm_8" overflow="hidden">
          <Avatar
            size="xl"
            source={profile.avatar ? { uri: profile.avatar } : undefined}
            fallbackText={profile.name.charAt(0)}
          />
        </Box>

        <Box flex={1} gap="xs_8">
          <Box flexDirection="row" alignItems="flex-end" gap="xs_8">
            <Text variant="h_24SemiBold_section" color="text">
              {profile.name}
            </Text>
            {profile.followsYou && !isOwnProfile && (
              <Box
                backgroundColor="tagDefaultBackground"
                paddingHorizontal="xs_8"
                paddingVertical="xxs_4"
                borderRadius="md_12">
                <Text variant="l_10SemiBold_tag" color="tagDefaultText">
                  Follows you
                </Text>
              </Box>
            )}
          </Box>

          {profile.location && (
            <Box gap="xxs_4">
              {profile.location.from && (
                <Text variant="b_14Regular_content" color="text">
                  From: {profile.location.from}
                </Text>
              )}
              {profile.location.current && (
                <Text variant="b_14Regular_content" color="text">
                  Live: {profile.location.current}
                </Text>
              )}
            </Box>
          )}
        </Box>
      </Box>

      {/* Bio Section */}
      {profile.bio && (
        <Box marginBottom="md_16">
          <Text variant="b_14Regular_content" color="textSecondary" numberOfLines={3}>
            {profile.bio}
          </Text>
        </Box>
      )}

      {/* Action Buttons */}
      <Box flexDirection="row" gap="sm_12" marginBottom="md_16">
        {isOwnProfile ? (
          <Pressable
            flex={1}
            backgroundColor="primary"
            borderRadius="lg_16"
            paddingVertical="sm_12"
            alignItems="center"
            onPress={handleEditPress}>
            <Box flexDirection="row" gap="xs_8" alignItems="center">
              <Pencil size={20} color={theme.colors.white} />
              <Text variant="b_16Medium_button" color="white">
                Edit Profile
              </Text>
            </Box>
          </Pressable>
        ) : (
          <>
            <Pressable
              flex={1}
              backgroundColor={profile.isFollowing ? 'surface' : 'primary'}
              borderRadius="lg_16"
              paddingVertical="sm_12"
              alignItems="center"
              onPress={onFollowPress}>
              <Box flexDirection="row" gap="xs_8" alignItems="center">
                {!profile.isFollowing && <UserPlus size={20} color={theme.colors.white} />}
                <Text variant="b_16Medium_button" color={profile.isFollowing ? 'primary' : 'white'}>
                  {profile.isFollowing ? 'Following' : 'Follow'}
                </Text>
              </Box>
            </Pressable>

            <Pressable
              backgroundColor="card"
              borderRadius="lg_16"
              paddingHorizontal="md_16"
              paddingVertical="sm_12"
              borderWidth={1}
              borderColor="border"
              onPress={onMorePress}>
              <DotsThree size={20} color={theme.colors.text} />
            </Pressable>
          </>
        )}
      </Box>
    </Box>
  );
};
