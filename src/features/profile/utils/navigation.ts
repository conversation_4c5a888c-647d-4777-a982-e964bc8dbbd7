import { NavigationProp } from '@react-navigation/native';

import { RootStackParamList, SettingsStackParamList } from '@/src/core/navigation/types';

/**
 * Navigate to a user's profile
 * @param navigation - The navigation object
 * @param userId - The user ID to navigate to (optional for own profile)
 * @param options - Additional navigation options
 */
export const navigateToProfile = (
  navigation: NavigationProp<RootStackParamList & SettingsStackParamList>,
  userId?: string,
  options?: {
    isOwnProfile?: boolean;
    fromEvent?: boolean;
    eventId?: string;
  }
) => {
  const { isOwnProfile = false, fromEvent = false, eventId } = options || {};

  if (isOwnProfile || !userId) {
    // Navigate to own profile through Settings
    navigation.navigate('Settings', {
      screen: 'Profile',
      params: { isOwnProfile: true },
    });
  } else {
    // Navigate to other user's profile as modal
    navigation.navigate('UserProfile', {
      userId,
      fromEvent,
      eventId,
    });
  }
};

/**
 * Navigate to host profile from an event
 * @param navigation - The navigation object
 * @param hostId - The host user ID
 * @param eventId - The event ID
 */
export const navigateToHostProfile = (
  navigation: NavigationProp<RootStackParamList>,
  hostId: string,
  eventId: string
) => {
  navigation.navigate('HostProfileModal', {
    userId: hostId,
    eventId,
  });
};

/**
 * Navigate to followers list
 * @param navigation - The navigation object
 * @param userId - The user ID whose followers to show
 */
export const navigateToFollowers = (
  navigation: NavigationProp<SettingsStackParamList>,
  userId: string
) => {
  navigation.navigate('Followers', { userId });
};

/**
 * Navigate to following list
 * @param navigation - The navigation object
 * @param userId - The user ID whose following to show
 */
export const navigateToFollowing = (
  navigation: NavigationProp<SettingsStackParamList>,
  userId: string
) => {
  navigation.navigate('Following', { userId });
};
