import { gql } from '@apollo/client';

// User fragments for reusability
export const USER_BASIC_FIELDS = gql`
  fragment UserBasicFields on User {
    id
    name
    email
    phoneNumber
    avatar
    profileImageURL
    coverImageURL
    isEmailVerified
    isPhoneVerified
    role
    createdAt
    updatedAt
    onlineStatus
    lastActiveAt
    activityVisibility
    locationSharingEnabled
    currentEventId
    profileCompleteness
    loginCount
  }
`;

export const PARTY_BASIC_FIELDS = gql`
  fragment PartyBasicFields on Party {
    id
    name
    title
    description
    type
    eventType
    status
    visibility
    startDate
    endDate
    startTime
    endTime
    maxGuests
    isComplete
    creatorId
    venueId
    coverImageId
    isPrivate
    ageRestriction
    tags
    category
    createdAt
    updatedAt
    address
    city
    placeType
    isFree
    ticketURL
    musicGenres
    performers
    dressCode
    isCancelled
    attendeeCount
    averageRating
    isUserAttending
    isUserInvited
    isUserHost
    location {
      lat
      lng
    }
  }
`;

export const CONNECTION_FIELDS = gql`
  fragment ConnectionFields on Connection {
    id
    followerId
    followingId
    connectionType
    createdAt
    interactionScore
    lastInteractionAt
    follower {
      ...UserBasicFields
    }
    following {
      ...UserBasicFields
    }
  }
  ${USER_BASIC_FIELDS}
`;

// Queries
export const GET_USER_PROFILE = gql`
  query GetUserProfile($id: UUID!) {
    user(id: $id) {
      ...UserBasicFields
      hostedParties {
        ...PartyBasicFields
      }
      attendedParties {
        id
        partyId
        userId
        status
        createdAt
        updatedAt
        party {
          ...PartyBasicFields
        }
      }
      friends {
        ...UserBasicFields
      }
      images {
        id
        userId
        url
        type
        isPrimary
        order
        createdAt
        updatedAt
      }
      artistProfile {
        id
        userId
        stageName
        bio
        genres
        socialLinks
        verified
        status
        createdAt
        updatedAt
      }
    }
  }
  ${USER_BASIC_FIELDS}
  ${PARTY_BASIC_FIELDS}
`;

export const GET_USER_FOLLOWERS = gql`
  query GetUserFollowers($userId: UUID!, $pagination: PaginationInput) {
    userFollowers(userId: $userId, pagination: $pagination) {
      totalCount
      hasNextPage
      endCursor
      followers {
        ...ConnectionFields
      }
    }
  }
  ${CONNECTION_FIELDS}
`;

export const GET_USER_FOLLOWING = gql`
  query GetUserFollowing($userId: UUID!, $pagination: PaginationInput) {
    userFollowing(userId: $userId, pagination: $pagination) {
      totalCount
      hasNextPage
      endCursor
      following {
        ...ConnectionFields
      }
    }
  }
  ${CONNECTION_FIELDS}
`;

export const GET_USER_STATS = gql`
  query GetUserStats($userId: UUID!) {
    socialStats(userId: $userId) {
      followersCount
      followingCount
      friendsCount
      mutualFriendsCount
    }
    userAttendanceStats(userId: $userId, period: "ALL_TIME") {
      totalEventsAttended
      totalEventsHosted
      averageRating
      lastEventDate
    }
  }
`;

export const CHECK_FOLLOWING_STATUS = gql`
  query CheckFollowingStatus($userId: UUID!) {
    isFollowing(userId: $userId)
  }
`;

export const GET_CONNECTION_STATUS = gql`
  query GetConnectionStatus($userId: UUID!) {
    connectionStatus(userId: $userId) {
      isFollowing
      isFollowedBy
      isFriend
      isBlocked
      isBlockedBy
      connectionType
      connectionId
    }
  }
`;

// Mutations
export const FOLLOW_USER = gql`
  mutation FollowUser($input: FollowUserInput!) {
    followUser(input: $input) {
      success
      message
      connection {
        ...ConnectionFields
      }
    }
  }
  ${CONNECTION_FIELDS}
`;

export const UNFOLLOW_USER = gql`
  mutation UnfollowUser($input: UnfollowUserInput!) {
    unfollowUser(input: $input) {
      success
      message
    }
  }
`;

export const UPDATE_USER_PROFILE = gql`
  mutation UpdateUserProfile($input: UserUpdateInput!) {
    updateUser(input: $input) {
      ...UserBasicFields
      images {
        id
        userId
        url
        type
        isPrimary
        order
        createdAt
        updatedAt
      }
    }
  }
  ${USER_BASIC_FIELDS}
`;

export const UPLOAD_PROFILE_PICTURE = gql`
  mutation UploadProfilePicture($file: Upload!) {
    uploadProfilePicture(file: $file) {
      success
      message
      imageUrl
    }
  }
`;

export const UPLOAD_COVER_IMAGE = gql`
  mutation UploadCoverImage($file: Upload!) {
    uploadUserImage(input: { image: $file, type: "COVER" }) {
      id
      url
      type
      isPrimary
    }
  }
`;

export const BLOCK_USER = gql`
  mutation BlockUser($input: BlockUserInput!) {
    blockUser(input: $input) {
      success
      message
    }
  }
`;

export const UNBLOCK_USER = gql`
  mutation UnblockUser($input: UnblockUserInput!) {
    unblockUser(input: $input) {
      success
      message
    }
  }
`;

export const REPORT_USER = gql`
  mutation ReportUser($input: ReportUserInput!) {
    reportUser(input: $input) {
      success
      message
    }
  }
`;
