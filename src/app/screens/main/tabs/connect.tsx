import React from 'react';

import { FlatList } from 'react-native';

import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { Box, Text, useTheme } from '@/src/core/theme';
import { Ava<PERSON>, Button, Card, Tag } from '@/src/shared/components';

// Mock connection data
const connectionData = [
  {
    id: '1',
    name: '<PERSON>',
    title: 'Senior React Developer',
    company: 'TechCorp',
    mutualConnections: 12,
    interests: ['React Native', 'GraphQL', 'TypeScript'],
    isConnected: false,
  },
  {
    id: '2',
    name: '<PERSON>',
    title: 'Product Designer',
    company: 'DesignStudio',
    mutualConnections: 8,
    interests: ['UI/UX', 'Figma', 'Design Systems'],
    isConnected: true,
  },
  {
    id: '3',
    name: '<PERSON>',
    title: '<PERSON>Ops Engineer',
    company: 'CloudTech',
    mutualConnections: 5,
    interests: ['AWS', 'Docker', 'Kubernetes'],
    isConnected: false,
  },
  {
    id: '4',
    name: '<PERSON>',
    title: 'Full Stack Developer',
    company: 'StartupXYZ',
    mutualConnections: 15,
    interests: ['Node.js', 'MongoDB', 'React'],
    isConnected: false,
  },
  {
    id: '5',
    name: 'Robert Kim',
    title: 'Data Scientist',
    company: 'DataCorp',
    mutualConnections: 3,
    interests: ['Python', 'Machine Learning', 'Analytics'],
    isConnected: true,
  },
];

export default function ConnectScreen() {
  const theme = useTheme();
  const insets = useSafeAreaInsets();

  const renderConnectionItem = ({ item }: { item: (typeof connectionData)[0] }) => (
    <Card variant="outlined" marginBottom="md_16" padding="md_16">
      <Box flexDirection="row" alignItems="center" marginBottom="sm_12">
        <Avatar
          size="l"
          fallbackText={item.name}
          source={{ uri: `https://api.dicebear.com/7.x/avataaars/png?seed=${item.name}` }}
        />
        <Box flex={1} marginLeft="sm_12">
          <Text variant="b_16SemiBold_button" color="text">
            {item.name}
          </Text>
          <Text variant="b_14Regular_input" color="textSecondary">
            {item.title}
          </Text>
          <Text variant="l_12Regular_helperText" color="textTertiary">
            {item.company}
          </Text>
        </Box>
      </Box>

      {/* Mutual Connections */}
      <Box marginBottom="sm_12">
        <Text variant="l_12Regular_helperText" color="textSecondary">
          👥 {item.mutualConnections} mutual connections
        </Text>
      </Box>

      {/* Interests */}
      <Box flexDirection="row" flexWrap="wrap" gap="xs_8" marginBottom="md_16">
        {item.interests.map((interest, index) => (
          <Tag key={index} text={interest} variant="ghost" />
        ))}
      </Box>

      {/* Action Buttons */}
      <Box flexDirection="row" gap="sm_12">
        {item.isConnected ? (
          <Button title="Connected ✓" variant="ghost" enabled={false} flex={1} />
        ) : (
          <Button title="Connect" variant="primary" onPress={() => {}} flex={1} />
        )}
        <Button title="Message" variant="outline" onPress={() => {}} flex={1} />
      </Box>
    </Card>
  );

  return (
    <Box flex={1} backgroundColor="background">
      {/* Header */}
      <Box paddingHorizontal="lg_24" paddingBottom="md_16" style={{ paddingTop: insets.top + 16 }}>
        <Box flexDirection="row" justifyContent="space-between" alignItems="center">
          <Box>
            <Text variant="h_24SemiBold_section" color="text">
              Connect 👥
            </Text>
            <Text variant="b_14Regular_input" color="textSecondary">
              Discover and connect with professionals
            </Text>
          </Box>
          <Button title="Search" variant="ghost" onPress={() => {}} />
        </Box>
      </Box>

      {/* Quick Stats */}
      <Box flexDirection="row" paddingHorizontal="lg_24" marginBottom="md_16" gap="sm_12">
        <Card variant="elevated" padding="sm_12" flex={1}>
          <Text variant="h_20Medium_subsection" color="primary" textAlign="center">
            48
          </Text>
          <Text variant="l_12Regular_helperText" color="textSecondary" textAlign="center">
            Connections
          </Text>
        </Card>
        <Card variant="elevated" padding="sm_12" flex={1}>
          <Text variant="h_20Medium_subsection" color="secondary" textAlign="center">
            12
          </Text>
          <Text variant="l_12Regular_helperText" color="textSecondary" textAlign="center">
            Pending
          </Text>
        </Card>
        <Card variant="elevated" padding="sm_12" flex={1}>
          <Text variant="h_20Medium_subsection" color="success" textAlign="center">
            5
          </Text>
          <Text variant="l_12Regular_helperText" color="textSecondary" textAlign="center">
            New This Week
          </Text>
        </Card>
      </Box>

      {/* Suggested Connections */}
      <Box paddingHorizontal="lg_24" marginBottom="md_16">
        <Text variant="b_16SemiBold_button" color="text">
          Suggested for you
        </Text>
      </Box>

      {/* Content */}
      <FlatList
        data={connectionData}
        renderItem={renderConnectionItem}
        keyExtractor={item => item.id}
        contentContainerStyle={{
          paddingHorizontal: 24,
          paddingBottom: Math.max(insets.bottom, 24),
        }}
        showsVerticalScrollIndicator={false}
      />
    </Box>
  );
}
