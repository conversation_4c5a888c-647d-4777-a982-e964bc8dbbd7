import React from 'react';

import { ScrollView } from 'react-native';

import { SafeAreaView } from 'react-native-safe-area-context';

import { Box, Text, useTheme } from '@/src/core/theme';
import {
  TicketEvent,
  TicketMarketplace,
  TicketTransfer,
  TicketUrgent,
} from '@/src/shared/components';

export default function TicketsScreen() {
  const theme = useTheme();

  // Mock data for demonstration
  const handleTransferAccept = () => {
    console.log('Accept transfer');
  };

  const handleTransferReject = () => {
    console.log('Reject transfer');
  };

  const handleBuyNow = () => {
    console.log('Buy now');
  };

  const handleViewDetails = () => {
    console.log('View details');
  };

  const handleContact = () => {
    console.log('Contact seller');
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: theme.colors.background }}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <Box flex={1} padding="md_16">
          {/* Header */}
          <Box marginBottom="lg_24">
            <Text variant="h_32SemiBold_Page" marginBottom="xs_8">
              Tickets
            </Text>
            <Text variant="b_16Regular_input" color="textSecondary">
              Your tickets and marketplace
            </Text>
          </Box>

          {/* Ticket Transfer Example */}
          <Box marginBottom="md_16">
            <Text variant="b_14Medium_button" color="textSecondary" marginBottom="sm_12">
              Pending Transfers
            </Text>
            <TicketTransfer
              eventName="Samba Night at Vila Madalena"
              recipientName="João Silva"
              ticketType="VIP Access"
              eventDate="December 15, 2024"
              eventLocation="Vila Madalena, São Paulo"
              expiresIn="2 hours"
              onCancel={() => console.log('Cancel transfer')}
              onMessage={() => console.log('Send message')}
              onContact={() => console.log('Contact')}
              onViewDetails={handleViewDetails}
              onResendInvite={() => console.log('Resend invite')}
            />
          </Box>

          {/* Urgent Tickets */}
          <Box marginBottom="md_16">
            <Text variant="b_14Medium_button" color="textSecondary" marginBottom="sm_12">
              Last Minute Deals
            </Text>
            <TicketUrgent
              eventName="Electronic Music Festival"
              urgentMessage="Only 3 tickets left!"
              eventDate="Today, Dec 15"
              eventTime="10:00 PM"
              eventLocation="Audio Club, São Paulo"
              ticketType="General Admission"
              remainingTime="4 hours"
              onBuyNow={handleBuyNow}
              onViewDetails={handleViewDetails}
            />
          </Box>

          {/* Your Tickets */}
          <Box marginBottom="md_16">
            <Text variant="b_14Medium_button" color="textSecondary" marginBottom="sm_12">
              Your Tickets
            </Text>
            <Box gap="sm_12">
              <TicketEvent
                eventName="Rock in Rio 2024"
                eventDate="September 15, 2024"
                eventTime="4:00 PM - 2:00 AM"
                eventLocation="Cidade do Rock, Rio de Janeiro"
                eventType="Weekend Pass"
                earlyBird={{
                  price: 650,
                  originalPrice: 850,
                  endsIn: '3 days',
                }}
                features={['All stages access', 'VIP lounge', 'Meet & greet']}
                onBuyTicket={handleBuyNow}
                onViewDetails={handleViewDetails}
              />

              <TicketEvent
                eventName="Jazz Night at Blue Note"
                eventDate="December 20, 2024"
                eventTime="8:00 PM"
                eventLocation="Blue Note, São Paulo"
                eventType="Premium Seating"
                features={['Welcome drink', 'Reserved table']}
                onBuyTicket={handleBuyNow}
                onViewDetails={handleViewDetails}
              />
            </Box>
          </Box>

          {/* Marketplace */}
          <Box marginBottom="md_16">
            <Text variant="b_14Medium_button" color="textSecondary" marginBottom="sm_12">
              Marketplace
            </Text>
            <Box gap="sm_12">
              <TicketMarketplace
                eventName="Lollapalooza 2024"
                ticketType="3-Day Pass"
                eventDate="March 22-24, 2024"
                eventLocation="Autódromo de Interlagos, São Paulo"
                swapType="sell"
                price={950}
                originalPrice={1200}
                sellerName="Maria Santos"
                sellerRating={4.8}
                onContact={handleContact}
                onViewDetails={handleViewDetails}
              />

              <TicketMarketplace
                eventName="Carnival Block - Acadêmicos"
                ticketType="Premium Access"
                eventDate="February 10, 2024"
                eventLocation="Sambódromo, Rio de Janeiro"
                swapType="trade"
                tradePreference="Electronic festivals or Rock concerts"
                sellerName="Pedro Oliveira"
                sellerRating={4.5}
                onContact={handleContact}
                onViewDetails={handleViewDetails}
              />

              <TicketMarketplace
                eventName="New Year's Eve Party - Copacabana"
                ticketType="Open Bar"
                eventDate="December 31, 2024"
                eventLocation="Copacabana Beach, Rio de Janeiro"
                swapType="donate"
                sellerName="Ana Costa"
                sellerRating={5.0}
                onContact={handleContact}
                onViewDetails={handleViewDetails}
              />
            </Box>
          </Box>

          {/* Transfer Example */}
          <Box marginBottom="md_16">
            <Text variant="b_14Medium_button" color="textSecondary" marginBottom="sm_12">
              Transfer History
            </Text>
            <TicketTransfer
              eventName="Summer Music Festival"
              recipientName="Carlos Mendes"
              ticketType="Early Bird Special"
              eventDate="January 15, 2025"
              eventLocation="Praia do Forte, Bahia"
              expiresIn="Transfer completed"
              onCancel={() => console.log('Cancel')}
              onMessage={() => console.log('Message')}
              onContact={() => console.log('Contact')}
              onViewDetails={handleViewDetails}
              onResendInvite={() => console.log('Resend')}
            />
          </Box>
        </Box>
      </ScrollView>
    </SafeAreaView>
  );
}
