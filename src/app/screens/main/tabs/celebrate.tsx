import { FlatList } from 'react-native';

import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { Box, Text } from '@/src/core/theme';
import { <PERSON><PERSON>, Card } from '@/src/shared/components';
import { Avatar } from '@/src/shared/components/Avatar';

// Mock celebration data
const celebrationData = [
  {
    id: '1',
    user: '<PERSON>',
    achievement: 'Completed first React Native app',
    reactions: 45,
    timestamp: '2 hours ago',
  },
  {
    id: '2',
    user: '<PERSON>',
    achievement: 'Got promoted to Senior Developer',
    reactions: 78,
    timestamp: '4 hours ago',
  },
  {
    id: '3',
    user: '<PERSON>',
    achievement: 'Published first npm package',
    reactions: 32,
    timestamp: '6 hours ago',
  },
  {
    id: '4',
    user: '<PERSON>',
    achievement: 'Launched startup MVP',
    reactions: 156,
    timestamp: '1 day ago',
  },
  {
    id: '5',
    user: '<PERSON>',
    achievement: 'Won hackathon first place',
    reactions: 89,
    timestamp: '2 days ago',
  },
];

export default function CelebrateScreen() {
  const insets = useSafeAreaInsets();

  const renderCelebrationItem = ({ item }: { item: (typeof celebrationData)[0] }) => (
    <Card variant="outlined" marginBottom="md_16" padding="md_16">
      <Box flexDirection="row" alignItems="center" marginBottom="sm_12">
        <Avatar
          size="m"
          fallbackText={item.user}
          source={{ uri: `https://api.dicebear.com/7.x/avataaars/png?seed=${item.user}` }}
        />
        <Box flex={1} marginLeft="sm_12">
          <Text variant="b_16SemiBold_button" color="text">
            {item.user}
          </Text>
          <Text variant="l_12Regular_helperText" color="textTertiary">
            {item.timestamp}
          </Text>
        </Box>
      </Box>

      <Box
        backgroundColor="surfaceBackground"
        padding="sm_12"
        borderRadius="md_12"
        marginBottom="sm_12">
        <Text variant="b_16SemiBold_button" color="text">
          🎉 {item.achievement}
        </Text>
      </Box>

      <Box flexDirection="row" justifyContent="space-between" alignItems="center">
        <Box flexDirection="row" alignItems="center" gap="xs_8">
          <Text variant="l_12Regular_helperText" color="textSecondary">
            🎉 {item.reactions} celebrations
          </Text>
        </Box>
        <Box flexDirection="row" gap="xs_8">
          <Button title="🎉" variant="ghost" onPress={() => {}} />
          <Button title="Share" variant="ghost" onPress={() => {}} />
        </Box>
      </Box>
    </Card>
  );

  return (
    <Box flex={1} backgroundColor="background">
      {/* Header */}
      <Box paddingHorizontal="lg_24" paddingBottom="md_16" style={{ paddingTop: insets.top + 16 }}>
        <Box flexDirection="row" justifyContent="space-between" alignItems="center">
          <Box>
            <Text variant="h_24SemiBold_section" color="text">
              Celebrate 🎉
            </Text>
            <Text variant="b_16Regular_input" color="textSecondary">
              Share and celebrate achievements
            </Text>
          </Box>
          <Button title="+ Share" variant="primary" onPress={() => {}} />
        </Box>
      </Box>

      {/* Content */}
      <FlatList
        data={celebrationData}
        renderItem={renderCelebrationItem}
        keyExtractor={item => item.id}
        contentContainerStyle={{
          paddingHorizontal: 24,
          paddingBottom: Math.max(insets.bottom, 24),
        }}
        showsVerticalScrollIndicator={false}
      />
    </Box>
  );
}
