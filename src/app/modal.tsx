import { Platform } from 'react-native';

import { FontAwesome } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StatusBar } from 'expo-status-bar';

import { Box, Column, SafeAreaWrapper, Text, useTheme, useThemeContext } from '@/src/core/theme';
import { <PERSON><PERSON>, Card } from '@/src/shared/components';

export default function ModalScreen() {
  const navigation = useNavigation();
  const theme = useTheme();
  const { themeName } = useThemeContext();

  return (
    <SafeAreaWrapper>
      <StatusBar style={Platform.OS === 'ios' ? 'light' : 'auto'} />

      <Box flex={1} padding="lg_24" alignItems="center" justifyContent="center">
        <Column spacing="lg_24" width="100%" maxWidth={500} alignItems="center">
          <FontAwesome name="info-circle" size={64} color={theme.colors.primary} />

          <Text variant="H_40Bold_title" textAlign="center">
            Theme System Information
          </Text>

          <Card variant="elevated" width="100%" padding="lg_24">
            <Column spacing="md_16">
              <Text variant="h_16Medium_formLabel">Current Theme: {themeName}</Text>

              <Text variant="b_16Regular_input">
                The Movuca app uses Shopify&apos;s Restyle library for theming with support for
                light and dark modes.
              </Text>

              <Text variant="b_16Regular_input">
                This modal demonstrates how the theme system adapts to different UI contexts while
                maintaining consistency.
              </Text>

              <Column spacing="xs_8" marginTop="xs_8">
                <Text variant="h_16Medium_formLabel">Features:</Text>
                <Text variant="b_14Regular_content">• Light/Dark mode with system detection</Text>
                <Text variant="b_14Regular_content">• Responsive layouts</Text>
                <Text variant="b_14Regular_content">• Consistent spacing and typography</Text>
                <Text variant="b_14Regular_content">• Themed components</Text>
              </Column>
            </Column>
          </Card>

          <Button
            title="Close"
            variant="secondary"
            onPress={() => navigation.goBack()}
            leftIcon={<FontAwesome name="arrow-left" size={16} color="white" />}
          />
        </Column>
      </Box>
    </SafeAreaWrapper>
  );
}
