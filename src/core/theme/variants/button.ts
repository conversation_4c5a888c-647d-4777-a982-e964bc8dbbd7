// Button Intent Variants - defines the purpose and base color scheme
export const buttonIntentVariants = {
  defaults: {
    paddingHorizontal: 'sm_12', // 12px horizontal padding
    paddingVertical: 'xxs_4', // 4px vertical padding
    borderRadius: 'sm_8',
    alignItems: 'center',
    justifyContent: 'center',
  },
  primary: {
    backgroundColor: 'primary',
    borderColor: 'primary',
  },
  secondary: {
    backgroundColor: 'buttonTintedBackground',
    borderColor: 'primaryLight',
  },
  danger: {
    backgroundColor: 'error',
    borderColor: 'error',
  },
  success: {
    backgroundColor: 'success',
    borderColor: 'success',
  },
  warning: {
    backgroundColor: 'warning',
    borderColor: 'warning',
  },
  neutral: {
    backgroundColor: 'textSecondary',
    borderColor: 'textSecondary',
  },
};

// Button Type Variants - defines the visual style (filled, outline, etc.)
export const buttonTypeVariants = {
  defaults: {},
  filled: {
    // backgroundColor is inherited from intent variants
  },
  outlined: {
    backgroundColor: 'transparent',
    borderWidth: 1,
  },
  borderless: {
    backgroundColor: 'transparent',
    borderWidth: 0,
  },
  tinted: {
    backgroundColor: 'primaryAlpha20',
    borderWidth: 0,
  },
  ghost: {
    backgroundColor: 'transparent',
    borderWidth: 0,
  },
};

// Button Size Variants - matches exact Figma specifications
export const buttonSizeVariants = {
  defaults: {},
  small: {
    paddingHorizontal: 'sm_12', // 12px horizontal padding
    paddingVertical: 'xs_8', // 8px vertical padding (not 4px!)
    borderRadius: 'md_12', // 12px radius for all small buttons in Figma
    minHeight: 32, // 32px height for small buttons
    gap: 'xs_8', // 8px gap between icon and text
  },
  default: {
    paddingHorizontal: 'md_16', // 16px horizontal padding
    paddingVertical: 'sm_12', // 12px vertical padding
    borderRadius: 'md_12', // 12px radius for default buttons
    minHeight: 48, // 48px height for default buttons
    gap: 'sm_12', // 12px gap between icon and text
  },
  large: {
    paddingHorizontal: 'lg_24', // 24px horizontal padding
    paddingVertical: 'md_16', // 16px vertical padding
    borderRadius: 'lg_16', // 16px radius for large buttons
    minHeight: 56, // 56px height for large buttons
    gap: 'md_16', // 16px gap between icon and text
  },
};

// Button State Variants - for visual states like disabled, pressed
export const buttonStateVariants = {
  defaults: {},
  disabled: {
    backgroundColor: 'disabled',
    borderColor: 'disabled',
    opacity: 0.6,
  },
  pressed: {
    opacity: 0.8,
  },
  focused: {
    // Add focus styles if needed
  },
};

// Icon Button Variants - matches exact Figma specifications
export const iconButtonVariants = {
  defaults: {
    padding: 'sm_12',
    borderRadius: 'md_12',
    alignItems: 'center',
    justifyContent: 'center',
  },
  small: {
    padding: 'xs_8', // 8px padding
    width: 32, // 32px width (not 26px!)
    height: 32, // 32px height (not 26px!)
    borderRadius: 'md_12', // 12px radius for small icon buttons
  },
  default: {
    padding: 'sm_12', // 12px padding
    width: 48, // 48px width (not 34px!)
    height: 48, // 48px height (not 34px!)
    borderRadius: 'md_12', // 12px radius for default icon buttons
  },
  large: {
    padding: 'md_16', // 16px padding
    width: 56, // 56px width (not 48px!)
    height: 56, // 56px height (not 48px!)
    borderRadius: 'lg_16', // 16px radius for large icon buttons
  },
};

// Social Button Variants - for login/social buttons
export const socialButtonVariants = {
  defaults: {
    backgroundColor: 'surfaceBackground',
    borderWidth: 0.5,
    borderColor: 'border',
    paddingHorizontal: 'sm_12',
    paddingVertical: 'sm_12',
    borderRadius: 'md_12',
    gap: 'sm_12',
  },
  apple: {
    backgroundColor: 'black',
    borderColor: 'black',
  },
  google: {
    backgroundColor: 'white',
    borderColor: 'border',
  },
  facebook: {
    backgroundColor: '#1877F2',
    borderColor: '#1877F2',
  },
};

// Export all variant types
export type ButtonIntentVariants = Exclude<keyof typeof buttonIntentVariants, 'defaults'>;
export type ButtonTypeVariants = Exclude<keyof typeof buttonTypeVariants, 'defaults'>;
export type ButtonSizeVariants = Exclude<keyof typeof buttonSizeVariants, 'defaults'>;
export type ButtonStateVariants = Exclude<keyof typeof buttonStateVariants, 'defaults'>;
export type IconButtonVariants = Exclude<keyof typeof iconButtonVariants, 'defaults'>;
export type SocialButtonVariants = Exclude<keyof typeof socialButtonVariants, 'defaults'>;
