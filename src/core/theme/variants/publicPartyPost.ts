import { Colors } from '../colors';

export interface PublicPartyPostVariants {
  default: {
    backgroundColor: keyof Colors;
    borderWidth: number;
    borderColor: keyof Colors;
    shadowColor?: keyof Colors;
    shadowOffset?: { width: number; height: number };
    shadowOpacity?: number;
    shadowRadius?: number;
    elevation?: number;
  };
  elevated: {
    backgroundColor: keyof Colors;
    borderWidth: number;
    borderColor: keyof Colors;
    shadowColor: keyof Colors;
    shadowOffset: { width: number; height: number };
    shadowOpacity: number;
    shadowRadius: number;
    elevation: number;
  };
}

export const publicPartyPostVariants: PublicPartyPostVariants = {
  default: {
    backgroundColor: 'cardBackground',
    borderWidth: 1,
    borderColor: 'mainBorder',
  },
  elevated: {
    backgroundColor: 'cardBackground',
    borderWidth: 1,
    borderColor: 'mainBorder',
    shadowColor: 'primaryDark',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
};
