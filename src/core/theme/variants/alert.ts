import { VariantProps } from '@shopify/restyle';

import { Theme } from '../theme';

export const alertVariants = {
  defaults: {
    width: '100%',
    borderRadius: 'lg_16',
    paddingVertical: 'md_16',
    paddingHorizontal: 'sm_12',
    alignItems: 'center',
    flexDirection: 'row',
    gap: 'xs_8',
  },
  success: {
    backgroundColor: 'alertSuccessBackground',
  },
  info: {
    backgroundColor: 'alertInfoBackground',
  },
  warning: {
    backgroundColor: 'alertWarningBackground',
  },
  error: {
    backgroundColor: 'alertErrorBackground',
  },
};

export type AvatarVariant = VariantProps<Theme, 'alertVariants'>;
