export const pillVariants = {
  defaults: {
    paddingHorizontal: 'xxs_4',
    paddingVertical: 'xxxs_2',
    borderRadius: 'xs_4',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'flex-start',
  },
  small: {
    gap: 'xxs_4',
    paddingHorizontal: 'xxs_4',
    paddingVertical: 'xxs_4',
  },
  compact: {
    gap: 'xxs_4',
    paddingHorizontal: 'xxs_4',
    paddingVertical: 'none_0',
  },
  default: {
    gap: 'xs_8',
    paddingHorizontal: 'xs_8',
    paddingVertical: 'xxs_4',
  },
  big: {
    paddingHorizontal: 'xs_8',
    paddingVertical: 'xxs_4',
    gap: 'xs_8',
    borderRadius: 'sm_8',
  },
  // New variants based on Figma design
  actionDefault: {
    gap: 'xxs_4',
    paddingHorizontal: 'xs_8',
    paddingVertical: 'xs_8',
    borderRadius: 'xl_20',
    backgroundColor: 'elevatedBackground',
    borderWidth: 0.5,
    borderColor: 'border',
  },
  actionFocused: {
    gap: 'xxs_4',
    paddingHorizontal: 'xs_8',
    paddingVertical: 'xs_8',
    borderRadius: 'xl_20',
    backgroundColor: 'primary',
  },
  reasonDefault: {
    gap: 'xxs_4',
    paddingHorizontal: 'xs_8',
    paddingVertical: 'xxs_4',
    borderRadius: 'xl_20',
    backgroundColor: 'subtleBackground',
  },
  reasonLarge: {
    gap: 'xs_8',
    paddingHorizontal: 'sm_12',
    paddingVertical: 'xs_8',
    borderRadius: 'xl_20',
    backgroundColor: 'subtleBackground',
  },
};

export type PillVariants = Exclude<keyof typeof pillVariants, 'defaults'>;
