import { VariantProps } from '@shopify/restyle';

import { Theme } from '../theme';

export const roundedButtonVariants = {
  defaults: {
    backgroundColor: 'subtleBackground',
    alignItems: 'center',
    borderRadius: 'circle_9999',
  },
  extraSmall: {
    padding: 'xxs_4',
  },
  small: {
    // width: 20,
    // height: 20,
    padding: 'xs_8',
  },
  medium: {
    // width: 32,
    // height: 32,
    padding: 'sm_12',
  },
} as const;

export const darkRoundedButtonVariants = {
  defaults: {
    backgroundColor: 'subtleBackground',
    alignItems: 'center',
    borderRadius: 'circle_9999',
  },
  extraSmall: {
    padding: 'xxs_4',
  },
  small: {
    // width: 20,
    // height: 20,
    padding: 'xs_8',
  },
  medium: {
    // width: 32,
    // height: 32,
    padding: 'sm_12',
  },
} as const;

export type RoundedButtonVariants = VariantProps<Theme, 'roundedButtonVariants'>;
