import { VariantProps } from '@shopify/restyle';

import { Theme } from '../theme';

export const avatarVariants = {
  defaults: {
    borderRadius: 'circle_9999',
    backgroundColor: 'subtleBackground',
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
  },

  xs: {
    width: 24,
    height: 24,
  },
  s: {
    width: 32,
    height: 32,
  },
  m: {
    width: 40,
    height: 40,
  },
  l: {
    width: 48,
    height: 48,
  },
  xl: {
    width: 56,
    height: 56,
  },
  '2xl': {
    width: 64,
    height: 64,
  },
  '3xl': {
    width: 102,
    height: 102,
  },
};

export type AvatarVariant = VariantProps<Theme, 'avatarVariants'>;
