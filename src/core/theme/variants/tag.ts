import { VariantProps } from '@shopify/restyle';

import { Theme } from '../theme';

export const tagVariants = {
  defaults: {
    borderWidth: 1,
    paddingVertical: 'xs_8',
    paddingHorizontal: 'sm_12',
    borderRadius: 'sm_8',
    alignItems: 'center',
    justifyContent: 'center',
  },
  default: {
    backgroundColor: 'tagDefaultBackground',
    borderColor: 'tagDefaultBorder',
  },
  success: {
    backgroundColor: 'tagSuccessBackground',
    borderColor: 'tagSuccessBackground',
  },
  error: {
    backgroundColor: 'tagErrorBackground',
    borderColor: 'tagErrorBackground',
  },
  warning: {
    backgroundColor: 'tagWarningBackground',
    borderColor: 'tagWarningBackground',
  },
  info: {
    backgroundColor: 'tagInfoBackground',
    borderColor: 'tagInfoBackground',
  },
  highlight: {
    backgroundColor: 'primary',
    borderColor: 'primary',
  },

  // Outline variants
  outline: {
    borderColor: 'tagDefaultBorder',
    backgroundColor: 'transparent',
  },
  outlineSuccess: {
    backgroundColor: 'transparent',
    borderColor: 'tagSuccessBackground',
  },
  outlineInfo: {
    backgroundColor: 'transparent',
    borderColor: 'tagInfoMain',
  },
  outlineWarning: {
    backgroundColor: 'transparent',
    borderColor: 'tagAlertMain',
  },
  outlineError: {
    backgroundColor: 'transparent',
    borderColor: 'tagErrorMain',
  },

  // Ghost variants
  ghost: {
    backgroundColor: 'transparent',
    borderColor: 'transparent',
  },
  ghostSuccess: {
    backgroundColor: 'transparent',
    borderColor: 'transparent',
  },
  ghostInfo: {
    backgroundColor: 'transparent',
    borderColor: 'transparent',
  },
  ghostWarning: {
    backgroundColor: 'transparent',
    borderColor: 'transparent',
  },
  ghostError: {
    backgroundColor: 'transparent',
    borderColor: 'transparent',
  },
};
export type TagVariant = VariantProps<Theme, 'tagVariants'>;
