import React, { forwardRef } from 'react';

import { ScrollView, ScrollViewProps } from 'react-native';

import {
  BackgroundColorProps,
  BorderProps,
  BoxProps,
  SpacingProps,
  backgroundColor,
  border,
  composeRestyleFunctions,
  spacing,
  useRestyle,
} from '@shopify/restyle';

import Box from './Box';
import { Theme } from './theme';

type RestyleProps = SpacingProps<Theme> & BorderProps<Theme> & BackgroundColorProps<Theme>;

const restyleFunctions = composeRestyleFunctions<Theme, RestyleProps>([
  spacing,
  border,
  backgroundColor,
]);

export type ScrollableContainerProps = ScrollViewProps &
  RestyleProps & {
    /**
     * Children components
     */
    children?: React.ReactNode;

    /**
     * Additional Box props for the content container
     */
    contentContainerProps?: BoxProps<Theme>;
  };

/**
 * Scrollable container that uses theme values
 */
const ScrollableContainer = forwardRef<ScrollView, ScrollableContainerProps>(
  ({ children, contentContainerProps, ...scrollViewProps }, ref) => {
    const restyleProps = useRestyle(restyleFunctions, scrollViewProps);

    return (
      <ScrollView
        ref={ref}
        bounces={false}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        {...scrollViewProps}
        contentContainerStyle={[{ flexGrow: 1 }, scrollViewProps.contentContainerStyle]}>
        <Box flex={1} {...restyleProps} {...contentContainerProps}>
          {children}
        </Box>
      </ScrollView>
    );
  }
);

ScrollableContainer.displayName = 'ScrollableContainer';

export default ScrollableContainer;
