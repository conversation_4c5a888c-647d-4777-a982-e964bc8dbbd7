import { ImageStyle, TextStyle, ViewStyle } from 'react-native';

import { Theme as NavigationTheme } from '@react-navigation/native';
import { createTheme, useTheme as useRestyleTheme } from '@shopify/restyle';

import {
  accent,
  colors,
  darkNeutral,
  error,
  neutral,
  primary,
  secondary,
  success,
  vip,
  warning,
} from './colors';
import fontFamily from './fontFamily';
import {
  borderRadii,
  breakpoints,
  fontSizes,
  lineHeights,
  spacing,
  strokes,
  zIndices,
} from './spacing';
import { alertVariants } from './variants/alert';
import { avatarVariants } from './variants/avatar';
import {
  buttonIntentVariants,
  buttonSizeVariants,
  buttonStateVariants,
  buttonTypeVariants,
  iconButtonVariants,
  socialButtonVariants,
} from './variants/button';
import { cardToolbarVariants } from './variants/cardToolbar';
import { pillVariants } from './variants/pill';
import { publicPartyPostVariants } from './variants/publicPartyPost';
import { darkRoundedButtonVariants, roundedButtonVariants } from './variants/roundedButton';
import { tagVariants } from './variants/tag';

/**
 * Type for named styles using React Native style types
 */
type NamedStyles<T> = {
  [P in keyof T]: ViewStyle | TextStyle | ImageStyle;
};

/**
 * Light theme creation using Restyle
 */
const lightTheme = createTheme({
  colors: {
    // First spread all flattened colors
    ...colors,

    // Then override with semantic names (these take precedence)
    // Background colors
    mainBackground: neutral.background,
    cardBackground: neutral.white,
    surfaceBackground: neutral.surfaceBackground,
    elevatedBackground: neutral.backgroundHigher,
    focusBackground: primary.ultraLight,
    subtleBackground: neutral.subtleBackground,

    // Text colors
    mainText: neutral.text,
    secondaryText: neutral.textSubtle,
    mutedText: neutral.textMuted,
    placeholderText: neutral.placeholder,
    inverseText: neutral.background,
    primaryText: primary.main,

    // Border colors
    mainBorder: neutral.border,
    subtleBorder: neutral.hover,
    focusBorder: primary.main,

    // Interactive state colors
    hoverState: neutral.hover,
    pressedState: neutral.subtleBackground,
    focusState: primary.extraLight,
    disabledBackground: neutral.disabled,
    disabledText: neutral.placeholder,

    // Brand colors
    brandMain: primary.main,
    brandLight: primary.light,
    brandDark: primary.dark,

    // Feedback colors
    successMain: success.mediumLight,
    successLight: success.ultraLight,
    successDark: success.mediumDark,

    warningMain: warning.mediumLight,
    warningLight: warning.ultraLight,
    warningDark: warning.mediumDark,

    errorMain: error.mediumLight,
    errorLight: error.ultraLight,
    errorDark: error.mediumDark,

    // Button specific colors
    buttonFilledBackground: primary.main,
    buttonFilledText: neutral.white,
    buttonFilledBackgroundPressed: primary.darker,
    buttonFilledBackgroundDisabled: neutral.disabled,
    buttonFilledTextDisabled: neutral.placeholder,
    buttonFilledBackgroundHover: primary.lighter,

    buttonTintedBackground: primary.extraLight,
    buttonTintedBackgroundPressed: primary.lighter,
    buttonTintedBackgroundDisabled: neutral.disabled,
    buttonTintedBackgroundHover: primary.lighter,
    buttonTintedText: primary.main,
    buttonTintedTextDisabled: neutral.placeholder,

    buttonOutlineBackground: 'transparent',
    buttonOutlineText: primary.main,
    buttonOutlineBorder: primary.main,
    buttonOutlineBackgroundHover: primary.lighter,
    buttonOutlineBackgroundPressed: primary.darker,
    buttonOutlineBorderDisabled: neutral.disabled,
    buttonOutlineTextDisabled: neutral.placeholder,

    // Input specific colors
    inputBackground: neutral.background,
    inputText: neutral.textStrong,
    inputPlaceholder: neutral.placeholder,

    inputBorder: neutral.border,
    inputBorderFocus: primary.main,
    inputBorderError: error.mediumLight,

    inputFocusedBackground: primary.ultraLight,
    inputFocusedText: primary.main,

    inputErrorBackground: error.mediumLight,
    inputErrorText: error.main,

    inputDisabledBackground: neutral.disabled,
    inputDisabledText: neutral.placeholder,

    // Icon colors
    iconPrimary: primary.dark, // #1953D2 - For primary actions, high contrast
    iconSecondary: secondary.dark, //#940b0b - For secondary actions, vibrant
    iconAccent: accent.medium, // #FF851B - For highlights, attention-grabbing
    iconMuted: neutral.textMuted, // #757575 - For subtle, non-interactive icons
    iconDisabled: neutral.disabled, // #E0E0E0 - For disabled states
    iconDefault: neutral.placeholder, // #9E9E9E - For input icons (e.g., search, clear)
    iconActive: primary.medium, // #5784F6 - For focused/active inputs
    iconSuccess: success.mediumLight, // #22C55E - For success states
    iconError: error.mediumLight, // #F75555 - For error states
    iconWarning: warning.mediumLight, // #F59E0B - For warning states

    iconActiveInput: neutral.text,
    iconFilledInput: neutral.textStrong,
    iconDisabledInput: neutral.disabled,
    iconDefaultInput: neutral.placeholder,

    alertSuccessBackground: success.alpha20,
    alertSuccessText: success.mediumDark,
    alertErrorBackground: error.alpha20,
    alertErrorText: error.dark,
    alertWarningBackground: warning.alpha20,
    alertWarningText: warning.dark,
    alertInfoBackground: primary.alpha20,
    alertInfoText: primary.dark,

    // Modal colors
    modalBackground: neutral.white,
    modalText: neutral.text,
    modalOverlayBackground: 'rgba(0, 0, 0, 0.5)',
    modalCloseButtonBackground: 'transparent',

    tagDefaultBackground: darkNeutral.subtleBackground,
    tagDefaultMain: neutral.textSubtle,
    tagDefaultSubtle: darkNeutral.textSubtle,
    tagDefaultText: darkNeutral.textSubtle,
    tagDefaultBorder: darkNeutral.subtleBackground,
    tagDefaultInverseText: darkNeutral.background,

    tagSuccessBackground: success.mediumDark,
    tagSuccessMain: success.extraLight,
    tagSuccessSubtle: success.ultraLight,
    tagSuccessText: success.extraLight,

    tagErrorBackground: error.lighter,
    tagErrorMain: error.darker,
    tagErrorSubtle: error.ultraLight,
    tagErrorText: error.extraLight,

    tagWarningBackground: warning.lighter,
    tagWarningMain: warning.darker,
    tagWarningSubtle: warning.dark,
    tagWarningText: warning.darker,

    tagInfoBackground: primary.lighter,
    tagInfoMain: primary.darker,
    tagInfoSubtle: primary.dark,
    tagInfoText: primary.darker,
    tagAlertMain: warning.darker,

    vipBackground: vip.primary,
    vipText: vip.primary,
    vipSurface: vip.secondary,
    vipBorder: vip.accent,
    vipContent: neutral.white,

    // Tab colors
    tabActiveText: primary.main,
    tabInactiveText: neutral.textMuted,
    tabActiveBackground: primary.extraLight,
    tabInactiveBackground: 'transparent',

    // Explicitly named individual colors for backward compatibility
    primary: primary.main,
    primaryLight: primary.light,
    primaryDark: primary.dark,

    secondary: secondary.main,
    secondaryLight: secondary.light,
    secondaryDark: secondary.dark,

    error: error.mediumLight,
    warning: warning.mediumLight,
    success: success.mediumLight,

    background: neutral.background,
    card: neutral.white,
    surface: neutral.white,

    text: neutral.text,
    textSecondary: neutral.textSubtle,
    textTertiary: neutral.textMuted,
    textInverted: neutral.white,

    border: neutral.border,

    disabled: neutral.disabled,
    transparent: 'transparent',
    white: neutral.white,
  },
  spacing,
  borderRadii,
  zIndices,
  breakpoints,
  strokes,
  textVariants: {
    //     Page Elements | Recommended Token
    // --------------|------------------
    // Page Title    | H_32SemiBold
    // Section Title | H_24SemiBold
    // Card Title    | H_16SemiBold
    // Alert Title   | H_16SemiBold
    // Alert Content | B_14Regular
    // Form Labels   | L_14SemiBold
    // Input Text    | B_16Regular
    // Helper Text   | L_12Regular
    // Error Message | L_12Medium (with error color)
    // Navigation    | B_16Medium
    // Buttons (lg)  | B_16SemiBold
    // Buttons (sm)  | B_14Medium
    // Tooltips      | L_12Regular
    // Badge/Tag     | L_10SemiBold
    defaults: {
      color: 'mainText',
      fontSize: fontSizes.lg_16,
      lineHeight: lineHeights.lg_24,
      fontFamily: fontFamily.Urbanist_400Regular,
    },
    d_56Black_hero: {
      fontSize: fontSizes.display_56,
      lineHeight: 'auto',
      fontFamily: fontFamily.Urbanist_900Black,
    },
    h_48Black_splash: {
      fontSize: fontSizes.display_48,
      lineHeight: lineHeights.display_56,
      fontFamily: fontFamily.Urbanist_900Black,
      letterSpacing: -0.5,
    },
    H_40Bold_title: {
      fontSize: fontSizes.display_40,
      lineHeight: lineHeights.display_48,
      fontFamily: fontFamily.Urbanist_700Bold,
    },
    h_32SemiBold_Page: {
      fontSize: fontSizes.h1_32,
      lineHeight: lineHeights.h1_40,
      fontFamily: fontFamily.Urbanist_600SemiBold,
      letterSpacing: -0.25,
    },
    h_32Light_intro: {
      fontSize: fontSizes.h1_32,
      lineHeight: lineHeights.h1_40,
      fontFamily: fontFamily.Urbanist_300Light,
      letterSpacing: -0.25,
    },
    h_24SemiBold_section: {
      fontSize: fontSizes.h3_24,
      lineHeight: lineHeights.h3_32,
      fontFamily: fontFamily.Urbanist_600SemiBold,
      letterSpacing: -0.25,
    },
    h_20Medium_subsection: {
      fontSize: fontSizes.xxl_20,
      lineHeight: lineHeights.xxl_28,
      fontFamily: fontFamily.Urbanist_500Medium,
    },
    h_18Bold_formTitle: {
      fontSize: fontSizes.xl_18,
      lineHeight: lineHeights.lg_24,
      fontFamily: fontFamily.Urbanist_700Bold,
    },
    h_16Medium_formLabel: {
      fontSize: fontSizes.lg_16,
      lineHeight: lineHeights.md_22,
      fontFamily: fontFamily.Urbanist_500Medium,
    },
    h_18SemiBold_cardTitle: {
      fontSize: fontSizes.xl_18,
      lineHeight: lineHeights.md_22,
      fontFamily: fontFamily.Urbanist_600SemiBold,
    },
    h_16SemiBold_alertTitle: {
      fontSize: fontSizes.lg_16,
      lineHeight: lineHeights.md_22,
      fontFamily: fontFamily.Urbanist_600SemiBold,
    },
    h_16SemiBold_button: {
      fontSize: fontSizes.lg_16,
      lineHeight: lineHeights.md_22,
      fontFamily: fontFamily.Urbanist_600SemiBold,
    },
    h_16SemiBold_buttonSm: {
      fontSize: fontSizes.sm_12,
      lineHeight: lineHeights.sm_16,
      fontFamily: fontFamily.Urbanist_600SemiBold,
    },
    l_14Medium_formHelperText: {
      fontSize: fontSizes.md_14,
      lineHeight: lineHeights.md_22,
      fontFamily: fontFamily.Urbanist_500Medium,
    },
    l_14Medium_info: {
      fontSize: fontSizes.md_14,
      lineHeight: lineHeights.md_22,
      fontFamily: fontFamily.Urbanist_500Medium,
    },
    l_14SemiBold_action: {
      fontSize: fontSizes.md_14,
      lineHeight: lineHeights.md_22,
      fontFamily: fontFamily.Urbanist_600SemiBold,
      letterSpacing: 0.1,
    },
    l_12SemiBold_button: {
      fontSize: fontSizes.sm_12,
      lineHeight: lineHeights.sm_16,
      fontFamily: fontFamily.Urbanist_600SemiBold,
      letterSpacing: 0.1,
    },
    l_12Regular_helperText: {
      fontSize: fontSizes.sm_12,
      lineHeight: lineHeights.sm_16,
      fontFamily: fontFamily.Urbanist_400Regular,
      letterSpacing: 0.2,
    },
    l_12Medium_message: {
      fontSize: fontSizes.sm_12,
      lineHeight: lineHeights.sm_16,
      fontFamily: fontFamily.Urbanist_500Medium,
      color: 'errorMain',
      letterSpacing: 0.2,
    },
    l_12Bold_highlight: {
      fontSize: fontSizes.sm_12,
      lineHeight: lineHeights.sm_16,
      fontFamily: fontFamily.Urbanist_700Bold,
      letterSpacing: 0.2,
    },
    l_10Medium_tooltip: {
      fontSize: fontSizes.xs_10,
      lineHeight: lineHeights.xs_14,
      fontFamily: fontFamily.Urbanist_500Medium,
      letterSpacing: 0.4,
    },
    l_10SemiBold_chip: {
      fontSize: fontSizes.xs_10,
      lineHeight: lineHeights.xs_14,
      fontFamily: fontFamily.Urbanist_600SemiBold,
      letterSpacing: 0.4,
    },
    l_8SemiBold_hint: {
      fontSize: fontSizes.xxs_8,
      lineHeight: lineHeights.xxs_8,
      fontFamily: fontFamily.Urbanist_600SemiBold,
      letterSpacing: 0.2,
    },
    l_10SemiBold_tag: {
      fontSize: fontSizes.xs_10,
      lineHeight: lineHeights.xs_14,
      fontFamily: fontFamily.Urbanist_600SemiBold,
      letterSpacing: 0.4,
    },
    b_24Bold_CTA: {
      fontSize: fontSizes.h3_24,
      lineHeight: lineHeights.xxl_28,
      fontFamily: fontFamily.Urbanist_700Bold,
    },
    b_18Bold_ComponentHeader: {
      fontSize: fontSizes.xl_18,
      lineHeight: lineHeights.md_22,
      fontFamily: fontFamily.Urbanist_700Bold,
      letterSpacing: 0.4,
    },
    b_16SemiBold_button: {
      fontSize: fontSizes.lg_16, // 16px for button text
      lineHeight: lineHeights.lg_24,
      fontFamily: fontFamily.Urbanist_600SemiBold,
      letterSpacing: 0.4,
    },
    b_16Regular_input: {
      fontSize: fontSizes.lg_16,
      lineHeight: lineHeights.lg_24,
      fontFamily: fontFamily.Urbanist_400Regular,
    },
    b_16SemiBold_navigation: {
      fontSize: fontSizes.xl_18,
      lineHeight: lineHeights.xl_26,
      fontFamily: fontFamily.Urbanist_600SemiBold,
      letterSpacing: 0.4,
    },
    b_16Bold_keypoint: {
      fontSize: fontSizes.xl_18,
      lineHeight: lineHeights.xl_26,
      fontFamily: fontFamily.Urbanist_700Bold,
      letterSpacing: 0.4,
    },
    b_14Bold_CardTitle: {
      fontSize: fontSizes.md_14,
      lineHeight: lineHeights.md_22,
      fontFamily: fontFamily.Urbanist_700Bold,
      letterSpacing: 0.2,
    },
    b_16Large_Subtitle: {
      fontSize: fontSizes.lg_16,
      lineHeight: lineHeights.md_22,
      fontFamily: fontFamily.Urbanist_700Bold,
      letterSpacing: 0.2,
    },
    b_12Medium_CardSubtitle: {
      fontSize: fontSizes.sm_12,
      lineHeight: lineHeights.sm_16,
      fontFamily: fontFamily.Urbanist_500Medium,
      letterSpacing: 0.2,
    },
    b_10Bold_CardTitle: {
      fontSize: fontSizes.xs_10,
      lineHeight: lineHeights.xs_14,
      fontFamily: fontFamily.Urbanist_700Bold,
      letterSpacing: 0.2,
    },
    b_14Regular_content: {
      fontSize: fontSizes.md_14,
      lineHeight: lineHeights.md_22,
      fontFamily: fontFamily.Urbanist_400Regular,
    },
    b_16Medium_button: {
      fontSize: fontSizes.lg_16, // 16px for button text
      lineHeight: lineHeights.lg_24,
      fontFamily: fontFamily.Urbanist_500Medium,
      letterSpacing: 0.4,
    },
    b_14Regular_input: {
      fontSize: fontSizes.md_14,
      lineHeight: lineHeights.md_22,
      fontFamily: fontFamily.Urbanist_400Regular,
    },
    b_14Medium_button: {
      fontSize: fontSizes.md_14,
      lineHeight: lineHeights.md_22,
      fontFamily: fontFamily.Urbanist_500Medium,
      letterSpacing: 0.4,
    },
    b_14Medium_link: {
      fontSize: fontSizes.md_14,
      lineHeight: lineHeights.md_22,
      fontFamily: fontFamily.Urbanist_500Medium,
      letterSpacing: 0.1,
    },
    b_14SemiBold_listTitle: {
      fontSize: fontSizes.md_14,
      lineHeight: lineHeights.md_22,
      fontFamily: fontFamily.Urbanist_600SemiBold,
      letterSpacing: 0.1,
    },
    b_10Medium_description: {
      fontSize: fontSizes.xs_10,
      lineHeight: lineHeights.xs_14,
      fontFamily: fontFamily.Urbanist_500Medium,
      letterSpacing: 0.2,
    },
    overline: {
      fontSize: fontSizes.xs_10,
      lineHeight: lineHeights.xs_14,
      textTransform: 'uppercase',
      letterSpacing: 1,
    },
    overline2: {
      fontFamily: fontFamily.Urbanist_500Medium,
      fontSize: {
        phone: fontSizes.xxs_8,
        tablet: fontSizes.xs_10,
      },
      lineHeight: {
        phone: lineHeights.xxs_8,
        tablet: lineHeights.xs_14,
      },
      textTransform: 'uppercase',
      letterSpacing: 1,
    },
  },
  // Button variants - multi-dimensional design system
  buttonIntentVariants,
  buttonTypeVariants,
  buttonSizeVariants,
  buttonStateVariants,
  iconButtonVariants,
  socialButtonVariants,

  // Legacy button variants for backward compatibility
  // TODO: Remove once all components are updated to use new Button component
  buttonVariants: {
    defaults: {
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: 'lg_16',
      paddingHorizontal: 'md_16',
      paddingVertical: 'sm_12',
      minHeight: 48,
    },
    primary: {
      backgroundColor: 'primary',
    },
    secondary: {
      backgroundColor: 'secondary',
    },
    tertiary: {
      backgroundColor: 'elevatedBackground',
    },
    outline: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      borderColor: 'primary',
    },
    ghost: {
      backgroundColor: 'transparent',
    },
    danger: {
      backgroundColor: 'error',
    },
  },

  // Common alert variants
  alertVariants,

  // Common card toolbar variants
  cardToolbarVariants,

  // Common card variants
  cardVariants: {
    defaults: {
      backgroundColor: 'cardBackground',
      // paddingVertical: 'md_16',
      borderRadius: 'lg_16',
      borderWidth: 1.5,
      borderColor: 'mainBorder',
      gap: 'sm_12',
    },
    elevated: {
      shadowColor: 'primaryDark',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 8,
      elevation: 4,
    },
    outlined: {
      borderWidth: 1,
      borderColor: 'mainBorder',
    },
  },
  // Common input variants
  inputVariants: {
    defaults: {
      borderWidth: 1,
      borderColor: 'inputBorder',
      borderRadius: 'md_12',
      padding: 'md_16',
      backgroundColor: 'inputBackground',
    },
    focused: {
      borderColor: 'inputBorderFocus',
      backgroundColor: 'focusBackground',
    },
    error: {
      borderColor: 'inputBorderError',
    },
    disabled: {
      backgroundColor: 'disabledBackground',
      borderColor: 'disabledBackground',
    },
  },
  // Chip variants
  chipVariants: {
    defaults: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: 'buttonTintedBackground',
      paddingVertical: 'xxs_4',
      paddingHorizontal: 'sm_12',
      borderRadius: 'sm_8',
    },
    solidSmall: {
      backgroundColor: 'buttonTintedBackground',
      paddingVertical: 'xxs_4',
      paddingHorizontal: 'sm_12',
      borderRadius: 'xs_4',
    },
    solidMedium: {
      backgroundColor: 'buttonTintedBackground',
      paddingVertical: 'xs_8',
      paddingHorizontal: 'sm_12',
      borderRadius: 'sm_8',
    },
    solidLarge: {
      backgroundColor: 'buttonTintedBackground',
      paddingVertical: 'sm_12',
      paddingHorizontal: 'md_16',
      borderRadius: 'md_12',
    },
    solidHover: {
      backgroundColor: 'buttonTintedBackgroundHover',
    },
    solidPressed: {
      backgroundColor: 'buttonTintedBackgroundPressed',
    },
    solidDisabled: {
      backgroundColor: 'buttonTintedBackgroundDisabled',
    },
    secondarySmall: {
      backgroundColor: 'buttonTintedBackground',
      paddingVertical: 'xxs_4',
      paddingHorizontal: 'sm_12',
      borderRadius: 'xs_4',
    },
    outlineSmall: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      paddingVertical: 'xxs_4',
      paddingHorizontal: 'sm_12',
      borderRadius: 'xs_4',
      borderColor: 'buttonOutlineBorder',
    },
    outlineMedium: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      paddingVertical: 'xs_8',
      paddingHorizontal: 'sm_12',
      borderRadius: 'sm_8',
      borderColor: 'buttonOutlineBorder',
    },
    outlineLarge: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      paddingVertical: 'sm_12',
      paddingHorizontal: 'md_16',
      borderRadius: 'md_12',
      borderColor: 'buttonOutlineBorder',
    },
    outlineHover: {
      backgroundColor: 'buttonOutlineBackgroundHover',
    },
    outlinePressed: {
      backgroundColor: 'buttonOutlineBackgroundPressed',
    },
    outlineDisabled: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      borderColor: 'buttonOutlineBorderDisabled',
    },
    ghost: {
      backgroundColor: 'transparent',
    },
    ghostHover: {
      backgroundColor: 'hoverState',
    },
    ghostPressed: {
      backgroundColor: 'pressedState',
    },
    selected: {
      borderWidth: 1,
      borderColor: 'brandMain',
    },
    disabled: {
      opacity: 0.6,
    },
  },
  switchVariants: {
    idle: {
      backgroundColor: 'switchDisabled',
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 'none_0',
    },
    enabled: {
      backgroundColor: 'switchEnabled',
      justifyContent: 'flex-end',
    },
    disabled: {
      backgroundColor: 'switchDisabled',
      justifyContent: 'flex-start',
      opacity: 0.6,
    },
  },

  //Tag variants
  tagVariants,

  // Public Party Post variants
  publicPartyPostVariants,

  // Avatar variants
  avatarVariants,

  pillVariants,
  roundedButtonVariants,

  ghostDisabled: {
    backgroundColor: 'transparent',
  },
  success: {
    backgroundColor: 'successMain',
  },
  successHover: {
    backgroundColor: 'successDark',
  },
  successPressed: {
    backgroundColor: 'successDark',
  },
  successDisabled: {
    backgroundColor: 'disabledBackground',
  },
  warning: {
    backgroundColor: 'warningMain',
  },
  warningHover: {
    backgroundColor: 'warningDark',
  },
  warningPressed: {
    backgroundColor: 'warningDark',
  },
  warningDisabled: {
    backgroundColor: 'disabledBackground',
  },
  error: {
    backgroundColor: 'errorMain',
  },
  errorHover: {
    backgroundColor: 'errorDark',
  },
  errorPressed: {
    backgroundColor: 'errorDark',
  },
  errorDisabled: {
    backgroundColor: 'disabledBackground',
  },
  disabled: {
    color: 'textTertiary',
    backgroundColor: 'disabledBackground',
  },

  // TextInput container variants for Box
  textInputContainerVariants: {
    defaults: {
      // this size is smaller because it is the inner border and second container
      borderRadius: 'md_12',
      backgroundColor: 'mainBackground',
      paddingHorizontal: 'md_16',
      opacity: 1,
    },
    focused: {
      backgroundColor: 'focusBackground',
    },

    filled: {
      backgroundColor: 'mainBackground',
    },
    error: {
      // borderColor: 'inputBorderError',
    },
    disabled: {
      backgroundColor: 'disabledBackground',
      // borderColor: 'disabledBackground',
      opacity: 0.4,
    },
  },
  // TextInput variants for Restyle
  textInputVariants: {
    defaults: {
      flex: 1,
      color: 'inputText',
      fontFamily: 'Urbanist_600SemiBold',
      fontSize: {
        phone: 16,
        tablet: 20,
      },
      paddingVertical: {
        phone: 'sm_12',
        tablet: 'ml_20',
      },
      letterSpacing: 0.2,
    },
    error: {
      color: 'errorMain',
    },
    passwordInputs: {
      fontFamily: 'Urbanist_500Medium',
      fontSize: {
        phone: 25,
        tablet: 30,
      },
    },
    seedPhraseInputs: {
      color: 'text',
    },
    searchBoxInput: {},
    swapInput: {
      fontFamily: 'Urbanist_300Light',
      textAlign: 'right',
      fontSize: {
        phone: 35,
        tablet: 40,
      },
      color: 'text',
    },
  },

  // Animated border variants for focus and error states
  animatedBorderVariants: {
    defaults: {
      overflow: 'hidden',
      // this size is bigger because it is the outer border and first container
      borderRadius: 'lg_16',
      borderWidth: 1,
      borderColor: 'inputBorder',
    },
    focused: {
      borderColor: 'inputBorderFocus',
      borderWidth: 2,
    },
    error: {
      borderColor: 'inputBorderError',
    },
    disabled: {
      // borderColor: 'inputBorderDisabled',
    },
  },
});

/**
 * Dark theme extends light theme with dark mode colors
 */
const darkTheme = {
  ...lightTheme,
  colors: {
    // Include all colors from light theme (including flattened)
    ...lightTheme.colors,

    // Then override with dark theme specific values
    // Background colors
    mainBackground: darkNeutral.background,
    cardBackground: darkNeutral.surfaceBackground,
    surfaceBackground: darkNeutral.surfaceBackground,
    subtleBackground: darkNeutral.subtleBackground,
    elevatedBackground: darkNeutral.backgroundHigher,
    focusBackground: darkNeutral.backgroundHigher,

    // Text colors
    mainText: darkNeutral.text,
    secondaryText: darkNeutral.textSubtle,
    mutedText: darkNeutral.textMuted,
    placeholderText: darkNeutral.placeholder,
    inverseText: darkNeutral.background,
    primaryText: darkNeutral.textSubtle,

    // Border colors
    mainBorder: darkNeutral.border,
    subtleBorder: darkNeutral.hover,
    focusBorder: primary.light,

    // Interactive state colors
    hoverState: darkNeutral.hover,
    pressedState: darkNeutral.subtleBackground,
    focusState: primary.darkest,
    disabledBackground: darkNeutral.disabled,
    disabledText: darkNeutral.placeholder,

    // Brand colors
    brandMain: primary.light,
    brandLight: primary.lighter,
    brandDark: primary.mediumLight,

    // Feedback colors
    successMain: success.light,
    successLight: success.darkest,
    successDark: success.lighter,

    warningMain: warning.light,
    warningLight: warning.darkest,
    warningDark: warning.lighter,

    errorMain: error.light,
    errorLight: error.darkest,
    errorDark: error.lighter,

    // Button specific colors
    buttonFilledBackground: primary.main,
    buttonFilledText: darkNeutral.white,
    buttonFilledBackgroundHover: primary.dark,
    buttonFilledBackgroundPressed: primary.darker,
    buttonFilledBackgroundDisabled: darkNeutral.disabled,
    buttonFilledTextDisabled: darkNeutral.placeholder,

    buttonTintedBackground: primary.light,
    buttonTintedBackgroundPressed: primary.veryLight,
    buttonTintedBackgroundDisabled: darkNeutral.disabled,
    buttonTintedBackgroundHover: primary.dark,
    buttonTintedText: primary.darkest,
    buttonTintedTextDisabled: darkNeutral.placeholder,

    buttonOutlineBackground: 'transparent',
    buttonOutlineText: primary.light,
    buttonOutlineBorder: primary.light,
    buttonOutlineBackgroundHover: primary.darkest,
    buttonOutlineBackgroundPressed: primary.darker,
    buttonOutlineBorderDisabled: darkNeutral.disabled,
    buttonOutlineTextDisabled: darkNeutral.placeholder,

    // Input specific colors
    inputBackground: darkNeutral.surfaceBackground,
    inputText: darkNeutral.textStrong,
    inputPlaceholder: darkNeutral.placeholder,
    inputBorder: darkNeutral.border,

    inputBorderFocus: primary.light,
    inputBorderError: error.light,

    inputFocusedBackground: darkNeutral.backgroundHigher,
    inputFocusedText: darkNeutral.textStrong,
    inputFocusedPlaceholder: darkNeutral.placeholder,
    inputFocusedBorder: primary.light,

    inputErrorBackground: error.mediumLight,
    inputErrorText: error.light,
    inputErrorBorder: error.light,

    inputDisabledBackground: darkNeutral.disabled,
    inputDisabledText: darkNeutral.placeholder,

    iconPrimary: primary.light,
    iconSecondary: secondary.light,
    iconAccent: accent.light,
    iconMuted: darkNeutral.textMuted,
    iconDisabled: darkNeutral.disabled,
    iconDefault: darkNeutral.placeholder,
    iconActive: primary.mediumLight,
    iconSuccess: success.light,
    iconError: error.light,
    iconWarning: warning.light,

    iconActiveInput: darkNeutral.text,
    iconFilledInput: darkNeutral.textStrong,
    iconDisabledInput: darkNeutral.disabled,
    iconDefaultInput: darkNeutral.placeholder,

    alertSuccessBackground: success.alpha50,
    alertSuccessText: success.extraLight,
    alertErrorBackground: error.alpha50,
    alertErrorText: error.extraLight,
    alertWarningBackground: warning.alpha50,
    alertWarningText: warning.ultraLight,
    alertInfoBackground: primary.alpha50,
    alertInfoText: primary.extraLight,

    // Modal colors
    modalBackground: darkNeutral.white,
    modalText: darkNeutral.text,
    modalOverlayBackground: 'rgba(0, 0, 0, 0.5)',
    modalCloseButtonBackground: 'transparent',

    tagDefaultBackground: darkNeutral.subtleBackground,
    tagDefaultMain: darkNeutral.textSubtle,
    tagDefaultSubtle: neutral.textSubtle,
    tagDefaultText: darkNeutral.textSubtle,
    tagDefaultBorder: darkNeutral.subtleBackground,
    tagDefaultInverseText: neutral.background,

    tagSuccessBackground: success.mediumLight,
    tagSuccessMain: success.darker,
    tagSuccessSubtle: success.dark,
    tagSuccessText: success.darker,

    tagErrorBackground: error.darker,
    tagErrorMain: error.lighter,
    tagErrorSubtle: error.light,
    tagErrorText: error.lighter,

    tagWarningBackground: warning.darker,
    tagWarningMain: warning.light,
    tagWarningSubtle: warning.lighter,
    tagWarningText: warning.light,

    tagInfoBackground: primary.darker,
    tagInfoMain: primary.lighter,
    tagInfoSubtle: primary.light,
    tagInfoText: primary.lighter,

    vipBackground: vip.primary,
    vipText: vip.primary,
    vipSurface: vip.secondary,
    vipBorder: vip.accent,
    vipContent: neutral.white,

    // Tab colors
    tabActiveText: primary.light,
    tabInactiveText: darkNeutral.textMuted,
    tabActiveBackground: primary.darkest,
    tabInactiveBackground: 'transparent',

    // For backward compatibility and direct access
    primary: primary.main,
    primaryLight: primary.mediumDark,
    primaryDark: primary.light,

    secondary: secondary.main,
    secondaryLight: secondary.mediumDark,
    secondaryDark: secondary.light,

    error: error.light,
    warning: warning.light,
    success: success.light,

    background: darkNeutral.background,
    card: darkNeutral.surfaceBackground,
    surface: darkNeutral.surfaceBackground,

    text: darkNeutral.text,
    textSecondary: darkNeutral.textSubtle,
    textTertiary: darkNeutral.textMuted,
    textInverted: darkNeutral.text,

    border: darkNeutral.border,
    borderDark: darkNeutral.textMuted,

    disabled: darkNeutral.disabled,
  },

  darkRoundedButtonVariants,

  chipVariants: {
    defaults: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: 'background',
      borderWidth: 1,
      borderColor: 'mainBorder',
      paddingVertical: 'xxs_4',
      paddingHorizontal: 'sm_12',
      borderRadius: 'sm_8',
    },
    solidSmall: {
      backgroundColor: 'buttonTintedBackground',
      paddingVertical: 'xxs_4',
      paddingHorizontal: 'sm_12',
      borderRadius: 'xs_4',
    },
    solidMedium: {
      backgroundColor: 'buttonTintedBackground',
      paddingVertical: 'xs_8',
      paddingHorizontal: 'sm_12',
      borderRadius: 'sm_8',
    },
    solidLarge: {
      backgroundColor: 'buttonTintedBackground',
      paddingVertical: 'sm_12',
      paddingHorizontal: 'md_16',
      borderRadius: 'md_12',
    },
    solidHover: {
      backgroundColor: 'buttonTintedBackgroundHover',
    },
    solidPressed: {
      backgroundColor: 'buttonTintedBackgroundPressed',
    },
    solidDisabled: {
      backgroundColor: 'buttonTintedBackgroundDisabled',
    },
    secondarySmall: {
      backgroundColor: 'background',
      paddingVertical: 'xxs_4',
      paddingHorizontal: 'sm_12',
      borderRadius: 'xs_4',
    },
    outlineSmall: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      paddingVertical: 'xxs_4',
      paddingHorizontal: 'sm_12',
      borderRadius: 'xs_4',
      borderColor: 'buttonOutlineBorder',
    },
    outlineMedium: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      paddingVertical: 'xs_8',
      paddingHorizontal: 'sm_12',
      borderRadius: 'sm_8',
      borderColor: 'buttonOutlineBorder',
    },
    outlineLarge: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      paddingVertical: 'sm_12',
      paddingHorizontal: 'md_16',
      borderRadius: 'md_12',
      borderColor: 'buttonOutlineBorder',
    },
    outlineHover: {
      backgroundColor: 'buttonOutlineBackgroundHover',
    },
    outlinePressed: {
      backgroundColor: 'buttonOutlineBackgroundPressed',
    },
    outlineDisabled: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      borderColor: 'buttonOutlineBorderDisabled',
    },
    ghost: {
      backgroundColor: 'transparent',
    },
    ghostHover: {
      backgroundColor: 'hoverState',
    },
    ghostPressed: {
      backgroundColor: 'pressedState',
    },
    selected: {
      borderWidth: 1,
      borderColor: 'brandMain',
    },
    disabled: {
      opacity: 0.6,
    },
  },
};

const navigationLightTheme: NavigationTheme = {
  dark: false,
  colors: {
    primary: lightTheme.colors.primary,
    background: lightTheme.colors.mainBackground,
    card: lightTheme.colors.mainBackground,
    text: lightTheme.colors.primary,
    border: lightTheme.colors.border,
    notification: lightTheme.colors.error,
  },
  fonts: {
    regular: {
      fontFamily: fontFamily.Urbanist_400Regular,
      fontWeight: '400',
    },
    medium: {
      fontWeight: '500',
      fontFamily: fontFamily.Urbanist_500Medium,
    },
    bold: {
      fontFamily: fontFamily.Urbanist_600SemiBold,
      fontWeight: '600',
    },
    heavy: {
      fontFamily: fontFamily.Urbanist_700Bold,
      fontWeight: '700',
    },
  },
};

const navigationDarkTheme: NavigationTheme = {
  dark: true,
  colors: {
    primary: darkTheme.colors.primaryDark,
    background: darkTheme.colors.mainBackground,
    card: darkTheme.colors.mainBackground,
    text: darkTheme.colors.primary,
    border: darkTheme.colors.border,
    notification: darkTheme.colors.error,
  },
  fonts: {
    regular: {
      fontFamily: fontFamily.Urbanist_400Regular,
      fontWeight: '400',
    },
    medium: {
      fontWeight: '500',
      fontFamily: fontFamily.Urbanist_500Medium,
    },
    bold: {
      fontFamily: fontFamily.Urbanist_600SemiBold,
      fontWeight: '600',
    },
    heavy: {
      fontFamily: fontFamily.Urbanist_700Bold,
      fontWeight: '700',
    },
  },
};

/**
 * Utility function to create styles with theme values
 * @param styles Function that receives theme and returns styles object
 * @returns Function that returns styles when called
 */
export const makeStyles = <T extends NamedStyles<T> | NamedStyles<unknown>>(
  styles: (theme: Theme) => T
) => {
  return () => {
    const theme = useTheme();
    return styles(theme);
  };
};

/**
 * Hook to access the current theme
 * @returns Current theme object
 */
export const useTheme = () => {
  return useRestyleTheme<Theme>();
};

// Export theme and types
export { lightTheme, darkTheme };

export type Theme = typeof lightTheme;
export type TokensThemeFontSizes = keyof typeof fontSizes;
export type Colors = typeof lightTheme.colors;
export type TokensThemeSizes = keyof typeof lightTheme.spacing;
export type TokensThemeColors = keyof Colors;
export type DarkTheme = typeof darkTheme;
export default lightTheme;

export type ChipKeyVariantsType = keyof Theme['chipVariants'];

export { navigationLightTheme, navigationDarkTheme };
