import { DimensionValue, Platform, useWindowDimensions } from 'react-native';

import { AtLeastOneResponsiveValue } from '@shopify/restyle';

import { useTheme } from './theme';

export type Breakpoint = 'phone' | 'tablet' | 'desktop';

export type ResponsiveDimensionValue<T> = AtLeastOneResponsiveValue<
  T,
  { phone: number; tablet: number; desktop: number }
>;
/**
 * Hook for responsive layouts
 * Provides utilities for handling different screen sizes
 */
export const useResponsive = () => {
  const { width, height } = useWindowDimensions();
  const theme = useTheme();

  // Determine current breakpoint
  const getBreakpoint = (): Breakpoint => {
    if (width >= theme.breakpoints.desktop) {
      return 'desktop';
    }
    if (width >= theme.breakpoints.tablet || (Platform.OS === 'ios' && Platform.isPad)) {
      return 'tablet';
    }
    return 'phone';
  };

  const breakpoint = getBreakpoint();

  // Check if current width is at least the specified breakpoint
  const isAtLeast = (bp: Breakpoint): boolean => {
    switch (bp) {
      case 'desktop':
        return width >= theme.breakpoints.desktop;
      case 'tablet':
        return width >= theme.breakpoints.tablet;
      case 'phone':
        return true;
      default:
        return false;
    }
  };

  // Check if current width is at most the specified breakpoint
  const isAtMost = (bp: Breakpoint): boolean => {
    switch (bp) {
      case 'phone':
        return width < theme.breakpoints.tablet;
      case 'tablet':
        return width < theme.breakpoints.desktop;
      case 'desktop':
        return true;
      default:
        return false;
    }
  };

  // Select value based on current breakpoint
  const select = <T>(values: { phone?: T; tablet?: T; desktop?: T }, defaultValue?: T): T => {
    if (values[breakpoint] !== undefined) {
      return values[breakpoint] as T;
    }

    // Fall back to the closest lower breakpoint
    if (breakpoint === 'desktop' && values.tablet !== undefined) {
      return values.tablet;
    }

    if ((breakpoint === 'desktop' || breakpoint === 'tablet') && values.phone !== undefined) {
      return values.phone;
    }

    // Return default value if provided
    if (defaultValue !== undefined) {
      return defaultValue;
    }

    throw new Error(`No value found for breakpoint ${breakpoint} and no default provided`);
  };

  // Check if device is in landscape orientation
  const isLandscape = width > height;

  return {
    width,
    height,
    breakpoint,
    isPhone: breakpoint === 'phone',
    isTablet: breakpoint === 'tablet',
    isDesktop: breakpoint === 'desktop',
    isAtLeast,
    isAtMost,
    select,
    isLandscape,
  };
};
