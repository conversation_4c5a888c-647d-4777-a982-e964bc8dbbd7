/**
 * Spacing values used for margin, padding, and positioning
 * Format: semantic_value for explicit size reference
 */
const spacing = {
  none_0: 0,
  xxxs_2: 2,
  xxs_4: 4,
  xs_8: 8,
  sm_12: 12,
  md_16: 16,
  ml_20: 20,
  lg_24: 24,
  xl_32: 32,
  xxl_40: 40,
  xxxl_48: 48,
  huge_64: 64,
  giant_80: 80,
  massive_128: 128,
};

/**
 * Border radius values
 * Format: semantic_value for explicit size reference
 */
const borderRadii = {
  none_0: 0,
  xxs_2: 2,
  xs_4: 4,
  sm_8: 8,
  md_12: 12,
  lg_16: 16,
  xl_20: 20,
  xxl_24: 24,
  xxxl_32: 32,
  circle_9999: 9999,
};

/**
 * Z-index values for controlling the stacking order
 */
const zIndices = {
  base: 0,
  low: 1,
  medium: 10,
  high: 100,
  highest: 999,
  modal: 1000,
  overlay: 2000,
  toast: 3000,
};

/**
 * Breakpoints for responsive design
 */
const breakpoints = {
  phone: 0,
  tablet: 768,
  desktop: 1024,
};

/**
 * Font sizes for consistent typography
 */
const fontSizes = {
  none_0: 0,
  xxxs_4: 4,
  xxs_8: 8,
  xs_10: 10,
  sm_12: 12,
  md_14: 14,
  lg_16: 16,
  xl_18: 18,
  xxl_20: 20,
  h3_24: 24,
  h2_28: 28,
  h1_32: 32,
  display_36: 36,
  display_40: 40,
  display_44: 44,
  display_48: 48,
  display_56: 56,
};

/**
 * Line heights for consistent typography
 */
const lineHeights = {
  none_0: 0,
  xxxs_4: 4,
  xxs_8: 8,
  xs_14: 14,
  sm_16: 16,
  md_22: 22,
  lg_24: 24,
  xl_26: 26,
  xxl_28: 28,
  h3_32: 32,
  h2_36: 36,
  h1_40: 40,
  display_44: 44,
  display_48: 48,
  display_56: 56,
  display_64: 64,
};
/**
 * STROKES
 * A set of stroke widths for borders and outlines in a party/dating app
 */
const strokes = {
  zero: 0, // No stroke
  whisper_0_5: 0.5, // Delicate, barely-there borders (like a whispered invitation)
  spark_1: 1, // Light, lively borders (like a spark of connection)
  glint_1_3: 1.3, // Shimmering, nuanced borders (like a glint of light)
  pulse_1_5: 1.5, // Lively, rhythmic outlines (like a pulsing beat)
  vibe_2: 2, // Standard outlines with energy
  flare_2_5: 2.5, // Radiant, standout strokes (like a flare at a party)
  bold_3: 3, // Strong, attention-grabbing strokes
};

export { spacing, borderRadii, zIndices, breakpoints, fontSizes, lineHeights, strokes };
