/**
 * PRIMARY COLOR PALETTE
 * A 12-step scale based on the primary brand color (#0052CC)
 */
const primary = {
  ultraLight: '#EDF4FF', // For subtle backgrounds, hover states
  extraLight: '#D6E7FF', // For secondary buttons, borders
  veryLight: '#B2DDFF', // For tertiary elements
  lighter: '#89C4FB', // For decorative elements
  light: '#66B0F6', // For active states
  mediumLight: '#368AFF', // For emphasis
  medium: '#1677FF', // For secondary actions
  mediumDark: '#0064F0', // For prominent elements
  dark: '#1953D2', // For important elements
  main: '#0052CC', // Primary brand color - for CTAs, links
  darker: '#0041A3', // For hover states
  darkest: '#00307A', // For text on light backgrounds,
  alpha50: 'rgba(0, 82, 204, 0.5)',
  alpha20: 'rgba(0, 82, 204, 0.2)',
  alpha10: 'rgba(0, 82, 204, 0.1)',
};

/**
 * SECONDARY COLOR PALETTE
 * A 12-step scale for secondary actions and supporting elements in a party/dating app
 */
const secondary = {
  ultraLight: '#FFEBEF', // Subtle backgrounds, hover states
  extraLight: '#FFD6DE', // Secondary buttons, borders
  veryLight: '#FFC1CD', // Tertiary elements
  lighter: '#FFACBC', // Decorative elements
  light: '#FF97AB', // Active states
  mediumLight: '#E62E50', // Emphasis
  medium: '#CC2945', // Secondary actions
  mediumDark: '#B3243C', // Main secondary color
  dark: '#992033', // Prominent elements
  main: '#FF385C', // Primary secondary actions
  darker: '#801C2B', // Hover states
  darkest: '#661723', // Text on light backgrounds
  alpha50: 'rgba(255, 56, 92, 0.5)',
  alpha10: 'rgba(255, 56, 92, 0.1)',
};

/**
 * ACCENT COLOR PALETTE
 * A 12-step scale for highlights and attention-grabbing elements
 */
const accent = {
  ultraLight: '#FFF4E6', // Subtle accent backgrounds
  extraLight: '#FFE8CC', // Accent borders
  veryLight: '#FFD8A8', // Secondary accent elements
  lighter: '#FFC78A', // Decorative accent elements
  light: '#FFAD66', // Active accent states
  mediumLight: '#FF973D', // Emphasis
  medium: '#FF851B', // Accent actions
  mediumDark: '#F97316', // Main accent color
  dark: '#D95F13', // Prominent accent elements
  main: '#B74C10', // Primary accent actions
  darker: '#943B0D', // Accent hover states
  darkest: '#722B0A', // Accent text on light backgrounds
  alpha10: 'rgba(183, 76, 16, 0.1)',
  alpha50: 'rgba(183, 76, 16, 0.5)',
};

/**
 * ERROR COLOR PALETTE
 * A 12-step scale for error, destructive, and cancel actions
 */
const error = {
  ultraLight: '#FFF1F1', // For subtle error backgrounds
  extraLight: '#FFE4E4', // For error borders
  veryLight: '#FECACA', // For secondary error elements
  lighter: '#FCA5A5', // For decorative error elements
  light: '#F87979', // For active error states
  mediumLight: '#F75555', // Main error color
  medium: '#E03C3C', // For error actions
  mediumDark: '#C42B2B', // For prominent error elements
  dark: '#A91F1F', // For important error elements
  main: '#8D1717', // For primary error actions
  darker: '#701212', // For error hover states
  darkest: '#520D0D', // For error text on light backgrounds
  alpha10: 'rgba(141, 23, 23, 0.1)',
  alpha20: 'rgba(141, 23, 23, 0.2)',
  alpha50: 'rgba(141, 23, 23, 0.5)',
};

/**
 * SUCCESS COLOR PALETTE
 * A 12-step scale for success and confirmation elements
 */
const success = {
  ultraLight: '#F0FDF4', // For subtle success backgrounds
  extraLight: '#DCFCE7', // For success borders
  veryLight: '#BBF7D0', // For secondary success elements
  lighter: '#86EFAC', // For decorative success elements
  light: '#4ADE80', // For active success states
  mediumLight: '#22C55E', // Main success color
  medium: '#16A34A', // For success actions
  mediumDark: '#117C39', // For prominent success elements
  dark: '#166534', // For important success elements
  main: '#14532D', // For primary success actions
  darker: '#0F3D21', // For success hover states
  darkest: '#052E16', // For success text on light backgrounds
  alpha10: 'rgba(74, 222, 128, 0.1)',
  alpha20: 'rgba(74, 222, 128, 0.2)',
  alpha50: 'rgba(74, 222, 128, 0.5)',
};

const vip = {
  primary: '#8A2BE2',
  accent: '#FFD700',
  secondary: '#A960ED',
  purple: '#4A1478',
  subtle: '#6F1DBB',
};

/**
 * WARNING COLOR PALETTE
 * A 12-step scale for warnings and caution elements
 */
const warning = {
  ultraLight: '#FFFBF0', // For subtle warning backgrounds
  extraLight: '#FEF3C7', // For warning borders
  veryLight: '#FDE68A', // For secondary warning elements
  lighter: '#FFE247', // For decorative warning elements
  light: '#FFDE2B', // For active warning states
  mediumLight: '#EAC500', // Main warning color
  medium: '#E1BE00', // For warning actions
  mediumDark: '#B49800', // For prominent warning elements
  dark: '#7F6B00', // For important warning elements
  main: '#F5CD00', // For primary warning actions
  darker: '#766300', // For warning hover states
  darkest: '#453A00', // For warning text on light backgrounds
  alpha10: 'rgba(245, 205, 0, 0.1)',
  alpha20: 'rgba(245, 205, 0, 0.2)',
  alpha50: 'rgba(245, 205, 0, 0.5)',
};

/**
 * NEUTRAL COLOR PALETTE (LIGHT THEME)
 * A 12-step grayscale for light theme UI elements
 */
const neutralLight = {
  white: '#FFFFFF', // Pure white for backgrounds
  background: '#FAFAFA', // Light theme background
  backgroundHigher: '#F5F8FC', // For elevated backgrounds
  surfaceBackground: '#FFFFFF', // For surface backgrounds
  subtleBackground: '#F5F5F5', // For subtle background variations
  hover: '#EEEEEE', // For hover states
  disabled: '#E0E0E0', // For disabled elements
  border: primary.extraLight, // For borders
  placeholder: '#9E9E9E', // For placeholder text
  textMuted: '#757575', // For secondary text
  textSubtle: '#616161', // For tertiary text
  text: '#424242', // Main text color
  textStrong: '#212121', // For emphasized text
  black: '#000000', // Pure black for special cases
  alpha10: 'rgba(66, 66, 66, 0.1)',
  alpha50: 'rgba(66, 66, 66, 0.5)',
};

/**
 * NEUTRAL COLOR PALETTE (DARK THEME)
 * A 12-step grayscale for dark theme UI elements
 */
const darkNeutral = {
  white: '#FFFFFF', // Pure white for special cases
  text: '#E0E0E0', // Main text color
  textSubtle: '#C7C7C7', // For tertiary text
  textStrong: '#F5F5F5', // For emphasized text
  textMuted: '#ADADAD', // For secondary text
  placeholder: '#8F8F8F', // For placeholder text
  border: '#707070', // For borders
  disabled: '#5E5E5E', // For disabled elements
  hover: '#EEEEEE', // For hover states
  subtleBackground: '#383838', // For subtle background variations
  surfaceBackground: '#35383F', // For surface elements
  backgroundHigher: '#2A2D36', // For elevated elements
  background: '#1F222A', // Dark theme background
  black: '#121418', // Near-black for special cases
  alpha10: 'rgba(31, 34, 42, 0.1)',
  alpha20: 'rgba(31, 34, 42, 0.2)',
  alpha30: 'rgba(31, 34, 42, 0.3)',
  alpha40: 'rgba(31, 34, 42, 0.4)',
  alpha50: 'rgba(31, 34, 42, 0.5)',
  alpha60: 'rgba(31, 34, 42, 0.6)',
  alpha70: 'rgba(31, 34, 42, 0.7)',
  alpha80: 'rgba(31, 34, 42, 0.8)',
  alpha90: 'rgba(31, 34, 42, 0.9)',
};

// Helper function to capitalize first letter
const capitalize = (str: string): string => str.charAt(0).toUpperCase() + str.slice(1);

// Type-safe helper function to flatten and rename colors
type FlattenColors<T extends Record<string, string>, P extends string> = {
  [K in keyof T as `${P}${K extends string ? (K extends `${P}${string}` ? K : K extends `${number}` ? K : Capitalize<K>) : never}`]: T[K];
};

function flattenColors<T extends Record<string, string>, P extends string>(
  prefix: P,
  obj: T
): FlattenColors<T, P> {
  const newObj: any = {};
  for (const [key, value] of Object.entries(obj)) {
    let newKey = key;
    // Capitalize the first letter of the key, unless it's already a number (like '10', '50')
    // or if the key already contains the prefix (like 'primary10' in 'primary')
    if (!key.startsWith(prefix) && !/^\d/.test(key)) {
      newKey = capitalize(key);
    }
    newObj[`${prefix}${newKey}`] = value;
  }
  return newObj;
}

// Transformed colors
const flattenedPrimary = flattenColors('primary', primary);
const flattenedSecondary = flattenColors('secondary', secondary);
const flattenedAccent = flattenColors('accent', accent);
const flattenedError = flattenColors('error', error);
const flattenedSuccess = flattenColors('success', success);
const flattenedWarning = flattenColors('warning', warning);
const flattenedNeutralLight = flattenColors('neutralLight', neutralLight);
const flattenedDarkNeutral = flattenColors('darkNeutral', darkNeutral);
const flattenedVip = flattenColors('vip', vip);

// Combine all flattened colors into a single object with proper typing
const colors = {
  ...flattenedPrimary,
  ...flattenedSecondary,
  ...flattenedAccent,
  ...flattenedError,
  ...flattenedSuccess,
  ...flattenedWarning,
  ...flattenedNeutralLight,
  ...flattenedDarkNeutral,
  ...flattenedVip,
} as const;

// Export type for the flattened colors
export type FlattenedColors = typeof colors;

export {
  colors,
  primary,
  secondary,
  accent,
  error,
  success,
  warning,
  neutralLight as neutral,
  darkNeutral,
  vip,
};
