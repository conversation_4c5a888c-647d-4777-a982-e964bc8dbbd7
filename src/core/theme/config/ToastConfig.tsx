import { BaseToast, ErrorToast, InfoToast, ToastConfig } from 'react-native-toast-message';

// Custom toast configuration to prevent text truncation
const toastConfig: ToastConfig = {
  success: ({ text1, text2 }) => (
    <BaseToast
      style={{
        borderLeftColor: '#4ade80',
        backgroundColor: '#fff',
        borderRadius: 8,
        height: 'auto',
        minHeight: 60,
        paddingVertical: 12,
        paddingHorizontal: 16,
      }}
      contentContainerStyle={{
        paddingHorizontal: 15,
      }}
      text1Style={{
        fontSize: 16,
        fontWeight: '600',
        color: '#1f2937',
        numberOfLines: 0, // Allow unlimited lines
      }}
      text2Style={{
        fontSize: 14,
        fontWeight: '400',
        color: '#6b7280',
        numberOfLines: 0, // Allow unlimited lines
      }}
      text1={text1}
      text2={text2}
    />
  ),
  error: ({ text1, text2 }) => (
    <ErrorToast
      style={{
        borderLeftColor: '#ef4444',
        backgroundColor: '#fff',
        borderRadius: 8,
        height: 'auto',
        minHeight: 60,
        paddingVertical: 12,
        paddingHorizontal: 16,
      }}
      contentContainerStyle={{
        paddingHorizontal: 15,
      }}
      text1Style={{
        fontSize: 16,
        fontWeight: '600',
        color: '#1f2937',
        numberOfLines: 0, // Allow unlimited lines
      }}
      text2Style={{
        fontSize: 14,
        fontWeight: '400',
        color: '#6b7280',
        numberOfLines: 0, // Allow unlimited lines
      }}
      text1={text1}
      text2={text2}
    />
  ),
  info: ({ text1, text2 }) => (
    <InfoToast
      style={{
        borderLeftColor: '#3b82f6',
        backgroundColor: '#fff',
        borderRadius: 8,
        height: 'auto',
        minHeight: 60,
        paddingVertical: 12,
        paddingHorizontal: 16,
      }}
      contentContainerStyle={{
        paddingHorizontal: 15,
      }}
      text1Style={{
        fontSize: 16,
        fontWeight: '600',
        color: '#1f2937',
        numberOfLines: 0, // Allow unlimited lines
      }}
      text2Style={{
        fontSize: 14,
        fontWeight: '400',
        color: '#6b7280',
        numberOfLines: 0, // Allow unlimited lines
      }}
      text1={text1}
      text2={text2}
    />
  ),
};

export default toastConfig;
