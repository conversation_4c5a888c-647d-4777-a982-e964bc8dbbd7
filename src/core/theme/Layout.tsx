import React from 'react';

import { ViewProps, ViewStyle } from 'react-native';

import { BoxProps } from '@shopify/restyle';

import Box from './Box';
import { Theme } from './theme';
import { useResponsive } from './useResponsive';

/**
 * Props for Row and Column components
 */
export type FlexBoxProps = BoxProps<Theme> &
  ViewProps & {
    /**
     * Children components
     */
    children?: React.ReactNode;
    /**
     * Align items
     */
    align?: 'flex-start' | 'flex-end' | 'center' | 'stretch' | 'baseline';
    /**
     * Justify content
     */
    justify?:
      | 'flex-start'
      | 'flex-end'
      | 'center'
      | 'space-between'
      | 'space-around'
      | 'space-evenly';
    /**
     * Spacing between children, uses theme spacing values
     */
    spacing?: keyof Theme['spacing'];
    /**
     * Wrap content
     */
    wrap?: boolean;
    /**
     * Flex property
     */
    flex?: number;
    style?: ViewStyle | ViewStyle[];
  };

/**
 * Row component for horizontal layouts
 */
export const Row: React.FC<FlexBoxProps> = ({
  children,
  align = 'center',
  justify = 'flex-start',
  spacing = 'md_16',
  wrap = false,
  flex,
  ...boxProps
}) => {
  return (
    <Box
      flexDirection="row"
      alignItems={align}
      justifyContent={justify}
      flexWrap={wrap ? 'wrap' : 'nowrap'}
      flex={flex}
      rowGap={spacing}
      style={boxProps.style}
      {...boxProps}>
      {children}
    </Box>
  );
};

/**
 * Column component for vertical layouts
 */
export const Column: React.FC<FlexBoxProps> = ({
  children,
  align = 'flex-start',
  justify = 'flex-start',
  gap,
  wrap = false,
  flex,
  ...boxProps
}) => {
  const { isPhone } = useResponsive();
  return (
    <Box
      flexDirection="column"
      alignItems={align}
      justifyContent={justify}
      flexWrap={wrap ? 'wrap' : 'nowrap'}
      flex={flex}
      gap={gap ?? (isPhone ? 'md_16' : 'lg_24')}
      style={boxProps.style}
      {...boxProps}>
      {children}
    </Box>
  );
};
