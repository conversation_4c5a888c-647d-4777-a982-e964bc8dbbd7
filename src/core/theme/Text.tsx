import { ComponentProps } from 'react';

import { TextStyle } from 'react-native';

import { createText } from '@shopify/restyle';
import { TOptions } from 'i18next';
import Animated from 'react-native-reanimated';

import { i18n, isRTL } from '../i18n';
import { TxKeyPath } from '../i18n/translationTypes';
import { Theme } from './theme';

const Text = createText<Theme>();

const TextAnimated = Animated.createAnimatedComponent(Text);

export const AnimatedText = TextAnimated;

// Translation helper function
export function translate(key: TxKeyPath, options?: TOptions): string {
  return i18n.t(key, options) as string;
}

export interface TypographyProps extends ComponentProps<typeof Text> {
  /**
   * Typography which is looked up via i18n.
   */
  tx?: TxKeyPath;
  /**
   * The text to display if not using `tx` or nested components.
   */
  text?: string;
  /**
   * Optional options to pass to i18n. Useful for interpolation
   * as well as explicitly setting locale or translation fallbacks.
   */
  txOptions?: TOptions;
}

/**
 * A Typography component that supports i18n translations.
 * Use the tx prop to provide a translation key, or text for static content.
 * The component automatically handles RTL languages based on the device's locale.
 */
function Typography(props: React.PropsWithChildren<TypographyProps>) {
  const { tx, txOptions, text, children, style, ...rest } = props;

  // Translate the text if a translation key is provided
  const i18nText = tx && translate(tx, txOptions);
  const content = i18nText || text || children;

  // Apply appropriate text style for RTL languages
  const rtlStyle: TextStyle = isRTL()
    ? { textAlign: 'right', writingDirection: 'rtl' as 'rtl' }
    : {};

  return (
    <Text style={[rtlStyle, style]} {...rest}>
      {content}
    </Text>
  );
}

// Export Typography as the default component for translation support
export default Typography;
