import React from 'react';

import { StyleSheet } from 'react-native';

import { BoxProps } from '@shopify/restyle';
import { SafeAreaView, SafeAreaViewProps } from 'react-native-safe-area-context';

import Box from './Box';
import { Theme, useTheme } from './theme';

export type SafeAreaWrapperProps = SafeAreaViewProps & {
  /**
   * Children components
   */
  children?: React.ReactNode;
  /**
   * Bottom edge inset
   */
  edges?: ('top' | 'right' | 'bottom' | 'left')[];
  /**
   * Background color from theme
   */
  backgroundColor?: keyof Theme['colors'];
  /**
   * Additional Box props for the inner container
   */
  boxProps?: BoxProps<Theme>;
};

/**
 * Safe area wrapper that uses the theme colors
 */
const SafeAreaWrapper: React.FC<SafeAreaWrapperProps> = ({
  children,
  edges = ['top', 'right', 'bottom', 'left'],
  backgroundColor = 'background',
  boxProps,
  style,
  ...props
}) => {
  const theme = useTheme();
  const bgColor = theme.colors[backgroundColor] || theme.colors.background;

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: bgColor, flex: 1 }, style]}
      edges={edges}
      {...props}>
      <Box flex={1} {...boxProps}>
        {children}
      </Box>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default SafeAreaWrapper;
