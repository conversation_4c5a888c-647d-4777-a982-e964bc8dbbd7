/* eslint-disable react-compiler/react-compiler */
import { ComponentProps } from 'react';

import { StyleProp, ViewStyle } from 'react-native';

import { createBox } from '@shopify/restyle';
import { RectButton } from 'react-native-gesture-handler';
import Animated, { useAnimatedStyle, useSharedValue, withSpring } from 'react-native-reanimated';

import { ButtonVariantAnimations } from '@/src/shared/constants/animation';

import { Theme } from '.';
import { haptics } from '../libs';

export type PressableProps = ComponentProps<typeof RectButton>;

const Pressable = createBox<Theme, PressableProps>(RectButton);

const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

type AnimatedPressableProps = ComponentProps<typeof AnimatedPressable>;

const PressableAnimated = (
  props: PressableProps &
    AnimatedPressableProps & {
      animatedStyle?: StyleProp<ViewStyle>;
      variant?: keyof typeof ButtonVariantAnimations;
    }
) => {
  const scale = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const handleActiveStateChange = async (active: boolean) => {
    await haptics.selection();
    const variantAnimation = props.variant
      ? ButtonVariantAnimations[props.variant]
      : ButtonVariantAnimations.primary;
    scale.value = withSpring(
      active ? variantAnimation.scale.pressed : variantAnimation.scale.default,
      variantAnimation.config
    );
  };

  const onPress = async (pointerInside: boolean) => {
    await haptics.selection();
    props.onPress?.(pointerInside);
    handleActiveStateChange(pointerInside);
    setTimeout(() => {
      handleActiveStateChange(!pointerInside);
    }, 100);
  };

  return (
    <AnimatedPressable
      activeOpacity={0}
      rippleRadius={null}
      onPress={onPress}
      onActiveStateChange={props.onActiveStateChange ?? handleActiveStateChange}
      {...props}
      style={[props.style, props.animatedStyle ?? animatedStyle]}
    />
  );
};

export { PressableAnimated };

export default Pressable;
