import { createContext, useCallback, useContext, useEffect, useMemo, useState } from 'react';

import { Appearance, Platform, useColorScheme } from 'react-native';

import { ThemeProvider as RestyleThemeProvider } from '@shopify/restyle';
import * as SystemUI from 'expo-system-ui';
import { useMMKVString } from 'react-native-mmkv';

import { Theme, darkTheme, lightTheme } from './theme';

type ThemeContextType = {
  theme: Theme;
  themeName: 'light' | 'dark';
  toggleTheme: () => void;
  setTheme: (themeName: 'light' | 'dark') => void;
};

/**
 * Context for theme management
 */
export const ThemeContext = createContext<ThemeContextType | null>(null);

export type ThemeProviderProps = {
  children: React.ReactNode;
  defaultTheme?: 'light' | 'dark' | 'system';
};

/**
 * Provider component for theme management
 * Handles theme switching and system theme detection
 */
export const ThemeProvider: React.FC<ThemeProviderProps> = ({
  children,
  defaultTheme = 'system',
}) => {
  const [lastTheme, setLastTheme] = useMMKVString('app.theme');
  const colorScheme = useColorScheme();
  const systemTheme = colorScheme === 'dark' ? 'dark' : 'light';

  const [themeName, setThemeName] = useState<'light' | 'dark'>(() => {
    if (lastTheme && lastTheme !== '') {
      return lastTheme as 'light' | 'dark';
    }
    return defaultTheme === 'system' ? systemTheme : defaultTheme;
  });

  useEffect(() => {
    const subscription = Appearance.addChangeListener(({ colorScheme }) => {
      if (defaultTheme === 'system') {
        setLastTheme(colorScheme === 'dark' ? 'dark' : 'light');
        setThemeName(colorScheme === 'dark' ? 'dark' : 'light');
      }
    });

    return () => {
      subscription.remove();
    };
  }, [defaultTheme, setLastTheme]);

  useEffect(() => {
    // Update system UI on native platforms
    if (Platform.OS !== 'web') {
      SystemUI.setBackgroundColorAsync(
        colorScheme === 'dark' ? darkTheme.colors.background : lightTheme.colors.background
      );
    }
    // Initialize web theming if on web platform
    else if (typeof document !== 'undefined') {
      // Initial setup of theme class
      const initialTheme = themeName === 'dark' ? 'dark' : 'light';
      document.documentElement.classList.remove('light-theme', 'dark-theme');
      document.documentElement.classList.add(`${initialTheme}-theme`);
    }
  }, [colorScheme, themeName]);

  // Initialize theme: prioritize lastTheme, then defaultTheme or systemTheme

  // Sync with system theme if defaultTheme is 'system' and no lastTheme is set
  useEffect(() => {
    if (defaultTheme === 'system' && (!lastTheme || lastTheme === '')) {
      setLastTheme(systemTheme);
      setThemeName(systemTheme);
    }
  }, [systemTheme, defaultTheme, lastTheme, setLastTheme]);

  // Toggle between light and dark themes
  const toggleTheme = useCallback(() => {
    setThemeName(prevTheme => {
      const newTheme = prevTheme === 'light' ? 'dark' : 'light';
      // Handle native platforms with Appearance API
      if (Platform.OS !== 'web') {
        Appearance.setColorScheme(newTheme);
      }
      // Handle web platform
      else {
        // Apply theme class to document element for CSS-based theming
        document.documentElement.classList.remove('light-theme', 'dark-theme');
        document.documentElement.classList.add(`${newTheme}-theme`);

        // You can also update meta theme-color for browser UI
        const metaThemeColor = document.querySelector('meta[name="theme-color"]');
        if (metaThemeColor) {
          metaThemeColor.setAttribute(
            'content',
            newTheme === 'dark' ? darkTheme.colors.background : lightTheme.colors.background
          );
        }
      }
      setLastTheme(newTheme);
      return newTheme;
    });
  }, [setLastTheme]);

  // Set specific theme and persist it
  const setTheme = useCallback(
    (theme: 'light' | 'dark') => {
      setThemeName(theme);
      setLastTheme(theme);

      // Handle platform-specific theme setting
      if (Platform.OS !== 'web') {
        // Use Appearance API for native platforms
        Appearance.setColorScheme(theme);
      } else if (typeof document !== 'undefined') {
        // Apply theme class for web platform
        document.documentElement.classList.remove('light-theme', 'dark-theme');
        document.documentElement.classList.add(`${theme}-theme`);

        // Update meta theme-color
        const metaThemeColor = document.querySelector('meta[name="theme-color"]');
        if (metaThemeColor) {
          metaThemeColor.setAttribute(
            'content',
            theme === 'dark' ? darkTheme.colors.background : lightTheme.colors.background
          );
        }
      }
    },
    [setLastTheme]
  );

  // Check if user has manually changed the theme
  const isUserThemeSet = useMemo(() => !!lastTheme && lastTheme !== '', [lastTheme]);

  // Select theme object
  const theme = themeName === 'dark' ? darkTheme : lightTheme;

  // Context value
  const contextValue: ThemeContextType = useMemo(
    () => ({
      theme,
      themeName,
      toggleTheme,
      setTheme,
      isUserThemeSet, // New property
    }),
    [theme, themeName, toggleTheme, setTheme, isUserThemeSet]
  );

  return (
    <ThemeContext.Provider value={contextValue}>
      <RestyleThemeProvider theme={theme}>{children}</RestyleThemeProvider>
    </ThemeContext.Provider>
  );
};

/**
 * Hook to access theme context
 * Provides theme object and theme management functions
 */
export const useThemeContext = (): ThemeContextType => {
  const context = useContext(ThemeContext);

  if (!context) {
    throw new Error('useThemeContext must be used within a ThemeProvider');
  }

  return context;
};
