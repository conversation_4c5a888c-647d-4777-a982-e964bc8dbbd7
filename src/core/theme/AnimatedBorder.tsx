import React, { ComponentProps, useEffect } from 'react';

import { Platform } from 'react-native';

import {
  BackgroundColorProps,
  BorderProps,
  BoxProps,
  LayoutProps,
  SpacingProps,
  VariantProps,
  createRestyleComponent,
  createVariant,
  useTheme,
} from '@shopify/restyle';
import Animated, {
  Easing,
  ReduceMotion,
  interpolateColor,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';

import { Theme } from './theme';

// Create a variant for the animated border
const animatedBorderVariant = createVariant<Theme, 'animatedBorderVariants'>({
  themeKey: 'animatedBorderVariants',
});

// Compose restyle functions for the animated border
type AnimatedBorderRestyleProps = SpacingProps<Theme> &
  BorderProps<Theme> &
  BackgroundColorProps<Theme> &
  LayoutProps<Theme> &
  VariantProps<Theme, 'animatedBorderVariants'>;

// Create a type that includes the style prop
type AnimatedViewProps = VariantProps<Theme, 'animatedBorderVariants'> &
  BoxProps<Theme> & {
    children: React.ReactNode;
    style?: ComponentProps<typeof Animated.View>['style'];
  };

const StyledAnimatedView = createRestyleComponent<AnimatedViewProps, Theme>(
  [animatedBorderVariant],
  Animated.View
);
// Create a base animated component with restyle

interface AnimatedBorderProps extends AnimatedBorderRestyleProps {
  children: React.ReactNode;
  visible: boolean;
  duration?: number;
  style?: ComponentProps<typeof Animated.View>['style'];
}

const AnimatedBorder = ({
  children,
  visible,
  duration = 600,
  variant,
  style,
  ...restyleProps
}: AnimatedBorderProps) => {
  // Progress for the animation effect
  const progress = useSharedValue(0);
  const borderWidth = useSharedValue(1);
  const theme = useTheme<Theme>();
  const isIOS = Platform.OS === 'ios';

  const colors = {
    focused: [theme.colors.inputBorder, theme.colors.inputBorderFocus],
    error: [theme.colors.inputBorder, theme.colors.inputBorderError],
    disabled: [theme.colors.inputBorder, theme.colors.disabled],
  };

  useEffect(() => {
    progress.value = withTiming(visible ? 1 : 0, {
      duration,
      easing: Easing.bezier(0.33, 1, 0.68, 1),
    });

    if (isIOS) {
      borderWidth.value = withTiming(visible ? 1.5 : 1, {
        duration: 150,
        reduceMotion: ReduceMotion.Never,
      });
    }
  }, [visible, duration, progress, isIOS, borderWidth]);

  // Animated styles for the border
  const animatedBorderStyle = useAnimatedStyle(() => {
    const borderColor = interpolateColor(
      progress.value,
      [0, 1],
      variant === 'error' ? colors.error : colors.focused
    );

    return {
      borderColor,
      borderWidth: isIOS ? borderWidth.value : 1.5,
      transform: [{ scale: interpolate(progress.value, [0, 1], [0.99, 1]) }],
    };
  });

  return (
    <StyledAnimatedView variant={variant} {...restyleProps} style={[style, animatedBorderStyle]}>
      {children}
    </StyledAnimatedView>
  );
};

// Helper function for interpolation
const interpolate = (value: number, inputRange: number[], outputRange: number[]) => {
  'worklet';
  if (value <= inputRange[0]) return outputRange[0];
  if (value >= inputRange[1]) return outputRange[1];

  const range = inputRange[1] - inputRange[0];
  const progress = (value - inputRange[0]) / range;
  const result = outputRange[0] + progress * (outputRange[1] - outputRange[0]);

  return result;
};

export default AnimatedBorder;
