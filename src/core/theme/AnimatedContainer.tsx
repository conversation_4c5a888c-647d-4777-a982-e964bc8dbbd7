import React, { useEffect } from 'react';

import { StyleSheet, ViewStyle } from 'react-native';

import { BoxProps } from '@shopify/restyle';
import Animated, {
  FadeIn,
  FadeOut,
  LinearTransition,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';

import Box from '../theme/Box';
import { Theme } from '../theme/theme';

type AnimatedContainerProps = BoxProps<Theme> & {
  children: React.ReactNode;
  duration?: number;
  style?: ViewStyle | ViewStyle[];
  animateOnMount?: boolean;
  entering?: FadeIn;
  exiting?: FadeOut;
  layout?: LinearTransition;
};

const AnimatedContainer = ({
  children,
  duration = 300,
  style,
  animateOnMount = true,
  entering = FadeIn.duration(duration),
  exiting = FadeOut.duration(duration),
  layout = LinearTransition.springify(),
  ...rest
}: AnimatedContainerProps) => {
  const opacity = useSharedValue(animateOnMount ? 0 : 1);

  useEffect(() => {
    if (animateOnMount) {
      opacity.value = withTiming(1, { duration });
    }
  }, [animateOnMount, duration, opacity]);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
  }));

  return (
    <Box {...rest} flex={1}>
      {/* Outer Animated.View handles layout animations */}
      <Animated.View layout={layout} style={[styles.container, style]}>
        {/* Inner Animated.View handles opacity animations */}
        <Animated.View entering={entering} exiting={exiting}>
          {children}
        </Animated.View>
      </Animated.View>
    </Box>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
});

export default AnimatedContainer;
