import React, { ComponentProps, useState } from 'react';

import {
  ActivityIndicator,
  Platform,
  TextInput as RNTextInput,
  TextInputProps as RNTextInputProps,
  ViewStyle,
} from 'react-native';

import { Fontisto } from '@expo/vector-icons';
import {
  BoxProps,
  VariantProps,
  createRestyleComponent,
  createVariant,
  useTheme,
} from '@shopify/restyle';
import Animated, { Keyframe, LayoutAnimationConfig } from 'react-native-reanimated';

import AnimatedBorder from './AnimatedBorder';
import Box from './Box';
import { Column, Row } from './Layout';
import Pressable, { PressableAnimated } from './Pressable';
import Text from './Text';
import { Theme } from './theme';
import { useResponsive } from './useResponsive';

const BaseTextInput = createRestyleComponent<
  VariantProps<Theme, 'textInputVariants'> & RNTextInputProps,
  Theme
>([createVariant({ themeKey: 'textInputVariants' })], RNTextInput);

const BaseTextInputContainer = createRestyleComponent<
  VariantProps<Theme, 'textInputContainerVariants'> &
    BoxProps<Theme> & { children: React.ReactNode },
  Theme
>([createVariant({ themeKey: 'textInputContainerVariants' })], Box);

type InputRightElementProps = {
  clearButtonVisible: boolean;
  trailing: React.ReactNode;
  onClear?: () => void;
  onFocus: () => void;
};

const clearButtonEnteringAnimation = new Keyframe({
  0: {
    opacity: 0,
    transform: [{ scale: 0.8 }, { rotate: '90deg' }], // Increased rotation angle
  },
  100: {
    opacity: 1,
    transform: [{ scale: 1 }, { rotate: '0deg' }],
  },
})
  .duration(200)
  .delay(100);

const clearButtonExitingAnimation = new Keyframe({
  0: {
    opacity: 1,
    transform: [{ scale: 1 }, { rotate: '0deg' }],
  },
  100: {
    opacity: 0,
    transform: [{ scale: 0.8 }, { rotate: '90deg' }], // Increased rotation angle
  },
}).duration(200);

const trailingEnteringAnimation = new Keyframe({
  0: {
    opacity: 0,
    transform: [{ scale: 0.8 }],
  },
  100: {
    opacity: 1,
    transform: [{ scale: 1 }],
  },
})
  .duration(100)
  .delay(200);

const trailingExitingAnimation = new Keyframe({
  0: {
    opacity: 1,
    transform: [{ scale: 1 }],
  },
  100: {
    opacity: 0,
    transform: [{ scale: 0.8 }],
  },
}).duration(100);

const InputTrailing = ({
  onFocus,
  onClear,
  clearButtonVisible,
  trailing,
}: InputRightElementProps) => {
  const theme = useTheme();
  const { select } = useResponsive();

  const isMounted = React.useRef(false);
  React.useEffect(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
    };
  }, []);

  return (
    <Row gap="xxs_4">
      {clearButtonVisible && (
        <Animated.View
          key="clear-container"
          entering={clearButtonEnteringAnimation}
          exiting={clearButtonExitingAnimation}>
          <PressableAnimated
            activeOpacity={0}
            variant="secondary"
            onPress={() => {
              onClear?.();
            }}>
            <Fontisto
              name="close"
              size={select({ phone: 16, tablet: 24 })}
              color={theme.colors.textSecondary}
            />
          </PressableAnimated>
        </Animated.View>
      )}
      {trailing && (
        <LayoutAnimationConfig skipEntering={!isMounted.current}>
          <Animated.View
            key="trailing-container"
            entering={trailingEnteringAnimation}
            exiting={trailingExitingAnimation}>
            <PressableAnimated activeOpacity={0} onPress={onFocus} variant="secondary">
              {trailing}
            </PressableAnimated>
          </Animated.View>
        </LayoutAnimationConfig>
      )}
    </Row>
  );
};

type Props = {
  label?: string;
  error?: string;
  helper?: string;
  leading?: React.ReactNode;
  trailing?: React.ReactNode;
  isLoading?: boolean;
  showClearButton?: boolean;
  variant?: ComponentProps<typeof BaseTextInputContainer>['variant'];
  textInputVariant?: ComponentProps<typeof BaseTextInput>['variant'];
  containerProps?: Partial<ComponentProps<typeof BaseTextInputContainer>>;
  ref?: React.RefObject<RNTextInput> | null;
};

const TextInputField = ({
  label,
  error,
  helper,
  leading,
  trailing,
  isLoading = false,
  showClearButton = true,
  variant,
  containerProps,
  textInputVariant,
  ref,
  ...restProps
}: Props & RNTextInputProps) => {
  const theme = useTheme<Theme>();
  const [isFocused, setIsFocused] = useState(false);
  const isAndroid = Platform.OS === 'android';

  // Create an internal ref if no ref is passed
  const internalRef = React.useRef<RNTextInput>(null);

  const inputRef = ref || internalRef;

  const focus = () => {
    inputRef?.current?.focus();
  };

  const shouldShowClearButton = showClearButton && restProps?.value?.length! > 0 && isFocused;

  const hasError = !!error;

  const getTextInputContainerVariant = () => {
    if (isFocused) {
      return 'focused';
    }
    if (hasError) {
      return 'error';
    }
    if (restProps.value?.length) {
      return 'filled';
    }
    return variant;
  };

  const getLabelColor = () => {
    if (hasError) {
      return 'error';
    }
    if (isFocused) {
      return 'mainText';
    }
    return 'textSecondary';
  };

  return (
    <Box width="100%" pointerEvents={variant === 'disabled' ? 'none' : 'auto'} gap="xxs_4">
      <AnimatedBorder visible={isFocused || hasError} variant={hasError ? 'error' : 'focused'}>
        <BaseTextInputContainer variant={getTextInputContainerVariant()} {...containerProps}>
          {label && (
            <Column gap="none_0" position="absolute" width="100%" px="md_16" pt="xs_8">
              <Pressable onPress={focus} activeOpacity={0} rippleRadius={null}>
                <Text variant="overline2" color={getLabelColor()}>
                  {label}
                </Text>
              </Pressable>
            </Column>
          )}
          <Box
            width="100%"
            flexDirection="row"
            alignItems="center"
            // This is due the underlineColorAndroid property on Android just hiding the underline and to removing
            style={{ transform: [...(isAndroid ? [{ translateX: -4 }] : []), { translateY: 4 }] }}>
            {leading && <Box marginRight="xs_8">{leading}</Box>}
            <BaseTextInput
              ref={inputRef}
              variant={hasError ? 'error' : textInputVariant}
              {...restProps}
              underlineColorAndroid="transparent"
              style={[restProps.style, { outline: 'none', borderWidth: 0 } as ViewStyle]}
              focusable={variant !== 'disabled'}
              editable={variant !== 'disabled'}
              onFocus={e => {
                setIsFocused(true);
                restProps.onFocus?.(e);
              }}
              onBlur={e => {
                setIsFocused(false);
                restProps.onBlur?.(e);
              }}
              cursorColor={theme.colors.brandMain}
              selectionColor={theme.colors.brandMain}
              selectionHandleColor={theme.colors.brandMain}
              placeholderTextColor={theme.colors.inputPlaceholder}
            />

            <InputTrailing
              onClear={() => restProps?.onChangeText?.('')}
              onFocus={focus}
              clearButtonVisible={shouldShowClearButton}
              trailing={trailing}
            />

            {isLoading && (
              <Box marginLeft="xs_8">
                <ActivityIndicator size="small" color={theme.colors.brandMain} />
              </Box>
            )}
          </Box>
        </BaseTextInputContainer>
      </AnimatedBorder>

      {(error || helper) && (
        <Box>
          <Text variant="b_14Regular_content" color={error ? 'errorMain' : 'textSecondary'}>
            {error || helper}
          </Text>
        </Box>
      )}
    </Box>
  );
};

export default TextInputField;
