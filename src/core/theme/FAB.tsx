/**
 * FAB (Floating Action Button) Component
 */
import React from 'react';

import { Pressable } from 'react-native';

import { Feather } from '@expo/vector-icons';
import { useTheme } from '@shopify/restyle';
import Animated, {
  FadeIn,
  FadeOut,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from 'react-native-reanimated';

import { useResponsive } from '@/src/core/theme/useResponsive';

import Box from './Box';
import Text from './Text';
import { Theme, TokensThemeColors } from './theme';

interface FABProps {
  icon: keyof typeof Feather.glyphMap;
  onPress: () => void;
  position?: 'bottom-right' | 'bottom-left' | 'bottom-center';
  label?: string;
  variant?: 'primary' | 'secondary';
}

const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

export const FAB: React.FC<FABProps> = ({
  icon,
  onPress,
  position = 'bottom-right',
  label,
  variant = 'primary',
}) => {
  const theme = useTheme<Theme>();
  const { select } = useResponsive();
  const scale = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const handlePressIn = () => {
    scale.set(
      withSpring(0.9, {
        damping: 15,
        stiffness: 400,
      })
    );
  };

  const handlePressOut = () => {
    scale.set(
      withSpring(1, {
        damping: 15,
        stiffness: 400,
      })
    );
  };

  // Position styles
  const positionStyles = {
    'bottom-right': {
      bottom: theme.spacing.lg_24,
      right: theme.spacing.md_16,
    },
    'bottom-left': {
      bottom: theme.spacing.lg_24,
      left: theme.spacing.md_16,
    },
    'bottom-center': {
      bottom: theme.spacing.lg_24,
      alignSelf: 'center',
    },
  };

  // Colors
  const backgroundColor: TokensThemeColors =
    variant === 'primary' ? 'brandMain' : 'elevatedBackground';
  const iconColor = variant === 'primary' ? theme.colors.white : theme.colors.mainText;
  const textColor = variant === 'primary' ? 'white' : 'mainText';

  return (
    <AnimatedPressable
      onPress={onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      entering={FadeIn.duration(300)}
      exiting={FadeOut.duration(300)}
      style={[
        {
          position: 'absolute',
          ...positionStyles[position],
          zIndex: 999,
        },
        animatedStyle,
      ]}>
      <Box
        flexDirection="row"
        alignItems="center"
        backgroundColor={backgroundColor}
        paddingVertical={label ? 'sm_12' : 'md_16'}
        paddingHorizontal={label ? 'md_16' : 'md_16'}
        borderRadius="circle_9999"
        style={{
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.2,
          shadowRadius: 8,
          elevation: 8,
        }}>
        <Feather name={icon} size={select({ phone: 24, tablet: 28 })} color={iconColor} />
        {label && (
          <Text variant="b_14Medium_button" color={textColor} marginLeft="xs_8">
            {label}
          </Text>
        )}
      </Box>
    </AnimatedPressable>
  );
};
