import { use } from 'react';

import { StyleSheet } from 'react-native';

import { useBottomTabBarHeight } from '@react-navigation/bottom-tabs';
import { BlurView } from 'expo-blur';

import { ThemeContext } from '../../theme/ThemeProvider';

export default function BlurTabBarBackground() {
  const themeContext = use(ThemeContext);
  const tint =
    themeContext?.themeName === 'dark' ? 'systemChromeMaterialDark' : 'systemChromeMaterialLight';

  return <BlurView tint={tint} intensity={100} style={StyleSheet.absoluteFill} />;
}

export function useBottomTabOverflow() {
  const tabBarHeight = useBottomTabBarHeight();
  return tabBarHeight;
}
