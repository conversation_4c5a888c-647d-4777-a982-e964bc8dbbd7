import React, { forwardRef } from 'react';

import { Box } from '@/src/core/theme';

import { TAB_BAR_HEIGHT } from './constants';
import { TabBarContainerProps } from './types';

export const TabBarContainer = forwardRef<any, TabBarContainerProps>(
  ({ children, style, onLayout }, ref) => {
    return (
      <Box
        ref={ref}
        position="absolute"
        top={0}
        left={0}
        right={0}
        zIndex="high"
        height={TAB_BAR_HEIGHT}
        backgroundColor="transparent"
        style={style}
        onLayout={onLayout}>
        {/* Background with blur effect */}
        <Box
          position="absolute"
          top={0}
          left={0}
          right={0}
          bottom={0}
          backgroundColor="cardBackground"
          opacity={0.9}
          borderBottomLeftRadius="xxl_24"
          borderBottomRightRadius="xxl_24"
        />

        {/* Border overlay */}
        <Box
          position="absolute"
          top={0}
          left={0}
          right={0}
          bottom={0}
          borderBottomLeftRadius="xxl_24"
          borderBottomRightRadius="xxl_24"
          borderWidth={0.5}
          borderColor="border"
          borderTopWidth={0}
        />

        {/* Content */}
        {children}
      </Box>
    );
  }
);

TabBarContainer.displayName = 'TabBarContainer';

export const TabBarContent: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <Box flex={1} flexDirection="row" alignItems="center" paddingHorizontal="md_16" gap="md_16">
      {children}
    </Box>
  );
};
