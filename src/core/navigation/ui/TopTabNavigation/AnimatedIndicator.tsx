import { memo } from 'react';

import Animated, { Extrapolation, interpolate, useAnimatedStyle } from 'react-native-reanimated';

import { useTheme } from '@/src/core/theme';

import { INDICATOR_BOTTOM_OFFSET, INDICATOR_HEIGHT } from './constants';
import { AnimatedIndicatorProps } from './types';

export const AnimatedIndicator = memo<AnimatedIndicatorProps>(({ position, layout, config }) => {
  const theme = useTheme();

  const inputRange = layout.positions.map((_, i) => i);
  const outputRangeX = layout.positions.map(
    pos => pos + (layout.tabWidth - config.indicatorWidth) / 2
  );

  const widthInputRange = inputRange.map(i => i - 0.1).concat(inputRange.map(i => i + 0.1));
  const widthOutputRange = new Array(inputRange.length)
    .fill(config.indicatorWidth * 0.8)
    .concat(new Array(inputRange.length).fill(config.indicatorWidth));

  const indicatorStyle = useAnimatedStyle(() => {
    'worklet';

    const translateXVal = interpolate(
      position.value,
      inputRange,
      outputRangeX,
      Extrapolation.CLAMP
    );

    const widthVal = interpolate(
      position.value,
      widthInputRange,
      widthOutputRange,
      Extrapolation.CLAMP
    );

    const opacity = interpolate(
      position.value,
      [-0.5, 0, inputRange.length - 1, inputRange.length - 0.5],
      [0, 1, 1, 0],
      Extrapolation.CLAMP
    );

    return {
      transform: [{ translateX: translateXVal }],
      width: widthVal,
      opacity,
    };
  });

  return (
    <Animated.View
      style={[
        {
          position: 'absolute',
          bottom: INDICATOR_BOTTOM_OFFSET,
          height: INDICATOR_HEIGHT,
          backgroundColor: theme.colors.primary,
          borderRadius: INDICATOR_HEIGHT / 2,
        },
        indicatorStyle,
      ]}
    />
  );
});

AnimatedIndicator.displayName = 'AnimatedIndicator';
