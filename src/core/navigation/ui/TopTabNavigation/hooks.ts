import { use<PERSON>allback, useEffect, useMemo, useRef, useState } from 'react';

import { View } from 'react-native';

import { TabNavigationState } from '@react-navigation/native';
import { Gesture } from 'react-native-gesture-handler';
import {
  SharedValue,
  runOnJS,
  useAnimatedReaction,
  useSharedValue,
  withSpring,
} from 'react-native-reanimated';

import { haptics } from '@/src/core/libs';

import { SCREEN_WIDTH, TAB_BAR_HEIGHT } from './constants';
import { TabBarConfig, TabLayout } from './types';

/**
 * Custom hook to calculate tab layout dimensions
 */
export const useTabLayout = (routeCount: number, config: Required<TabBarConfig>): TabLayout => {
  return useMemo(() => {
    const { horizontalPadding, tabGap, tabStyle } = config;

    if (tabStyle === 'scrollable') {
      // For scrollable tabs, calculate based on content width
      // This is a simplified version - you might want to measure actual text width
      const minTabWidth = 80;
      const positions = Array.from({ length: routeCount }, (_, i) => {
        return horizontalPadding / 2 + i * (minTabWidth + tabGap);
      });

      return {
        tabWidth: minTabWidth,
        totalWidth: positions[positions.length - 1] + minTabWidth + horizontalPadding / 2,
        positions,
      };
    }

    // Fixed width tabs
    const availableWidth = SCREEN_WIDTH - horizontalPadding;
    const tabWidth = (availableWidth - (routeCount - 1) * tabGap) / routeCount;

    const positions = Array.from({ length: routeCount }, (_, i) => {
      return horizontalPadding / 2 + i * (tabWidth + tabGap);
    });

    return {
      tabWidth,
      totalWidth: SCREEN_WIDTH,
      positions,
    };
  }, [routeCount, config]);
};

/**
 * Custom hook to manage tab animations and gestures
 */
export const useTabAnimation = (
  state: TabNavigationState<any>,
  layout: TabLayout,
  config: Required<TabBarConfig>
) => {
  const position = useSharedValue(state.index);
  const translateX = useSharedValue(0);

  // Calculate indicator position
  const getIndicatorPosition = useCallback(
    (index: number) => {
      const tabPosition = layout.positions[index];
      const indicatorOffset = (layout.tabWidth - config.indicatorWidth) / 2;
      return tabPosition + indicatorOffset;
    },
    [layout, config.indicatorWidth]
  );

  // Initialize position
  useEffect(() => {
    translateX.value = getIndicatorPosition(state.index);
  }, [getIndicatorPosition, state.index, translateX]);

  // Animate on index change
  useEffect(() => {
    position.value = withSpring(state.index, config.animationConfig);
    translateX.set(withSpring(getIndicatorPosition(state.index), config.animationConfig));
  }, [state.index, getIndicatorPosition, config.animationConfig, position, translateX]);

  const triggerHaptics = () => {
    haptics.light();
  };

  const enableHaptics = config.enableHaptics;
  // Haptic feedback on tab change
  useAnimatedReaction(
    () => Math.round(position.value),
    (currentIndex, previousIndex) => {
      'worklet';
      if (enableHaptics && currentIndex !== previousIndex && previousIndex !== null) {
        runOnJS(triggerHaptics)();
      }
    }
  );

  return {
    position,
    translateX,
  };
};

/**
 * Custom hook for gesture-based navigation
 * Currently disabled due to worklet issues - will be re-implemented
 */
export const useTabGestures = (
  navigation: any,
  state: TabNavigationState<any>,
  layout: TabLayout,
  position: SharedValue<number>,
  config: Required<TabBarConfig>
) => {
  const startX = useSharedValue(0);

  const enableGestures = config.enableGestures;
  const tabGap = config.tabGap;
  const tabWidth = layout.tabWidth;
  const animationConfig = config.animationConfig;
  const routesLength = state.routes.length;
  const currentIndex = state.index;

  const navigateToIndex = useCallback(
    (index: number) => {
      navigation.navigate(state.routes[index].name);
    },
    [navigation, state.routes]
  );

  const gesture = useMemo(
    () =>
      Gesture.Pan()
        .onBegin(event => {
          startX.value = event.x;
        })
        .onUpdate(event => {
          'worklet';
          if (!enableGestures) return;

          const diff = startX.value - event.x;
          const fullTabWidth = tabWidth + tabGap;
          const progress = diff / fullTabWidth;

          position.set(Math.max(0, Math.min(routesLength - 1, currentIndex + progress)));
        })
        .onEnd(event => {
          'worklet';
          if (!enableGestures) return;

          const currentPosition = position.value;
          const velocityX = event.velocityX;

          const targetIndex =
            velocityX > 500
              ? Math.floor(currentPosition)
              : velocityX < -500
                ? Math.ceil(currentPosition)
                : Math.round(currentPosition);

          const clampedIndex = Math.max(0, Math.min(routesLength - 1, targetIndex));

          if (clampedIndex !== currentIndex) {
            runOnJS(navigateToIndex)(clampedIndex); // ✅ JS-safe call
          } else {
            position.set(withSpring(currentIndex, animationConfig)); // ✅ all values safe
          }
        }),
    [
      startX,
      enableGestures,
      tabWidth,
      tabGap,
      position,
      routesLength,
      currentIndex,
      navigateToIndex,
      animationConfig,
    ]
  );

  return gesture;
};

/**
 * Custom hook that returns the actual measured height of the tab bar
 * Uses layout measurements to get the real height instead of relying on configuration
 *
 * @returns An object containing the measured height and utilities for layout measurement
 *
 * @example
 * const { height: tabBarHeight, ref: tabBarRef, onLayout } = useTopTabBarHeight();
 *
 * // In your component:
 * <View ref={tabBarRef} onLayout={onLayout}>
 *   {/* Tab bar content *
 * </View>
 *
 * // The height will be updated automatically when the layout changes
 * console.log(tabBarHeight); // Actual measured height
 */
export const useTopTabBarHeight = () => {
  const [measuredHeight, setMeasuredHeight] = useState<number>(TAB_BAR_HEIGHT);
  const ref = useRef<View>(null);

  const onLayout = useCallback(
    (event: any) => {
      const { height } = event.nativeEvent.layout;
      if (height > 0 && height !== measuredHeight) {
        setMeasuredHeight(height);
      }
    },
    [measuredHeight]
  );

  // Return the measured height and utilities
  return useMemo(
    () => ({
      height: measuredHeight,
      ref,
      onLayout,
      defaultHeight: TAB_BAR_HEIGHT,
    }),
    [measuredHeight, onLayout]
  );
};
