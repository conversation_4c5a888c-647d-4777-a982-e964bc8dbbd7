import React, { useCallback, useMemo } from 'react';

import { ScrollView } from 'react-native';

import { GestureDetector } from 'react-native-gesture-handler';
import Animated, { useAnimatedScrollHandler, useSharedValue } from 'react-native-reanimated';

import { AnimatedIndicator } from './AnimatedIndicator';
import { TabBarContainer, TabBarContent } from './TabBarContainer';
import { useTabBarHeight } from './TabBarHeightContext';
import { TabButton } from './TabButton';
import { DEFAULT_CONFIG } from './constants';
import { useTabAnimation, useTabGestures, useTabLayout } from './hooks';
import type { CustomTabBarProps } from './types';

const AnimatedScrollView = Animated.createAnimatedComponent(ScrollView);

export const CustomTabBar: React.FC<CustomTabBarProps> = ({
  state,
  descriptors,
  navigation,
  config: userConfig,
}) => {
  // Get tab bar height context
  const { setHeight } = useTabBarHeight();

  // Handle layout measurement
  const handleLayout = useCallback(
    (event: any) => {
      const { height } = event.nativeEvent.layout;
      if (height > 0) {
        setHeight(height);
      }
    },
    [setHeight]
  );

  // Merge user config with defaults
  const config = useMemo(
    () => ({
      ...DEFAULT_CONFIG,
      ...userConfig,
      animationConfig: {
        ...DEFAULT_CONFIG.animationConfig,
        ...userConfig?.animationConfig,
      },
    }),
    [userConfig]
  );

  // Calculate tab layout
  const layout = useTabLayout(state.routes.length, config);

  // Setup animations
  const { position } = useTabAnimation(state, layout, config);

  // Setup gesture handlers
  const gesture = useTabGestures(navigation, state, layout, position, config);

  // Scroll view ref for scrollable tabs
  const scrollX = useSharedValue(0);
  const scrollViewRef = React.useRef<ScrollView>(null);

  // Handle tab press
  const handleTabPress = useCallback(
    (index: number) => {
      const route = state.routes[index];
      const event = navigation.emit({
        type: 'tabPress',
        target: route.key,
        canPreventDefault: true,
      });

      if (state.index !== index && !event.defaultPrevented) {
        navigation.navigate(route.name);

        // Auto-scroll to focused tab if scrollable
        if (config.tabStyle === 'scrollable' && scrollViewRef.current) {
          const tabPosition = layout.positions[index];
          const scrollToX = Math.max(0, tabPosition - 100); // Center the tab
          scrollViewRef.current.scrollTo({ x: scrollToX, animated: true });
        }
      }
    },
    [state, navigation, config.tabStyle, layout]
  );

  // Scroll handler for scrollable tabs
  const scrollHandler = useAnimatedScrollHandler({
    onScroll: event => {
      scrollX.value = event.contentOffset.x;
    },
  });

  const renderTabButton = useCallback(
    (route: any, index: number) => {
      const { options } = descriptors[route.key];
      const label =
        options.tabBarLabel !== undefined
          ? options.tabBarLabel
          : options.title !== undefined
            ? options.title
            : route.name;

      const isFocused = state.index === index;

      return (
        <TabButton
          key={route.key}
          label={typeof label === 'string' ? label : route.name}
          index={index}
          isFocused={isFocused}
          onPress={() => handleTabPress(index)}
          onLongPress={() =>
            navigation.emit({
              type: 'tabLongPress',
              target: route.key,
            })
          }
          accessibilityState={isFocused ? { selected: true } : { selected: false }}
          accessibilityLabel={options.tabBarAccessibilityLabel}
          testID={options.tabBarButtonTestID}
          badge={options.tabBarBadge}
          icon={options.tabBarIcon?.({
            focused: isFocused,
            color: '',
          })}
          position={position}
        />
      );
    },
    [descriptors, state.index, handleTabPress, navigation, position]
  );

  const content = (
    <>
      {state.routes.map(renderTabButton)}
      <AnimatedIndicator position={position} layout={layout} config={config} />
    </>
  );

  if (config.enableGestures) {
    return (
      <GestureDetector gesture={gesture}>
        <TabBarContainer onLayout={handleLayout}>
          {config.tabStyle === 'scrollable' ? (
            <AnimatedScrollView
              ref={scrollViewRef}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={{
                paddingHorizontal: config.horizontalPadding / 2,
              }}
              onScroll={scrollHandler}
              scrollEventThrottle={16}>
              {content}
            </AnimatedScrollView>
          ) : (
            <TabBarContent>{content}</TabBarContent>
          )}
        </TabBarContainer>
      </GestureDetector>
    );
  }

  return (
    <TabBarContainer onLayout={handleLayout}>
      {config.tabStyle === 'scrollable' ? (
        <AnimatedScrollView
          ref={scrollViewRef}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{
            paddingHorizontal: config.horizontalPadding / 2,
          }}
          onScroll={scrollHandler}
          scrollEventThrottle={16}>
          {content}
        </AnimatedScrollView>
      ) : (
        <TabBarContent>{content}</TabBarContent>
      )}
    </TabBarContainer>
  );
};
