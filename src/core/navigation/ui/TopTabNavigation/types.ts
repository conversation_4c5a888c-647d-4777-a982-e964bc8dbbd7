import React, { ReactNode } from 'react';

import { ViewStyle } from 'react-native';

import { MaterialTopTabBarProps } from '@react-navigation/material-top-tabs';
import { SharedValue } from 'react-native-reanimated';

export interface TabBarConfig {
  indicatorWidth?: number;
  horizontalPadding?: number;
  tabGap?: number;
  height?: number;
  animationConfig?: {
    damping?: number;
    mass?: number;
    stiffness?: number;
  };
  enableHaptics?: boolean;
  enableGestures?: boolean;
  tabStyle?: 'fixed' | 'scrollable';
  showBadges?: boolean;
}

export interface TabLayout {
  tabWidth: number;
  totalWidth: number;
  positions: number[];
}

export interface TabButtonProps {
  label: string;
  index: number;
  isFocused: boolean;
  onPress: () => void;
  onLongPress: () => void;
  accessibilityState: { selected: boolean };
  accessibilityLabel?: string;
  testID?: string;
  badge?: string | number | React.ReactElement;
  icon?: ReactNode;
  position: SharedValue<number>;
}

export interface AnimatedIndicatorProps {
  position: SharedValue<number>;
  layout: TabLayout;
  config: Required<TabBarConfig>;
}

export interface CustomTabBarProps extends MaterialTopTabBarProps {
  config?: TabBarConfig;
}

export interface TabBarContainerProps {
  children: ReactNode;
  style?: ViewStyle;
  onLayout?: (event: any) => void;
}
