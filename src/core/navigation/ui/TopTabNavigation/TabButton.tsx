import React, { memo, useCallback } from 'react';

import { TouchableOpacity } from 'react-native';

import Animated, {
  Extrapolation,
  interpolate,
  interpolateColor,
  useAnimatedStyle,
  useDerivedValue,
  withTiming,
} from 'react-native-reanimated';

import { Box, Text, useResponsive, useTheme } from '@/src/core/theme';

import { triggerHaptic } from './haptics';
import { TabButtonProps } from './types';

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);

export const TabButton = memo<TabButtonProps>(
  ({
    label,
    index,
    isFocused,
    onPress,
    onLongPress,
    accessibilityState,
    accessibilityLabel,
    testID,
    badge,
    icon,
    position,
  }) => {
    const theme = useTheme();
    const { isTablet } = useResponsive();

    const handlePress = useCallback(async () => {
      if (!isFocused) {
        await triggerHaptic.selection();
      }
      onPress();
    }, [isFocused, onPress]);

    const animatedStyle = useAnimatedStyle(() => {
      const inputRange = [index - 1, index, index + 1];

      const scale = interpolate(position.value, inputRange, [0.9, 1, 0.9], Extrapolation.CLAMP);

      return {
        transform: [{ scale }],
      };
    });

    // Use a derived value to smooth out the color transition
    const animatedColorProgress = useDerivedValue(() => {
      const diff = Math.abs(position.value - index);
      return withTiming(diff < 0.5 ? 1 : 0, { duration: 200 });
    });

    const textAnimatedStyle = useAnimatedStyle(() => {
      'worklet';

      // Simple interpolation between two colors based on progress
      const color = interpolateColor(
        animatedColorProgress.value,
        [0, 1],
        [theme.colors.textTertiary, theme.colors.primary]
      );

      return {
        color,
        // Ensure text is always fully opaque
        opacity: 1,
      };
    });

    return (
      <AnimatedTouchableOpacity
        style={[
          {
            flex: 1,
            alignItems: 'center',
            justifyContent: 'center',
            paddingVertical: 12,
          },
          animatedStyle,
        ]}
        accessibilityRole="button"
        accessibilityState={accessibilityState}
        accessibilityLabel={accessibilityLabel}
        testID={testID}
        onPress={handlePress}
        onLongPress={onLongPress}
        activeOpacity={0.7}>
        {icon && <Box marginBottom="xxs_4">{icon}</Box>}

        <Animated.Text
          style={[
            {
              fontSize: isTablet ? 16 : 14,
              fontFamily: theme.textVariants.b_14Medium_button.fontFamily,
              textAlign: 'center',
            },
            textAnimatedStyle,
          ]}>
          {label}
        </Animated.Text>

        {badge !== undefined && (
          <Box
            position="absolute"
            top={4}
            right={-8}
            backgroundColor="errorMain"
            borderRadius="circle_9999"
            paddingHorizontal="xxs_4"
            minWidth={16}
            height={16}
            alignItems="center"
            justifyContent="center">
            <Text variant="l_10SemiBold_chip" color="white" numberOfLines={1}>
              {typeof badge === 'number' && badge > 99 ? '99+' : badge}
            </Text>
          </Box>
        )}
      </AnimatedTouchableOpacity>
    );
  }
);

TabButton.displayName = 'TabButton';
