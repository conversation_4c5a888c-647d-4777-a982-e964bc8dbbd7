import { runOnJS } from 'react-native-reanimated';

import { haptics } from '@/src/core/libs/haptics';

/**
 * Worklet-compatible haptic feedback utilities
 */

// Create individual worklet functions that can be called directly
export const triggerSelectionWorklet = () => {
  'worklet';
  runOnJS(haptics.selection)();
};

export const triggerLightWorklet = () => {
  'worklet';
  runOnJS(haptics.light)();
};

export const triggerMediumWorklet = () => {
  'worklet';
  runOnJS(haptics.medium)();
};

export const triggerHeavyWorklet = () => {
  'worklet';
  runOnJS(haptics.heavy)();
};

export const triggerSuccessWorklet = () => {
  'worklet';
  runOnJS(haptics.success)();
};

export const triggerWarningWorklet = () => {
  'worklet';
  runOnJS(haptics.warning)();
};

export const triggerErrorWorklet = () => {
  'worklet';
  runOnJS(haptics.error)();
};

export const triggerRigidWorklet = () => {
  'worklet';
  runOnJS(haptics.rigid)();
};

export const triggerSoftWorklet = () => {
  'worklet';
  runOnJS(haptics.soft)();
};

// Direct JS thread version (for non-worklet contexts)
export const triggerHaptic = haptics;
