import React, { createContext, useCallback, useContext, useState } from 'react';

import { TAB_BAR_HEIGHT } from './constants';

interface TabBarHeightContextType {
  height: number;
  setHeight: (height: number) => void;
  defaultHeight: number;
}

const TabBarHeightContext = createContext<TabBarHeightContextType | undefined>(undefined);

export const TabBarHeightProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [height, setHeightState] = useState<number>(TAB_BAR_HEIGHT);

  const setHeight = useCallback(
    (newHeight: number) => {
      if (newHeight > 0 && newHeight !== height) {
        setHeightState(newHeight);
      }
    },
    [height]
  );

  const value = React.useMemo(
    () => ({
      height,
      setHeight,
      defaultHeight: TAB_BAR_HEIGHT,
    }),
    [height, setHeight]
  );

  return <TabBarHeightContext.Provider value={value}>{children}</TabBarHeightContext.Provider>;
};

/**
 * Hook to get the current tab bar height from context
 * Returns the actual measured height of the tab bar
 *
 * @returns An object containing the current height, setHeight function, and default height
 *
 * @example
 * const { height, setHeight, defaultHeight } = useTabBarHeight();
 * console.log(height); // Current measured height
 * setHeight(52); // Update height (if context is available)
 * console.log(defaultHeight); // Default height (48)
 */
export const useTabBarHeight = (): TabBarHeightContextType => {
  const context = useContext(TabBarHeightContext);
  if (context === undefined) {
    // Fallback with a no-op setHeight function if context is not available
    return {
      height: TAB_BAR_HEIGHT,
      defaultHeight: TAB_BAR_HEIGHT,
      setHeight: () => {
        console.warn(
          'TabBarHeightContext not found. Make sure to wrap your app with TabBarHeightProvider.'
        );
      },
    };
  }
  return context;
};
