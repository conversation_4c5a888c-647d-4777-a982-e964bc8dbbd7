import { Dimensions } from 'react-native';

import { theme } from '@/src/core/theme';

import { TabBarConfig } from './types';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export const DEFAULT_CONFIG: Required<TabBarConfig> = {
  indicatorWidth: 24,
  horizontalPadding: theme.spacing.xl_32,
  tabGap: theme.spacing.md_16,
  animationConfig: {
    damping: 15,
    mass: 1,
    stiffness: 150,
  },
  enableHaptics: true,
  enableGestures: false, // Temporarily disabled until proper implementation
  tabStyle: 'fixed',
  showBadges: false,
};

export const TAB_BAR_HEIGHT = 48;
export const INDICATOR_HEIGHT = 3;
export const INDICATOR_BOTTOM_OFFSET = 3;

export { SCREEN_WIDTH };
