import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackNavigationProp, NativeStackScreenProps } from '@react-navigation/native-stack';

export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  EventRegistration: undefined;
  Modal: { type: 'create-post' | 'settings' };
  NotFound: undefined;
  UserProfile: { userId: string; fromEvent?: boolean; eventId?: string };
  HostProfileModal: { userId: string; hostName: string };
};

export type RootStackScreenProps<T extends keyof RootStackParamList> = NativeStackScreenProps<
  RootStackParamList,
  T
>;

export type AuthStackParamList = {
  // Method Selection
  AuthChoice: undefined;
  Welcome: undefined;

  // Phone Authentication
  PhoneSignup: undefined;
  PhoneLogin: undefined;
  PhoneVerification: { phone: string };

  // Email Authentication
  EmailSignup: undefined;
  EmailLogin: undefined;
  EmailVerification: { email: string; isOTPLogin?: boolean };

  // OTP Authentication
  OTPSignup: undefined;

  // Passwordless Authentication
  PasswordlessRequest: undefined;
  PasswordlessVerify: { email: string };

  // Legacy routes (keep for compatibility)
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
  Verification: undefined;
  Onboarding: undefined;
};

export type AuthStackScreenProps<T extends keyof AuthStackParamList> = NativeStackScreenProps<
  AuthStackParamList,
  T
>;

export type ExploreTopTabParamList = {
  Discover: undefined;
  Local: undefined;
  Trending: undefined;
};

export type ExploreStackParamList = {
  ExploreTabs: undefined;
  EventDetails: { eventId: string; location?: any };
  Filters: undefined;
  LocationPicker: undefined;
  Search: undefined;
  Notifications: undefined;
};

export type MainTabParamList = {
  Explore: undefined;
  Events: undefined;
  Create: undefined;
  Tickets: undefined;
  Discovery: undefined;
  Settings: undefined;
  Mapbox: undefined;
};

export type SettingsStackParamList = {
  SettingsMain: undefined;
  Profile: { userId?: string; isOwnProfile?: boolean };
  EditProfile: undefined;
  Followers: { userId: string };
  Following: { userId: string };
  UserEvents: { userId: string; filter?: 'attending' | 'hosting' | 'attended' | 'hosted' };
  UserReviews: { userId: string };
  UserPhotos: { userId: string };
  PrivacySettings: undefined;
  NotificationSettings: undefined;
  DevicePermissions: undefined;
  LanguageSettings: undefined;
  BiometricSettings: undefined;
  AccessibilitySettings: undefined;
  AboutApp: undefined;
  DisplayName: undefined;
  GetUsername: undefined;
  AddProfilePhoto: undefined;
  EnableFaceId: undefined;
};

export type SettingsStackScreenProps<T extends keyof SettingsStackParamList> =
  NativeStackScreenProps<SettingsStackParamList, T>;

export type MainTabScreenProps<T extends keyof MainTabParamList> = CompositeScreenProps<
  BottomTabScreenProps<MainTabParamList, T>,
  RootStackScreenProps<keyof RootStackParamList>
>;

export type EventsStackParamList = {
  EventsList: undefined;
  EventDetails: { eventId: string; location: any };
  CreateEventDetails: { selectedLocation?: any };
  CreateEvent: undefined;
  SearchEvents: undefined;
  EventSummary: undefined;
  EventPreferences: undefined;
  EventSuccess: { eventId: string; eventName: string };
  EventLocationSearch: { onLocationSelect?: (location: any) => void };
  EventsDetailsScreen: { eventId: string };
};

export type EventsStackScreenProps<T extends keyof EventsStackParamList> = NativeStackScreenProps<
  EventsStackParamList,
  T
>;

export type DiscoveryStackParamList = {
  Search: undefined;
  Radar: undefined;
  Filters: undefined;
};

export type DiscoveryStackScreenProps<T extends keyof DiscoveryStackParamList> =
  NativeStackScreenProps<DiscoveryStackParamList, T>;

export type MapboxStackParamList = {
  MapboxTest: undefined;
  Mapbox3D: undefined;
  MapboxTerrain: undefined;
};

export type MapboxStackScreenProps<T extends keyof MapboxStackParamList> = NativeStackScreenProps<
  MapboxStackParamList,
  T
>;

export type NotFoundStackParamList = {
  NotFound: undefined;
};

export type EventRegistrationStackParamList = {
  CreateEventDetails:
    | {
        selectedLocation?: {
          id: string;
          name: string;
          address: string;
          latitude: number;
          longitude: number;
        };
      }
    | undefined;
  EventSummary: undefined;
  EventPreferences: undefined;
  EventSuccess: {
    eventId: string;
    eventName: string;
  };
  EventLocationSearch: undefined;
  ChooseOptions: undefined;
  CreationHub: undefined;
  TemplateSelection: {
    category: string;
    categoryLabel: string;
  };
  QuickDetails: undefined;
  Marketplace: undefined;
  VenueRegistration: undefined;
  GroupMeetup: undefined;
  FlashParty: undefined;
};

export type EventRegistrationStackScreenProps<T extends keyof EventRegistrationStackParamList> =
  NativeStackScreenProps<EventRegistrationStackParamList, T>;

// Navigation params
export type VenueRegistrationStackParamList = {
  VenueBasicDetails: undefined;
  VenueOpeningTimes: undefined;
  VenueFeatures: undefined;
  VenueSummary: undefined;
  VenueSuccess: { venueId: string };
};

export type VenueRegistrationNavigationProps =
  NativeStackNavigationProp<VenueRegistrationStackParamList>;

export type VenueRegistrationStackScreenProps<T extends keyof VenueRegistrationStackParamList> =
  NativeStackScreenProps<VenueRegistrationStackParamList, T>;

declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {
      // Global navigation interface extension
    }
  }
}
