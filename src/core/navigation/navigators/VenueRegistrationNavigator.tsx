import { createNativeStackNavigator } from '@react-navigation/native-stack';

import {
  EventSuccessScreen,
  VenueBasicDetailsScreen,
  VenueFeaturesScreen,
  VenueOpeningTimesScreen,
  VenueSummaryScreen,
} from '@/src/features/event-registration';

import { VenueRegistrationStackParamList } from '../types';

const Stack = createNativeStackNavigator<VenueRegistrationStackParamList>();

export const VenueRegistrationNavigator = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}>
      <Stack.Screen name="VenueBasicDetails" component={VenueBasicDetailsScreen} />
      <Stack.Screen name="VenueOpeningTimes" component={VenueOpeningTimesScreen} />
      <Stack.Screen name="VenueFeatures" component={VenueFeaturesScreen} />
      <Stack.Screen name="VenueSummary" component={VenueSummaryScreen} />
      <Stack.Screen name="VenueSuccess" component={EventSuccessScreen} />
    </Stack.Navigator>
  );
};
