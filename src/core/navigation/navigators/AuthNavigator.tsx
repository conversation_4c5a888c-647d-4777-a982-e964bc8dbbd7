import { createNativeStackNavigator } from '@react-navigation/native-stack';

import {
  AuthChoiceScreen,
  EmailLoginScreen,
  EmailSignupScreen,
  OTPSignupScreen,
  OnboardingScreen,
  PasswordlessRequestScreen,
  PasswordlessVerifyScreen,
  PhoneSignupScreen,
  VerificationScreen,
  WelcomeScreen,
} from '@/src/features/auth/screens';

import { AuthStackParamList } from '../types';

const Stack = createNativeStackNavigator<AuthStackParamList>();

export default function AuthLayout() {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      {/* Initial Screens */}
      <Stack.Screen name="Welcome" component={WelcomeScreen} />
      <Stack.Screen name="Onboarding" component={OnboardingScreen} />
      <Stack.Screen name="AuthChoice" component={AuthChoiceScreen} />

      {/* Phone Authentication */}
      <Stack.Screen name="PhoneSignup" component={PhoneSignupScreen} />
      <Stack.Screen name="PhoneVerification" component={VerificationScreen} />

      {/* Email Authentication */}
      <Stack.Screen name="EmailLogin" component={EmailLoginScreen} />
      <Stack.Screen name="EmailSignup" component={EmailSignupScreen} />
      <Stack.Screen name="EmailVerification" component={VerificationScreen} />

      {/* OTP Authentication */}
      <Stack.Screen name="OTPSignup" component={OTPSignupScreen} />

      {/* Passwordless Authentication */}
      <Stack.Screen name="PasswordlessRequest" component={PasswordlessRequestScreen} />
      <Stack.Screen name="PasswordlessVerify" component={PasswordlessVerifyScreen} />

      {/* Legacy routes for compatibility */}
      <Stack.Screen name="Login" component={EmailLoginScreen} />
      <Stack.Screen name="Register" component={PhoneSignupScreen} />
      <Stack.Screen name="Verification" component={VerificationScreen} />
    </Stack.Navigator>
  );
}
