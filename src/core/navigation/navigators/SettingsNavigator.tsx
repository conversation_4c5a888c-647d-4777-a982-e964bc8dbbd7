import React from 'react';

import { Platform } from 'react-native';

import { createNativeStackNavigator } from '@react-navigation/native-stack';

import EditProfileScreen from '@/src/features/profile/screens/EditProfileScreen';
import FollowersScreen from '@/src/features/profile/screens/FollowersScreen';
import FollowingScreen from '@/src/features/profile/screens/FollowingScreen';
// Profile screens
import ProfileScreen from '@/src/features/profile/screens/ProfileScreen';
import AboutApp from '@/src/features/settings/screens/AboutApp';
import AccessibilitySettings from '@/src/features/settings/screens/AccessibilitySettings';
import AddProfilePhotoScreen from '@/src/features/settings/screens/AddProfilePhoto';
import BiometricSettings from '@/src/features/settings/screens/BiometricSettings';
import DevicePermissions from '@/src/features/settings/screens/DevicePermissions';
import DisplayNameScreen from '@/src/features/settings/screens/DisplayName';
import EnableFaceIdScreen from '@/src/features/settings/screens/EnableFaceId';
import GetUsernameScreen from '@/src/features/settings/screens/GetUsername';
import LanguageSettings from '@/src/features/settings/screens/LanguageSettings';
import NotificationSettings from '@/src/features/settings/screens/NotificationSettings';
import SettingsScreen from '@/src/features/settings/screens/Settings';

import type { SettingsStackParamList } from '../types';

const Stack = createNativeStackNavigator<SettingsStackParamList>();

export default function SettingsNavigator() {
  const platform = React.useMemo(() => {
    return Platform.OS;
  }, []);

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        animation: platform === 'ios' ? 'ios_from_right' : 'fade_from_bottom',
      }}>
      <Stack.Screen name="SettingsMain" component={SettingsScreen} />

      {/* Profile Screens */}
      <Stack.Screen name="Profile" component={ProfileScreen} />
      <Stack.Screen
        name="EditProfile"
        component={EditProfileScreen}
        options={{
          presentation: 'modal',
          animation: 'slide_from_bottom',
        }}
      />
      <Stack.Screen name="Followers" component={FollowersScreen} />
      <Stack.Screen name="Following" component={FollowingScreen} />

      {/* Settings Screens */}
      <Stack.Screen name="NotificationSettings" component={NotificationSettings} />
      <Stack.Screen name="DevicePermissions" component={DevicePermissions} />
      <Stack.Screen name="LanguageSettings" component={LanguageSettings} />
      <Stack.Screen name="BiometricSettings" component={BiometricSettings} />
      <Stack.Screen name="AccessibilitySettings" component={AccessibilitySettings} />
      <Stack.Screen name="AboutApp" component={AboutApp} />
      <Stack.Screen name="DisplayName" component={DisplayNameScreen} />
      <Stack.Screen name="GetUsername" component={GetUsernameScreen} />
      <Stack.Screen name="AddProfilePhoto" component={AddProfilePhotoScreen} />
      <Stack.Screen name="EnableFaceId" component={EnableFaceIdScreen} />
    </Stack.Navigator>
  );
}
