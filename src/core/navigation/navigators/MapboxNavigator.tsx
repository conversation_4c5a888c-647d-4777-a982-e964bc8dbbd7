import React from 'react';

import { createNativeStackNavigator } from '@react-navigation/native-stack';

import { useTheme } from '@/src/core/theme';
import { Mapbox3DModelScreen } from '@/src/features/mapbox/screens/Mapbox3DModelScreen';
import { MapboxCustomTerrainScreen } from '@/src/features/mapbox/screens/MapboxCustomTerrainScreen';
import { MapboxTestScreen } from '@/src/features/mapbox/screens/MapboxTestScreen';

import { MapboxStackParamList } from '../types';

const Stack = createNativeStackNavigator<MapboxStackParamList>();

export const MapboxNavigator: React.FC = () => {
  const theme = useTheme();

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        contentStyle: {
          backgroundColor: theme.colors.background,
        },
      }}>
      <Stack.Screen
        name="MapboxTest"
        component={MapboxTestScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="Mapbox3D"
        component={Mapbox3DModelScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="MapboxTerrain"
        component={MapboxCustomTerrainScreen}
        options={{
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  );
};
