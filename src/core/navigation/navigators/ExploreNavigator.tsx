import React, { useMemo } from 'react';

import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { t } from 'i18next';
import { Bell, MagnifyingGlass, NavigationArrow } from 'phosphor-react-native';

import {
  ExploreStackParamList,
  ExploreTopTabParamList,
  MainTabParamList,
} from '@/src/core/navigation/types';
import { CustomTabBar } from '@/src/core/navigation/ui/TopTabNavigation';
import DiscoverScreen from '@/src/features/explore/screens/discover';
import LocalScreen from '@/src/features/explore/screens/local';
// import TrendingScreen from '@/src/features/explore/screens/trending';
import { useLocationStore } from '@/src/features/explore/stores/locationStore';
import { NotificationsScreen } from '@/src/features/notifications';
import { TrendingScreen } from '@/src/features/trending';
import { Avatar } from '@/src/shared/components';

import { Box, Pressable, Text, useResponsive, useTheme } from '../../theme';

const Stack = createNativeStackNavigator<ExploreStackParamList>();
const Tab = createMaterialTopTabNavigator<ExploreTopTabParamList>();

function ExploreTabs() {
  const tabBarConfig = useMemo(
    () => ({
      indicatorWidth: 32,
      horizontalPadding: 32,
      tabGap: 16,
      animationConfig: {
        damping: 20,
        mass: 1,
        stiffness: 180,
      },
      enableHaptics: true,
      enableGestures: true,
      tabStyle: 'fixed' as const,
      showBadges: false,
    }),
    []
  );

  return (
    <Tab.Navigator
      tabBar={props => <CustomTabBar {...props} config={tabBarConfig} />}
      screenOptions={{
        swipeEnabled: false,
        lazy: true,
        lazyPreloadDistance: 1,
      }}>
      <Tab.Screen
        name="Discover"
        component={DiscoverScreen}
        options={{
          tabBarLabel: t('explore.tabs.discover'),
        }}
      />
      <Tab.Screen
        name="Local"
        component={LocalScreen}
        options={{
          tabBarLabel: t('explore.tabs.local'),
          // Example of adding a badge
          // tabBarBadge: 3,
        }}
      />
      <Tab.Screen
        name="Trending"
        component={TrendingScreen}
        options={{
          tabBarLabel: t('explore.tabs.trending'),
        }}
      />
    </Tab.Navigator>
  );
}

export default function ExploreNavigator() {
  const { isTablet } = useResponsive();
  const { currentLocation } = useLocationStore();
  const theme = useTheme();

  return (
    <Stack.Navigator
      screenOptions={{
        headerShadowVisible: false,
      }}>
      <Stack.Screen
        name="ExploreTabs"
        component={ExploreTabs}
        options={({ navigation }) => ({
          headerBackground: () => <Box backgroundColor="card" flex={1} />,
          title: '',
          headerLeft: () => (
            <Pressable
              onPress={() => {
                const parentNavigation = navigation.getParent();
                if (parentNavigation) {
                  parentNavigation.navigate('Settings', {
                    screen: 'Profile',
                    params: { isOwnProfile: true },
                  });
                }
              }}
              flexDirection="row"
              alignItems="center"
              gap="sm_12">
              <Avatar size="s" weight="fill" />
            </Pressable>
          ),
          headerTitle: () => (
            <Pressable
              flexDirection="row"
              alignItems="center"
              gap="xxs_4"
              activeOpacity={0}
              onPress={() => navigation.navigate('LocationPicker')}>
              <NavigationArrow
                size={isTablet ? 20 : 16}
                color={theme.colors.primary}
                weight="fill"
              />
              <Text variant="b_14Medium_link" color="text" numberOfLines={1}>
                {currentLocation?.name || t('explore.selectLocation')}
              </Text>
            </Pressable>
          ),
          headerRight: () => (
            <Box flexDirection="row" alignItems="center" gap="md_16">
              <Pressable activeOpacity={0} onPress={() => navigation.navigate('Discovery')}>
                <MagnifyingGlass size={isTablet ? 24 : 20} color={theme.colors.mainText} />
              </Pressable>

              <Pressable activeOpacity={0} onPress={() => navigation.navigate('Notifications')}>
                <Bell size={isTablet ? 24 : 20} color={theme.colors.mainText} />
              </Pressable>
            </Box>
          ),
        })}
      />
      <Stack.Screen
        name="Notifications"
        component={NotificationsScreen}
        options={{
          title: 'Notifications',
          headerShadowVisible: false,
          headerStyle: {
            backgroundColor: theme.colors.mainBackground,
          },
        }}
      />

      {/* Add other screens as we implement them */}
    </Stack.Navigator>
  );
}
