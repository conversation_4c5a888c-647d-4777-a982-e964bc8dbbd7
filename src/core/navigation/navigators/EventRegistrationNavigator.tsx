import React from 'react';

import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useTheme } from '@shopify/restyle';

import { Theme } from '@/src/core/theme';
import {
  ChooseOptionsScreen,
  CreateEventDetailsScreen,
  EventPreferencesScreen,
  EventSuccessScreen,
  EventSummaryScreen,
} from '@/src/features/event-registration';
import { EventRegistrationProvider } from '@/src/features/event-registration/context/EventRegistrationContext';
import CreationHubScreen from '@/src/features/event-registration/screens/CreationHubScreen';
import FlashPartyScreen from '@/src/features/event-registration/screens/FlashPartyScreen';
import GroupMeetupScreen from '@/src/features/event-registration/screens/GroupMeetupScreen';
import MarketplaceScreen from '@/src/features/event-registration/screens/MarketplaceScreen';
import QuickDetailsScreen from '@/src/features/event-registration/screens/QuickDetailsScreen';
import TemplateSelectionScreen from '@/src/features/event-registration/screens/TemplateSelectionScreen';
import EventLocationSearchScreen from '@/src/features/event-registration/screens/event-location-search';

import { EventRegistrationStackParamList } from '../types';
import { VenueRegistrationNavigator } from './VenueRegistrationNavigator';

const Stack = createNativeStackNavigator<EventRegistrationStackParamList>();

export const EventRegistrationNavigator = () => {
  const theme = useTheme<Theme>();

  return (
    <EventRegistrationProvider>
      <Stack.Navigator
        initialRouteName="CreationHub"
        screenOptions={{
          headerStyle: {
            backgroundColor: theme.colors.background,
          },
          headerBlurEffect: 'systemChromeMaterial',
          headerTintColor: theme.colors.text,
          headerTitleStyle: {
            fontFamily: 'Urbanist-Bold',
            fontSize: 24,
          },
          headerTransparent: true,
        }}>
        <Stack.Screen
          name="CreateEventDetails"
          component={CreateEventDetailsScreen}
          options={{
            title: 'Create party',
          }}
        />
        <Stack.Screen
          name="EventSummary"
          component={EventSummaryScreen}
          options={{
            title: 'Summary overview',
          }}
        />
        <Stack.Screen
          name="EventPreferences"
          component={EventPreferencesScreen}
          options={{
            title: 'Preferences',
          }}
        />
        <Stack.Screen
          name="EventSuccess"
          component={EventSuccessScreen}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="EventLocationSearch"
          component={EventLocationSearchScreen}
          options={{
            headerShown: false,
            presentation: 'formSheet',
            sheetAllowedDetents: [0.5, 1.0],
            sheetGrabberVisible: true,
            sheetCornerRadius: 20,
          }}
        />

        <Stack.Screen
          name="ChooseOptions"
          component={ChooseOptionsScreen}
          options={{
            headerShown: false,
          }}
        />

        <Stack.Screen
          name="CreationHub"
          component={CreationHubScreen}
          options={{
            headerShown: false,
          }}
        />

        <Stack.Screen
          name="TemplateSelection"
          component={TemplateSelectionScreen}
          options={{
            headerShown: false,
          }}
        />

        <Stack.Screen
          name="QuickDetails"
          component={QuickDetailsScreen}
          options={{
            headerShown: false,
            presentation: 'modal',
          }}
        />

        <Stack.Screen
          name="Marketplace"
          component={MarketplaceScreen}
          options={{
            title: 'Ingressos',
            presentation: 'modal',
          }}
        />

        <Stack.Screen
          name="VenueRegistration"
          component={VenueRegistrationNavigator}
          options={{
            headerShown: false,
          }}
        />

        <Stack.Screen
          name="GroupMeetup"
          component={GroupMeetupScreen}
          options={{
            headerShown: false,
          }}
        />

        <Stack.Screen
          name="FlashParty"
          component={FlashPartyScreen}
          options={{
            headerShown: false,
          }}
        />
      </Stack.Navigator>
    </EventRegistrationProvider>
  );
};
