import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useTheme } from '@shopify/restyle';

import { Theme } from '@/src/core/theme';
import { CreateEventDetailsScreen } from '@/src/features/event-registration/screens/create-event-details.screen';
import EventLocationSearchScreen from '@/src/features/event-registration/screens/event-location-search';
import { EventsListScreen } from '@/src/features/events';
import CreateEventScreen from '@/src/features/events/screens/CreateEventScreen';
import EventDetails from '@/src/features/events/screens/EventDetails';
import EventDetailsScreen from '@/src/features/events/screens/EventDetailsScreen';

import { EventsStackParamList } from '../types';

const Stack = createNativeStackNavigator<EventsStackParamList>();

export const EventsNavigator = () => {
  const theme = useTheme<Theme>();

  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: theme.colors.background,
        },
        headerTintColor: theme.colors.text,
        headerTitleStyle: {
          fontFamily: 'Urbanist-Bold',
          fontSize: 24,
        },
      }}>
      <Stack.Screen
        name="EventsList"
        component={EventsListScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="CreateEvent"
        component={CreateEventScreen}
        options={{
          title: 'Create Event',
        }}
      />
      <Stack.Screen
        name="CreateEventDetails"
        component={CreateEventDetailsScreen}
        options={() => ({
          title: 'Create Event Details',
        })}
      />
      <Stack.Screen
        name="EventDetails"
        component={EventDetails}
        options={() => ({
          title: ' Event Details',
        })}
      />
      <Stack.Screen
        name="SearchEvents"
        component={CreateEventScreen}
        options={{
          title: 'Search Events',
        }}
      />
      <Stack.Screen
        name="EventLocationSearch"
        component={EventLocationSearchScreen}
        options={{
          headerShown: false,
          sheetAllowedDetents: [0.5, 1.0],
          presentation: 'formSheet',
          sheetGrabberVisible: true,
          sheetCornerRadius: 20,
        }}
      />
      <Stack.Screen
        name="EventsDetailsScreen"
        component={EventDetailsScreen}
        options={{
          title: 'Event Details',
        }}
      />
    </Stack.Navigator>
  );
};

export default EventsNavigator;
