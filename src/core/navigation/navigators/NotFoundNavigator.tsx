import { useNavigation } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

import { NotFoundStackParamList, NotFoundStackScreenProps } from '@/src/core/navigation/types';
import { Box, Text } from '@/src/core/theme';

const Stack = createNativeStackNavigator<NotFoundStackParamList>();

export default function NotFoundScreen() {
  const navigation = useNavigation<NotFoundStackScreenProps<'NotFound'>['navigation']>();

  return (
    <Stack.Navigator>
      <Stack.Screen name="NotFound" options={{ title: 'Oops!' }} component={NotFoundScreen} />
      <Box flex={1} justifyContent="center" alignItems="center" padding="ml_20">
        <Text variant="b_16Regular_input">This screen doesn&apos;t exist.</Text>
      </Box>
    </Stack.Navigator>
  );
}
