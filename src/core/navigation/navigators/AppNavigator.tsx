import { ActivityIndicator } from 'react-native';

import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

import ModalScreen from '@/src/app/modal';
import { useAuthSync } from '@/src/core/hooks/useAuthSync';
import { useTokenRefresh } from '@/src/core/hooks/useTokenRefresh';
import { Box, useThemeContext } from '@/src/core/theme';
import { useIsAuthenticated, useIsInitializing } from '@/src/features/auth/store/authStore';
import HostProfileModal from '@/src/features/profile/screens/HostProfileModal';
import UserProfileModal from '@/src/features/profile/screens/UserProfileModal';

import { navigationDarkTheme, navigationLightTheme } from '../../theme/theme';
import { RootStackParamList } from '../types';
import AuthNavigator from './AuthNavigator';
import { navigationRef } from './RootNavigation';
import TabNavigator from './TabNavigator';

const Stack = createNativeStackNavigator<RootStackParamList>();

export default function AppNavigator() {
  const isAuthenticated = useIsAuthenticated();
  const isInitializing = useIsInitializing();

  const navigationTheme = useThemeContext();
  const theme = navigationTheme?.themeName === 'light' ? navigationLightTheme : navigationDarkTheme;

  // Show loading screen only during initial app startup auth check
  if (isInitializing) {
    return (
      <Box flex={1} backgroundColor="background" justifyContent="center" alignItems="center">
        <ActivityIndicator size="large" color={theme.colors.primary} />
      </Box>
    );
  }

  return (
    <NavigationContainer
      ref={navigationRef}
      theme={{
        colors: theme.colors,
        dark: navigationTheme?.themeName === 'dark',
        fonts: theme.fonts,
      }}>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        {isAuthenticated ? (
          <>
            <Stack.Screen name="Main" component={TabNavigator} />
            <Stack.Group screenOptions={{ presentation: 'modal' }}>
              <Stack.Screen name="Modal" component={ModalScreen} />
              <Stack.Screen
                name="UserProfile"
                component={UserProfileModal}
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="HostProfileModal"
                component={HostProfileModal}
                options={{
                  presentation: 'containedModal',
                  headerShown: false,
                }}
              />
            </Stack.Group>
          </>
        ) : (
          <Stack.Screen name="Auth" component={AuthNavigator} options={{}} />
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
}
