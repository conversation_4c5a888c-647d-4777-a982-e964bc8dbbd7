import React from 'react';

import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useTheme } from '@shopify/restyle';

import { Theme } from '@/src/core/theme';
import { FiltersScreen, RadarScreen, SearchScreen } from '@/src/features/discovery';

import { DiscoveryStackParamList } from '../types';

const Stack = createNativeStackNavigator<DiscoveryStackParamList>();

export const DiscoveryNavigator = () => {
  const theme = useTheme<Theme>();

  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: theme.colors.background,
        },
        headerTintColor: theme.colors.text,
        headerTitleStyle: {
          fontFamily: 'Urbanist-Bold',
          fontSize: 24,
        },
      }}>
      <Stack.Screen
        name="Search"
        component={SearchScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="Radar"
        component={RadarScreen}
        options={{
          title: 'Radar',
        }}
      />
      <Stack.Screen
        name="Filters"
        component={FiltersScreen}
        options={{
          title: 'Filters',
        }}
      />
    </Stack.Navigator>
  );
};

export default DiscoveryNavigator;
