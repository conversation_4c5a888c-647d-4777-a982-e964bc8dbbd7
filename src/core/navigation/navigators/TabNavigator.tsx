import { use } from 'react';

import { Platform } from 'react-native';

import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';

import TicketsScreen from '@/src/app/screens/main/tabs/tickets';
import DiscoveryNavigator from '@/src/core/navigation/navigators/DiscoveryNavigator';
import EventsNavigator from '@/src/core/navigation/navigators/EventsNavigator';
import ExploreNavigator from '@/src/core/navigation/navigators/ExploreNavigator';
import SettingsNavigator from '@/src/core/navigation/navigators/SettingsNavigator';
import HapticTabButton from '@/src/core/navigation/ui/HapticTabButton';
import BlurTabBarBackground from '@/src/core/navigation/ui/TabBarBackground';
import {
  CirclesThreePlusIcon,
  ConfettiIcon,
  FireIcon,
  PlusIcon,
  SearchIcon,
  TicketIcon,
} from '@/src/shared/components/Icons/TabIcons';

import { useTheme } from '../../theme';
import { ThemeContext } from '../../theme/ThemeProvider';
import { navigationDarkTheme, navigationLightTheme } from '../../theme/theme';
import { MainTabParamList } from '../types';
import { EventRegistrationNavigator } from './EventRegistrationNavigator';

const Tab = createBottomTabNavigator<MainTabParamList>();

export default function TabNavigator() {
  const theme = useTheme();
  const themeContext = use(ThemeContext);

  const navigationTheme =
    themeContext?.themeName === 'light' ? navigationLightTheme : navigationDarkTheme;

  // Use navigationTheme for consistency to avoid race conditions
  const tabBarBackgroundColor = navigationTheme.colors.card;

  return (
    <Tab.Navigator
      key={themeContext?.themeName} // Force re-render when theme changes
      screenOptions={{
        tabBarShowLabel: false,
        lazy: false,
        tabBarActiveBackgroundColor: Platform.select({
          web: tabBarBackgroundColor,
        }),
        tabBarActiveTintColor: navigationTheme.colors.primary,
        tabBarInactiveTintColor: navigationTheme.colors.text,
        animation: 'shift',
        headerShown: false,
        tabBarButton: HapticTabButton,
        tabBarBackground: BlurTabBarBackground,
        tabBarPosition: Platform.select({
          web: 'left',
        }),
        tabBarStyle: Platform.select({
          ios: {
            paddingTop: 12,
            position: 'absolute',
            borderTopColor: 'transparent',
          },
          default: {
            backgroundColor: tabBarBackgroundColor,
            paddingTop: 12,
            borderColor: 'transparent',
          },
        }),
        tabBarVariant: Platform.select({
          web: 'material',
        }),
      }}>
      {/* <Tab.Screen
        name="Explore"
        component={ExploreNavigator}
        options={{
          tabBarIcon: ({ focused }) => <FireIcon focused={focused} size={28} />,
        }}
      />
      <Tab.Screen
        name="Events"
        component={EventsNavigator}
        options={{
          tabBarIcon: ({ focused }) => <ConfettiIcon focused={focused} size={28} />,
        }}
      /> */}
      <Tab.Screen
        name="Create"
        component={EventRegistrationNavigator}
        options={{
          tabBarIcon: ({ focused }) => <PlusIcon focused={focused} size={28} />,
        }}
      />
      <Tab.Screen
        name="Tickets"
        component={TicketsScreen}
        options={{
          tabBarIcon: ({ focused }) => <TicketIcon focused={focused} size={28} />,
        }}
      />
      {/* <Tab.Screen
        name="Discovery"
        component={DiscoveryNavigator}
        options={{
          tabBarIcon: ({ focused }) => <SearchIcon focused={focused} size={28} />,
        }}
      />
      <Tab.Screen
        name="Settings"
        component={SettingsNavigator}
        options={{
          tabBarIcon: ({ focused }) => <CirclesThreePlusIcon focused={focused} size={28} />,
        }}
      /> */}
    </Tab.Navigator>
  );
}
