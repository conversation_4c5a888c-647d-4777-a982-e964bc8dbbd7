/**
 * Encryption utilities for secure data handling
 * Essential security measures for global MVP
 */
import Crypto<PERSON><PERSON> from 'crypto-js';

import { secureStorage } from '../storage';

/**
 * Encryption configuration
 */
const ENCRYPTION_CONFIG = {
  algorithm: 'AES',
  keySize: 256 / 32, // 256 bits
  ivSize: 128 / 32, // 128 bits
  iterations: 1000,
};

/**
 * Generate a secure random key
 */
export function generateSecureKey(): string {
  return CryptoJS.lib.WordArray.random(256 / 8).toString();
}

/**
 * Generate a secure random IV (Initialization Vector)
 */
export function generateIV(): string {
  return CryptoJS.lib.WordArray.random(128 / 8).toString();
}

/**
 * Encrypt sensitive data
 */
export function encrypt(plaintext: string, key?: string): { encrypted: string; iv: string } {
  try {
    // Use provided key or generate a new one
    const encryptionKey = key || generateSecureKey();
    const iv = generateIV();

    // Convert strings to WordArrays
    const keyWordArray = CryptoJS.enc.Utf8.parse(encryptionKey);
    const ivWordArray = CryptoJS.enc.Utf8.parse(iv);

    // Encrypt the data
    const encrypted = CryptoJS.AES.encrypt(plaintext, keyWordArray, {
      iv: ivWordArray,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    });

    return {
      encrypted: encrypted.toString(),
      iv: iv,
    };
  } catch (error) {
    console.error('Encryption error:', error);
    throw new Error('Failed to encrypt data');
  }
}

/**
 * Decrypt sensitive data
 */
export function decrypt(encryptedData: string, key: string, iv: string): string {
  try {
    // Convert strings to WordArrays
    const keyWordArray = CryptoJS.enc.Utf8.parse(key);
    const ivWordArray = CryptoJS.enc.Utf8.parse(iv);

    // Decrypt the data
    const decrypted = CryptoJS.AES.decrypt(encryptedData, keyWordArray, {
      iv: ivWordArray,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    });

    return decrypted.toString(CryptoJS.enc.Utf8);
  } catch (error) {
    console.error('Decryption error:', error);
    throw new Error('Failed to decrypt data');
  }
}

/**
 * Hash sensitive data (one-way)
 */
export function hashData(data: string, salt?: string): string {
  try {
    const saltToUse = salt || generateSecureKey();
    return CryptoJS.PBKDF2(data, saltToUse, {
      keySize: ENCRYPTION_CONFIG.keySize,
      iterations: ENCRYPTION_CONFIG.iterations,
    }).toString();
  } catch (error) {
    console.error('Hashing error:', error);
    throw new Error('Failed to hash data');
  }
}

/**
 * Verify hashed data
 */
export function verifyHash(data: string, hash: string, salt: string): boolean {
  try {
    const hashedInput = hashData(data, salt);
    return hashedInput === hash;
  } catch (error) {
    console.error('Hash verification error:', error);
    return false;
  }
}

/**
 * Securely store encrypted data
 */
export async function storeEncryptedData(
  key: string,
  data: string,
  encryptionKey?: string
): Promise<void> {
  try {
    const { encrypted, iv } = encrypt(data, encryptionKey);

    // Store encrypted data and IV separately
    secureStorage.setObject(`encrypted_${key}`, {
      data: encrypted,
      iv: iv,
      timestamp: Date.now(),
    });
  } catch (error) {
    console.error('Secure storage error:', error);
    throw new Error('Failed to store encrypted data');
  }
}

/**
 * Retrieve and decrypt stored data
 */
export async function retrieveEncryptedData(
  key: string,
  decryptionKey: string
): Promise<string | null> {
  try {
    const storedData = secureStorage.getObject<{
      data: string;
      iv: string;
      timestamp: number;
    }>(`encrypted_${key}`);

    if (!storedData) {
      return null;
    }

    return decrypt(storedData.data, decryptionKey, storedData.iv);
  } catch (error) {
    console.error('Secure retrieval error:', error);
    return null;
  }
}

/**
 * Generate a secure random password
 */
export function generateSecurePassword(
  length: number = 16,
  options: {
    includeUppercase?: boolean;
    includeLowercase?: boolean;
    includeNumbers?: boolean;
    includeSymbols?: boolean;
  } = {}
): string {
  const {
    includeUppercase = true,
    includeLowercase = true,
    includeNumbers = true,
    includeSymbols = false,
  } = options;

  let charset = '';
  if (includeUppercase) charset += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  if (includeLowercase) charset += 'abcdefghijklmnopqrstuvwxyz';
  if (includeNumbers) charset += '0123456789';
  if (includeSymbols) charset += '!@#$%^&*()_+-=[]{}|;:,.<>?';

  if (charset === '') {
    throw new Error('At least one character type must be included');
  }

  let password = '';
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * charset.length);
    password += charset[randomIndex];
  }

  return password;
}

/**
 * Sanitize user input to prevent XSS
 */
export function sanitizeInput(input: string): string {
  return input
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate password strength
 */
export function validatePasswordStrength(password: string): {
  isValid: boolean;
  score: number;
  feedback: string[];
} {
  const feedback: string[] = [];
  let score = 0;

  // Check length
  if (password.length >= 8) {
    score += 1;
  } else {
    feedback.push('Password must be at least 8 characters long');
  }

  // Check for uppercase
  if (/[A-Z]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Password must contain at least one uppercase letter');
  }

  // Check for lowercase
  if (/[a-z]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Password must contain at least one lowercase letter');
  }

  // Check for numbers
  if (/\d/.test(password)) {
    score += 1;
  } else {
    feedback.push('Password must contain at least one number');
  }

  // Check for special characters
  if (/[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Password must contain at least one special character');
  }

  return {
    isValid: score >= 4,
    score,
    feedback,
  };
}
