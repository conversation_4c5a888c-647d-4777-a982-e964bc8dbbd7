/**
 * Token management for secure authentication
 * Handles JWT tokens, refresh logic, and secure storage
 */
import { secureStorage } from '../storage';
import { decrypt, encrypt, generateSecureKey } from './encryption';

/**
 * Token storage keys
 */
const STORAGE_KEYS = {
  ACCESS_TOKEN: 'auth_access_token',
  REFRESH_TOKEN: 'auth_refresh_token',
  TOKEN_EXPIRY: 'auth_token_expiry',
  ENCRYPTION_KEY: 'auth_encryption_key',
} as const;

/**
 * Token types
 */
export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresAt: number;
}

export interface TokenValidationResult {
  isValid: boolean;
  isExpired: boolean;
  needsRefresh: boolean;
  timeUntilExpiry?: number;
}

/**
 * Token manager class for secure token handling
 */
class TokenManager {
  private encryptionKey: string | null = null;

  /**
   * Initialize token manager with encryption key
   */
  private async initializeEncryptionKey(): Promise<string> {
    if (this.encryptionKey) {
      return this.encryptionKey;
    }

    // Try to get existing key
    let key = secureStorage.getString(STORAGE_KEYS.ENCRYPTION_KEY);

    if (!key) {
      // Generate new key if none exists
      key = generateSecureKey();
      secureStorage.set(STORAGE_KEYS.ENCRYPTION_KEY, key);
    }

    this.encryptionKey = key;
    return key;
  }

  /**
   * Store tokens securely
   */
  async storeTokens(tokens: AuthTokens): Promise<void> {
    try {
      const encryptionKey = await this.initializeEncryptionKey();

      // Encrypt tokens before storing
      const encryptedAccessToken = encrypt(tokens.accessToken, encryptionKey);
      const encryptedRefreshToken = encrypt(tokens.refreshToken, encryptionKey);

      // Store encrypted tokens
      secureStorage.setObject(STORAGE_KEYS.ACCESS_TOKEN, encryptedAccessToken);
      secureStorage.setObject(STORAGE_KEYS.REFRESH_TOKEN, encryptedRefreshToken);
      secureStorage.set(STORAGE_KEYS.TOKEN_EXPIRY, tokens.expiresAt.toString());

      console.log('Tokens stored securely');
    } catch (error) {
      console.error('Failed to store tokens:', error);
      throw new Error('Token storage failed');
    }
  }

  /**
   * Retrieve and decrypt stored tokens
   */
  async getTokens(): Promise<AuthTokens | null> {
    try {
      const encryptionKey = await this.initializeEncryptionKey();

      // Get encrypted tokens
      const encryptedAccessToken = secureStorage.getObject<{
        encrypted: string;
        iv: string;
      }>(STORAGE_KEYS.ACCESS_TOKEN);

      const encryptedRefreshToken = secureStorage.getObject<{
        encrypted: string;
        iv: string;
      }>(STORAGE_KEYS.REFRESH_TOKEN);

      const expiryString = secureStorage.getString(STORAGE_KEYS.TOKEN_EXPIRY);

      if (!encryptedAccessToken || !encryptedRefreshToken || !expiryString) {
        return null;
      }

      // Decrypt tokens
      const accessToken = decrypt(
        encryptedAccessToken.encrypted,
        encryptionKey,
        encryptedAccessToken.iv
      );

      const refreshToken = decrypt(
        encryptedRefreshToken.encrypted,
        encryptionKey,
        encryptedRefreshToken.iv
      );

      return {
        accessToken,
        refreshToken,
        expiresAt: parseInt(expiryString, 10),
      };
    } catch (error) {
      console.error('Failed to retrieve tokens:', error);
      return null;
    }
  }

  /**
   * Get only the access token
   */
  async getAccessToken(): Promise<string | null> {
    const tokens = await this.getTokens();
    return tokens?.accessToken || null;
  }

  /**
   * Get only the refresh token
   */
  async getRefreshToken(): Promise<string | null> {
    const tokens = await this.getTokens();
    return tokens?.refreshToken || null;
  }

  /**
   * Validate current tokens
   */
  async validateTokens(): Promise<TokenValidationResult> {
    const tokens = await this.getTokens();

    if (!tokens) {
      return {
        isValid: false,
        isExpired: true,
        needsRefresh: false,
      };
    }

    const now = Date.now();
    const timeUntilExpiry = tokens.expiresAt - now;
    const isExpired = timeUntilExpiry <= 0;
    const needsRefresh = timeUntilExpiry <= 5 * 60 * 1000; // Refresh if expires within 5 minutes

    return {
      isValid: !isExpired,
      isExpired,
      needsRefresh,
      timeUntilExpiry: Math.max(0, timeUntilExpiry),
    };
  }

  /**
   * Check if tokens exist
   */
  async hasTokens(): Promise<boolean> {
    const tokens = await this.getTokens();
    return tokens !== null;
  }

  /**
   * Clear all stored tokens
   */
  async clearTokens(): Promise<void> {
    try {
      secureStorage.delete(STORAGE_KEYS.ACCESS_TOKEN);
      secureStorage.delete(STORAGE_KEYS.REFRESH_TOKEN);
      secureStorage.delete(STORAGE_KEYS.TOKEN_EXPIRY);

      console.log('Tokens cleared');
    } catch (error) {
      console.error('Failed to clear tokens:', error);
    }
  }

  /**
   * Update access token (during refresh)
   */
  async updateAccessToken(newAccessToken: string, newExpiresAt: number): Promise<void> {
    const tokens = await this.getTokens();

    if (!tokens) {
      throw new Error('No existing tokens to update');
    }

    await this.storeTokens({
      accessToken: newAccessToken,
      refreshToken: tokens.refreshToken,
      expiresAt: newExpiresAt,
    });
  }

  /**
   * Decode JWT payload (without verification)
   * Note: This is for reading token data, not for security validation
   */
  decodeTokenPayload(token: string): Record<string, any> | null {
    try {
      const parts = token.split('.');
      if (parts.length !== 3) {
        return null;
      }

      const payload = parts[1];
      const decoded = atob(payload.replace(/-/g, '+').replace(/_/g, '/'));
      return JSON.parse(decoded);
    } catch (error) {
      console.error('Failed to decode token payload:', error);
      return null;
    }
  }

  /**
   * Get user ID from access token
   */
  async getUserId(): Promise<string | null> {
    const accessToken = await this.getAccessToken();

    if (!accessToken) {
      return null;
    }

    const payload = this.decodeTokenPayload(accessToken);
    return payload?.sub || payload?.userId || null;
  }

  /**
   * Get token expiry from payload
   */
  async getTokenExpiry(): Promise<number | null> {
    const accessToken = await this.getAccessToken();

    if (!accessToken) {
      return null;
    }

    const payload = this.decodeTokenPayload(accessToken);
    return payload?.exp ? payload.exp * 1000 : null; // Convert to milliseconds
  }

  /**
   * Check if user has specific permission/role
   */
  async hasPermission(permission: string): Promise<boolean> {
    const accessToken = await this.getAccessToken();

    if (!accessToken) {
      return false;
    }

    const payload = this.decodeTokenPayload(accessToken);
    const permissions = payload?.permissions || [];
    const roles = payload?.roles || [];

    return permissions.includes(permission) || roles.includes(permission);
  }

  /**
   * Get all user permissions from token
   */
  async getUserPermissions(): Promise<string[]> {
    const accessToken = await this.getAccessToken();

    if (!accessToken) {
      return [];
    }

    const payload = this.decodeTokenPayload(accessToken);
    return [...(payload?.permissions || []), ...(payload?.roles || [])];
  }
}

// Export singleton instance
export const tokenManager = new TokenManager();

// Export types
export type { TokenValidationResult, AuthTokens };
