import React, { createContext, useContext } from 'react';

import { ApolloClient, ApolloProvider } from '@apollo/client';

import { client } from './config';

// Create a context for the GraphQL client
const GraphQLContext = createContext<ApolloClient<any> | null>(null);

interface GraphQLProviderProps {
  children: React.ReactNode;
}

/**
 * GraphQL Provider using the pre-configured Apollo Client
 * This provider wraps the app with Apollo's GraphQL capabilities
 * and provides the client instance through context
 */
export const GraphQLProvider: React.FC<GraphQLProviderProps> = ({ children }) => {
  return (
    <GraphQLContext.Provider value={client}>
      <ApolloProvider client={client}>{children}</ApolloProvider>
    </GraphQLContext.Provider>
  );
};

/**
 * Hook to access the Apollo Client instance
 * @returns ApolloClient instance
 * @throws Error if used outside GraphQLProvider
 */
export const useGraphQL = () => {
  const context = useContext(GraphQLContext);
  if (!context) {
    throw new Error('useGraphQL must be used within a GraphQLProvider');
  }
  return context;
};

/**
 * Re-export the configured Apollo client for direct use
 */
export { client as apolloClient } from './config';
