/**
 * Core API exports for global MVP
 * Centralized API client and caching utilities
 */

// API caching
export { apiCache, CACHE_CATEGORIES } from './cache';
export type { CacheEntry, CacheMetadata, CacheCategory } from './cache';

// GraphQL/Apollo Client
export { client, updateClientHeaders } from './config';
export { GraphQLProvider, useGraphQL, apolloClient } from './context';
export type { ApolloClient } from '@apollo/client';
