import { gql } from '@apollo/client';

import { AuthTokens } from '@/src/features/auth/types';

import { secureStorage } from '../storage';

const REFRESH_TOKEN_MUTATION = gql`
  mutation RefreshToken($refreshToken: String!) {
    refreshToken(refreshToken: $refreshToken) {
      token
      refreshToken
      expiresAt
    }
  }
`;

/**
 * Refresh authentication tokens without creating circular dependencies
 */
export async function refreshAuthTokens(): Promise<AuthTokens> {
  try {
    const tokens = secureStorage.getObject<AuthTokens>('auth.tokens');
    if (!tokens?.refreshToken) {
      throw new Error('No refresh token available');
    }

    // We'll need to make a direct HTTP request here to avoid circular dependency
    const response = await fetch(process.env.EXPO_PUBLIC_GRAPHQL_URL || '', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: REFRESH_TOKEN_MUTATION.loc?.source.body,
        variables: { refreshToken: tokens.refreshToken },
      }),
    });

    const result = await response.json();

    if (result.errors) {
      throw new Error(result.errors[0]?.message || 'Token refresh failed');
    }

    const newTokens = result.data.refreshToken;
    secureStorage.setObject('auth.tokens', newTokens);

    return newTokens;
  } catch (error) {
    secureStorage.delete('auth.tokens');
    secureStorage.delete('auth.user');

    // Import and update auth store to trigger logout
    const { useAuthStore } = await import('@/src/features/auth/store/authStore');
    const authStore = useAuthStore.getState();
    authStore.clearAuth();

    throw error;
  }
}
