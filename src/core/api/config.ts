import { Platform } from 'react-native';

import { ApolloClient, InMemoryCache, createHttpLink, from } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';
import { onError } from '@apollo/client/link/error';

// Import auth store to properly handle auth state
import { useAuthStore } from '@/src/features/auth/store/authStore';

import { secureStorage } from '../storage';
import { tokenRefreshLink } from './tokenRefreshLink';

let defaultUri = process.env.EXPO_PUBLIC_GRAPHQL_URL;
if (defaultUri === undefined || defaultUri === '') {
  if (__DEV__ && Platform.OS === 'android') {
    defaultUri = 'http://************:8080/query';
  } else if (__DEV__ && Platform.OS === 'ios') {
    defaultUri = 'http://localhost:8080/query';
  }
}

// GraphQL endpoint - update this to your actual endpoint
const GRAPHQL_URI = defaultUri;

// Create the http link
const httpLink = createHttpLink({
  uri: GRAPHQL_URI,
  credentials: 'include', // Include cookies if needed
});

// Auth link to add Bearer token to requests
const authLink = setContext(async (operation, { headers }) => {
  try {
    // Get tokens from secure storage
    const tokens = secureStorage.getObject<{
      accessToken: string;
      refreshToken: string;
      expiresIn: number;
    }>('auth.tokens');

    // The API returns 'token' but we store it as 'accessToken'
    // Check both formats for compatibility
    const token = tokens?.accessToken || (tokens as any)?.token;

    // Debug log with operation details
    console.log(
      `🔐 [${operation.operationName}] Auth token status:`,
      token ? 'Token found' : 'No token',
      {
        hasToken: !!token,
        tokenLength: token?.length,
        tokenPrefix: token ? token.substring(0, 20) + '...' : 'none',
      }
    );

    return {
      headers: {
        ...headers,
        authorization: token ? `Bearer ${token}` : '',
        'Content-Type': 'application/json',
      },
    };
  } catch (error) {
    console.error('Error retrieving auth token:', error);
    return {
      headers: {
        ...headers,
        'Content-Type': 'application/json',
      },
    };
  }
});

// Error handling link
const errorLink = onError(({ graphQLErrors, networkError, operation }) => {
  if (graphQLErrors) {
    let shouldClearAuth = false;

    graphQLErrors.forEach(({ message, locations, path, extensions }) => {
      console.error(
        `GraphQL error: Message: ${message}, Location: ${locations}, Path: ${path}`,
        extensions
      );

      // Handle authentication errors
      if (
        extensions?.code === 'UNAUTHENTICATED' ||
        extensions?.code === 'FORBIDDEN' ||
        extensions?.code === 'NOT_AUTHENTICATED'
      ) {
        // Check if we have a token in storage
        const tokens = secureStorage.getObject<{
          accessToken: string;
          refreshToken: string;
          expiresIn: number;
        }>('auth.tokens');

        const hasToken = !!(tokens?.accessToken || (tokens as any)?.token);

        console.warn('⚠️ Authentication error detected', {
          code: extensions?.code,
          operation: operation.operationName,
          message,
          hasTokenInStorage: hasToken,
          path: path,
        });

        // Mark for auth clearing if needed
        if (!hasToken || operation.operationName === 'GetCurrentUser') {
          shouldClearAuth = true;
        }
      }
    });

    // Clear auth after processing all errors
    if (shouldClearAuth) {
      console.warn('🗑️ Clearing auth data due to authentication failure');

      // Schedule the auth clear for next tick to avoid state update during render
      setTimeout(() => {
        const authStore = useAuthStore.getState();
        authStore.clearAuth();
      }, 0);
    }
  }

  if (networkError) {
    console.error(`Network error: ${networkError.message}`, networkError);

    // Handle specific network errors
    if ('statusCode' in networkError) {
      switch (networkError.statusCode) {
        case 401:
          // Unauthorized - clear auth data
          secureStorage.delete('auth.tokens');
          secureStorage.delete('auth.user');
          break;
        case 403:
          // Forbidden
          console.error('Access forbidden');
          break;
        case 500:
          // Server error
          console.error('Server error occurred');
          break;
        default:
          console.error(`HTTP ${networkError.statusCode} error`);
      }
    }
  }
});

// Note: Add retry logic here if needed for handling failed requests

// Combine all links with token refresh
const link = from([errorLink, tokenRefreshLink, authLink, httpLink]);

// Configure cache
const cache = new InMemoryCache({
  typePolicies: {
    User: {
      fields: {
        // Add any field policies for caching if needed
      },
    },
    // Add other type policies as needed
  },
});

// Initialize Apollo Client
export const client = new ApolloClient({
  link,
  cache,
  defaultOptions: {
    watchQuery: {
      errorPolicy: 'all', // Return both data and errors
      notifyOnNetworkStatusChange: true,
    },
    query: {
      errorPolicy: 'all',
      notifyOnNetworkStatusChange: true,
    },
    mutate: {
      errorPolicy: 'all',
    },
  },
  // Enable development tools in development
  connectToDevTools: process.env.NODE_ENV === 'development',
});

// Helper function to update client headers (useful for testing)
export const updateClientHeaders = (newHeaders: Record<string, string>) => {
  // This is a utility function if you need to dynamically update headers
  client.setLink(
    from([
      errorLink,
      setContext((_, { headers }) => ({
        headers: {
          ...headers,
          ...newHeaders,
        },
      })),
      httpLink,
    ])
  );
};

export type ApolloClientType = typeof client;
