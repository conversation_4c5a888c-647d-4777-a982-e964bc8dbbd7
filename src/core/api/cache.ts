/**
 * API caching layer for global performance optimization
 * Reduces API calls and improves UX for users worldwide
 */
import { cacheStorage } from '../storage';

/**
 * Cache configuration
 */
const CACHE_CONFIG = {
  DEFAULT_TTL: 5 * 60 * 1000, // 5 minutes
  MAX_CACHE_SIZE: 50, // Maximum number of cached items per category
  CACHE_VERSION: '1.0.0',
} as const;

/**
 * Cache categories for different types of data
 */
export const CACHE_CATEGORIES = {
  USER_PROFILES: 'user_profiles',
  API_RESPONSES: 'api_responses',
  STATIC_DATA: 'static_data',
  SEARCH_RESULTS: 'search_results',
  FEEDS: 'feeds',
} as const;

export type CacheCategory = (typeof CACHE_CATEGORIES)[keyof typeof CACHE_CATEGORIES];

/**
 * Cache entry structure
 */
interface CacheEntry<T = any> {
  data: T;
  timestamp: number;
  ttl: number;
  category: CacheCategory;
  version: string;
}

/**
 * Cache metadata for management
 */
interface CacheMetadata {
  totalEntries: number;
  categories: Record<CacheCategory, number>;
  lastCleanup: number;
}

/**
 * API Cache manager
 */
class APICache {
  private readonly metadataKey = 'api_cache_metadata';

  /**
   * Generate cache key
   */
  private generateKey(category: CacheCategory, identifier: string): string {
    return `cache_${category}_${identifier}`;
  }

  /**
   * Get cache metadata
   */
  private getMetadata(): CacheMetadata {
    const defaultMetadata: CacheMetadata = {
      totalEntries: 0,
      categories: {
        [CACHE_CATEGORIES.USER_PROFILES]: 0,
        [CACHE_CATEGORIES.API_RESPONSES]: 0,
        [CACHE_CATEGORIES.STATIC_DATA]: 0,
        [CACHE_CATEGORIES.SEARCH_RESULTS]: 0,
        [CACHE_CATEGORIES.FEEDS]: 0,
      },
      lastCleanup: Date.now(),
    };

    return cacheStorage.getObject<CacheMetadata>(this.metadataKey) || defaultMetadata;
  }

  /**
   * Update cache metadata
   */
  private updateMetadata(metadata: CacheMetadata): void {
    cacheStorage.setObject(this.metadataKey, metadata);
  }

  /**
   * Store data in cache
   */
  async set<T>(
    category: CacheCategory,
    identifier: string,
    data: T,
    ttl: number = CACHE_CONFIG.DEFAULT_TTL
  ): Promise<void> {
    try {
      const key = this.generateKey(category, identifier);
      const entry: CacheEntry<T> = {
        data,
        timestamp: Date.now(),
        ttl,
        category,
        version: CACHE_CONFIG.CACHE_VERSION,
      };

      cacheStorage.setObject(key, entry);

      // Update metadata
      const metadata = this.getMetadata();
      metadata.totalEntries += 1;
      metadata.categories[category] += 1;
      this.updateMetadata(metadata);

      // Check if we need cleanup
      if (metadata.categories[category] > CACHE_CONFIG.MAX_CACHE_SIZE) {
        await this.cleanupCategory(category);
      }
    } catch (error) {
      console.warn('Cache set error:', error);
    }
  }

  /**
   * Retrieve data from cache
   */
  async get<T>(category: CacheCategory, identifier: string): Promise<T | null> {
    try {
      const key = this.generateKey(category, identifier);
      const entry = cacheStorage.getObject<CacheEntry<T>>(key);

      if (!entry) {
        return null;
      }

      // Check version compatibility
      if (entry.version !== CACHE_CONFIG.CACHE_VERSION) {
        await this.delete(category, identifier);
        return null;
      }

      // Check TTL
      const now = Date.now();
      if (now - entry.timestamp > entry.ttl) {
        await this.delete(category, identifier);
        return null;
      }

      return entry.data;
    } catch (error) {
      console.warn('Cache get error:', error);
      return null;
    }
  }

  /**
   * Delete specific cache entry
   */
  async delete(category: CacheCategory, identifier: string): Promise<void> {
    try {
      const key = this.generateKey(category, identifier);
      const existed = cacheStorage.contains(key);

      if (existed) {
        cacheStorage.delete(key);

        // Update metadata
        const metadata = this.getMetadata();
        metadata.totalEntries = Math.max(0, metadata.totalEntries - 1);
        metadata.categories[category] = Math.max(0, metadata.categories[category] - 1);
        this.updateMetadata(metadata);
      }
    } catch (error) {
      console.warn('Cache delete error:', error);
    }
  }

  /**
   * Check if cache entry exists and is valid
   */
  async has(category: CacheCategory, identifier: string): Promise<boolean> {
    const data = await this.get(category, identifier);
    return data !== null;
  }

  /**
   * Clear entire category
   */
  async clearCategory(category: CacheCategory): Promise<void> {
    try {
      const metadata = this.getMetadata();
      const categoryCount = metadata.categories[category];

      // This is a simplified approach - in a real implementation,
      // you might want to iterate through all keys
      metadata.totalEntries = Math.max(0, metadata.totalEntries - categoryCount);
      metadata.categories[category] = 0;

      this.updateMetadata(metadata);
    } catch (error) {
      console.warn('Cache clear category error:', error);
    }
  }

  /**
   * Cleanup expired entries in a category
   */
  async cleanupCategory(category: CacheCategory): Promise<void> {
    try {
      // This is a simplified cleanup - in a real implementation,
      // you would iterate through all keys with the category prefix
      // and remove expired ones based on their timestamps

      console.log(`Cleaning up cache category: ${category}`);

      // For now, just update the cleanup timestamp
      const metadata = this.getMetadata();
      metadata.lastCleanup = Date.now();
      this.updateMetadata(metadata);
    } catch (error) {
      console.warn('Cache cleanup error:', error);
    }
  }

  /**
   * Clear all cache data
   */
  async clearAll(): Promise<void> {
    try {
      // Clear metadata
      cacheStorage.delete(this.metadataKey);

      // Note: This is a simplified approach
      // In a real implementation, you would iterate through all cache keys
      console.log('Cache cleared completely');
    } catch (error) {
      console.warn('Cache clear all error:', error);
    }
  }

  /**
   * Get cache statistics
   */
  async getStats(): Promise<{
    totalEntries: number;
    categories: Record<CacheCategory, number>;
    lastCleanup: Date;
    estimatedSize: string;
  }> {
    const metadata = this.getMetadata();

    // Rough estimation of cache size
    const estimatedSizeKB = metadata.totalEntries * 2; // ~2KB per entry
    const estimatedSize =
      estimatedSizeKB > 1024
        ? `${(estimatedSizeKB / 1024).toFixed(1)} MB`
        : `${estimatedSizeKB} KB`;

    return {
      totalEntries: metadata.totalEntries,
      categories: metadata.categories,
      lastCleanup: new Date(metadata.lastCleanup),
      estimatedSize,
    };
  }

  /**
   * Cache an API response with automatic key generation
   */
  async cacheAPIResponse<T>(
    endpoint: string,
    params: Record<string, any>,
    data: T,
    ttl?: number
  ): Promise<void> {
    // Generate consistent key from endpoint and params
    const sortedParams = Object.keys(params)
      .sort()
      .reduce(
        (sorted, key) => {
          sorted[key] = params[key];
          return sorted;
        },
        {} as Record<string, any>
      );

    const identifier = `${endpoint}_${JSON.stringify(sortedParams)}`;
    await this.set(CACHE_CATEGORIES.API_RESPONSES, identifier, data, ttl);
  }

  /**
   * Get cached API response
   */
  async getCachedAPIResponse<T>(endpoint: string, params: Record<string, any>): Promise<T | null> {
    const sortedParams = Object.keys(params)
      .sort()
      .reduce(
        (sorted, key) => {
          sorted[key] = params[key];
          return sorted;
        },
        {} as Record<string, any>
      );

    const identifier = `${endpoint}_${JSON.stringify(sortedParams)}`;
    return await this.get<T>(CACHE_CATEGORIES.API_RESPONSES, identifier);
  }

  /**
   * Cache with automatic refresh on expiry
   */
  async getOrRefresh<T>(
    category: CacheCategory,
    identifier: string,
    refreshFn: () => Promise<T>,
    ttl?: number
  ): Promise<T> {
    // Try to get from cache first
    let data = await this.get<T>(category, identifier);

    if (data !== null) {
      return data;
    }

    // Cache miss or expired - refresh data
    data = await refreshFn();
    await this.set(category, identifier, data, ttl);

    return data;
  }
}

// Export singleton instance
export const apiCache = new APICache();

// Export types and constants
export type { CacheEntry, CacheMetadata };
