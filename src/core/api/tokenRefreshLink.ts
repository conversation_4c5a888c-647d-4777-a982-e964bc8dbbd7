import { ApolloLink, FetchResult, NextLink, Observable, Operation } from '@apollo/client';
import { onError } from '@apollo/client/link/error';

import { useAuthStore } from '@/src/features/auth/store/authStore';

import { secureStorage } from '../storage';
import { refreshAuthTokens } from './tokenRefresh';

interface TokenRefreshRequest {
  operation: Operation;
  forward: NextLink;
  resolve: (value: Observable<FetchResult>) => void;
  reject: (reason: any) => void;
}

class TokenRefreshLink extends ApolloLink {
  private isRefreshing = false;
  private pendingRequests: TokenRefreshRequest[] = [];

  private async refreshToken(): Promise<boolean> {
    if (this.isRefreshing) {
      return false;
    }

    this.isRefreshing = true;

    try {
      console.log('🔄 Refreshing token...');
      const newTokens = await refreshAuthTokens();

      if (newTokens) {
        console.log('✅ Token refreshed successfully');
        this.isRefreshing = false;

        // Process pending requests with new token
        this.pendingRequests.forEach(({ operation, forward, resolve }) => {
          this.addTokenToOperation(operation);
          resolve(forward(operation));
        });
        this.pendingRequests = [];

        return true;
      }
    } catch (error) {
      console.error('❌ Token refresh failed:', error);
      this.isRefreshing = false;

      // Reject all pending requests
      this.pendingRequests.forEach(({ reject }) => {
        reject(error);
      });
      this.pendingRequests = [];

      // Clear auth state
      const authStore = useAuthStore.getState();
      authStore.clearAuth();
    }

    return false;
  }

  private addTokenToOperation(operation: Operation): void {
    const tokens = secureStorage.getObject<{
      accessToken: string;
      refreshToken: string;
      expiresIn: number;
    }>('auth.tokens');

    const token = tokens?.accessToken || (tokens as any)?.token;

    if (token) {
      operation.setContext({
        headers: {
          ...operation.getContext().headers,
          authorization: `Bearer ${token}`,
        },
      });
    }
  }

  public request(operation: Operation, forward: NextLink): Observable<FetchResult> {
    // Skip refresh for auth-related operations
    const isAuthOperation = ['RefreshToken', 'Login', 'Register'].includes(
      operation.operationName || ''
    );

    if (isAuthOperation) {
      return forward(operation);
    }

    return new Observable(observer => {
      let handle: any;

      // If currently refreshing, queue the request
      if (this.isRefreshing) {
        this.pendingRequests.push({
          operation,
          forward,
          resolve: observable => {
            handle = observable.subscribe(observer);
          },
          reject: error => {
            observer.error(error);
          },
        });
        return;
      }

      // Try the request
      handle = forward(operation).subscribe({
        next: result => {
          observer.next(result);
        },
        error: async error => {
          // Check if error is due to authentication
          const isAuthError = error.graphQLErrors?.some((err: any) =>
            ['UNAUTHENTICATED', 'NOT_AUTHENTICATED', 'TOKEN_EXPIRED'].includes(err.extensions?.code)
          );

          if (isAuthError) {
            // Try to refresh token
            const refreshSuccess = await this.refreshToken();

            if (refreshSuccess) {
              // Retry the original request with new token
              this.addTokenToOperation(operation);
              forward(operation).subscribe(observer);
            } else {
              observer.error(error);
            }
          } else {
            observer.error(error);
          }
        },
        complete: () => {
          observer.complete();
        },
      });

      return () => {
        if (handle) handle.unsubscribe();
      };
    });
  }
}

export const tokenRefreshLink = new TokenRefreshLink();

// Error link specifically for handling auth errors after refresh fails
export const authErrorLink = onError(({ graphQLErrors, operation, forward }) => {
  if (graphQLErrors) {
    for (const err of graphQLErrors) {
      if (err.extensions?.code === 'TOKEN_EXPIRED') {
        // Return forward(operation) to retry
        return forward(operation);
      }
    }
  }
});
