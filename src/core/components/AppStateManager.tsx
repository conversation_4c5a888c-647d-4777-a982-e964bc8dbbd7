import React, { createContext, useCallback, useContext, useEffect, useRef } from 'react';

import { AppState, AppStateStatus } from 'react-native';

import { toast } from '@/src/core/libs/toast';
import { authOperationManager } from '@/src/core/utils/authOperationManager';
import { authService } from '@/src/features/auth/services/auth.service';
import { useAuth } from '@/src/features/auth/services/context';

interface AppStateContextValue {
  isActive: boolean;
}

const AppStateContext = createContext<AppStateContextValue>({ isActive: true });

/**
 * App state and token refresh manager
 * Handles background/foreground transitions and token refresh
 * IMPORTANT: This component should only be mounted once at the app root
 */
export const AppStateManager: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isActive, setIsActive] = React.useState(AppState.currentState === 'active');
  const appState = useRef(AppState.currentState);
  const isMountedRef = useRef(true);
  const lastRefreshTime = useRef<number>(0);

  const { isAuthenticated } = useAuth();

  // Minimum time between refresh attempts (5 minutes)
  const MIN_REFRESH_INTERVAL = 5 * 60 * 1000;

  const handleAppStateChange = useCallback(
    async (nextAppState: AppStateStatus) => {
      // Check if component is still mounted
      if (!isMountedRef.current) {
        console.log('⚠️ AppStateManager unmounted, skipping state change');
        return;
      }

      const prevState = appState.current;
      appState.current = nextAppState;

      // Log state transition
      console.log(`📱 App state: ${prevState} → ${nextAppState}`);

      // Update active state
      setIsActive(nextAppState === 'active');

      // Only handle when app comes to foreground
      if (prevState.match(/inactive|background/) && nextAppState === 'active' && isAuthenticated) {
        console.log('📱 App became active - checking token status');

        // Check if enough time has passed since last refresh
        const now = Date.now();
        const timeSinceLastRefresh = now - lastRefreshTime.current;

        if (timeSinceLastRefresh < MIN_REFRESH_INTERVAL) {
          console.log(
            `⏰ Skipping refresh - only ${Math.round(timeSinceLastRefresh / 1000)}s since last refresh`
          );
          return;
        }

        // Check current state before attempting refresh
        const { isRefreshing } = authOperationManager.getState();
        if (isRefreshing) {
          console.log('⏸️ Refresh already in progress via AuthOperationManager');
          return;
        }

        try {
          // Use AuthOperationManager for refresh coordination
          await authOperationManager.executeRefresh(async () => {
            // Double-check mounted state before executing
            if (!isMountedRef.current) {
              console.log('⚠️ Component unmounted during refresh, aborting');
              return false;
            }

            console.log('🔄 [AppStateManager] Calling authService.refreshToken directly');
            const newTokens = await authService.refreshToken();

            if (isMountedRef.current && newTokens) {
              lastRefreshTime.current = now;
              console.log('✅ Token refreshed on app activation');
              return true;
            }

            return false;
          });
        } catch (error: any) {
          console.error('❌ Token refresh failed:', error);

          // Check if it's an auth error (not network error)
          if (!error.networkError && error.message?.includes('Invalid refresh token')) {
            // Invalid auth state - show toast
            toast.error('Session Expired', {
              title: 'Please login again to continue',
              message: error.message,
            });
          }
        }
      }
    },
    [MIN_REFRESH_INTERVAL, isAuthenticated]
  );

  useEffect(() => {
    console.log('✅ AppStateManager mounted');
    isMountedRef.current = true;

    const subscription = AppState.addEventListener('change', handleAppStateChange);

    return () => {
      console.log('🔚 AppStateManager unmounted');
      isMountedRef.current = false;
      subscription.remove();
    };
  }, [handleAppStateChange]);

  useEffect(() => {
    console.log(`🔐 Auth state: ${isAuthenticated ? 'Authenticated' : 'Not authenticated'}`);
  }, [isAuthenticated]);

  // Debug: Log auth operation manager state periodically and force reset if stuck
  useEffect(() => {
    if (__DEV__) {
      let stuckCounter = 0;

      const interval = setInterval(() => {
        const state = authOperationManager.getState();
        if (state.isRefreshing || state.isSigningOut) {
          console.log('🔍 AuthOperationManager State:', state);
          stuckCounter++;

          // If stuck for more than 60 seconds (12 x 5 seconds), force reset
          if (stuckCounter >= 12) {
            console.warn('⚠️ AuthOperationManager appears stuck, force resetting...');
            authOperationManager.forceReset();
            stuckCounter = 0;
          }
        } else {
          stuckCounter = 0; // Reset counter when not stuck
        }
      }, 5000); // Check every 5 seconds

      return () => clearInterval(interval);
    }
  }, []);

  return <AppStateContext.Provider value={{ isActive }}>{children}</AppStateContext.Provider>;
};

export const useAppState = () => useContext(AppStateContext);
