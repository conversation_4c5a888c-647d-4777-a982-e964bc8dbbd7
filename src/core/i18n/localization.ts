/**
 * Localization utilities for global MVP
 * Handles timezone, date formatting, and number formatting for worldwide users
 */
import {
  Locale,
  addDays,
  format,
  formatDistance,
  formatRelative,
  isValid,
  parseISO,
  startOfDay,
} from 'date-fns';
import { format as formatTz, toDate, toZonedTime } from 'date-fns-tz';
import { getCalendars, getLocales } from 'expo-localization';

import i18n from './config';

// Dynamic date-fns locale cache
let dateFnsLocaleCache: Map<string, Locale> = new Map();

// Static date-fns locale imports (required for Metro bundler)
const DATE_FNS_LOCALES: Record<string, () => Promise<Locale>> = {
  // English variants
  en: async () => (await import('date-fns/locale/en-US')).enUS,
  'en-US': async () => (await import('date-fns/locale/en-US')).enUS,
  'en-GB': async () => (await import('date-fns/locale/en-GB')).enGB,
  'en-AU': async () => (await import('date-fns/locale/en-AU')).enAU,
  'en-CA': async () => (await import('date-fns/locale/en-CA')).enCA,
  'en-NZ': async () => (await import('date-fns/locale/en-NZ')).enNZ,
  'en-ZA': async () => (await import('date-fns/locale/en-ZA')).enZA,

  // European languages
  da: async () => (await import('date-fns/locale/da')).da,
  'da-DK': async () => (await import('date-fns/locale/da')).da,
  de: async () => (await import('date-fns/locale/de')).de,
  'de-DE': async () => (await import('date-fns/locale/de')).de,
  'de-AT': async () => (await import('date-fns/locale/de-AT')).deAT,
  el: async () => (await import('date-fns/locale/el')).el,
  'el-GR': async () => (await import('date-fns/locale/el')).el,
  es: async () => (await import('date-fns/locale/es')).es,
  'es-ES': async () => (await import('date-fns/locale/es')).es,
  'es-MX': async () => (await import('date-fns/locale/es')).es,
  'es-AR': async () => (await import('date-fns/locale/es')).es,
  'es-CO': async () => (await import('date-fns/locale/es')).es,
  fr: async () => (await import('date-fns/locale/fr')).fr,
  'fr-FR': async () => (await import('date-fns/locale/fr')).fr,
  'fr-CA': async () => (await import('date-fns/locale/fr-CA')).frCA,
  'fr-CH': async () => (await import('date-fns/locale/fr')).fr,
  hr: async () => (await import('date-fns/locale/hr')).hr,
  'hr-HR': async () => (await import('date-fns/locale/hr')).hr,
  it: async () => (await import('date-fns/locale/it')).it,
  'it-IT': async () => (await import('date-fns/locale/it')).it,
  nl: async () => (await import('date-fns/locale/nl')).nl,
  'nl-NL': async () => (await import('date-fns/locale/nl')).nl,
  'nl-BE': async () => (await import('date-fns/locale/nl-BE')).nlBE,
  pl: async () => (await import('date-fns/locale/pl')).pl,
  'pl-PL': async () => (await import('date-fns/locale/pl')).pl,
  ru: async () => (await import('date-fns/locale/ru')).ru,
  'ru-RU': async () => (await import('date-fns/locale/ru')).ru,
  tr: async () => (await import('date-fns/locale/tr')).tr,
  'tr-TR': async () => (await import('date-fns/locale/tr')).tr,

  // Portuguese variants
  pt: async () => (await import('date-fns/locale/pt-BR')).ptBR, // Default to Brazilian
  'pt-BR': async () => (await import('date-fns/locale/pt-BR')).ptBR,
  'pt-PT': async () => (await import('date-fns/locale/pt')).pt,

  // Asian languages
  ja: async () => (await import('date-fns/locale/ja')).ja,
  'ja-JP': async () => (await import('date-fns/locale/ja')).ja,
  ko: async () => (await import('date-fns/locale/ko')).ko,
  'ko-KR': async () => (await import('date-fns/locale/ko')).ko,
  zh: async () => (await import('date-fns/locale/zh-CN')).zhCN, // Default to Simplified
  'zh-CN': async () => (await import('date-fns/locale/zh-CN')).zhCN,
  'zh-TW': async () => (await import('date-fns/locale/zh-TW')).zhTW,
  'zh-HK': async () => (await import('date-fns/locale/zh-HK')).zhHK,

  // Other languages - fallback to English for unsupported ones
  sw: async () => (await import('date-fns/locale/en-US')).enUS, // Swahili -> English
  'sw-KE': async () => (await import('date-fns/locale/en-US')).enUS,
};

/**
 * Load date-fns locale with intelligent fallbacks
 */
async function loadDateFnsLocale(languageTag: string): Promise<Locale | null> {
  // Check cache first
  if (dateFnsLocaleCache.has(languageTag)) {
    return dateFnsLocaleCache.get(languageTag)!;
  }

  // Extract base language from tag
  const baseLanguage = languageTag.split('-')[0].toLowerCase();

  // Try variants in order of preference
  const variants = [
    languageTag.toLowerCase(), // exact match
    languageTag, // case-sensitive match
    baseLanguage, // base language
  ];

  for (const variant of variants) {
    const importFunction = DATE_FNS_LOCALES[variant];
    if (importFunction) {
      try {
        const locale = await importFunction();
        dateFnsLocaleCache.set(languageTag, locale);
        return locale;
      } catch (error) {
        console.warn(`Failed to load date-fns locale for ${variant}:`, error);
        continue;
      }
    }
  }

  // Ultimate fallback to English
  try {
    const fallbackLocale = await DATE_FNS_LOCALES['en-US']();
    dateFnsLocaleCache.set(languageTag, fallbackLocale);
    return fallbackLocale;
  } catch {
    return null;
  }
}

/**
 * Get current user's timezone from device calendar settings
 */
export function getUserTimezone(): string {
  return getCalendars()[0].timeZone!;
}

/**
 * Get user's locale preferences from device settings
 * Returns the primary locale with fallback
 */
export function getUserLocales() {
  return getLocales();
}

/**
 * Get user's primary locale preference
 */
export function getUserPrimaryLocale() {
  const locales = getUserLocales();
  return locales[0];
}

/**
 * Get date-fns locale for current app language or device locale
 */
export async function getDateLocale(): Promise<Locale | null> {
  // Try current i18n language first
  const currentLanguage = i18n.language;
  if (currentLanguage) {
    const locale = await loadDateFnsLocale(currentLanguage);
    if (locale) return locale;
  }

  // Try device locales
  const deviceLocales = getUserLocales();
  for (const deviceLocale of deviceLocales) {
    const locale = await loadDateFnsLocale(deviceLocale.languageTag);
    if (locale) return locale;
  }

  // Fallback
  return await loadDateFnsLocale('en-US');
}

/**
 * Get cached date-fns locale (sync version)
 * Returns null if locale hasn't been loaded yet
 */
export function getDateLocaleSync(): Locale | null {
  const currentLanguage = i18n.language;
  if (dateFnsLocaleCache.has(currentLanguage)) {
    return dateFnsLocaleCache.get(currentLanguage)!;
  }

  // Try device locales from cache
  const deviceLocales = getUserLocales();
  for (const deviceLocale of deviceLocales) {
    if (dateFnsLocaleCache.has(deviceLocale.languageTag)) {
      return dateFnsLocaleCache.get(deviceLocale.languageTag)!;
    }
  }

  return null;
}

/**
 * Get comprehensive locale information from device preferences
 */
/**
 * Get current locale string from i18n or device settings
 */
export function getCurrentLocale(): string {
  // First try i18n language
  if (i18n.language) {
    return i18n.language;
  }

  // Fallback to device locale
  const userLocale = getUserPrimaryLocale();
  return userLocale.languageTag || 'en-US';
}

export function getLocaleInfo() {
  const userLocale = getUserPrimaryLocale();
  const deviceLocales = getUserLocales();
  const currentLanguage = i18n.language;

  // Primary locale info from device
  const primaryLocale = userLocale || deviceLocales[0];

  return {
    // Use device locale for all formatting
    locale: primaryLocale?.languageTag || currentLanguage || 'en-US',
    currency: primaryLocale?.currencyCode || 'USD',
    region: primaryLocale?.regionCode || 'US',
    languageTag: primaryLocale?.languageTag || currentLanguage || 'en-US',
    languageCode: primaryLocale?.languageCode || currentLanguage?.split('-')[0] || 'en',

    // Additional device-specific locale info
    digitGroupingSeparator: primaryLocale?.digitGroupingSeparator,
    decimalSeparator: primaryLocale?.decimalSeparator,
    measurementSystem: primaryLocale?.measurementSystem,
    textDirection: primaryLocale?.textDirection,

    // All device locales for fallback
    allLocales: deviceLocales,
  };
}

/**
 * Get number formatting options using device locale preferences
 */
export function getNumberFormatOptions() {
  const localeInfo = getLocaleInfo();
  return {
    locale: localeInfo.locale,
    currency: localeInfo.currency,
    digitGroupingSeparator: localeInfo.digitGroupingSeparator,
    decimalSeparator: localeInfo.decimalSeparator,
  };
}

/**
 * Get the best matching language from device preferences
 */
export function getBestMatchingLanguage(): string {
  const localeInfo = getLocaleInfo();
  return localeInfo.languageCode;
}

/**
 * Format date for current locale and timezone (async version)
 */
export async function formatDate(
  date: Date | string,
  formatString: string = 'PPP',
  options?: {
    timezone?: string;
    locale?: Locale;
  }
): Promise<string> {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;

  if (!isValid(dateObj)) {
    return '';
  }

  const timezone = options?.timezone || getUserTimezone();
  const locale = options?.locale || (await getDateLocale());

  try {
    const zonedDate = toZonedTime(dateObj, timezone);
    return format(zonedDate, formatString, locale ? { locale } : undefined);
  } catch (error) {
    console.warn('Date formatting error:', error);
    return format(dateObj, formatString, locale ? { locale } : undefined);
  }
}

/**
 * Format date for current locale and timezone (sync version)
 * Falls back to basic formatting if locale is not loaded
 */
export function formatDateSync(
  date: Date | string,
  formatString: string = 'PPP',
  options?: {
    timezone?: string;
    locale?: Locale;
  }
): string {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;

  if (!isValid(dateObj)) {
    return '';
  }

  const timezone = options?.timezone || getUserTimezone();
  const locale = options?.locale || getDateLocaleSync();

  try {
    const zonedDate = toZonedTime(dateObj, timezone);
    if (locale) {
      return format(zonedDate, formatString, { locale });
    } else {
      // Basic formatting without locale
      return format(zonedDate, formatString);
    }
  } catch (error) {
    console.warn('Date formatting error:', error);
    return format(dateObj, formatString, locale ? { locale } : undefined);
  }
}

/**
 * Format date with timezone information (async version)
 */
export async function formatDateWithTimezone(
  date: Date | string,
  formatString: string = 'PPP p (zzz)',
  timezone?: string
): Promise<string> {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;

  if (!isValid(dateObj)) {
    return '';
  }

  const tz = timezone || getUserTimezone();

  try {
    return formatTz(dateObj, formatString, {
      timeZone: tz,
      locale: (await getDateLocale()) || undefined,
    });
  } catch (error) {
    console.warn('Timezone formatting error:', error);
    return await formatDate(date, formatString);
  }
}

/**
 * Format date with timezone information (sync version)
 */
export function formatDateWithTimezoneSync(
  date: Date | string,
  formatString: string = 'PPP p (zzz)',
  timezone?: string
): string {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;

  if (!isValid(dateObj)) {
    return '';
  }

  const tz = timezone || getUserTimezone();
  const locale = getDateLocaleSync();

  try {
    return formatTz(dateObj, formatString, {
      timeZone: tz,
      locale: locale || undefined,
    });
  } catch (error) {
    console.warn('Timezone formatting error:', error);
    return formatDateSync(date, formatString);
  }
}

/**
 * Format relative time (e.g., "2 hours ago") - async version
 */
export async function formatRelativeTime(
  date: Date | string,
  baseDate?: Date | string
): Promise<string> {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  const baseDateObj = baseDate
    ? typeof baseDate === 'string'
      ? parseISO(baseDate)
      : baseDate
    : new Date();

  if (!isValid(dateObj) || !isValid(baseDateObj)) {
    return '';
  }

  const locale = await getDateLocale();

  try {
    return formatDistance(dateObj, baseDateObj, {
      addSuffix: true,
      locale: locale || undefined,
    });
  } catch (error) {
    console.warn('Relative time formatting error:', error);
    return '';
  }
}

/**
 * Format relative time (e.g., "2 hours ago") - sync version
 */
export function formatRelativeTimeSync(date: Date | string, baseDate?: Date | string): string {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  const baseDateObj = baseDate
    ? typeof baseDate === 'string'
      ? parseISO(baseDate)
      : baseDate
    : new Date();

  if (!isValid(dateObj) || !isValid(baseDateObj)) {
    return '';
  }

  const locale = getDateLocaleSync();

  try {
    return formatDistance(dateObj, baseDateObj, {
      addSuffix: true,
      locale: locale || undefined,
    });
  } catch (error) {
    console.warn('Relative time formatting error:', error);
    return '';
  }
}

/**
 * Format relative date (e.g., "last Friday at 10:30 AM") - async version
 */
export async function formatRelativeDate(
  date: Date | string,
  baseDate?: Date | string
): Promise<string> {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  const baseDateObj = baseDate
    ? typeof baseDate === 'string'
      ? parseISO(baseDate)
      : baseDate
    : new Date();

  if (!isValid(dateObj) || !isValid(baseDateObj)) {
    return '';
  }

  const locale = await getDateLocale();

  try {
    return formatRelative(dateObj, baseDateObj, {
      locale: locale || undefined,
    });
  } catch (error) {
    console.warn('Relative date formatting error:', error);
    return await formatDate(date, 'PPp');
  }
}

/**
 * Format relative date (e.g., "last Friday at 10:30 AM") - sync version
 */
export function formatRelativeDateSync(date: Date | string, baseDate?: Date | string): string {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  const baseDateObj = baseDate
    ? typeof baseDate === 'string'
      ? parseISO(baseDate)
      : baseDate
    : new Date();

  if (!isValid(dateObj) || !isValid(baseDateObj)) {
    return '';
  }

  const locale = getDateLocaleSync();

  try {
    return formatRelative(dateObj, baseDateObj, {
      locale: locale || undefined,
    });
  } catch (error) {
    console.warn('Relative date formatting error:', error);
    return formatDateSync(date, 'PPp');
  }
}

/**
 * Convert UTC date to user's timezone
 */
export function toUserTimezone(utcDate: Date | string): Date {
  const dateObj = typeof utcDate === 'string' ? parseISO(utcDate) : utcDate;
  return toZonedTime(dateObj, getUserTimezone());
}

/**
 * Convert user's timezone date to UTC
 */
export function toUtc(localDate: Date | string): Date {
  const dateObj = typeof localDate === 'string' ? parseISO(localDate) : localDate;
  return toDate(dateObj, {
    timeZone: getUserTimezone(),
  });
}

/**
 * Format number according to current locale with enhanced fallback
 */
export function formatNumber(number: number, options?: Intl.NumberFormatOptions): string {
  const { locale } = getNumberFormatOptions();

  try {
    return new Intl.NumberFormat(locale, options).format(number);
  } catch (error) {
    console.warn(`Number formatting error for locale ${locale}:`, error);
    // Try with base language if full locale fails
    const baseLanguage = locale.split('-')[0];
    try {
      return new Intl.NumberFormat(baseLanguage, options).format(number);
    } catch (fallbackError) {
      console.warn(`Number formatting fallback error for ${baseLanguage}:`, fallbackError);
      // Ultimate fallback to default formatting
      return new Intl.NumberFormat('en-US', options).format(number);
    }
  }
}

/**
 * Format currency according to current locale with enhanced fallback
 */
export function formatCurrency(
  amount: number,
  currencyCode?: string,
  options?: Intl.NumberFormatOptions
): string {
  const { locale, currency } = getNumberFormatOptions();
  const finalCurrency = currencyCode || currency;

  try {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: finalCurrency,
      ...options,
    }).format(amount);
  } catch (error) {
    console.warn(`Currency formatting error for ${locale} ${finalCurrency}:`, error);
    // Try with base language if full locale fails
    const baseLanguage = locale.split('-')[0];
    try {
      return new Intl.NumberFormat(baseLanguage, {
        style: 'currency',
        currency: finalCurrency,
        ...options,
      }).format(amount);
    } catch (fallbackError) {
      console.warn(`Currency formatting fallback error:`, fallbackError);
      // Try with a known stable currency format
      try {
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: finalCurrency,
          ...options,
        }).format(amount);
      } catch (_ultimateFallbackError) {
        // Last resort: manual formatting
        return `${finalCurrency} ${amount.toFixed(2)}`;
      }
    }
  }
}

/**
 * Format percentage according to current locale with enhanced fallback
 */
export function formatPercentage(value: number, options?: Intl.NumberFormatOptions): string {
  const { locale } = getNumberFormatOptions();

  try {
    return new Intl.NumberFormat(locale, {
      style: 'percent',
      ...options,
    }).format(value);
  } catch (error) {
    console.warn(`Percentage formatting error for locale ${locale}:`, error);
    // Try with base language if full locale fails
    const baseLanguage = locale.split('-')[0];
    try {
      return new Intl.NumberFormat(baseLanguage, {
        style: 'percent',
        ...options,
      }).format(value);
    } catch (fallbackError) {
      console.warn(`Percentage formatting fallback error:`, fallbackError);
      // Ultimate fallback
      return new Intl.NumberFormat('en-US', {
        style: 'percent',
        ...options,
      }).format(value);
    }
  }
}

/**
 * Get localized day/month names - async version
 */
export async function getLocalizedDateParts() {
  const locale = await getDateLocale();
  const sampleDate = new Date(2024, 0, 1); // January 1, 2024 (Monday)

  const days = Array.from({ length: 7 }, (_, i) => {
    const date = addDays(sampleDate, i);
    return {
      short: format(date, 'EEE', locale ? { locale } : undefined),
      long: format(date, 'EEEE', locale ? { locale } : undefined),
    };
  });

  const months = Array.from({ length: 12 }, (_, i) => {
    const date = new Date(2024, i, 1);
    return {
      short: format(date, 'MMM', locale ? { locale } : undefined),
      long: format(date, 'MMMM', locale ? { locale } : undefined),
    };
  });

  return { days, months };
}

/**
 * Get localized day/month names - sync version
 */
export function getLocalizedDatePartsSync() {
  const locale = getDateLocaleSync();
  const sampleDate = new Date(2024, 0, 1); // January 1, 2024 (Monday)

  const days = Array.from({ length: 7 }, (_, i) => {
    const date = addDays(sampleDate, i);
    return {
      short: format(date, 'EEE', locale ? { locale } : undefined),
      long: format(date, 'EEEE', locale ? { locale } : undefined),
    };
  });

  const months = Array.from({ length: 12 }, (_, i) => {
    const date = new Date(2024, i, 1);
    return {
      short: format(date, 'MMM', locale ? { locale } : undefined),
      long: format(date, 'MMMM', locale ? { locale } : undefined),
    };
  });

  return { days, months };
}

/**
 * Check if current locale uses RTL (Right-to-Left) layout
 * Uses device locale detection from expo-localization
 */
export function isRTL(): boolean {
  const localeInfo = getLocaleInfo();
  return localeInfo.textDirection === 'rtl';
}

/**
 * Get localized time format (12h vs 24h) from device calendar settings
 */
export function getTimeFormat(): '12h' | '24h' {
  const calendar = getCalendars()[0];
  return calendar.uses24hourClock ? '24h' : '12h';
}

/**
 * Format time according to user preference - async version
 */
export async function formatTime(
  date: Date | string,
  options?: {
    timezone?: string;
    format?: '12h' | '24h';
  }
): Promise<string> {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;

  if (!isValid(dateObj)) {
    return '';
  }

  const timeFormat = options?.format || getTimeFormat();
  const formatString = timeFormat === '24h' ? 'HH:mm' : 'h:mm a';

  return await formatDate(dateObj, formatString, {
    timezone: options?.timezone,
  });
}

/**
 * Get localized weekday names
 * @param options - Configuration for weekday format
 * @returns Array of localized weekday names starting from Monday
 */
export async function getLocalizedWeekdays(options?: {
  locale?: string;
  format?: 'long' | 'short' | 'narrow';
  startWithMonday?: boolean;
}): Promise<string[]> {
  // Get locale and handle null case
  const locale = await getDateLocale();
  const formatType = options?.format || 'long';
  const startWithMonday = options?.startWithMonday !== false; // Default true

  // Create dates for a week starting from Sunday
  const baseDate = new Date(2024, 0, 7); // Sunday, January 7, 2024
  const weekdays: string[] = [];

  for (let i = 0; i < 7; i++) {
    const date = addDays(baseDate, i);
    let formatted: string;

    // Create format options object, only including locale if it's not null
    const formatOptions = locale ? { locale } : {};

    switch (formatType) {
      case 'short':
        formatted = format(date, 'EEE', formatOptions); // Mon, Tue, etc.
        break;
      case 'narrow':
        formatted = format(date, 'EEEEE', formatOptions); // M, T, etc.
        break;
      case 'long':
      default:
        formatted = format(date, 'EEEE', formatOptions); // Monday, Tuesday, etc.
        break;
    }

    weekdays.push(formatted);
  }

  // Rearrange to start with Monday if requested (default)
  if (startWithMonday) {
    const sunday = weekdays.shift()!;
    weekdays.push(sunday);
  }

  return weekdays;
}

/**
 * Get localized weekday names - sync version
 * Uses Intl.DateTimeFormat for synchronous operation
 * @param options - Configuration for weekday format
 * @returns Array of localized weekday names starting from Monday
 */
export function getLocalizedWeekdaysSync(options?: {
  locale?: string;
  format?: 'long' | 'short' | 'narrow';
  startWithMonday?: boolean;
}): string[] {
  const locale = options?.locale || getCurrentLocale();
  const formatType = options?.format || 'long';
  const startWithMonday = options?.startWithMonday !== false; // Default true

  const formatter = new Intl.DateTimeFormat(locale, { weekday: formatType });

  // Create dates for a week starting from Sunday
  const baseDate = new Date(2024, 0, 7); // Sunday, January 7, 2024
  const weekdays: string[] = [];

  for (let i = 0; i < 7; i++) {
    const date = new Date(baseDate);
    date.setDate(baseDate.getDate() + i);
    weekdays.push(formatter.format(date));
  }

  // Rearrange to start with Monday if requested (default)
  if (startWithMonday) {
    const sunday = weekdays.shift()!;
    weekdays.push(sunday);
  }

  return weekdays;
}

/**
 * Get localized day name by day of week
 * @param dayOfWeek - 0-6 where 0 is Sunday, or 'monday', 'tuesday', etc.
 * @param options - Configuration for format
 * @returns Localized day name
 */
export function getLocalizedDayName(
  dayOfWeek: number | string,
  options?: {
    locale?: string;
    format?: 'long' | 'short' | 'narrow';
  }
): string {
  const locale = options?.locale || getCurrentLocale();
  const formatType = options?.format || 'long';

  let dayIndex: number;
  if (typeof dayOfWeek === 'string') {
    const dayMap: Record<string, number> = {
      sunday: 0,
      monday: 1,
      tuesday: 2,
      wednesday: 3,
      thursday: 4,
      friday: 5,
      saturday: 6,
    };
    dayIndex = dayMap[dayOfWeek.toLowerCase()] ?? 0;
  } else {
    dayIndex = dayOfWeek;
  }

  const formatter = new Intl.DateTimeFormat(locale, { weekday: formatType });
  const date = new Date(2024, 0, 7 + dayIndex); // Sunday Jan 7, 2024 + dayIndex

  return formatter.format(date);
}

/**
 * Format time according to user preference - sync version
 */
export function formatTimeSync(
  date: Date | string,
  options?: {
    timezone?: string;
    format?: '12h' | '24h';
  }
): string {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;

  if (!isValid(dateObj)) {
    return '';
  }

  const timeFormat = options?.format || getTimeFormat();
  const formatString = timeFormat === '24h' ? 'HH:mm' : 'h:mm a';

  return formatDateSync(dateObj, formatString, {
    timezone: options?.timezone,
  });
}

/**
 * Get business hours in user's timezone
 */
export function getBusinessHours(timezone?: string) {
  const tz = timezone || getUserTimezone();
  const now = new Date();
  const startOfToday = startOfDay(now);

  // Default business hours: 9 AM - 5 PM
  const businessStart = new Date(startOfToday.getTime() + 9 * 60 * 60 * 1000); // 9 AM
  const businessEnd = new Date(startOfToday.getTime() + 17 * 60 * 60 * 1000); // 5 PM

  return {
    start: toZonedTime(businessStart, tz),
    end: toZonedTime(businessEnd, tz),
    isBusinessHours: now >= businessStart && now <= businessEnd,
  };
}
