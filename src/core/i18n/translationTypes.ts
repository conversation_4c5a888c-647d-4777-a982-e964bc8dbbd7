/**
 * Type definitions for translation keys
 * Auto-generated from translation files
 */
import enTranslations from '../../../translations/en.json';

// Define the translation structure based on the English translation file
export type TranslationKeys = typeof enTranslations;

// Create deep recursive type for nested dot notation paths
export type RecursiveDotNotation<T, P extends string = ''> = {
  [K in keyof T]: T[K] extends object
    ? RecursiveDotNotation<T[K], `${P}${P extends '' ? '' : '.'}${K & string}`>
    : `${P}${P extends '' ? '' : '.'}${K & string}`;
}[keyof T];

// Type for accessing any translation key using dot notation
export type TxKeyPath = RecursiveDotNotation<TranslationKeys>;
