import { getLocales } from 'expo-localization';

export interface LanguageResource {
  code: string;
  name: string;
  nativeName: string;
  flag: string;
  flagUrl: string;
  translations: Record<string, any>;
}

interface LanguageMetadata {
  name: string;
  nativeName: string;
  flag: string;
  flagUrl: string;
}

// Language code to country code mapping for flag URLs
// This covers most common language-country associations
const LANGUAGE_TO_COUNTRY_CODE: Record<string, string> = {
  en: 'us',
  'en-GB': 'gb',
  'en-US': 'us',
  'en-CA': 'ca',
  'en-AU': 'au',
  es: 'es',
  'es-MX': 'mx',
  'es-AR': 'ar',
  'pt-BR': 'br',
  'pt-PT': 'pt',
  fr: 'fr',
  'fr-CA': 'ca',
  'fr-BE': 'be',
  de: 'de',
  'de-AT': 'at',
  'de-CH': 'ch',
  it: 'it',
  'it-CH': 'ch',
  nl: 'nl',
  'nl-BE': 'be',
  ja: 'jp',
  ko: 'kr',
  'zh-CN': 'cn',
  'zh-TW': 'tw',
  'zh-HK': 'hk',
  ru: 'ru',
  ar: 'sa',
  'ar-EG': 'eg',
  'ar-AE': 'ae',
  hi: 'in',
  th: 'th',
  vi: 'vn',
  sv: 'se',
  no: 'no',
  da: 'dk',
  fi: 'fi',
  pl: 'pl',
  tr: 'tr',
  el: 'gr',
  he: 'il',
  cs: 'cz',
  sk: 'sk',
  hu: 'hu',
  ro: 'ro',
  bg: 'bg',
  hr: 'hr', // Croatian - this was missing!
  sr: 'rs',
  sl: 'si',
  et: 'ee',
  lv: 'lv',
  lt: 'lt',
  uk: 'ua',
  be: 'by',
  ka: 'ge',
  hy: 'am',
  az: 'az',
  kk: 'kz',
  uz: 'uz',
  mn: 'mn',
  id: 'id',
  ms: 'my',
  tl: 'ph',
  bn: 'bd',
  ur: 'pk',
  fa: 'ir',
  sw: 'ke',
  am: 'et',
  or: 'in',
  ta: 'in',
  te: 'in',
  ml: 'in',
  kn: 'in',
  gu: 'in',
  pa: 'in',
  ne: 'np',
  si: 'lk',
  my: 'mm',
  km: 'kh',
  lo: 'la',
  mt: 'mt',
  is: 'is',
  ga: 'ie',
  cy: 'gb',
  eu: 'es',
  ca: 'es',
  gl: 'es',
  lb: 'lu',
  mk: 'mk',
  sq: 'al',
  bs: 'ba',
  me: 'me',
  af: 'za',
  zu: 'za',
  xh: 'za',
  st: 'za',
  tn: 'za',
  ts: 'za',
  ss: 'za',
  ve: 'za',
  nr: 'za',
};

/**
 * Generates metadata for any language code dynamically
 * Uses translation file content to get localized names when possible
 */
function generateLanguageMetadata(
  code: string,
  translations?: Record<string, any>
): LanguageMetadata {
  // Try to get localized language name from translation file
  const localizedName = translations?.language?.name || translations?.common?.language || null;

  // Get country code for flag
  const countryCode = LANGUAGE_TO_COUNTRY_CODE[code] || code.split('-')[0] || 'un';

  // Generate flag URL
  const flagUrl = `https://flagcdn.com/w20/${countryCode}.png`;

  // Generate display names
  const { englishName, nativeName } = generateDisplayNames(code, localizedName);

  return {
    name: englishName,
    nativeName: nativeName,
    flag: getFlagEmoji(countryCode),
    flagUrl: flagUrl,
  };
}

/**
 * Generates display names for a language
 */
function generateDisplayNames(
  code: string,
  localizedName?: string
): { englishName: string; nativeName: string } {
  const baseCode = code.split('-')[0];
  const region = code.includes('-') ? code.split('-')[1] : '';

  // Language name mappings in English and native script
  const languageNames: Record<string, { english: string; native: string }> = {
    en: { english: 'English', native: 'English' },
    es: { english: 'Spanish', native: 'Español' },
    pt: { english: 'Portuguese', native: 'Português' },
    fr: { english: 'French', native: 'Français' },
    de: { english: 'German', native: 'Deutsch' },
    it: { english: 'Italian', native: 'Italiano' },
    nl: { english: 'Dutch', native: 'Nederlands' },
    ja: { english: 'Japanese', native: '日本語' },
    ko: { english: 'Korean', native: '한국어' },
    zh: { english: 'Chinese', native: '中文' },
    ru: { english: 'Russian', native: 'Русский' },
    ar: { english: 'Arabic', native: 'العربية' },
    hi: { english: 'Hindi', native: 'हिन्दी' },
    th: { english: 'Thai', native: 'ไทย' },
    vi: { english: 'Vietnamese', native: 'Tiếng Việt' },
    sv: { english: 'Swedish', native: 'Svenska' },
    no: { english: 'Norwegian', native: 'Norsk' },
    da: { english: 'Danish', native: 'Dansk' },
    fi: { english: 'Finnish', native: 'Suomi' },
    pl: { english: 'Polish', native: 'Polski' },
    tr: { english: 'Turkish', native: 'Türkçe' },
    el: { english: 'Greek', native: 'Ελληνικά' },
    he: { english: 'Hebrew', native: 'עברית' },
    cs: { english: 'Czech', native: 'Čeština' },
    sk: { english: 'Slovak', native: 'Slovenčina' },
    hu: { english: 'Hungarian', native: 'Magyar' },
    ro: { english: 'Romanian', native: 'Română' },
    bg: { english: 'Bulgarian', native: 'Български' },
    hr: { english: 'Croatian', native: 'Hrvatski' }, // Croatian added!
    sr: { english: 'Serbian', native: 'Српски' },
    sl: { english: 'Slovenian', native: 'Slovenščina' },
    et: { english: 'Estonian', native: 'Eesti' },
    lv: { english: 'Latvian', native: 'Latviešu' },
    lt: { english: 'Lithuanian', native: 'Lietuvių' },
    uk: { english: 'Ukrainian', native: 'Українська' },
    be: { english: 'Belarusian', native: 'Беларуская' },
    ka: { english: 'Georgian', native: 'ქართული' },
    hy: { english: 'Armenian', native: 'Հայերեն' },
    az: { english: 'Azerbaijani', native: 'Azərbaycan' },
    kk: { english: 'Kazakh', native: 'Қазақша' },
    uz: { english: 'Uzbek', native: 'Oʻzbekcha' },
    mn: { english: 'Mongolian', native: 'Монгол' },
    id: { english: 'Indonesian', native: 'Bahasa Indonesia' },
    ms: { english: 'Malay', native: 'Bahasa Melayu' },
    tl: { english: 'Filipino', native: 'Filipino' },
  };

  const baseLang = languageNames[baseCode];

  if (baseLang) {
    // Use localized name if available from translation file
    const nativeName = localizedName || baseLang.native;
    const englishName = region ? `${baseLang.english} (${region.toUpperCase()})` : baseLang.english;
    const displayNativeName = region ? `${nativeName} (${region.toUpperCase()})` : nativeName;

    return {
      englishName,
      nativeName: displayNativeName,
    };
  }

  // Fallback for unknown languages
  const capitalizedCode = code.charAt(0).toUpperCase() + code.slice(1);
  return {
    englishName: capitalizedCode,
    nativeName: capitalizedCode,
  };
}

/**
 * Gets flag emoji for country code (fallback for when image loading fails)
 */
function getFlagEmoji(countryCode: string): string {
  const flagEmojis: Record<string, string> = {
    us: '🇺🇸',
    gb: '🇬🇧',
    ca: '🇨🇦',
    au: '🇦🇺',
    es: '🇪🇸',
    mx: '🇲🇽',
    ar: '🇦🇷',
    br: '🇧🇷',
    pt: '🇵🇹',
    fr: '🇫🇷',
    be: '🇧🇪',
    de: '🇩🇪',
    at: '🇦🇹',
    ch: '🇨🇭',
    it: '🇮🇹',
    nl: '🇳🇱',
    jp: '🇯🇵',
    kr: '🇰🇷',
    cn: '🇨🇳',
    tw: '🇹🇼',
    hk: '🇭🇰',
    ru: '🇷🇺',
    sa: '🇸🇦',
    eg: '🇪🇬',
    ae: '🇦🇪',
    in: '🇮🇳',
    th: '🇹🇭',
    vn: '🇻🇳',
    se: '🇸🇪',
    no: '🇳🇴',
    dk: '🇩🇰',
    fi: '🇫🇮',
    pl: '🇵🇱',
    tr: '🇹🇷',
    gr: '🇬🇷',
    il: '🇮🇱',
    cz: '🇨🇿',
    sk: '🇸🇰',
    hu: '🇭🇺',
    ro: '🇷🇴',
    bg: '🇧🇬',
    hr: '🇭🇷',
    rs: '🇷🇸',
    si: '🇸🇮',
    ee: '🇪🇪',
    lv: '🇱🇻',
    lt: '🇱🇹',
    ua: '🇺🇦',
    by: '🇧🇾',
    ge: '🇬🇪',
    am: '🇦🇲',
    az: '🇦🇿',
    kz: '🇰🇿',
    uz: '🇺🇿',
    mn: '🇲🇳',
    id: '🇮🇩',
    my: '🇲🇾',
    ph: '🇵🇭',
  };

  return flagEmojis[countryCode] || '🌐';
}

// Static imports for React Native Metro bundler - only import files that actually exist
// This prevents ghost languages and ensures clean 1:1 mapping
function importTranslations(): Record<string, any> {
  const translations: Record<string, any> = {};

  // Import only the translation files that actually exist in /translations/
  // When you add a new language, add its import line here
  try {
    translations.en = require('../../../translations/en.json');
  } catch {}
  try {
    translations.es = require('../../../translations/es.json');
  } catch {}
  try {
    translations['pt-BR'] = require('../../../translations/pt-BR.json');
  } catch {}
  try {
    translations.fr = require('../../../translations/fr.json');
  } catch {}
  try {
    translations.de = require('../../../translations/de.json');
  } catch {}
  try {
    translations.it = require('../../../translations/it.json');
  } catch {}
  try {
    translations.ja = require('../../../translations/ja.json');
  } catch {}
  try {
    translations.ko = require('../../../translations/ko.json');
  } catch {}
  try {
    translations.ru = require('../../../translations/ru.json');
  } catch {}
  try {
    translations.nl = require('../../../translations/nl.json');
  } catch {}
  try {
    translations.da = require('../../../translations/da.json');
  } catch {}
  try {
    translations.pl = require('../../../translations/pl.json');
  } catch {}
  try {
    translations.tr = require('../../../translations/tr.json');
  } catch {}
  try {
    translations.el = require('../../../translations/el.json');
  } catch {}
  try {
    translations.hr = require('../../../translations/hr.json');
  } catch {}
  try {
    translations.sw = require('../../../translations/sw.json');
  } catch {}

  // TO ADD NEW LANGUAGES:
  // 1. Create the .json file in /translations/
  // 2. Add the import line here: try { translations.CODE = require('../../../translations/CODE.json'); } catch {}
  // 3. That's it! No other code changes needed.

  return translations;
}

// Cache for performance
let cachedLanguageResources: LanguageResource[] | null = null;
let cachedTranslations: Record<string, any> | null = null;

/**
 * Validates if a translation object has the minimum required keys
 */
function isValidTranslation(translations: any): boolean {
  if (!translations || typeof translations !== 'object') {
    return false;
  }

  // Check for essential translation keys that should exist in any valid translation
  const requiredKeys = ['common', 'auth', 'navigation'];
  return requiredKeys.some(key => key in translations);
}

/**
 * Gets all available languages that have valid translation files
 * Generates metadata dynamically for any language
 */
export function getAvailableLanguages(): LanguageResource[] {
  if (cachedLanguageResources) {
    return cachedLanguageResources;
  }

  if (!cachedTranslations) {
    cachedTranslations = importTranslations();
  }

  const availableLanguages: LanguageResource[] = [];

  Object.entries(cachedTranslations).forEach(([code, translations]) => {
    if (isValidTranslation(translations)) {
      // Generate metadata dynamically - works for ANY language code
      const metadata = generateLanguageMetadata(code, translations);

      availableLanguages.push({
        code,
        name: metadata.name,
        nativeName: metadata.nativeName,
        flag: metadata.flag,
        flagUrl: metadata.flagUrl,
        translations,
      });
    }
  });

  // Add English (UK) variant using English translations if en exists
  const enResource = availableLanguages.find(lang => lang.code === 'en');
  if (enResource && !availableLanguages.find(lang => lang.code === 'en-GB')) {
    const ukMetadata = generateLanguageMetadata('en-GB', enResource.translations);
    availableLanguages.push({
      code: 'en-GB',
      name: ukMetadata.name,
      nativeName: ukMetadata.nativeName,
      flag: ukMetadata.flag,
      flagUrl: ukMetadata.flagUrl,
      translations: enResource.translations,
    });
  }

  // Sort languages by English name for consistent ordering
  availableLanguages.sort((a, b) => a.name.localeCompare(b.name));

  cachedLanguageResources = availableLanguages;
  return availableLanguages;
}

/**
 * Gets supported language codes from available translations
 */
export function getSupportedLanguageCodes(): string[] {
  return getAvailableLanguages().map(lang => lang.code);
}

/**
 * Gets available language resources as a map for i18n configuration
 */
export function getLanguageResources(): Record<string, { translation: Record<string, any> }> {
  const resources: Record<string, { translation: Record<string, any> }> = {};

  getAvailableLanguages().forEach(language => {
    resources[language.code] = {
      translation: language.translations,
    };
  });

  return resources;
}

/**
 * Detects the best language based on device locale and available translations
 */
export function detectBestLanguage(): string {
  const deviceLocales = getLocales();
  const supportedCodes = getSupportedLanguageCodes();

  // First, try to find exact locale match
  for (const locale of deviceLocales) {
    if (supportedCodes.includes(locale.languageTag)) {
      return locale.languageTag;
    }
  }

  // Then try to find language match (ignore region)
  for (const locale of deviceLocales) {
    const languageCode = locale.languageCode || 'en';
    if (supportedCodes.includes(languageCode)) {
      return languageCode;
    }
  }

  // Finally, check for partial matches (e.g., 'en' for 'en-GB')
  for (const locale of deviceLocales) {
    const languageCode = locale.languageCode || 'en';
    const matchingCode = supportedCodes.find(
      code => code.startsWith(languageCode) || languageCode?.startsWith(code.split('-')[0])
    );
    if (matchingCode) {
      return matchingCode;
    }
  }

  // Default fallback
  return 'en';
}

/**
 * Gets a language resource by code
 */
export function getLanguageByCode(code: string): LanguageResource | undefined {
  return getAvailableLanguages().find(lang => lang.code === code);
}

/**
 * Checks if a language code is supported
 */
export function isLanguageSupported(code: string): boolean {
  return getSupportedLanguageCodes().includes(code);
}

/**
 * Refreshes the language cache (useful for hot reloading during development)
 */
export function refreshLanguageCache(): void {
  cachedLanguageResources = null;
  cachedTranslations = null;
}

/**
 * Gets statistics about available languages
 */
export function getLanguageStats(): {
  total: number;
  codes: string[];
  hasRequiredKeys: string[];
  missing: string[];
} {
  const available = getAvailableLanguages();
  const allTranslations = cachedTranslations || importTranslations();

  return {
    total: available.length,
    codes: available.map(lang => lang.code),
    hasRequiredKeys: available
      .filter(lang => isValidTranslation(lang.translations))
      .map(lang => lang.code),
    missing: Object.keys(allTranslations).filter(
      code => !available.find(lang => lang.code === code)
    ),
  };
}
