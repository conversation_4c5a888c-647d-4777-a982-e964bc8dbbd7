/**
 * Internationalization setup and initialization
 * This file should be imported at app startup to configure i18n and validation
 */
import {
  initializeLocalizedValidation,
  updateValidationLocale,
} from '@/src/shared/validation/localized';

import i18n from './config';

/**
 * Initialize i18n and validation localization
 * Call this function at app startup
 */
export function initializeI18n() {
  // Initialize localized validation with current language
  initializeLocalizedValidation();

  // Update validation locale when language changes
  i18n.on('languageChanged', () => {
    updateValidationLocale();
  });
}

/**
 * Utility to manually refresh validation locale
 * Useful when language changes or for testing
 */
export function refreshValidationLocale() {
  updateValidationLocale();
  console.log('🔄 Validation locale refreshed for language:', i18n.language);
}

/**
 * Get current locale information
 */
export function getCurrentLocaleInfo() {
  return {
    language: i18n.language,
    languages: i18n.languages,
    isInitialized: i18n.isInitialized,
  };
}
