/**
 * Core i18n exports for global MVP
 * Centralized internationalization and localization utilities
 */

// Main i18n configuration
export { default as i18n } from './config';

// Localization utilities
export {
  getUserTimezone,
  getUserLocales,
  getUserPrimaryLocale,
  getDateLocale,
  getNumberFormatOptions,
  formatDate,
  formatDateWithTimezone,
  formatRelativeTime,
  formatRelativeDate,
  toUserTimezone,
  toUtc,
  formatNumber,
  formatCurrency,
  formatPercentage,
  getLocalizedDateParts,
  isRTL,
  getTimeFormat,
  formatTime,
  getBusinessHours,
} from './localization';

// Re-export useTranslation hook for convenience
export { useTranslation } from 'react-i18next';

// Setup and initialization utilities
export { initializeI18n, refreshValidationLocale, getCurrentLocaleInfo } from './setup';
