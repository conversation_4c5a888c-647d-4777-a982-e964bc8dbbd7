/**
 * Tests for localized weekday functionality
 */
import { getLocalizedDayName, getLocalizedWeekdaysSync } from '../localization';

describe('Localization - Weekdays', () => {
  test('getLocalizedDayName should return correct day names', () => {
    // Test with English locale
    expect(getLocalizedDayName('monday', { locale: 'en-US' })).toBe('Monday');
    expect(getLocalizedDayName('tuesday', { locale: 'en-US', format: 'short' })).toBe('Tue');
    expect(getLocalizedDayName('wednesday', { locale: 'en-US', format: 'narrow' })).toBe('W');

    // Test with Spanish locale
    expect(getLocalizedDayName('monday', { locale: 'es-ES' })).toBe('lunes');
    expect(getLocalizedDayName('tuesday', { locale: 'es-ES', format: 'short' })).toBe('mar');

    // Test with numeric input
    expect(getLocalizedDayName(1, { locale: 'en-US' })).toBe('Monday'); // 1 = Monday
    expect(getLocalizedDayName(0, { locale: 'en-US' })).toBe('Sunday'); // 0 = Sunday
  });

  test('getLocalizedWeekdaysSync should return array of weekdays', () => {
    const weekdays = getLocalizedWeekdaysSync({ locale: 'en-US' });
    expect(weekdays).toHaveLength(7);
    expect(weekdays[0]).toBe('Monday'); // Should start with Monday by default
    expect(weekdays[6]).toBe('Sunday'); // Should end with Sunday

    const weekdaysShort = getLocalizedWeekdaysSync({
      locale: 'en-US',
      format: 'short',
    });
    expect(weekdaysShort[0]).toBe('Mon');
    expect(weekdaysShort[1]).toBe('Tue');
  });

  test('should handle startWithMonday option', () => {
    const weekdaysStartingSunday = getLocalizedWeekdaysSync({
      locale: 'en-US',
      startWithMonday: false,
    });
    expect(weekdaysStartingSunday[0]).toBe('Sunday');
    expect(weekdaysStartingSunday[1]).toBe('Monday');
  });
});
