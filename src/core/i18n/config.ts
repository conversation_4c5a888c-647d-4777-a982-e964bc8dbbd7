/**
 * Internationalization configuration for global MVP
 * Supports multiple languages, RTL, and regional preferences
 */
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

import { appStorage } from '../storage';
import { detectBestLanguage, getLanguageResources, isLanguageSupported } from './languageDetection';

// Get dynamic resources from language detection utility
const resources = getLanguageResources();

// Get user's preferred language with correct fallback behavior
function getInitialLanguage(): string {
  // // First priority: Check for explicitly saved language preference
  // const savedLanguage = appStorage.getString('user_language');
  // if (savedLanguage && isLanguageSupported(savedLanguage)) {
  //   console.log('Using saved language:', savedLanguage);
  //   return savedLanguage;
  // }

  // console.log('No saved language found, using dynamic detection');

  // Second priority: Use dynamic detection based on device locale
  return detectBestLanguage();
}

// Initialize i18n
i18n.use(initReactI18next).init({
  resources,
  lng: getInitialLanguage(),
  fallbackLng: 'en',
  debug: false,

  // Interpolation settings
  interpolation: {
    escapeValue: false, // React already escapes values
  },

  // React i18next settings
  react: {
    useSuspense: false, // Avoid suspense for better UX
  },

  // Pluralization support
  pluralSeparator: '_',
  contextSeparator: '_',

  // Keep full language tags to properly match resources
  load: 'currentOnly', // Use the exact language tag matching
  preload: ['en'], // Preload fallback language

  // Namespace settings
  defaultNS: 'translation',
  fallbackNS: 'translation',
});

// Save language preference when changed
i18n.on('languageChanged', lng => {
  appStorage.setString('user_language', lng);
});

export default i18n;
