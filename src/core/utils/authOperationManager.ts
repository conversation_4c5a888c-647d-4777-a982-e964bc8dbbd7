/**
 * Auth operation manager to prevent concurrent operations
 * Ensures only one sign out or refresh operation happens at a time
 */

class AuthOperationManager {
  private static instance: AuthOperationManager;
  private signOutInProgress = false;
  private refreshInProgress = false;
  private signOutPromise: Promise<void> | null = null;
  private refreshPromise: Promise<any> | null = null;

  static getInstance(): AuthOperationManager {
    if (!AuthOperationManager.instance) {
      AuthOperationManager.instance = new AuthOperationManager();
    }
    return AuthOperationManager.instance;
  }

  async executeSignOut(signOutFn: () => Promise<void>): Promise<void> {
    // If sign out is already in progress, return the existing promise
    if (this.signOutInProgress && this.signOutPromise) {
      console.log('⏸️ Sign out already in progress, waiting...');
      return this.signOutPromise;
    }

    this.signOutInProgress = true;

    // Create promise with guaranteed cleanup
    this.signOutPromise = Promise.resolve()
      .then(() => signOutFn())
      .catch(error => {
        console.error('❌ Sign out error:', error);
        throw error;
      })
      .finally(() => {
        // Always reset flags
        this.signOutInProgress = false;
        this.signOutPromise = null;
        console.log('✅ Sign out flags reset');
      });

    return this.signOutPromise;
  }

  async executeRefresh<T>(refreshFn: () => Promise<T>): Promise<T> {
    // If refresh is already in progress, return the existing promise
    if (this.refreshInProgress && this.refreshPromise) {
      console.log('⏸️ Token refresh already in progress, waiting...');
      return this.refreshPromise;
    }

    this.refreshInProgress = true;

    // Add timeout to prevent stuck operations
    const timeoutMs = 30000; // 30 seconds
    let timeoutId: NodeJS.Timeout;

    const timeoutPromise = new Promise<never>((_, reject) => {
      timeoutId = setTimeout(() => {
        console.error('⏰ Refresh operation timed out after 30 seconds - force resetting');
        this.forceReset();
        reject(new Error('Refresh operation timed out'));
      }, timeoutMs);
    });

    // Create promise with guaranteed cleanup
    this.refreshPromise = Promise.race([
      Promise.resolve()
        .then(() => {
          console.log('🔄 Starting refresh operation...');
          return refreshFn();
        })
        .then(result => {
          console.log('✅ Refresh completed successfully');
          clearTimeout(timeoutId);
          return result;
        })
        .catch(error => {
          console.error('❌ Refresh error:', error);
          clearTimeout(timeoutId);
          throw error;
        }),
      timeoutPromise,
    ]).finally(() => {
      // Always reset flags - this is critical
      this.refreshInProgress = false;
      this.refreshPromise = null;
      console.log('✅ Refresh flags reset');
      clearTimeout(timeoutId);
    });

    return this.refreshPromise;
  }

  isSignOutInProgress(): boolean {
    return this.signOutInProgress;
  }

  isRefreshInProgress(): boolean {
    return this.refreshInProgress;
  }

  // Get current state for debugging
  getState() {
    return {
      isSigningOut: this.signOutInProgress,
      isRefreshing: this.refreshInProgress,
    };
  }

  // Force reset for emergency use
  forceReset(): void {
    console.warn('⚠️ Force resetting auth operation flags');
    this.signOutInProgress = false;
    this.refreshInProgress = false;
    this.signOutPromise = null;
    this.refreshPromise = null;
  }

  reset(): void {
    this.signOutInProgress = false;
    this.refreshInProgress = false;
    this.signOutPromise = null;
    this.refreshPromise = null;
  }
}

export const authOperationManager = AuthOperationManager.getInstance();
