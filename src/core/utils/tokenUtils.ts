import { jwtDecode } from 'jwt-decode';

interface JWTPayload {
  exp?: number;
  iat?: number;
  sub?: string;
  [key: string]: any;
}

/**
 * Decode and extract information from JWT token
 */
export function decodeToken(token: string): JWTPayload | null {
  try {
    return jwtDecode<JWTPayload>(token);
  } catch (error) {
    console.error('Failed to decode token:', error);
    return null;
  }
}

/**
 * Get token expiration time in milliseconds
 */
export function getTokenExpiry(token: string): number | null {
  const decoded = decodeToken(token);
  if (!decoded?.exp) {
    return null;
  }

  // JWT exp is in seconds, convert to milliseconds
  return decoded.exp * 1000;
}

/**
 * Check if token is expired
 */
export function isTokenExpired(token: string): boolean {
  const expiry = getTokenExpiry(token);
  if (!expiry) {
    // If we can't determine expiry, consider it expired
    return true;
  }

  return Date.now() >= expiry;
}

/**
 * Get time until token expires in milliseconds
 */
export function getTimeUntilExpiry(token: string): number {
  const expiry = getTokenExpiry(token);
  if (!expiry) {
    return 0;
  }

  const timeUntilExpiry = expiry - Date.now();
  return Math.max(0, timeUntilExpiry);
}

/**
 * Check if token needs refresh (5 minutes before expiry)
 */
export function shouldRefreshToken(token: string, bufferMinutes = 5): boolean {
  const timeUntilExpiry = getTimeUntilExpiry(token);
  const bufferMs = bufferMinutes * 60 * 1000;

  return timeUntilExpiry <= bufferMs;
}

/**
 * Calculate when to schedule token refresh
 */
export function calculateRefreshTime(token: string, bufferMinutes = 5): number | null {
  const expiry = getTokenExpiry(token);
  if (!expiry) {
    return null;
  }

  const bufferMs = bufferMinutes * 60 * 1000;
  const refreshTime = expiry - bufferMs - Date.now();

  // If already past refresh time, refresh immediately
  return Math.max(0, refreshTime);
}
