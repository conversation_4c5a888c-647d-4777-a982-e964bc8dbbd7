import * as BackgroundTask from 'expo-background-task';
import * as TaskManager from 'expo-task-manager';

import { authService } from '@/src/features/auth/services/auth.service';
import { useAuthStore } from '@/src/features/auth/store/authStore';

import { secureStorage } from '../storage';
import { getTimeUntilExpiry, shouldRefreshToken } from '../utils/tokenUtils';

// Task name for token refresh
const TOKEN_REFRESH_TASK_NAME = 'movuca-token-refresh';

// Minimum interval for background fetch (15 minutes on iOS)
const BACKGROUND_FETCH_INTERVAL = 15 * 60; // 15 minutes in seconds

/**
 * Define the background task for token refresh
 */
TaskManager.defineTask(TOKEN_REFRESH_TASK_NAME, async () => {
  try {
    console.log('🔄 [Background] Token refresh task started');

    // Check if user is authenticated
    const tokens = secureStorage.getObject<{
      accessToken: string;
      refreshToken: string;
      expiresIn: number;
    }>('auth.tokens');

    if (!tokens?.accessToken) {
      console.log('❌ [Background] No token found, skipping refresh');
      return BackgroundTask.BackgroundTaskResult.Failed;
    }

    // Check if token needs refresh
    if (!shouldRefreshToken(tokens.accessToken, 10)) {
      // 10 minutes buffer for background
      const timeUntilExpiry = getTimeUntilExpiry(tokens.accessToken);
      console.log(`⏰ [Background] Token still valid for ${timeUntilExpiry / 1000 / 60} minutes`);
      return BackgroundTask.BackgroundTaskResult.Failed;
    }

    // Perform token refresh
    console.log('🔄 [Background] Refreshing token...');

    try {
      const newTokens = await authService.refreshToken();

      if (newTokens) {
        console.log('✅ [Background] Token refreshed successfully');

        // Update auth store if app is running
        const authStore = useAuthStore.getState();
        if (authStore) {
          authStore.setTokens(newTokens);
        }

        return BackgroundTask.BackgroundTaskResult.Success;
      } else {
        // Null return means invalid refresh token
        console.error('❌ [Background] Invalid refresh token - clearing auth');

        // Clear auth state
        const authStore = useAuthStore.getState();
        if (authStore) {
          authStore.clearAuth();
        }

        return BackgroundTask.BackgroundTaskResult.Failed;
      }
    } catch (refreshError: any) {
      // Check if it's a network error
      if (refreshError.networkError) {
        console.warn('⚠️ [Background] Network error during refresh - will retry later');
      } else {
        console.error('❌ [Background] Token refresh error:', refreshError.message);
      }
      return BackgroundTask.BackgroundTaskResult.Failed;
    }
  } catch (error) {
    console.error('❌ [Background] Error in token refresh task:', error);
    return BackgroundTask.BackgroundTaskResult.Failed;
  }
});

/**
 * Background task manager for token refresh
 */
export class TokenRefreshBackgroundManager {
  /**
   * Register the background task
   */
  static register = async (): Promise<void> => {
    try {
      // Check if task is already registered
      const isRegistered = await TaskManager.isTaskRegisteredAsync(TOKEN_REFRESH_TASK_NAME);

      if (isRegistered) {
        console.log('✅ Token refresh task already registered');
        return;
      }

      // Register background fetch
      await BackgroundTask.registerTaskAsync(TOKEN_REFRESH_TASK_NAME, {
        minimumInterval: BACKGROUND_FETCH_INTERVAL,
      });

      console.log('✅ Token refresh background task registered');

      // Get initial status
      const status = await BackgroundTask.getStatusAsync();
      console.log('📱 Background fetch status:', this.getStatusString(status));
    } catch (error) {
      console.error('❌ Failed to register token refresh task:', error);
    }
  };

  /**
   * Unregister the background task
   */
  static unregister = async (): Promise<void> => {
    try {
      // First check if the task exists
      const isRegistered = await TaskManager.isTaskRegisteredAsync(TOKEN_REFRESH_TASK_NAME);

      if (!isRegistered) {
        console.log('ℹ️ Token refresh task not registered, skipping unregister');
        return;
      }

      // Try to unregister with a try-catch to handle platform-specific issues
      try {
        await BackgroundTask.unregisterTaskAsync(TOKEN_REFRESH_TASK_NAME);
        console.log('✅ Token refresh background task unregistered');
      } catch (unregisterError: any) {
        // Check if it's a TaskNotFoundException
        if (
          unregisterError.message?.includes('TaskNotFoundException') ||
          unregisterError.code === 'ERR_TASK_NOT_FOUND'
        ) {
          console.log('ℹ️ Task was already unregistered or never existed');
        } else {
          // Re-throw other errors
          throw unregisterError;
        }
      }
    } catch (error: any) {
      // Log but don't throw - unregistering is not critical
      console.warn('⚠️ Could not check/unregister background task:', error.message);
    }
  };

  /**
   * Check if background fetch is available
   */
  static isAvailable = async (): Promise<boolean> => {
    const status = await BackgroundTask.getStatusAsync();
    return status === BackgroundTask.BackgroundTaskStatus.Available;
  };

  /**
   * Get background fetch status
   */
  static getStatus = async (): Promise<BackgroundTask.BackgroundTaskStatus> => {
    return await BackgroundTask.getStatusAsync();
  };

  /**
   * Convert status to readable string
   */
  static getStatusString = (status: BackgroundTask.BackgroundTaskStatus): string => {
    switch (status) {
      case BackgroundTask.BackgroundTaskStatus.Restricted:
        return 'Restricted';
      case BackgroundTask.BackgroundTaskStatus.Available:
        return 'Available';
      default:
        return 'Unknown';
    }
  };

  /**
   * Request background permissions (iOS only)
   */
  static requestPermissions = async (): Promise<boolean> => {
    try {
      // On iOS, this will prompt the user
      // On Android, permissions are granted at install time
      const status = await BackgroundTask.getStatusAsync();
      if (status === BackgroundTask.BackgroundTaskStatus.Restricted) {
        console.warn('⚠️ Background fetch restricted by system');
        return false;
      }

      return true;
    } catch (error) {
      console.error('❌ Error requesting background permissions:', error);
      return false;
    }
  };

  /**
   * Manually trigger the background task (for testing)
   */
  static testTask = async (): Promise<void> => {
    try {
      console.log('🧪 Manually triggering token refresh task...');

      // This will only work in development
      if (__DEV__) {
        await TaskManager.getTaskOptionsAsync(TOKEN_REFRESH_TASK_NAME);
        // The task will be executed
        console.log('✅ Test task triggered');
      } else {
        console.warn('⚠️ Manual task trigger only works in development');
      }
    } catch (error) {
      console.error('❌ Failed to trigger test task:', error);
    }
  };
}

// Export for convenience
export const registerTokenRefreshTask = TokenRefreshBackgroundManager.register;
export const unregisterTokenRefreshTask = TokenRefreshBackgroundManager.unregister;
