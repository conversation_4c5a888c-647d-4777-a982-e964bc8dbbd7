import { useEffect, useRef } from 'react';
import { AppState, AppStateStatus } from 'react-native';

import { authService } from '@/src/features/auth/services/auth.service';
import { useAuthStore } from '@/src/features/auth/store/authStore';
import { showToast } from '@/src/core/libs/toast';
import { useTokenRefresh } from './useTokenRefresh';

// Global state to prevent multiple instances
let appStateManagerInitialized = false;
let refreshInProgress = false;

/**
 * Hook to manage app state changes and handle token refresh
 * Consolidates all app state logic in one place
 */
export function useAppStateManager() {
  const { isAuthenticated, signOut } = useAuthStore();
  const { refreshNow } = useTokenRefresh();
  const appState = useRef(AppState.currentState);
  const lastRefreshTime = useRef<number>(0);

  useEffect(() => {
    // Prevent multiple instances
    if (appStateManagerInitialized) {
      console.log('⚠️ App state manager already initialized, skipping');
      return;
    }
    
    appStateManagerInitialized = true;
    let mounted = true;
    
    // Minimum time between refresh attempts (5 minutes)
    const MIN_REFRESH_INTERVAL = 5 * 60 * 1000;

    const handleAppStateChange = async (nextAppState: AppStateStatus) => {
      if (!mounted) return;
      // Log state transition
      console.log(`📱 App state: ${appState.current} → ${nextAppState}`);
      
      // Only handle when app comes to foreground
      if (
        appState.current.match(/inactive|background/) &&
        nextAppState === 'active' &&
        isAuthenticated
      ) {
        console.log('📱 App became active - checking token status');
        
        // Check if enough time has passed since last refresh
        const now = Date.now();
        const timeSinceLastRefresh = now - lastRefreshTime.current;
        
        if (timeSinceLastRefresh < MIN_REFRESH_INTERVAL) {
          console.log(`⏰ Skipping refresh - only ${Math.round(timeSinceLastRefresh / 1000)}s since last refresh`);
          appState.current = nextAppState;
          return;
        }
        
        if (refreshInProgress) {
          console.log('⏸️ App state refresh already in progress');
          appState.current = nextAppState;
          return;
        }
        
        refreshInProgress = true;

        try {
          const newTokens = await refreshNow();
          
          if (newTokens) {
            lastRefreshTime.current = now;
            console.log('✅ Token refreshed on app activation');
          }
        } catch (error: any) {
          console.error('❌ [Foreground] Token refresh failed:', error);
          
          // Check if it's an auth error (not network error)
          if (!error.networkError && error.message?.includes('No refresh token')) {
            // Invalid auth state - sign out
            showToast({
              type: 'error',
              text1: 'Session Expired',
              text2: 'Please login again to continue',
            });
            
            await signOut();
          }
        } finally {
          refreshInProgress = false;
        }
      }

      appState.current = nextAppState;
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);

    return () => {
      mounted = false;
      appStateManagerInitialized = false;
      refreshInProgress = false;
      subscription.remove();
    };
  }, [isAuthenticated, refreshNow, signOut]);
}
