import { useEffect } from 'react';

import { authService } from '@/src/features/auth/services/auth.service';
import { useAuthStore } from '@/src/features/auth/store/authStore';

import {
  TokenRefreshBackgroundManager,
  registerTokenRefreshTask,
  unregisterTokenRefreshTask,
} from '../services/tokenRefreshBackground';
import { secureStorage } from '../storage';
import { authOperationManager } from '../utils/authOperationManager';
import { getTimeUntilExpiry, shouldRefreshToken } from '../utils/tokenUtils';

/**
 * Hook to manage token refresh
 * Handles background task registration and provides manual refresh
 */
export function useTokenRefresh() {
  const { isAuthenticated, tokens } = useAuthStore();

  useEffect(() => {
    let intervalId: NodeJS.Timeout | null | number = null;
    let mounted = true;

    const setupTokenRefresh = async () => {
      if (!mounted) return;

      if (!isAuthenticated || !tokens) {
        // Unregister background task if user is not authenticated
        await unregisterTokenRefreshTask();
        return;
      }

      // Register background task for token refresh
      const hasPermission = await TokenRefreshBackgroundManager.requestPermissions();
      if (hasPermission) {
        await registerTokenRefreshTask();
        console.log('✅ Background token refresh registered');
      } else {
        console.warn('⚠️ Background refresh not available, using foreground refresh only');
      }

      // Also set up foreground refresh for immediate needs
      const checkAndRefreshToken = async () => {
        if (!mounted) {
          return;
        }

        // Check if refresh is already in progress via AuthOperationManager
        const { isRefreshing } = authOperationManager.getState();
        if (isRefreshing) {
          console.log('⏸️ [Foreground] Refresh already in progress via AuthOperationManager');
          return;
        }

        const tokenData = secureStorage.getObject<{
          accessToken: string;
          refreshToken: string;
          expiresIn: number;
        }>('auth.tokens');

        if (!tokenData?.accessToken) return;

        // Check if token needs refresh (5 minutes buffer for foreground)
        if (shouldRefreshToken(tokenData.accessToken, 5)) {
          try {
            console.log('🔄 [Foreground] Token needs refresh - checking state first');
            const currentState = authOperationManager.getState();
            console.log('🔍 [Foreground] AuthOperationManager state:', currentState);

            await authOperationManager.executeRefresh(async () => {
              console.log('🔄 [Foreground] Inside executeRefresh callback');

              // Double-check mounted state
              if (!mounted) {
                console.log('⚠️ [Foreground] Component unmounted during refresh, aborting');
                return false;
              }

              const newTokens = await authService.refreshToken();

              if (newTokens) {
                console.log('✅ [Foreground] Token refreshed successfully');
                return true;
              } else {
                // Null return means invalid refresh token
                console.error(
                  '❌ [Foreground] Invalid refresh token - already cleared by auth service'
                );
                // Auth state already cleared by authService.refreshToken()
                // Just return false to indicate failure
                return false;
              }
            });
          } catch (error: any) {
            if (error.networkError) {
              console.warn('⚠️ [Foreground] Network error during refresh - will retry later');
            } else {
              console.error('❌ [Foreground] Token refresh failed:', error);
            }
          }
        } else {
          const timeUntilExpiry = getTimeUntilExpiry(tokenData.accessToken);
          console.log(
            `⏰ [Foreground] Token valid for ${Math.round(timeUntilExpiry / 1000 / 60)} minutes`
          );
        }
      };

      // Check immediately
      await checkAndRefreshToken();

      // Set up periodic check while app is in foreground (every 5 minutes)
      intervalId = setInterval(checkAndRefreshToken, 5 * 60 * 1000);
    };

    setupTokenRefresh();

    // Cleanup
    return () => {
      mounted = false;

      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [isAuthenticated, tokens]);

  // Manual refresh function
  const refreshNow = async () => {
    console.log('🔄 refreshNow called - checking AuthOperationManager state');
    const currentState = authOperationManager.getState();
    console.log('🔍 Current state before refresh:', currentState);

    return authOperationManager.executeRefresh(async () => {
      console.log('🔄 Manual token refresh requested - inside executeRefresh');
      const newTokens = await authService.refreshToken();

      if (!newTokens) {
        // Null return means invalid refresh token
        console.error('❌ Manual refresh failed - invalid token');
        // Auth state already cleared by authService.refreshToken()
        throw new Error('Invalid refresh token');
      }

      console.log('✅ Manual refresh completed successfully');
      return newTokens;
    });
  };

  // Test background task (dev only)
  const testBackgroundTask = async () => {
    if (__DEV__) {
      await TokenRefreshBackgroundManager.testTask();
    }
  };

  // Get background task status
  const getBackgroundStatus = async () => {
    const status = await TokenRefreshBackgroundManager.getStatus();
    const statusString = TokenRefreshBackgroundManager.getStatusString(status);
    const isAvailable = await TokenRefreshBackgroundManager.isAvailable();

    return {
      status,
      statusString,
      isAvailable,
    };
  };

  return {
    refreshNow,
    testBackgroundTask,
    getBackgroundStatus,
  };
}
