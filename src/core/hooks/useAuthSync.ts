import { useEffect } from 'react';

import { AppState, AppStateStatus } from 'react-native';

import { useAuthStore } from '@/src/features/auth/store/authStore';

import { secureStorage } from '../storage';

/**
 * Hook to sync auth store with secure storage
 * This ensures that if tokens are cleared directly from storage,
 * the auth store state is updated accordingly
 */
export function useAuthSync() {
  useEffect(() => {
    // Function to check and sync auth state
    const syncAuthState = () => {
      const storedTokens = secureStorage.getObject('auth.tokens');
      const storedUser = secureStorage.getObject('auth.user');
      const authStore = useAuthStore.getState();

      // If storage is empty but store thinks we're authenticated
      if (!storedTokens && authStore.isAuthenticated) {
        console.warn('🔄 Auth sync: Detected auth mismatch - clearing store');
        authStore.clearAuth();
      }

      // If storage has tokens but store doesn't
      if (storedTokens && !authStore.tokens) {
        console.warn('🔄 Auth sync: Detected tokens in storage but not in store');
        authStore.setTokens(storedTokens);
        if (storedUser) {
          authStore.setUser(storedUser);
        }
      }
    };

    // Check on mount
    syncAuthState();

    // Check when app comes to foreground
    const subscription = AppState.addEventListener('change', (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active') {
        syncAuthState();
      }
    });

    // Check periodically (every 5 seconds) while app is active
    const interval = setInterval(() => {
      if (AppState.currentState === 'active') {
        syncAuthState();
      }
    }, 5000);

    return () => {
      subscription.remove();
      clearInterval(interval);
    };
  }, []);
}
