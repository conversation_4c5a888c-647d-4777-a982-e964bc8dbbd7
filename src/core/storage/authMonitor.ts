import { MMKV } from 'react-native-mmkv';

import { useAuthStore } from '@/src/features/auth/store/authStore';

// Create a dedicated MMKV instance for auth monitoring
const authMonitor = new MMKV({ id: 'auth-monitor' });

// Storage event listener for auth changes
export function setupAuthStorageListener() {
  const listener = authMonitor.addOnValueChangedListener(key => {
    if (key === 'auth.tokens' || key === 'auth.user') {
      console.log(`🔄 Storage change detected for ${key}`);

      // Get the current values from storage
      const tokensStr = authMonitor.getString('auth.tokens');
      const userStr = authMonitor.getString('auth.user');

      const tokens = tokensStr ? JSON.parse(tokensStr) : null;
      const user = userStr ? JSON.parse(userStr) : null;

      const authStore = useAuthStore.getState();

      // Update the store based on storage
      if (!tokens && authStore.isAuthenticated) {
        console.warn('🔄 Auth storage cleared - updating store');
        authStore.setTokens(null);
        authStore.setUser(null);
      }
    }
  });

  return () => {
    // Clean up listener
    listener.remove();
  };
}
