/**
 * MMKV Storage Integration Layer
 * Provides a clean abstraction over MMKV for consistent usage across the app
 */
import { MMKV } from 'react-native-mmkv';

/**
 * Storage interface for testing and potential future storage provider swapping
 */
export interface IStorage {
  getString(key: string): string | undefined;
  getNumber(key: string): number | undefined;
  getBoolean(key: string): boolean | undefined;
  getObject<T>(key: string): T | undefined;
  setString(key: string, value: string): void;
  setNumber(key: string, value: number): void;
  setBoolean(key: string, value: boolean): void;
  setObject<T>(key: string, value: T): void;
  delete(key: string): void;
  clearAll(): void;
  getAllKeys(): string[];
  contains(key: string): boolean;
}

/**
 * MMKV Storage Implementation
 */
class MMKVStorage implements IStorage {
  private storage: MMKV;

  constructor(id: string = 'default') {
    this.storage = new MMKV({ id });
  }

  getString(key: string): string | undefined {
    return this.storage.getString(key);
  }

  getNumber(key: string): number | undefined {
    return this.storage.getNumber(key);
  }

  getBoolean(key: string): boolean | undefined {
    return this.storage.getBoolean(key);
  }

  getObject<T>(key: string): T | undefined {
    const value = this.storage.getString(key);
    if (!value) return undefined;

    try {
      return JSON.parse(value) as T;
    } catch (error) {
      console.warn(`Failed to parse object from storage key "${key}":`, error);
      return undefined;
    }
  }

  setString(key: string, value: string): void {
    this.storage.set(key, value);
  }

  setNumber(key: string, value: number): void {
    this.storage.set(key, value);
  }

  setBoolean(key: string, value: boolean): void {
    this.storage.set(key, value);
  }

  setObject<T>(key: string, value: T): void {
    try {
      const serialized = JSON.stringify(value);
      this.storage.set(key, serialized);
    } catch (error) {
      console.error(`Failed to serialize object for storage key "${key}":`, error);
    }
  }

  delete(key: string): void {
    this.storage.delete(key);
  }

  clearAll(): void {
    this.storage.clearAll();
  }

  getAllKeys(): string[] {
    return this.storage.getAllKeys();
  }

  contains(key: string): boolean {
    return this.storage.contains(key);
  }

  /**
   * Get the raw MMKV instance
   * Needed for compatibility with react-native-mmkv hooks like useMMKVObject
   */
  getMMKVInstance(): MMKV {
    return this.storage;
  }

  /**
   * Get storage statistics
   */
  getStorageInfo(): { size: number; keyCount: number } {
    const keys = this.getAllKeys();
    return {
      size: 0, // MMKV doesn't provide size info directly
      keyCount: keys.length,
    };
  }

  /**
   * Batch operations for performance
   */
  batch(operations: { type: 'set' | 'delete'; key: string; value?: any }[]) {
    operations.forEach(op => {
      if (op.type === 'set') {
        if (typeof op.value === 'string') {
          this.setString(op.key, op.value);
        } else if (typeof op.value === 'number') {
          this.setNumber(op.key, op.value);
        } else if (typeof op.value === 'boolean') {
          this.setBoolean(op.key, op.value);
        } else {
          this.setObject(op.key, op.value);
        }
      } else if (op.type === 'delete') {
        this.delete(op.key);
      }
    });
  }
}

/**
 * Storage instances for different use cases
 */
export const appStorage = new MMKVStorage('app-storage');
export const cacheStorage = new MMKVStorage('cache-storage');
export const secureStorage = new MMKVStorage('secure-storage');
export const userStorage = new MMKVStorage('user-storage');

/**
 * Storage factory for creating custom instances
 */
export const createStorage = (id: string): IStorage => new MMKVStorage(id);

/**
 * Zustand MMKV storage adapter
 */
export const createZustandMMKVStorage = (storage: IStorage) => ({
  setItem: (key: string, value: string) => storage.setString(key, value),
  getItem: (key: string) => storage.getString(key) ?? null,
  removeItem: (key: string) => storage.delete(key),
});
