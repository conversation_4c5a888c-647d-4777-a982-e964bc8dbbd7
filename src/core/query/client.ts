import { QueryClient } from '@tanstack/react-query';

/**
 * TanStack Query client configuration
 * Handles operations with individual loading/error states
 */
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
      retry: (failureCount, error) => {
        // Don't retry auth errors
        if (error && typeof error === 'object' && 'status' in error) {
          const status = (error as any).status;
          if (status === 401 || status === 403) return false;
        }
        return failureCount < 2;
      },
    },
    mutations: {
      retry: false, // Don't retry operations by default
    },
  },
});
