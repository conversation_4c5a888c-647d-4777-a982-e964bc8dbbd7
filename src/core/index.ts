/**
 * Core module exports for global MVP
 * Centralized access to all core functionality
 */

// Storage layer
export * from './storage';

// Security utilities
export * from './security';

// API client and caching
export { apiClient, APIError, TimeoutError, apiCache, CACHE_CATEGORIES } from './api';
export type {
  RequestConfig,
  APIResponse,
  HTTPMethod,
  CacheEntry,
  CacheMetadata,
  CacheCategory,
} from './api';

// Internationalization
export * from './i18n';

// Third-party integrations
export * from './libs';
