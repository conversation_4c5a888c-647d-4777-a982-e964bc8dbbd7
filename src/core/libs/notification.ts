/**
 * Notification integration - provides push notifications using Notifee
 * Handles local notifications, push notifications, and notification channels
 */
import { Platform } from 'react-native';

import notifee, {
  AndroidBadgeIconType,
  AndroidChannel,
  AndroidColor,
  AndroidImportance,
  AndroidStyle,
  AndroidVisibility,
  AuthorizationStatus,
  Event,
  EventType,
  IOSForegroundPresentationOptions,
  Notification,
  RepeatFrequency,
  TimestampTrigger,
  TriggerType,
} from '@notifee/react-native';

export interface NotificationChannel {
  id: string;
  name: string;
  description?: string;
  importance?: AndroidImportance;
  visibility?: AndroidVisibility;
  sound?: string;
  vibration?: boolean;
  badge?: boolean;
  lights?: boolean;
  lightColor?: string;
}

export interface NotificationAction {
  id: string;
  title: string;
  icon?: string;
  destructive?: boolean;
  authenticationRequired?: boolean;
  foreground?: boolean;
}

export interface NotificationData {
  [key: string]: string | number | boolean;
}

export interface BaseNotificationOptions {
  id?: string;
  title: string;
  body: string;
  subtitle?: string;
  data?: NotificationData;
  badge?: number;
  sound?: string;
  largeIcon?: string;
  smallIcon?: string;
  color?: string;
  category?: string;
  actions?: NotificationAction[];
}

export interface AndroidNotificationOptions extends BaseNotificationOptions {
  channelId?: string;
  importance?: AndroidImportance;
  visibility?: AndroidVisibility;
  autoCancel?: boolean;
  ongoing?: boolean;
  progress?: {
    max: number;
    current: number;
    indeterminate?: boolean;
  };
  bigText?: string;
  bigPicture?: string;
  style?: {
    type: AndroidStyle;
    text?: string;
    title?: string;
    summary?: string;
    picture?: string;
    largeIcon?: string;
  };
  lights?: {
    color: AndroidColor;
    onMs: number;
    offMs: number;
  };
  vibrationPattern?: number[];
  groupId?: string;
  groupSummary?: boolean;
  sortKey?: string;
  tag?: string;
}

export interface IOSNotificationOptions extends BaseNotificationOptions {
  critical?: boolean;
  criticalVolume?: number;
  interruptionLevel?: 'passive' | 'active' | 'timeSensitive' | 'critical';
  relevanceScore?: number;
  threadId?: string;
  targetContentId?: string;
  summaryArgument?: string;
  summaryArgumentCount?: number;
  foregroundPresentationOptions?: IOSForegroundPresentationOptions;
}

export interface ScheduledNotificationOptions {
  trigger: {
    type: 'timestamp';
    timestamp: number;
    repeatFrequency?: RepeatFrequency;
    alarmManager?: {
      allowWhileIdle?: boolean;
      exact?: boolean;
    };
  };
}

export interface NotificationPermissions {
  authorizationStatus: AuthorizationStatus;
  settings: {
    alert: boolean;
    badge: boolean;
    sound: boolean;
    carPlay: boolean;
    criticalAlert: boolean;
    provisional: boolean;
    providesAppNotificationSettings: boolean;
    lockScreen: boolean;
    notificationCenter: boolean;
  };
}

export interface NotificationEventHandler {
  onNotificationReceived?: (notification: Notification) => void;
  onNotificationPressed?: (notification: Notification) => void;
  onActionPressed?: (action: { actionId: string; notification: Notification }) => void;
  onBackgroundEvent?: (event: Event) => void;
}

export interface NotificationIntegration {
  // Permissions
  requestPermission(): Promise<NotificationPermissions>;
  getPermissions(): Promise<NotificationPermissions>;
  openSettings(): Promise<void>;

  // Channels (Android)
  createChannel(channel: NotificationChannel): Promise<string>;
  createChannels(channels: NotificationChannel[]): Promise<void>;
  deleteChannel(channelId: string): Promise<void>;
  getChannels(): Promise<AndroidChannel[]>;

  // Basic notifications
  displayNotification(options: BaseNotificationOptions): Promise<string>;
  displayAndroidNotification(options: AndroidNotificationOptions): Promise<string>;
  displayIOSNotification(options: IOSNotificationOptions): Promise<string>;

  // Scheduled notifications
  scheduleNotification(
    options: BaseNotificationOptions & ScheduledNotificationOptions
  ): Promise<string>;
  cancelNotification(notificationId: string): Promise<void>;
  cancelAllNotifications(): Promise<void>;
  getScheduledNotifications(): Promise<Notification[]>;

  // Badge management
  setBadgeCount(count: number): Promise<void>;
  getBadgeCount(): Promise<number>;
  incrementBadge(): Promise<void>;
  decrementBadge(): Promise<void>;

  // Event handlers
  setEventHandlers(handlers: NotificationEventHandler): () => void;

  // Utility
  isNotificationDisplayed(notificationId: string): Promise<boolean>;
  getDisplayedNotifications(): Promise<Notification[]>;
  cancelDisplayedNotification(notificationId: string): Promise<void>;
  cancelDisplayedNotifications(notificationIds: string[]): Promise<void>;
}

class NotificationIntegrationImpl implements NotificationIntegration {
  private eventUnsubscribers: (() => void)[] = [];

  async requestPermission(): Promise<NotificationPermissions> {
    try {
      const settings = await notifee.requestPermission({
        alert: true,
        badge: true,
        sound: true,
        provisional: false,
        criticalAlert: false,
      });

      return {
        authorizationStatus: settings.authorizationStatus,
        settings: {
          alert: settings.ios?.alert === 1,
          badge: settings.ios?.badge === 1,
          sound: settings.ios?.sound === 1,
          carPlay: settings.ios?.carPlay === 1,
          criticalAlert: settings.ios?.criticalAlert === 1,
          provisional: settings.ios?.provisional === 1,
          providesAppNotificationSettings: settings.ios?.providesAppNotificationSettings === 1,
          lockScreen: settings.ios?.lockScreen === 1,
          notificationCenter: settings.ios?.notificationCenter === 1,
        },
      };
    } catch (error) {
      console.warn('Error requesting notification permission:', error);
      throw error;
    }
  }

  async getPermissions(): Promise<NotificationPermissions> {
    try {
      const settings = await notifee.getNotificationSettings();

      return {
        authorizationStatus: settings.authorizationStatus,
        settings: {
          alert: settings.ios?.alert === 1,
          badge: settings.ios?.badge === 1,
          sound: settings.ios?.sound === 1,
          carPlay: settings.ios?.carPlay === 1,
          criticalAlert: settings.ios?.criticalAlert === 1,
          provisional: settings.ios?.provisional === 1,
          providesAppNotificationSettings: settings.ios?.providesAppNotificationSettings === 1,
          lockScreen: settings.ios?.lockScreen === 1,
          notificationCenter: settings.ios?.notificationCenter === 1,
        },
      };
    } catch (error) {
      console.warn('Error getting notification permissions:', error);
      throw error;
    }
  }

  async openSettings(): Promise<void> {
    try {
      await notifee.openNotificationSettings();
    } catch (error) {
      console.warn('Error opening notification settings:', error);
      throw error;
    }
  }

  async createChannel(channel: NotificationChannel): Promise<string> {
    if (Platform.OS !== 'android') {
      return channel.id;
    }

    try {
      const channelId = await notifee.createChannel({
        id: channel.id,
        name: channel.name,
        description: channel.description,
        importance: channel.importance || AndroidImportance.DEFAULT,
        visibility: channel.visibility || AndroidVisibility.PRIVATE,
        sound: channel.sound || 'default',
        vibration: channel.vibration !== false,
        badge: channel.badge !== false,
        lights: channel.lights !== false,
        lightColor: channel.lightColor || AndroidColor.DEFAULT,
      });

      return channelId;
    } catch (error) {
      console.warn('Error creating notification channel:', error);
      throw error;
    }
  }

  async createChannels(channels: NotificationChannel[]): Promise<void> {
    if (Platform.OS !== 'android') {
      return;
    }

    try {
      const androidChannels = channels.map(channel => ({
        id: channel.id,
        name: channel.name,
        description: channel.description,
        importance: channel.importance || AndroidImportance.DEFAULT,
        visibility: channel.visibility || AndroidVisibility.PRIVATE,
        sound: channel.sound || 'default',
        vibration: channel.vibration !== false,
        badge: channel.badge !== false,
        lights: channel.lights !== false,
        lightColor: channel.lightColor || AndroidColor.DEFAULT,
      }));

      await notifee.createChannels(androidChannels);
    } catch (error) {
      console.warn('Error creating notification channels:', error);
      throw error;
    }
  }

  async deleteChannel(channelId: string): Promise<void> {
    if (Platform.OS !== 'android') {
      return;
    }

    try {
      await notifee.deleteChannel(channelId);
    } catch (error) {
      console.warn('Error deleting notification channel:', error);
      throw error;
    }
  }

  async getChannels(): Promise<AndroidChannel[]> {
    if (Platform.OS !== 'android') {
      return [];
    }

    try {
      return await notifee.getChannels();
    } catch (error) {
      console.warn('Error getting notification channels:', error);
      throw error;
    }
  }

  async displayNotification(options: BaseNotificationOptions): Promise<string> {
    try {
      const notification: Notification = {
        id: options.id,
        title: options.title,
        body: options.body,
        subtitle: options.subtitle,
        data: options.data || {},
        ...(Platform.OS === 'android' && {
          android: {
            channelId: 'default',
            smallIcon: options.smallIcon || 'ic_launcher',
            largeIcon: options.largeIcon,
            color: options.color,
            autoCancel: true,
            badge: options.badge,
            sound: options.sound || 'default',
            actions: options.actions?.map(action => ({
              title: action.title,
              pressAction: {
                id: action.id,
                launchActivity: action.foreground ? 'default' : undefined,
              },
              icon: action.icon,
            })),
          },
        }),
        ...(Platform.OS === 'ios' && {
          ios: {
            sound: options.sound || 'default',
            badge: options.badge,
            categoryId: options.category,
            foregroundPresentationOptions: {
              alert: true,
              badge: true,
              sound: true,
            },
          },
        }),
      };

      return await notifee.displayNotification(notification);
    } catch (error) {
      console.warn('Error displaying notification:', error);
      throw error;
    }
  }

  async displayAndroidNotification(options: AndroidNotificationOptions): Promise<string> {
    if (Platform.OS !== 'android') {
      return this.displayNotification(options);
    }

    try {
      const notification: Notification = {
        id: options.id,
        title: options.title,
        body: options.body,
        subtitle: options.subtitle,
        data: options.data || {},
        android: {
          channelId: options.channelId || 'default',
          importance: options.importance || AndroidImportance.DEFAULT,
          visibility: options.visibility || AndroidVisibility.PRIVATE,
          smallIcon: options.smallIcon || 'ic_launcher',
          largeIcon: options.largeIcon,
          color: options.color,
          autoCancel: options.autoCancel !== false,
          ongoing: options.ongoing || false,
          badge: options.badge,
          sound: options.sound || 'default',
          vibrationPattern: options.vibrationPattern,
          lights: options.lights,
          progress: options.progress,
          style: options.style,
          groupId: options.groupId,
          groupSummary: options.groupSummary,
          sortKey: options.sortKey,
          tag: options.tag,
          actions: options.actions?.map(action => ({
            title: action.title,
            pressAction: {
              id: action.id,
              launchActivity: action.foreground ? 'default' : undefined,
            },
            icon: action.icon,
          })),
        },
      };

      return await notifee.displayNotification(notification);
    } catch (error) {
      console.warn('Error displaying Android notification:', error);
      throw error;
    }
  }

  async displayIOSNotification(options: IOSNotificationOptions): Promise<string> {
    if (Platform.OS !== 'ios') {
      return this.displayNotification(options);
    }

    try {
      const notification: Notification = {
        id: options.id,
        title: options.title,
        body: options.body,
        subtitle: options.subtitle,
        data: options.data || {},
        ios: {
          sound: options.sound || 'default',
          badge: options.badge,
          categoryId: options.category,
          critical: options.critical,
          criticalVolume: options.criticalVolume,
          interruptionLevel: options.interruptionLevel,
          relevanceScore: options.relevanceScore,
          threadId: options.threadId,
          targetContentId: options.targetContentId,
          summaryArgument: options.summaryArgument,
          summaryArgumentCount: options.summaryArgumentCount,
          foregroundPresentationOptions: options.foregroundPresentationOptions || {
            alert: true,
            badge: true,
            sound: true,
          },
        },
      };

      return await notifee.displayNotification(notification);
    } catch (error) {
      console.warn('Error displaying iOS notification:', error);
      throw error;
    }
  }

  async scheduleNotification(
    options: BaseNotificationOptions & ScheduledNotificationOptions
  ): Promise<string> {
    try {
      const notification: Notification = {
        id: options.id,
        title: options.title,
        body: options.body,
        subtitle: options.subtitle,
        data: options.data || {},
        ...(Platform.OS === 'android' && {
          android: {
            channelId: 'default',
            smallIcon: options.smallIcon || 'ic_launcher',
            largeIcon: options.largeIcon,
            color: options.color,
            autoCancel: true,
            badge: options.badge,
            sound: options.sound || 'default',
          },
        }),
        ...(Platform.OS === 'ios' && {
          ios: {
            sound: options.sound || 'default',
            badge: options.badge,
            categoryId: options.category,
          },
        }),
      };

      const trigger: TimestampTrigger = {
        type: TriggerType.TIMESTAMP,
        timestamp: options.trigger.timestamp,
        repeatFrequency: options.trigger.repeatFrequency,
        alarmManager: options.trigger.alarmManager,
      };

      return await notifee.createTriggerNotification(notification, trigger);
    } catch (error) {
      console.warn('Error scheduling notification:', error);
      throw error;
    }
  }

  async cancelNotification(notificationId: string): Promise<void> {
    try {
      await notifee.cancelNotification(notificationId);
    } catch (error) {
      console.warn('Error canceling notification:', error);
      throw error;
    }
  }

  async cancelAllNotifications(): Promise<void> {
    try {
      await notifee.cancelAllNotifications();
    } catch (error) {
      console.warn('Error canceling all notifications:', error);
      throw error;
    }
  }

  async getScheduledNotifications(): Promise<Notification[]> {
    try {
      return await notifee.getTriggerNotifications();
    } catch (error) {
      console.warn('Error getting scheduled notifications:', error);
      throw error;
    }
  }

  async setBadgeCount(count: number): Promise<void> {
    try {
      await notifee.setBadgeCount(count);
    } catch (error) {
      console.warn('Error setting badge count:', error);
      throw error;
    }
  }

  async getBadgeCount(): Promise<number> {
    try {
      return await notifee.getBadgeCount();
    } catch (error) {
      console.warn('Error getting badge count:', error);
      return 0;
    }
  }

  async incrementBadge(): Promise<void> {
    try {
      await notifee.incrementBadgeCount();
    } catch (error) {
      console.warn('Error incrementing badge:', error);
      throw error;
    }
  }

  async decrementBadge(): Promise<void> {
    try {
      await notifee.decrementBadgeCount();
    } catch (error) {
      console.warn('Error decrementing badge:', error);
      throw error;
    }
  }

  setEventHandlers(handlers: NotificationEventHandler): () => void {
    // Clear existing handlers
    this.eventUnsubscribers.forEach(unsubscribe => unsubscribe());
    this.eventUnsubscribers = [];

    // Foreground event listener
    if (
      handlers.onNotificationReceived ||
      handlers.onNotificationPressed ||
      handlers.onActionPressed
    ) {
      const foregroundUnsubscribe = notifee.onForegroundEvent(({ type, detail }) => {
        switch (type) {
          case EventType.DISMISSED:
            // Notification dismissed
            break;
          case EventType.PRESS:
            if (handlers.onNotificationPressed && detail.notification) {
              handlers.onNotificationPressed(detail.notification);
            }
            break;
          case EventType.ACTION_PRESS:
            if (handlers.onActionPressed && detail.notification && detail.pressAction) {
              handlers.onActionPressed({
                actionId: detail.pressAction.id,
                notification: detail.notification,
              });
            }
            break;
          case EventType.DELIVERED:
            if (handlers.onNotificationReceived && detail.notification) {
              handlers.onNotificationReceived(detail.notification);
            }
            break;
        }
      });
      this.eventUnsubscribers.push(foregroundUnsubscribe);
    }

    // Background event listener
    if (handlers.onBackgroundEvent) {
      notifee.onBackgroundEvent(async event => {
        handlers.onBackgroundEvent?.(event);
      });
    }

    // Return cleanup function
    return () => {
      this.eventUnsubscribers.forEach(unsubscribe => unsubscribe());
      this.eventUnsubscribers = [];
    };
  }

  async isNotificationDisplayed(notificationId: string): Promise<boolean> {
    try {
      const notifications = await notifee.getDisplayedNotifications();
      return notifications.some(notification => notification.id === notificationId);
    } catch (error) {
      console.warn('Error checking if notification is displayed:', error);
      return false;
    }
  }

  async getDisplayedNotifications(): Promise<Notification[]> {
    try {
      return await notifee.getDisplayedNotifications();
    } catch (error) {
      console.warn('Error getting displayed notifications:', error);
      return [];
    }
  }

  async cancelDisplayedNotification(notificationId: string): Promise<void> {
    try {
      await notifee.cancelDisplayedNotification(notificationId);
    } catch (error) {
      console.warn('Error canceling displayed notification:', error);
      throw error;
    }
  }

  async cancelDisplayedNotifications(notificationIds: string[]): Promise<void> {
    try {
      await notifee.cancelDisplayedNotifications(notificationIds);
    } catch (error) {
      console.warn('Error canceling displayed notifications:', error);
      throw error;
    }
  }
}

export const notification = new NotificationIntegrationImpl();
