/**
 * Alert integration - provides native alert dialogs
 * Uses React Native Alert API with enhanced functionality
 */
import { Alert, AlertButton, AlertOptions } from 'react-native';

import { haptics } from './haptics';

export interface AlertAction {
  text: string;
  onPress?: () => void | Promise<void>;
  style?: 'default' | 'cancel' | 'destructive';
}

export interface AlertIntegrationOptions extends AlertOptions {
  haptic?: 'success' | 'warning' | 'error' | 'none';
}

export interface ConfirmOptions {
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm?: () => void | Promise<void>;
  onCancel?: () => void | Promise<void>;
  destructive?: boolean;
  haptic?: 'success' | 'warning' | 'error' | 'none';
}

export interface AlertIntegration {
  // Basic alert methods
  show(
    title: string,
    message?: string,
    actions?: AlertAction[],
    options?: AlertIntegrationOptions
  ): Promise<void>;

  // Convenience methods
  info(title: string, message?: string, options?: AlertIntegrationOptions): Promise<void>;
  success(title: string, message?: string, options?: AlertIntegrationOptions): Promise<void>;
  warning(title: string, message?: string, options?: AlertIntegrationOptions): Promise<void>;
  error(title: string, message?: string, options?: AlertIntegrationOptions): Promise<void>;

  // Confirmation dialogs
  confirm(options: ConfirmOptions): Promise<boolean>;

  // Destructive action confirmation
  confirmDestructive(options: ConfirmOptions): Promise<boolean>;

  // Simple yes/no dialog
  yesNo(title: string, message?: string, onYes?: () => void, onNo?: () => void): Promise<boolean>;
}

class AlertIntegrationImpl implements AlertIntegration {
  private async triggerHaptic(type?: 'success' | 'warning' | 'error' | 'none'): Promise<void> {
    if (type && type !== 'none') {
      try {
        await haptics[type]();
      } catch (error) {
        console.warn('Alert haptic feedback unavailable:', error);
      }
    }
  }

  async show(
    title: string,
    message?: string,
    actions: AlertAction[] = [],
    options: AlertIntegrationOptions = {}
  ): Promise<void> {
    await this.triggerHaptic(options.haptic);

    const alertButtons: AlertButton[] = actions.map(action => ({
      text: action.text,
      onPress: action.onPress,
      style: action.style || 'default',
    }));

    // If no actions provided, add default OK button
    if (alertButtons.length === 0) {
      alertButtons.push({
        text: 'OK',
        style: 'default',
      });
    }

    return new Promise(resolve => {
      Alert.alert(title, message, alertButtons, {
        ...options,
        onDismiss: () => {
          resolve();
          options.onDismiss?.();
        },
      });
    });
  }

  async info(
    title: string,
    message?: string,
    options: AlertIntegrationOptions = {}
  ): Promise<void> {
    await this.show(title, message, [{ text: 'OK' }], {
      ...options,
      haptic: options.haptic || 'none',
    });
  }

  async success(
    title: string,
    message?: string,
    options: AlertIntegrationOptions = {}
  ): Promise<void> {
    await this.show(title, message, [{ text: 'OK' }], {
      ...options,
      haptic: options.haptic || 'success',
    });
  }

  async warning(
    title: string,
    message?: string,
    options: AlertIntegrationOptions = {}
  ): Promise<void> {
    await this.show(title, message, [{ text: 'OK' }], {
      ...options,
      haptic: options.haptic || 'warning',
    });
  }

  async error(
    title: string,
    message?: string,
    options: AlertIntegrationOptions = {}
  ): Promise<void> {
    await this.show(title, message, [{ text: 'OK' }], {
      ...options,
      haptic: options.haptic || 'error',
    });
  }

  async confirm(options: ConfirmOptions): Promise<boolean> {
    return new Promise(async resolve => {
      await this.triggerHaptic(options.haptic);

      const confirmText = options.confirmText || 'Confirm';
      const cancelText = options.cancelText || 'Cancel';

      Alert.alert(
        options.title,
        options.message,
        [
          {
            text: cancelText,
            style: 'cancel',
            onPress: () => {
              options.onCancel?.();
              resolve(false);
            },
          },
          {
            text: confirmText,
            style: options.destructive ? 'destructive' : 'default',
            onPress: () => {
              options.onConfirm?.();
              resolve(true);
            },
          },
        ],
        { cancelable: true, onDismiss: () => resolve(false) }
      );
    });
  }

  async confirmDestructive(options: ConfirmOptions): Promise<boolean> {
    return this.confirm({
      ...options,
      destructive: true,
      haptic: options.haptic || 'warning',
      confirmText: options.confirmText || 'Delete',
    });
  }

  async yesNo(
    title: string,
    message?: string,
    onYes?: () => void,
    onNo?: () => void
  ): Promise<boolean> {
    return this.confirm({
      title,
      message: message || '',
      confirmText: 'Yes',
      cancelText: 'No',
      onConfirm: onYes,
      onCancel: onNo,
    });
  }
}

export const alert = new AlertIntegrationImpl();
