/**
 * Store review integration
 * Abstracts expo-store-review for app rating functionality
 */
import * as StoreReview from 'expo-store-review';

import { appStorage } from '../storage';

/**
 * Store review integration class
 */
class StoreReviewIntegration {
  private readonly REVIEW_PROMPT_KEY = 'review_prompt_data';
  private readonly MIN_SESSIONS_BEFORE_PROMPT = 5;
  private readonly DAYS_BETWEEN_PROMPTS = 30;

  /**
   * Check if store review is available
   */
  async isAvailable(): Promise<boolean> {
    try {
      return await StoreReview.isAvailableAsync();
    } catch (error) {
      console.warn('Store review availability check failed:', error);
      return false;
    }
  }

  /**
   * Request store review (iOS only - shows native prompt)
   */
  async requestReview(): Promise<boolean> {
    try {
      const isAvailable = await this.isAvailable();
      if (!isAvailable) {
        console.warn('Store review not available on this platform');
        return false;
      }

      await StoreReview.requestReview();

      // Record that we showed the review prompt
      await this.recordReviewPromptShown();

      return true;
    } catch (error) {
      console.error('Store review request failed:', error);
      return false;
    }
  }

  /**
   * Check if we should show review prompt based on app usage
   */
  async shouldShowReviewPrompt(): Promise<boolean> {
    try {
      const isAvailable = await this.isAvailable();
      if (!isAvailable) return false;

      const reviewData = await this.getReviewPromptData();
      const currentTime = Date.now();

      // Check if enough time has passed since last prompt
      if (reviewData.lastPromptTime) {
        const daysSinceLastPrompt =
          (currentTime - reviewData.lastPromptTime) / (1000 * 60 * 60 * 24);

        if (daysSinceLastPrompt < this.DAYS_BETWEEN_PROMPTS) {
          return false;
        }
      }

      // Check if user has had enough sessions
      if (reviewData.sessionCount < this.MIN_SESSIONS_BEFORE_PROMPT) {
        return false;
      }

      // Don't show if user already reviewed or declined
      if (reviewData.hasReviewed || reviewData.hasDeclined) {
        return false;
      }

      return true;
    } catch (error) {
      console.warn('Failed to check review prompt eligibility:', error);
      return false;
    }
  }

  /**
   * Increment session count for review prompt logic
   */
  async incrementSessionCount(): Promise<void> {
    try {
      const reviewData = await this.getReviewPromptData();
      reviewData.sessionCount += 1;
      reviewData.lastSessionTime = Date.now();

      await this.saveReviewPromptData(reviewData);
    } catch (error) {
      console.warn('Failed to increment session count:', error);
    }
  }

  /**
   * Record that user reviewed the app
   */
  async recordUserReviewed(): Promise<void> {
    try {
      const reviewData = await this.getReviewPromptData();
      reviewData.hasReviewed = true;
      reviewData.reviewTime = Date.now();

      await this.saveReviewPromptData(reviewData);
    } catch (error) {
      console.warn('Failed to record user review:', error);
    }
  }

  /**
   * Record that user declined to review
   */
  async recordUserDeclined(): Promise<void> {
    try {
      const reviewData = await this.getReviewPromptData();
      reviewData.hasDeclined = true;
      reviewData.declineTime = Date.now();

      await this.saveReviewPromptData(reviewData);
    } catch (error) {
      console.warn('Failed to record user decline:', error);
    }
  }

  /**
   * Record that we showed the review prompt
   */
  private async recordReviewPromptShown(): Promise<void> {
    try {
      const reviewData = await this.getReviewPromptData();
      reviewData.lastPromptTime = Date.now();
      reviewData.promptCount += 1;

      await this.saveReviewPromptData(reviewData);
    } catch (error) {
      console.warn('Failed to record review prompt shown:', error);
    }
  }

  /**
   * Get review prompt data from storage
   */
  private async getReviewPromptData(): Promise<{
    sessionCount: number;
    promptCount: number;
    lastSessionTime?: number;
    lastPromptTime?: number;
    hasReviewed: boolean;
    hasDeclined: boolean;
    reviewTime?: number;
    declineTime?: number;
  }> {
    try {
      const data = appStorage.getObject(this.REVIEW_PROMPT_KEY);

      return {
        sessionCount: 0,
        promptCount: 0,
        hasReviewed: false,
        hasDeclined: false,
        ...data,
      };
    } catch (error) {
      console.warn('Failed to get review prompt data:', error);
      return {
        sessionCount: 0,
        promptCount: 0,
        hasReviewed: false,
        hasDeclined: false,
      };
    }
  }

  /**
   * Save review prompt data to storage
   */
  private async saveReviewPromptData(data: any): Promise<void> {
    try {
      appStorage.setObject(this.REVIEW_PROMPT_KEY, data);
    } catch (error) {
      console.warn('Failed to save review prompt data:', error);
    }
  }

  /**
   * Get review statistics for analytics
   */
  async getReviewStats(): Promise<{
    sessionCount: number;
    promptCount: number;
    hasReviewed: boolean;
    hasDeclined: boolean;
    daysSinceInstall?: number;
  }> {
    try {
      const reviewData = await this.getReviewPromptData();

      let daysSinceInstall: number | undefined;
      if (reviewData.lastSessionTime) {
        daysSinceInstall = (Date.now() - reviewData.lastSessionTime) / (1000 * 60 * 60 * 24);
      }

      return {
        sessionCount: reviewData.sessionCount,
        promptCount: reviewData.promptCount,
        hasReviewed: reviewData.hasReviewed,
        hasDeclined: reviewData.hasDeclined,
        daysSinceInstall,
      };
    } catch (error) {
      console.warn('Failed to get review stats:', error);
      return {
        sessionCount: 0,
        promptCount: 0,
        hasReviewed: false,
        hasDeclined: false,
      };
    }
  }

  /**
   * Reset review prompt data (for testing)
   */
  async resetReviewData(): Promise<void> {
    try {
      appStorage.delete(this.REVIEW_PROMPT_KEY);
    } catch (error) {
      console.warn('Failed to reset review data:', error);
    }
  }

  /**
   * Smart review prompt - handles the logic automatically
   */
  async smartReviewPrompt(): Promise<'shown' | 'not_ready' | 'not_available'> {
    try {
      const shouldShow = await this.shouldShowReviewPrompt();

      if (!shouldShow) {
        return 'not_ready';
      }

      const success = await this.requestReview();

      if (success) {
        return 'shown';
      }

      return 'not_available';
    } catch (error) {
      console.error('Smart review prompt failed:', error);
      return 'not_available';
    }
  }
}

// Export singleton instance
export const storeReview = new StoreReviewIntegration();
