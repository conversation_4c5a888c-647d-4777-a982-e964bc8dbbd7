/**
 * Toast integration - provides toast notifications
 * Uses burnt and react-native-toast-message for different use cases
 */
import { toast as burntToast } from 'burnt';
import RNToastMessage from 'react-native-toast-message';

export interface ToastOptions {
  title?: string;
  message?: string;
  duration?: number;
  haptic?: 'success' | 'warning' | 'error' | 'none';
  from?: 'bottom' | 'top';
}

export interface DetailedToastOptions extends ToastOptions {
  onPress?: () => void;
  autoHide?: boolean;
  visibilityTime?: number;
  position?: 'bottom' | 'top';
}

export interface Toast {
  // Simple system-level toasts (using burnt)
  success(message: string, options?: ToastOptions): Promise<void>;
  error(message: string, options?: ToastOptions): Promise<void>;
  warning(message: string, options?: ToastOptions): Promise<void>;
  info(message: string, options?: ToastOptions): Promise<void>;

  // Custom toast with more control (using react-native-toast-message)
  show(options: DetailedToastOptions): void;
  hide(): void;

  // Utility methods
  loading(message: string): void;
  hideLoading(): void;
}

type ToastType = 'burnt' | 'react-native-toast-message';

class ToastImpl implements Toast {
  private defaultOptions: ToastOptions = {
    from: 'bottom',
  };

  toastToUse: ToastType = 'burnt';

  configure(options: ToastOptions, toastToUse: ToastType = 'burnt') {
    this.defaultOptions = {
      ...this.defaultOptions,
      ...options,
    };
    this.toastToUse = toastToUse;
  }

  async success(message: string, options: ToastOptions = {}): Promise<void> {
    // For success messages, use react-native-toast-message directly to avoid truncation
    // Burnt toasts have character limits that cause truncation of long success messages

    if (this.toastToUse === 'burnt') {
      await burntToast({
        title: options.title || 'Success',
        message,
        duration: options.duration || 2,
        haptic: options.haptic || 'success',
        from: options.from || 'bottom',
        preset: 'done',
      });
    } else {
      RNToastMessage.show({
        type: 'success',
        text1: options.title || 'Success',
        text2: message,
        visibilityTime: (options.duration || 2) * 1000,
        position: options.from === 'bottom' ? 'bottom' : 'top',
      });
    }
  }

  async error(message: string, options: ToastOptions = {}): Promise<void> {
    if (this.toastToUse === 'burnt') {
      await burntToast({
        title: options.title || 'Error',
        message,
        duration: options.duration || 3,
        haptic: options.haptic || 'error',
        from: options.from || 'bottom',
        preset: 'done',
      });
    } else {
      RNToastMessage.show({
        type: 'error',
        text1: options.title || 'Error',
        text2: message,
        visibilityTime: (options.duration || 3) * 1000,
        position: options.from === 'bottom' ? 'bottom' : 'top',
      });
    }
  }

  async warning(message: string, options: ToastOptions = {}): Promise<void> {
    if (this.toastToUse === 'burnt') {
      await burntToast({
        title: options.title || 'Warning',
        message,
        duration: options.duration || 2.5,
        haptic: options.haptic || 'warning',
        from: options.from || 'bottom',
        preset: 'none',
      });
    } else {
      RNToastMessage.show({
        type: 'info',
        text1: options.title || 'Warning',
        text2: message,
        visibilityTime: (options.duration || 2.5) * 1000,
        position: options.from === 'bottom' ? 'bottom' : 'top',
      });
    }
  }

  async info(message: string, options: ToastOptions = {}): Promise<void> {
    if (this.toastToUse === 'burnt') {
      await burntToast({
        title: options.title || 'Info',
        message,
        duration: options.duration || 2,
        haptic: options.haptic || 'none',
        from: options.from || 'bottom',
        preset: 'none',
      });
    } else {
      RNToastMessage.show({
        type: 'info',
        text1: options.title || 'Info',
        text2: message,
        visibilityTime: (options.duration || 2) * 1000,
        position: options.from === 'bottom' ? 'bottom' : 'top',
      });
    }
  }

  show(options: DetailedToastOptions): void {
    RNToastMessage.show({
      type: 'success', // Default type, can be overridden
      text1: options.title || '',
      text2: options.message || '',
      visibilityTime: options.visibilityTime || 3000,
      autoHide: options.autoHide ?? true,
      position: options.position || 'bottom',
      onPress: options.onPress,
    });
  }

  hide(): void {
    RNToastMessage.hide();
  }

  loading(message: string): void {
    RNToastMessage.show({
      type: 'info',
      text1: 'Loading...',
      text2: message,
      autoHide: false,
      visibilityTime: 0,
      position: 'bottom',
    });
  }

  hideLoading(): void {
    RNToastMessage.hide();
  }
}

export const toast = new ToastImpl();
