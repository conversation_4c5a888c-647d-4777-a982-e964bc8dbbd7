/**
 * Haptics integration - provides tactile feedback
 * Uses expo-haptics for cross-platform haptic feedback
 */
import * as Haptics from 'expo-haptics';

export interface HapticsIntegration {
  // Light feedback for subtle interactions
  light(): Promise<void>;

  // Medium feedback for moderate interactions
  medium(): Promise<void>;

  // Heavy feedback for strong interactions
  heavy(): Promise<void>;

  // Success feedback for completed actions
  success(): Promise<void>;

  // Warning feedback for caution actions
  warning(): Promise<void>;

  // Error feedback for failed actions
  error(): Promise<void>;

  // Selection feedback for picker/selector changes
  selection(): Promise<void>;

  // Rigid feedback for strong physical interactions
  rigid(): Promise<void>;

  // Soft feedback for gentle interactions
  soft(): Promise<void>;
}

class HapticsIntegrationImpl implements HapticsIntegration {
  async light(): Promise<void> {
    try {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    } catch (error) {
      console.warn('Haptics light feedback unavailable:', error);
    }
  }

  async medium(): Promise<void> {
    try {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    } catch (error) {
      console.warn('Haptics medium feedback unavailable:', error);
    }
  }

  async heavy(): Promise<void> {
    try {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    } catch (error) {
      console.warn('Haptics heavy feedback unavailable:', error);
    }
  }

  async success(): Promise<void> {
    try {
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch (error) {
      console.warn('Haptics success feedback unavailable:', error);
    }
  }

  async warning(): Promise<void> {
    try {
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
    } catch (error) {
      console.warn('Haptics warning feedback unavailable:', error);
    }
  }

  async error(): Promise<void> {
    try {
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    } catch (error) {
      console.warn('Haptics error feedback unavailable:', error);
    }
  }

  async selection(): Promise<void> {
    try {
      await Haptics.selectionAsync();
    } catch (error) {
      console.warn('Haptics selection feedback unavailable:', error);
    }
  }

  async rigid(): Promise<void> {
    try {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Rigid);
    } catch (error) {
      console.warn('Haptics rigid feedback unavailable:', error);
    }
  }

  async soft(): Promise<void> {
    try {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Soft);
    } catch (error) {
      console.warn('Haptics soft feedback unavailable:', error);
    }
  }
}

export const haptics = new HapticsIntegrationImpl();
