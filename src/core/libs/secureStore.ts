/**
 * Secure store integration
 * Abstracts expo-secure-store for sensitive data storage
 */
import * as SecureStore from 'expo-secure-store';

/**
 * Secure store options
 */
export interface SecureStoreOptions {
  requireAuthentication?: boolean;
  authenticationPrompt?: string;
  keychainService?: string;
  sharedPreferencesName?: string;
}

/**
 * Secure store integration class
 */
class SecureStoreIntegration {
  private readonly defaultOptions: SecureStoreOptions = {
    requireAuthentication: false,
    keychainService: 'movuca-app',
    sharedPreferencesName: 'movuca-secure-prefs',
  };

  /**
   * Store a value securely
   */
  async setItem(key: string, value: string, options: SecureStoreOptions = {}): Promise<void> {
    try {
      const finalOptions = { ...this.defaultOptions, ...options };

      await SecureStore.setItemAsync(key, value, {
        requireAuthentication: finalOptions.requireAuthentication,
        authenticationPrompt: finalOptions.authenticationPrompt,
        keychainService: finalOptions.keychainService,
        sharedPreferencesName: finalOptions.sharedPreferencesName,
      });
    } catch (error) {
      console.error('Secure store setItem error:', error);
      throw new Error(`Failed to store secure item: ${key}`);
    }
  }

  /**
   * Retrieve a value securely
   */
  async getItem(key: string, options: SecureStoreOptions = {}): Promise<string | null> {
    try {
      const finalOptions = { ...this.defaultOptions, ...options };

      return await SecureStore.getItemAsync(key, {
        requireAuthentication: finalOptions.requireAuthentication,
        authenticationPrompt: finalOptions.authenticationPrompt,
        keychainService: finalOptions.keychainService,
        sharedPreferencesName: finalOptions.sharedPreferencesName,
      });
    } catch (error) {
      console.error('Secure store getItem error:', error);
      return null;
    }
  }

  /**
   * Delete a secure item
   */
  async deleteItem(key: string, options: SecureStoreOptions = {}): Promise<void> {
    try {
      const finalOptions = { ...this.defaultOptions, ...options };

      await SecureStore.deleteItemAsync(key, {
        keychainService: finalOptions.keychainService,
        sharedPreferencesName: finalOptions.sharedPreferencesName,
      });
    } catch (error) {
      console.error('Secure store deleteItem error:', error);
      throw new Error(`Failed to delete secure item: ${key}`);
    }
  }

  /**
   * Check if secure store is available
   */
  async isAvailable(): Promise<boolean> {
    try {
      return await SecureStore.isAvailableAsync();
    } catch (error) {
      console.warn('Secure store availability check failed:', error);
      return false;
    }
  }

  /**
   * Store object as JSON securely
   */
  async setObject<T>(key: string, value: T, options: SecureStoreOptions = {}): Promise<void> {
    try {
      const jsonValue = JSON.stringify(value);
      await this.setItem(key, jsonValue, options);
    } catch (error) {
      console.error('Secure store setObject error:', error);
      throw new Error(`Failed to store secure object: ${key}`);
    }
  }

  /**
   * Retrieve object from JSON securely
   */
  async getObject<T>(key: string, options: SecureStoreOptions = {}): Promise<T | null> {
    try {
      const jsonValue = await this.getItem(key, options);
      if (jsonValue === null) return null;

      return JSON.parse(jsonValue) as T;
    } catch (error) {
      console.error('Secure store getObject error:', error);
      return null;
    }
  }

  /**
   * Store authentication tokens securely
   */
  async storeAuthTokens(tokens: {
    accessToken: string;
    refreshToken?: string;
    expiresAt?: number;
  }): Promise<void> {
    await this.setObject('auth_tokens', tokens, {
      requireAuthentication: false, // Don't require auth for tokens themselves
      authenticationPrompt: 'Authenticate to access your account',
    });
  }

  /**
   * Retrieve authentication tokens securely
   */
  async getAuthTokens(): Promise<{
    accessToken: string;
    refreshToken?: string;
    expiresAt?: number;
  } | null> {
    return await this.getObject('auth_tokens', {
      requireAuthentication: false,
      authenticationPrompt: 'Authenticate to access your account',
    });
  }

  /**
   * Clear authentication tokens
   */
  async clearAuthTokens(): Promise<void> {
    await this.deleteItem('auth_tokens');
  }

  /**
   * Store biometric authentication preference
   */
  async setBiometricEnabled(enabled: boolean): Promise<void> {
    await this.setItem('biometric_enabled', enabled.toString());
  }

  /**
   * Get biometric authentication preference
   */
  async isBiometricEnabled(): Promise<boolean> {
    const value = await this.getItem('biometric_enabled');
    return value === 'true';
  }

  /**
   * Store sensitive user preferences
   */
  async setUserPreferences(preferences: Record<string, any>): Promise<void> {
    await this.setObject('user_preferences', preferences, {
      requireAuthentication: true,
      authenticationPrompt: 'Authenticate to access your preferences',
    });
  }

  /**
   * Get sensitive user preferences
   */
  async getUserPreferences(): Promise<Record<string, any> | null> {
    return await this.getObject('user_preferences', {
      requireAuthentication: true,
      authenticationPrompt: 'Authenticate to access your preferences',
    });
  }

  /**
   * Clear all secure data (useful for logout)
   */
  async clearAll(): Promise<void> {
    try {
      const keysToDelete = ['auth_tokens', 'user_preferences', 'biometric_enabled'];

      await Promise.all(keysToDelete.map(key => this.deleteItem(key).catch(() => {})));
    } catch (error) {
      console.error('Failed to clear all secure data:', error);
    }
  }
}

// Export singleton instance
export const secureStore = new SecureStoreIntegration();

// Export types
export type { SecureStoreOptions };
