/**
 * Image Picker Integration Layer
 * Provides a clean abstraction over expo-image-picker for consistent usage
 */
import * as ImageManipulator from 'expo-image-manipulator';
import * as ImagePicker from 'expo-image-picker';

export interface ImagePickerResult {
  uri: string;
  width: number;
  height: number;
  size: number;
  cancelled: boolean;
}

export interface ImagePickerOptions {
  allowsEditing?: boolean;
  aspect?: [number, number];
  quality?: number;
  mediaTypes?: 'images' | 'videos' | 'all';
  selectionLimit?: number;
}

export interface ImageProcessingOptions {
  resize?: { width: number; height: number };
  compress?: number;
  format?: 'jpeg' | 'png';
}

/**
 * Image picker service interface
 */
export interface IImagePicker {
  requestPermissions(): Promise<boolean>;
  pickFromLibrary(options?: ImagePickerOptions): Promise<ImagePickerResult | null>;
  takePhoto(options?: ImagePickerOptions): Promise<ImagePickerResult | null>;
  processImage(uri: string, options: ImageProcessingOptions): Promise<ImagePickerResult>;
  getImageDimensions(uri: string): Promise<{ width: number; height: number }>;
  validateImageFile(uri: string, maxSize: number): Promise<boolean>;
}

/**
 * Expo Image Picker implementation
 */
class ExpoImagePicker implements IImagePicker {
  getImageDimensions(uri: string): Promise<{ width: number; height: number }> {
    throw new Error('Method not implemented.');
  }
  validateImageFile(uri: string, maxSize: number): Promise<boolean> {
    throw new Error('Method not implemented.');
  }
  async requestPermissions(): Promise<boolean> {
    try {
      const [mediaLibraryResult, cameraResult] = await Promise.all([
        ImagePicker.requestMediaLibraryPermissionsAsync(),
        ImagePicker.requestCameraPermissionsAsync(),
      ]);

      return mediaLibraryResult.granted && cameraResult.granted;
    } catch (error) {
      console.error('Failed to request image picker permissions:', error);
      return false;
    }
  }

  async pickFromLibrary(options: ImagePickerOptions = {}): Promise<ImagePickerResult | null> {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: this.convertMediaTypes(options.mediaTypes || 'images'),
        allowsEditing: options.allowsEditing ?? true,
        aspect: options.aspect,
        quality: options.quality ?? 0.8,
        selectionLimit: options.selectionLimit ?? 1,
      });

      if (result.canceled) {
        return { uri: '', width: 0, height: 0, size: 0, cancelled: true };
      }

      const asset = result.assets[0];
      const size = await this.getImageSize(asset.uri);

      return {
        uri: asset.uri,
        width: asset.width ?? 0,
        height: asset.height ?? 0,
        size,
        cancelled: false,
      };
    } catch (error) {
      console.error('Failed to pick image from library:', error);
      throw new Error('Failed to pick image from library');
    }
  }

  async takePhoto(options: ImagePickerOptions = {}): Promise<ImagePickerResult | null> {
    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: this.convertMediaTypes(options.mediaTypes || 'images'),
        allowsEditing: options.allowsEditing ?? true,
        aspect: options.aspect,
        quality: options.quality ?? 0.8,
      });

      if (result.canceled) {
        return { uri: '', width: 0, height: 0, size: 0, cancelled: true };
      }

      const asset = result.assets[0];
      const size = await this.getImageSize(asset.uri);

      return {
        uri: asset.uri,
        width: asset.width ?? 0,
        height: asset.height ?? 0,
        size,
        cancelled: false,
      };
    } catch (error) {
      console.error('Failed to take photo:', error);
      throw new Error('Failed to take photo');
    }
  }

  async processImage(uri: string, options: ImageProcessingOptions): Promise<ImagePickerResult> {
    try {
      const actions: ImageManipulator.Action[] = [];

      if (options.resize) {
        actions.push({ resize: options.resize });
      }

      const result = await ImageManipulator.manipulateAsync(uri, actions, {
        compress: options.compress ?? 0.8,
        format:
          options.format === 'png'
            ? ImageManipulator.SaveFormat.PNG
            : ImageManipulator.SaveFormat.JPEG,
      });

      const size = await this.getImageSize(result.uri);

      return {
        uri: result.uri,
        width: result.width,
        height: result.height,
        size,
        cancelled: false,
      };
    } catch (error) {
      console.error('Failed to process image:', error);
      throw new Error('Failed to process image');
    }
  }

  private convertMediaTypes(
    type: 'images' | 'videos' | 'all'
  ): ImagePicker.MediaType | ImagePicker.MediaType[] {
    switch (type) {
      case 'images':
        return ['images'];
      case 'videos':
        return ['videos'];
      case 'all':
        return ['images', 'videos'];
      default:
        return ['images'];
    }
  }

  private async getImageSize(uri: string): Promise<number> {
    try {
      const response = await fetch(uri);
      const blob = await response.blob();
      return blob.size;
    } catch (error) {
      console.warn('Failed to get image size:', error);
      return 0;
    }
  }
}

/**
 * Global image picker instance
 */
export const imagePicker = new ExpoImagePicker();

/**
 * Image picker factory for testing
 */
export const createImagePicker = (): IImagePicker => new ExpoImagePicker();
