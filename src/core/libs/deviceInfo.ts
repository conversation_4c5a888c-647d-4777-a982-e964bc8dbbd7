/**
 * Device info integration
 * Abstracts expo-cellular and other device information APIs
 */
import { Platform } from 'react-native';

import * as Cellular from 'expo-cellular';
import * as Device from 'expo-device';
import * as Network from 'expo-network';

/**
 * Device information interfaces
 */
export interface DeviceInfo {
  platform: 'ios' | 'android' | 'web';
  osVersion: string;
  deviceName?: string;
  deviceType: 'phone' | 'tablet' | 'desktop' | 'tv' | 'unknown';
  brand?: string;
  manufacturer?: string;
  modelName?: string;
  isDevice: boolean;
  totalMemory?: number;
}

export interface NetworkInfo {
  isConnected: boolean;
  type: 'wifi' | 'cellular' | 'ethernet' | 'unknown' | 'none';
  isInternetReachable: boolean;
}

export interface CellularInfo {
  allowsVoip?: boolean;
  carrier?: string;
  isoCountryCode?: string;
  mobileCountryCode?: string;
  mobileNetworkCode?: string;
}

/**
 * Device info integration class
 */
class DeviceInfoIntegration {
  /**
   * Get comprehensive device information
   */
  async getDeviceInfo(): Promise<DeviceInfo> {
    try {
      const deviceType = await this.getDeviceType();

      return {
        platform: Platform.OS as 'ios' | 'android' | 'web',
        osVersion: Platform.Version.toString(),
        deviceName: Device.deviceName || undefined,
        deviceType,
        brand: Device.brand || undefined,
        manufacturer: Device.manufacturer || undefined,
        modelName: Device.modelName || undefined,
        isDevice: Device.isDevice,
        totalMemory: Device.totalMemory || undefined,
      };
    } catch (error) {
      console.warn('Failed to get device info:', error);
      return {
        platform: Platform.OS as 'ios' | 'android' | 'web',
        osVersion: Platform.Version.toString(),
        deviceType: 'unknown',
        isDevice: Device.isDevice,
      };
    }
  }

  /**
   * Get device type classification
   */
  private async getDeviceType(): Promise<'phone' | 'tablet' | 'desktop' | 'tv' | 'unknown'> {
    try {
      const deviceType = Device.deviceType;

      switch (deviceType) {
        case Device.DeviceType.PHONE:
          return 'phone';
        case Device.DeviceType.TABLET:
          return 'tablet';
        case Device.DeviceType.DESKTOP:
          return 'desktop';
        case Device.DeviceType.TV:
          return 'tv';
        default:
          return 'unknown';
      }
    } catch (error) {
      console.warn('Failed to get device type:', error);
      return 'unknown';
    }
  }

  /**
   * Get network information
   */
  async getNetworkInfo(): Promise<NetworkInfo> {
    try {
      const networkState = await Network.getNetworkStateAsync();

      return {
        isConnected: networkState.isConnected || false,
        type: this.mapNetworkType(networkState.type),
        isInternetReachable: networkState.isInternetReachable || false,
      };
    } catch (error) {
      console.warn('Failed to get network info:', error);
      return {
        isConnected: false,
        type: 'unknown',
        isInternetReachable: false,
      };
    }
  }

  /**
   * Map network type to standardized format
   */
  private mapNetworkType(networkType: any): 'wifi' | 'cellular' | 'ethernet' | 'unknown' | 'none' {
    switch (networkType) {
      case Network.NetworkStateType.WIFI:
        return 'wifi';
      case Network.NetworkStateType.CELLULAR:
        return 'cellular';
      case Network.NetworkStateType.ETHERNET:
        return 'ethernet';
      case Network.NetworkStateType.NONE:
        return 'none';
      default:
        return 'unknown';
    }
  }

  /**
   * Get cellular information (mobile networks)
   */
  async getCellularInfo(): Promise<CellularInfo> {
    try {
      if (Platform.OS !== 'ios' && Platform.OS !== 'android') {
        return {};
      }

      const [allowsVoip, carrier, isoCountryCode, mobileCountryCode, mobileNetworkCode] =
        await Promise.all([
          Cellular.allowsVoipAsync().catch(() => undefined),
          Cellular.getCarrierNameAsync().catch(() => undefined),
          Cellular.getIsoCountryCodeAsync().catch(() => undefined),
          Cellular.getMobileCountryCodeAsync().catch(() => undefined),
          Cellular.getMobileNetworkCodeAsync().catch(() => undefined),
        ]);

      return {
        allowsVoip: allowsVoip || false,
        carrier: carrier || undefined,
        isoCountryCode: isoCountryCode || undefined,
        mobileCountryCode: mobileCountryCode || undefined,
        mobileNetworkCode: mobileNetworkCode || undefined,
      };
    } catch (error) {
      console.warn('Failed to get cellular info:', error);
      return {};
    }
  }

  /**
   * Check if device is on a cellular connection
   */
  async isOnCellular(): Promise<boolean> {
    try {
      const networkInfo = await this.getNetworkInfo();
      return networkInfo.type === 'cellular';
    } catch (error) {
      console.warn('Failed to check cellular connection:', error);
      return false;
    }
  }

  /**
   * Check if device is on WiFi
   */
  async isOnWiFi(): Promise<boolean> {
    try {
      const networkInfo = await this.getNetworkInfo();
      return networkInfo.type === 'wifi';
    } catch (error) {
      console.warn('Failed to check WiFi connection:', error);
      return false;
    }
  }

  /**
   * Get connection quality assessment
   */
  async getConnectionQuality(): Promise<'excellent' | 'good' | 'poor' | 'offline'> {
    try {
      const networkInfo = await this.getNetworkInfo();

      if (!networkInfo.isConnected) {
        return 'offline';
      }

      // Basic quality assessment based on connection type
      switch (networkInfo.type) {
        case 'wifi':
        case 'ethernet':
          return 'excellent';
        case 'cellular': {
          // For cellular, we could check carrier info for more details
          const cellularInfo = await this.getCellularInfo();

          // This is a simplified assessment
          // In a real app, you might want to do speed tests or ping tests
          if (cellularInfo.carrier) {
            return 'good';
          }
          return 'poor';
        }
        default:
          return 'poor';
      }
    } catch (error) {
      console.warn('Failed to assess connection quality:', error);
      return 'poor';
    }
  }

  /**
   * Get device capabilities for feature detection
   */
  async getDeviceCapabilities(): Promise<{
    hasCellular: boolean;
    hasWiFi: boolean;
    supportsVoip: boolean;
    isTablet: boolean;
    isPhone: boolean;
    hasHighMemory: boolean;
  }> {
    try {
      const [deviceInfo, cellularInfo] = await Promise.all([
        this.getDeviceInfo(),
        this.getCellularInfo(),
      ]);

      return {
        hasCellular: !!cellularInfo.carrier,
        hasWiFi: true, // Most devices have WiFi capability
        supportsVoip: cellularInfo.allowsVoip || false,
        isTablet: deviceInfo.deviceType === 'tablet',
        isPhone: deviceInfo.deviceType === 'phone',
        hasHighMemory: (deviceInfo.totalMemory || 0) > 4 * 1024 * 1024 * 1024, // > 4GB
      };
    } catch (error) {
      console.warn('Failed to get device capabilities:', error);
      return {
        hasCellular: false,
        hasWiFi: false,
        supportsVoip: false,
        isTablet: false,
        isPhone: false,
        hasHighMemory: false,
      };
    }
  }

  /**
   * Get comprehensive device context for analytics
   */
  async getDeviceContext(): Promise<{
    device: DeviceInfo;
    network: NetworkInfo;
    cellular?: CellularInfo;
    capabilities: any;
    connectionQuality: string;
  }> {
    try {
      const [device, network, cellular, capabilities, connectionQuality] = await Promise.all([
        this.getDeviceInfo(),
        this.getNetworkInfo(),
        this.getCellularInfo(),
        this.getDeviceCapabilities(),
        this.getConnectionQuality(),
      ]);

      return {
        device,
        network,
        cellular,
        capabilities,
        connectionQuality,
      };
    } catch (error) {
      console.error('Failed to get device context:', error);
      throw error;
    }
  }

  /**
   * Check if device should use data-saving features
   */
  async shouldUseDataSaving(): Promise<boolean> {
    try {
      const [networkInfo, connectionQuality] = await Promise.all([
        this.getNetworkInfo(),
        this.getConnectionQuality(),
      ]);

      return (
        networkInfo.type === 'cellular' ||
        connectionQuality === 'poor' ||
        !networkInfo.isInternetReachable
      );
    } catch (error) {
      console.warn('Failed to check data saving requirement:', error);
      return false;
    }
  }
}

// Export singleton instance
export const deviceInfo = new DeviceInfoIntegration();
