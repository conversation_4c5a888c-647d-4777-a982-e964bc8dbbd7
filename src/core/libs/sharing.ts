/**
 * Sharing integration
 * Abstracts expo-sharing for social sharing functionality
 */
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';

/**
 * Share content types
 */
export interface ShareContent {
  title?: string;
  message?: string;
  url?: string;
  files?: string[];
}

export interface ShareResult {
  success: boolean;
  error?: string;
  dismissed?: boolean;
}

/**
 * Sharing integration class
 */
class SharingIntegration {
  /**
   * Check if sharing is available
   */
  async isAvailable(): Promise<boolean> {
    try {
      return await Sharing.isAvailableAsync();
    } catch (error) {
      console.warn('Sharing availability check failed:', error);
      return false;
    }
  }

  /**
   * Share a file
   */
  async shareFile(
    fileUri: string,
    options?: {
      mimeType?: string;
      dialogTitle?: string;
      UTI?: string;
    }
  ): Promise<ShareResult> {
    try {
      const isAvailable = await this.isAvailable();

      if (!isAvailable) {
        return {
          success: false,
          error: 'Sharing is not available on this device',
        };
      }

      // Check if file exists
      const fileInfo = await FileSystem.getInfoAsync(fileUri);
      if (!fileInfo.exists) {
        return {
          success: false,
          error: 'File does not exist',
        };
      }

      await Sharing.shareAsync(fileUri, {
        mimeType: options?.mimeType,
        dialogTitle: options?.dialogTitle,
        UTI: options?.UTI,
      });

      return { success: true };
    } catch (error) {
      console.error('File sharing failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Sharing failed',
      };
    }
  }

  /**
   * Share text content
   */
  async shareText(
    text: string,
    options?: {
      dialogTitle?: string;
    }
  ): Promise<ShareResult> {
    try {
      // Create temporary text file
      const textFileUri = `${FileSystem.cacheDirectory}shared_text_${Date.now()}.txt`;

      await FileSystem.writeAsStringAsync(textFileUri, text);

      const result = await this.shareFile(textFileUri, {
        mimeType: 'text/plain',
        dialogTitle: options?.dialogTitle,
      });

      // Clean up temporary file
      try {
        await FileSystem.deleteAsync(textFileUri);
      } catch (cleanupError) {
        console.warn('Failed to cleanup temporary file:', cleanupError);
      }

      return result;
    } catch (error) {
      console.error('Text sharing failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Text sharing failed',
      };
    }
  }

  /**
   * Share an image
   */
  async shareImage(
    imageUri: string,
    options?: {
      title?: string;
      dialogTitle?: string;
    }
  ): Promise<ShareResult> {
    try {
      return await this.shareFile(imageUri, {
        mimeType: 'image/jpeg',
        dialogTitle: options?.dialogTitle || options?.title,
      });
    } catch (error) {
      console.error('Image sharing failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Image sharing failed',
      };
    }
  }

  /**
   * Share app content (profile, post, etc.)
   */
  async shareAppContent(content: {
    title: string;
    description?: string;
    url?: string;
    imageUri?: string;
  }): Promise<ShareResult> {
    try {
      let shareText = content.title;

      if (content.description) {
        shareText += `\n\n${content.description}`;
      }

      if (content.url) {
        shareText += `\n\n${content.url}`;
      }

      // If there's an image, share it with the text
      if (content.imageUri) {
        // Create a text file with the content
        const textFileUri = `${FileSystem.cacheDirectory}shared_content_${Date.now()}.txt`;
        await FileSystem.writeAsStringAsync(textFileUri, shareText);

        // Share both files (this depends on platform support)
        const result = await this.shareFile(content.imageUri, {
          dialogTitle: `Share ${content.title}`,
        });

        // Clean up
        try {
          await FileSystem.deleteAsync(textFileUri);
        } catch (cleanupError) {
          console.warn('Failed to cleanup temporary file:', cleanupError);
        }

        return result;
      } else {
        // Share just the text
        return await this.shareText(shareText, {
          dialogTitle: `Share ${content.title}`,
        });
      }
    } catch (error) {
      console.error('App content sharing failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Content sharing failed',
      };
    }
  }

  /**
   * Share user profile
   */
  async shareProfile(profile: {
    name: string;
    bio?: string;
    profileUrl?: string;
    avatarUri?: string;
  }): Promise<ShareResult> {
    const content = {
      title: `Check out ${profile.name}'s profile on Movuca`,
      description: profile.bio,
      url: profile.profileUrl,
      imageUri: profile.avatarUri,
    };

    return await this.shareAppContent(content);
  }

  /**
   * Share app download link
   */
  async shareApp(customMessage?: string): Promise<ShareResult> {
    const defaultMessage = 'Check out Movuca - a great app for connecting with people!';
    const appStoreUrl = 'https://apps.apple.com/app/movuca'; // Update with actual URL
    const playStoreUrl = 'https://play.google.com/store/apps/details?id=com.movuca.app'; // Update with actual URL

    const message = customMessage || defaultMessage;
    const shareText = `${message}\n\niOS: ${appStoreUrl}\nAndroid: ${playStoreUrl}`;

    return await this.shareText(shareText, {
      dialogTitle: 'Share Movuca App',
    });
  }

  /**
   * Create shareable link for app content
   */
  createShareableLink(content: {
    type: 'profile' | 'post' | 'event';
    id: string;
    title?: string;
  }): string {
    // This would be your app's deep link structure
    const baseUrl = 'https://movuca.app'; // Update with actual domain

    switch (content.type) {
      case 'profile':
        return `${baseUrl}/profile/${content.id}`;
      case 'post':
        return `${baseUrl}/post/${content.id}`;
      case 'event':
        return `${baseUrl}/event/${content.id}`;
      default:
        return baseUrl;
    }
  }

  /**
   * Share with specific apps (if needed)
   */
  async shareToSocialMedia(
    content: ShareContent,
    platform?: 'twitter' | 'facebook' | 'instagram'
  ): Promise<ShareResult> {
    // This is a basic implementation
    // For specific social media sharing, you might want to use their SDKs

    let shareText = '';

    if (content.title) {
      shareText += content.title;
    }

    if (content.message) {
      shareText += (shareText ? '\n\n' : '') + content.message;
    }

    if (content.url) {
      shareText += (shareText ? '\n\n' : '') + content.url;
    }

    return await this.shareText(shareText, {
      dialogTitle: platform ? `Share to ${platform}` : 'Share to Social Media',
    });
  }

  /**
   * Get sharing statistics for analytics
   */
  getShareUrl(contentType: string, contentId: string): string {
    return this.createShareableLink({
      type: contentType as any,
      id: contentId,
    });
  }
}

// Export singleton instance
export const sharing = new SharingIntegration();
