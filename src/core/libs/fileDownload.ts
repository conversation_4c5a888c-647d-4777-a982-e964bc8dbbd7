/**
 * File download integration - handles file downloads and temporary storage
 * Uses expo-file-system for cross-platform file operations
 */
import * as FileSystem from 'expo-file-system';

export interface DownloadResult {
  success: boolean;
  localUri?: string;
  error?: string;
}

export interface FileDownloadIntegration {
  downloadImage(url: string, filename?: string): Promise<DownloadResult>;
  downloadFile(url: string, filename?: string): Promise<DownloadResult>;
  createTempFilename(extension: string): string;
  cleanupTempFiles(): Promise<void>;
  getFileInfo(uri: string): Promise<FileSystem.FileInfo>;
}

class FileDownloadIntegrationImpl implements FileDownloadIntegration {
  private tempFiles: string[] = [];

  async downloadImage(url: string, filename?: string): Promise<DownloadResult> {
    try {
      const fileExtension = url.split('.').pop()?.toLowerCase() || 'jpg';
      const tempFilename = filename || this.createTempFilename(fileExtension);
      const localUri = FileSystem.documentDirectory + tempFilename;

      const downloadResult = await FileSystem.downloadAsync(url, localUri);

      if (downloadResult.status === 200) {
        this.tempFiles.push(localUri);
        return {
          success: true,
          localUri: downloadResult.uri,
        };
      } else {
        return {
          success: false,
          error: `Download failed with status: ${downloadResult.status}`,
        };
      }
    } catch (error) {
      console.error('Failed to download image:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown download error',
      };
    }
  }

  async downloadFile(url: string, filename?: string): Promise<DownloadResult> {
    return this.downloadImage(url, filename); // Same implementation for now
  }

  createTempFilename(extension: string): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `temp_${timestamp}_${random}.${extension}`;
  }

  async cleanupTempFiles(): Promise<void> {
    try {
      for (const filePath of this.tempFiles) {
        try {
          const fileInfo = await FileSystem.getInfoAsync(filePath);
          if (fileInfo.exists) {
            await FileSystem.deleteAsync(filePath);
          }
        } catch (error) {
          console.warn('Failed to delete temp file:', filePath, error);
        }
      }
      this.tempFiles = [];
    } catch (error) {
      console.error('Failed to cleanup temp files:', error);
    }
  }

  async getFileInfo(uri: string): Promise<FileSystem.FileInfo> {
    return FileSystem.getInfoAsync(uri);
  }
}

export const fileDownload = new FileDownloadIntegrationImpl();
