/**
 * Magic Modal Integration for React Native
 *
 * Provides imperative modal management with enhanced features:
 * - Type-safe modal presentation
 * - Built-in animations
 * - Return value support
 * - Multiple modal stacking
 * - Toast integration
 *
 * @example
 * ```tsx
 * // Simple modal
 * modal.show(() => <MyModal />);
 *
 * // With return value
 * const result = await modalUtils.showAndWait<boolean>(() => (
 *   <ConfirmModal onConfirm={() => modal.hide(true)} />
 * ));
 * ```
 */
import React from 'react';

import { KeyboardAvoidingView, Platform, Pressable, StyleSheet, Text, View } from 'react-native';

import { BlurView } from 'expo-blur';
import {
  type Direction,
  MagicModalHideReason,
  type NewConfigProps,
  magicModal,
  useMagicModal,
} from 'react-native-magic-modal';
import {
  FadeIn,
  FadeOut,
  SlideInDown,
  SlideInUp,
  SlideOutDown,
  SlideOutUp,
  ZoomIn,
  ZoomOut,
} from 'react-native-reanimated';

// Removed theme import to avoid circular dependency
import { haptics } from './haptics';
import { toast } from './toast';

export interface ShowModalOptions {
  /** Unique identifier for modal tracking */
  id?: string;
  /** Auto-close timeout in milliseconds */
  autoCloseMs?: number;
  /** Show toast notification on close */
  onCloseToast?: {
    type: 'success' | 'error' | 'info';
    message: string;
  };
  /** Haptic feedback on show */
  hapticFeedback?: 'impact' | 'success' | 'warning' | 'error';
  /** Animation configuration */
  animation?: {
    type?: 'slide' | 'fade' | 'scale' | 'spring';
    duration?: number;
  } /** Background configuration */;
  background?: {
    opacity?: number;
    blur?: boolean;
    blurIntensity?: number;
    closeOnPress?: boolean;
  };
  /** Modal positioning */
  position?: 'center' | 'bottom' | 'top';
  /** Gesture configuration */
  gestures?: {
    swipeToClose?: boolean;
    swipeDirection?: Direction;
    swipeThreshold?: number;
  };
  /** Safe area handling */
  safeArea?: {
    top?: boolean;
    bottom?: boolean;
  };
  /** Keyboard avoiding behavior */
  keyboardAvoidingBehavior?: 'height' | 'position' | 'padding';
}

// Extended modal state tracking
interface ModalState {
  modalId: string;
  options?: ShowModalOptions;
  showTime: number;
  timeoutId?: NodeJS.Timeout | number;
}
class MagicModalManager {
  private activeModals = new Map<string, ModalState>();
  private modalIdMap = new Map<string, string>();

  disableFullWindowOverlay() {
    magicModal.disableFullWindowOverlay();
  }

  enableFullWindowOverlay() {
    magicModal.enableFullWindowOverlay();
  }
  /**
   * Show a modal with enhanced options
   */
  show<T = any>(Component: () => React.ReactElement<T>, options?: ShowModalOptions) {
    // Haptic feedback
    if (options?.hapticFeedback) {
      // Map the feedback type to the appropriate haptics method
      switch (options.hapticFeedback) {
        case 'success':
          haptics.success();
          break;
        case 'error':
          haptics.error();
          break;
        case 'warning':
          haptics.warning();
          break;
        case 'impact':
          // Map 'impact' to medium impact
          haptics.medium();
          break;
        default:
          // For any other values, use selection as a fallback
          haptics.selection();
          break;
      }
    }

    // Enhanced component with animation and positioning
    const EnhancedComponent = () => {
      return (
        <ModalWrapper options={options}>
          <Component />
        </ModalWrapper>
      );
    };

    // Prepare magic modal configuration
    const config: NewConfigProps = {
      animationInTiming: options?.animation?.duration || 250,
      animationOutTiming: options?.animation?.duration || 250,
      swipeDirection:
        options?.gestures?.swipeToClose === false
          ? undefined
          : options?.gestures?.swipeDirection || 'down',
      swipeVelocityThreshold: options?.gestures?.swipeThreshold || 500,
      hideBackdrop: options?.background?.blur || false,
      backdropColor: options?.background?.blur
        ? 'transparent'
        : `rgba(0, 0, 0, ${options?.background?.opacity || 0.5})`,
      onBackdropPress: options?.background?.closeOnPress === false ? undefined : undefined, // Let default behavior handle it
    };

    // Add entering/exiting animations based on type
    const animationType = options?.animation?.type || 'fade';
    const duration = options?.animation?.duration || 250;

    switch (animationType) {
      case 'slide':
        if (options?.position === 'bottom') {
          config.entering = SlideInDown.duration(duration);
          config.exiting = SlideOutDown.duration(duration);
        } else if (options?.position === 'top') {
          config.entering = SlideInUp.duration(duration);
          config.exiting = SlideOutUp.duration(duration);
        } else {
          config.entering = SlideInDown.duration(duration);
          config.exiting = SlideOutDown.duration(duration);
        }
        break;
      case 'scale':
      case 'spring':
        config.entering = ZoomIn.duration(duration);
        config.exiting = ZoomOut.duration(duration);
        break;

      case 'fade':
      default:
        config.entering = FadeIn.duration(duration);
        config.exiting = FadeOut.duration(duration);
        break;
    }

    const result = magicModal.show(EnhancedComponent, config);

    // Track modal state
    const state: ModalState = {
      modalId: result.modalID,
      options,
      showTime: Date.now(),
    };

    this.activeModals.set(result.modalID, state);

    // Store ID mapping
    if (options?.id) {
      this.modalIdMap.set(options.id, result.modalID);
    }
    // Auto-close timer
    if (options?.autoCloseMs) {
      const timeoutId = setTimeout(() => {
        this.hide('auto-close', result.modalID);
      }, options.autoCloseMs);
      state.timeoutId = timeoutId;
    }

    // Handle toast on close
    if (options?.onCloseToast) {
      result.promise
        .then(hideResult => {
          const { type, message } = options.onCloseToast!;
          toast[type](message);
        })
        .catch(() => {
          // Modal was closed without data
        });
    }

    return result;
  }

  /**
   * Hide modal with enhanced cleanup
   */
  hide<T = any>(value?: T, modalId?: string): boolean {
    const targetModalId = modalId || this.getLatestModalId();

    if (!targetModalId) {
      return false;
    }
    const state = this.activeModals.get(targetModalId);

    // Clear timeout if exists
    if (state?.timeoutId) {
      clearTimeout(state.timeoutId);
    }

    // Cleanup
    this.activeModals.delete(targetModalId);
    if (state?.options?.id) {
      this.modalIdMap.delete(state.options.id);
    }

    // Use the correct hide signature
    magicModal.hide(
      value === undefined
        ? { reason: MagicModalHideReason.INTENTIONAL_HIDE, data: undefined }
        : { reason: MagicModalHideReason.INTENTIONAL_HIDE, data: value },
      { modalID: targetModalId }
    );

    return true;
  }

  /**
   * Hide modal by custom ID
   */
  hideById<T = any>(id: string, value?: T): boolean {
    const modalId = this.modalIdMap.get(id);
    if (!modalId) return false;
    return this.hide(value, modalId);
  }

  /**
   * Hide all modals
   */
  hideAll(): void {
    // Clear all timeouts
    this.activeModals.forEach(state => {
      if (state.timeoutId) {
        clearTimeout(state.timeoutId);
      }
    });

    // Clear tracking
    this.activeModals.clear();
    this.modalIdMap.clear();

    // Use the library's hideAll
    magicModal.hideAll();
  }

  /**
   * Check if a modal is currently shown
   */
  isModalShown(modalId?: string): boolean {
    if (typeof modalId === 'string' && !modalId.includes('-')) {
      // It's a custom ID
      return this.modalIdMap.has(modalId);
    }
    return modalId ? this.activeModals.has(modalId) : this.activeModals.size > 0;
  }
  /**
   * Get number of active modals
   */
  getModalCount(): number {
    return this.activeModals.size;
  }

  /**
   * Get modal display duration
   */
  getModalDuration(modalId: string): number | null {
    const state = this.activeModals.get(modalId);
    return state ? Date.now() - state.showTime : null;
  }

  private getLatestModalId(): string | undefined {
    const modalIds = Array.from(this.activeModals.keys());
    return modalIds[modalIds.length - 1];
  }
}

// Modal wrapper component for positioning and enhanced features
const ModalWrapper: React.FC<{
  children: React.ReactNode;
  options?: ShowModalOptions;
}> = ({ children, options }) => {
  // Position styles
  const containerStyle = React.useMemo(() => {
    const baseStyle: any = {
      flex: 1,
    };

    switch (options?.position) {
      case 'bottom':
        return {
          ...baseStyle,
          justifyContent: 'flex-end',
        };
      case 'top':
        return {
          ...baseStyle,
          justifyContent: 'flex-start',
        };
      case 'center':
      default:
        return {
          ...baseStyle,
          justifyContent: 'center',
          alignItems: 'center',
        };
    }
  }, [options?.position]);
  const renderContent = () => {
    if (options?.keyboardAvoidingBehavior && Platform.OS === 'ios') {
      return (
        <KeyboardAvoidingView style={containerStyle} behavior={options.keyboardAvoidingBehavior}>
          {children}
        </KeyboardAvoidingView>
      );
    }

    return <View style={containerStyle}>{children}</View>;
  };

  // If blur is enabled, wrap with BlurView
  if (options?.background?.blur && Platform.OS !== 'web') {
    return (
      <>
        <BlurView
          style={StyleSheet.absoluteFill}
          intensity={options.background.blurIntensity || 80}
          tint="dark"
        />
        {renderContent()}
      </>
    );
  }
  return renderContent();
};

// Create singleton instance
export const modal = new MagicModalManager();

// Enhanced modal utilities
export const modalUtils = {
  /**
   * Show modal and wait for result
   */
  async showAndWait<T = any>(
    Component: () => React.ReactElement,
    options?: ShowModalOptions
  ): Promise<T | undefined> {
    const result = modal.show(Component, options);

    try {
      const hideResult = await result.promise;
      if (hideResult.reason === MagicModalHideReason.INTENTIONAL_HIDE) {
        return hideResult.data as T;
      }
      return undefined;
    } catch {
      return undefined;
    }
  },

  /**
   * Show modal with swipe gestures disabled
   * Perfect for search modals, forms, or content that needs internal scrolling
   */ showFixed(Component: () => React.ReactElement, options?: Omit<ShowModalOptions, 'gestures'>) {
    return modal.show(Component, {
      ...options,
      gestures: {
        swipeToClose: false,
        swipeDirection: undefined,
      },
    });
  },

  /**
   * Show modal with swipe gestures disabled and wait for result
   */
  async showFixedAndWait<T = any>(
    Component: () => React.ReactElement,
    options?: Omit<ShowModalOptions, 'gestures'>
  ): Promise<T | undefined> {
    return modalUtils.showAndWait(Component, {
      ...options,
      gestures: {
        swipeToClose: false,
        swipeDirection: undefined,
      },
    });
  },

  /**
   * Show full-screen modal (typically for complex forms or search)
   */ showFullScreen(
    Component: () => React.ReactElement,
    options?: Omit<ShowModalOptions, 'position' | 'gestures' | 'animation'>
  ) {
    return modal.show(Component, {
      ...options,
      position: 'center',
      animation: {
        type: 'slide',
        duration: 300,
      },
      gestures: {
        swipeToClose: false,
        swipeDirection: undefined,
      },
      background: {
        opacity: 0.9,
        closeOnPress: false,
        ...options?.background,
      },
      keyboardAvoidingBehavior: 'padding',
    });
  },

  /**
   * Show bottom sheet modal with swipe-to-dismiss
   */
  showBottomSheet(
    Component: () => React.ReactElement,
    options?: Omit<ShowModalOptions, 'position' | 'animation' | 'gestures'>
  ) {
    return modal.show(Component, {
      ...options,
      position: 'bottom',
      animation: {
        type: 'slide',
        duration: 300,
      },
      gestures: {
        swipeToClose: true,
        swipeDirection: 'down',
        swipeThreshold: 100,
      },
      safeArea: {
        bottom: true,
      },
    });
  },

  /**
   * Show confirmation modal
   */
  async showConfirm(
    title: string,
    message: string,
    options?: {
      confirmText?: string;
      cancelText?: string;
      confirmVariant?: 'primary' | 'danger';
    }
  ): Promise<boolean> {
    return (
      (await modalUtils.showAndWait<boolean>(
        () => {
          const { hide } = useMagicModal<boolean>();
          return (
            <View
              style={{
                backgroundColor: 'white',
                margin: 20,
                padding: 20,
                borderRadius: 12,
                minWidth: 280,
                maxWidth: 400,
              }}>
              <Text
                style={{
                  fontSize: 18,
                  fontWeight: 'bold',
                  marginBottom: 12,
                  color: '#000',
                }}>
                {title}
              </Text>
              <Text
                style={{
                  fontSize: 16,
                  marginBottom: 24,
                  color: '#666',
                  lineHeight: 22,
                }}>
                {message}
              </Text>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'flex-end',
                  gap: 12,
                }}>
                {' '}
                <Pressable
                  onPress={() => hide(false)}
                  style={({ pressed }) => ({
                    paddingVertical: 12,
                    paddingHorizontal: 24,
                    borderRadius: 8,
                    backgroundColor: pressed ? '#f0f0f0' : '#e0e0e0',
                  })}>
                  <Text style={{ color: '#000' }}>{options?.cancelText || 'Cancel'}</Text>
                </Pressable>
                <Pressable
                  onPress={() => hide(true)}
                  style={({ pressed }) => ({
                    paddingVertical: 12,
                    paddingHorizontal: 24,
                    borderRadius: 8,
                    backgroundColor: pressed
                      ? options?.confirmVariant === 'danger'
                        ? '#d32f2f'
                        : '#1976d2'
                      : options?.confirmVariant === 'danger'
                        ? '#f44336'
                        : '#2196f3',
                  })}>
                  <Text style={{ color: 'white', fontWeight: 'bold' }}>
                    {options?.confirmText || 'Confirm'}
                  </Text>
                </Pressable>
              </View>
            </View>
          );
        },
        {
          gestures: {
            swipeToClose: false,
            swipeDirection: undefined,
          },
          background: {
            closeOnPress: false,
            opacity: 0.5,
          },
          position: 'center',
          animation: {
            type: 'scale',
            duration: 200,
          },
        }
      )) || false
    );
  },
};

// Re-export for convenience
export { MagicModalHideReason, useMagicModal };
