/**
 * Foursquare Places API Integration
 * Main integration class for interacting with Foursquare API
 */
import { i18n } from '../../i18n';
import { network } from '../network';
import { API_DEFAULTS, CATEGORY_GROUPS, ERROR_CODES } from './constants';
import type {
  AutocompleteParams,
  AutocompleteResult,
  CategoryId,
  FoursquareConfig,
  FoursquareError,
  FoursquarePlace,
  FoursquareResponse,
  PhotoParams,
  PlaceDetailsParams,
  PlacePhoto,
  PlaceSearchParams,
  PlaceTip,
  RecommendationsParams,
} from './types';
import {
  buildQueryString,
  calculateDistance,
  filterPartyVenues,
  formatDistance,
  getCategoryIconUrl,
  getFormattedAddress,
  getPhotoUrl,
  getPriceDisplay,
  getPrimaryCategory,
  getRatingStars,
  isOpenNow,
} from './utils';

/**
 * Maps app locales to Foursquare supported locales
 * Foursquare supports: en, es, fr, de, it, ja, th, tr, ko, ru, pt, id
 */
function mapAppLocaleToFoursquare(appLocale: string): string {
  // Create a mapping from app locales to Foursquare locales
  const localeMap: Record<string, string> = {
    en: 'en',
    'en-US': 'en',
    'en-GB': 'en',
    es: 'es',
    'es-MX': 'es',
    'es-AR': 'es',
    'pt-BR': 'pt',
    'pt-PT': 'pt',
    fr: 'fr',
    'fr-CA': 'fr',
    de: 'de',
    'de-AT': 'de',
    'de-CH': 'de',
    it: 'it',
    'it-CH': 'it',
    ja: 'ja',
    ko: 'ko',
    ru: 'ru',
    th: 'th',
    tr: 'tr',
    id: 'id',
  };

  // Get the base language code
  const baseLocale = appLocale.split('-')[0];

  // Try exact match first, then base language
  return localeMap[appLocale] || localeMap[baseLocale] || 'en';
}

/**
 * Foursquare Places API Integration
 */
export class FoursquarePlacesIntegration {
  private config: FoursquareConfig;
  private baseUrl: string;
  private version: string;

  constructor(config: FoursquareConfig) {
    this.config = config;
    this.baseUrl = config.baseUrl || API_DEFAULTS.BASE_URL;
    this.version = config.version || API_DEFAULTS.VERSION;
  }

  /**
   * Search for places
   */
  async searchPlaces(params: PlaceSearchParams): Promise<FoursquareResponse<FoursquarePlace>> {
    try {
      // Always filter to party venues unless categories are explicitly provided
      const categories = params.categories || CATEGORY_GROUPS.PARTY_VENUES;

      const queryParams = buildQueryString({
        query: params.query,
        ll: params.ll,
        near: params.near,
        radius: params.radius,
        fsq_category_ids: categories,
        limit: params.limit || API_DEFAULTS.DEFAULT_LIMIT,
        sort: params.sort,
        open_now: params.openNow,
        min_price: params.minPrice,
        max_price: params.maxPrice,
        cursor: params.cursor,
      });

      const response = await network.get<FoursquareResponse<FoursquarePlace>>(
        `${this.baseUrl}/places/search?${queryParams}`,
        this.getHeaders()
      );

      console.log({ response }, 'search places api');

      const result = this.handleResponse<FoursquarePlace>(response);

      // Additional filtering to ensure we only return party venues

      return result;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Get place details
   */
  async getPlaceDetails(params: PlaceDetailsParams): Promise<FoursquarePlace> {
    try {
      const { fsq_place_id, fields } = params; // Changed from fsqId
      const fieldsParam = fields ? `?fields=${fields.join(',')}` : '';

      const response = await network.get<FoursquarePlace>(
        `${this.baseUrl}/places/${fsq_place_id}${fieldsParam}`, // Changed from fsqId
        this.getHeaders()
      );

      return this.handleSingleResponse(response);
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Autocomplete place search
   */
  async autocomplete(params: AutocompleteParams): Promise<FoursquareResponse<AutocompleteResult>> {
    try {
      // The Foursquare Places API autocomplete endpoint uses different parameters
      const queryParams = buildQueryString({
        query: params.query,
        ll: params.ll,
        radius: params.radius,
        limit: params.limit || 10,
        // Remove types and bias as they might not be supported in autocomplete
        // types: params.types,
        // bias: params.bias,
      });

      const requestUrl = `${this.baseUrl}/autocomplete?${queryParams}`;
      const headers = this.getHeaders();

      const response = await network.get<FoursquareResponse<AutocompleteResult>>(
        requestUrl,
        headers
      );

      return this.handleResponse<AutocompleteResult>(response);
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Get place recommendations
   */
  async getRecommendations(
    params: RecommendationsParams
  ): Promise<FoursquareResponse<FoursquarePlace>> {
    try {
      // Always filter to party venues unless categories are explicitly provided
      const categories = params.categories || CATEGORY_GROUPS.PARTY_VENUES;

      const queryParams = buildQueryString({
        ll: params.ll,
        near: params.near,
        radius: params.radius || API_DEFAULTS.DEFAULT_RADIUS,
        fsq_category_ids: categories,
        limit: params.limit || API_DEFAULTS.DEFAULT_LIMIT,
        open_now: params.openNow,
        sort: params.sort,
        cursor: params.cursor,
      });

      const response = await network.get<FoursquareResponse<FoursquarePlace>>(
        `${this.baseUrl}/places/recommendations?${queryParams}`,
        this.getHeaders()
      );

      const result = this.handleResponse<FoursquarePlace>(response);

      // Additional filtering to ensure we only return party venues
      result.results = filterPartyVenues(result.results);

      return result;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Get photos for a place
   */
  async getPlacePhotos(params: PhotoParams): Promise<PlacePhoto[]> {
    try {
      const { fsq_place_id: fsqId, ...photoParams } = params;
      const queryParams = buildQueryString({
        limit: photoParams.limit,
        sort: photoParams.sort,
        classifications: photoParams.classifications,
      });

      const response = await network.get<PlacePhoto[]>(
        `${this.baseUrl}/places/${fsqId}/photos?${queryParams}`,
        this.getHeaders()
      );

      return this.handleArrayResponse(response);
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Get tips for a place
   */
  async getPlaceTips(fsq_place_id: string, limit?: number): Promise<PlaceTip[]> {
    try {
      const limitParam = limit ? `?limit=${limit}` : '';
      const response = await network.get<PlaceTip[]>(
        `${this.baseUrl}/places/${fsq_place_id}/tips${limitParam}`, // Changed from fsqId
        this.getHeaders()
      );

      return this.handleArrayResponse(response);
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Search nearby places
   */
  async searchNearby(
    latitude: number,
    longitude: number,
    options?: {
      radius?: number;
      categories?: string[];
      limit?: number;
      openNow?: boolean;
    }
  ): Promise<FoursquareResponse<FoursquarePlace>> {
    const params: PlaceSearchParams = {
      query: '',
      ll: `${latitude},${longitude}`,
      radius: options?.radius,
      categories: [...(options?.categories || []), ...CATEGORY_GROUPS.PARTY_VENUES],
      limit: options?.limit,
      openNow: options?.openNow,
      sort: 'distance',
    };

    return this.searchPlaces(params);
  }

  /**
   * Get trending places
   */
  async getTrendingPlaces(
    latitude: number,
    longitude: number,
    radius?: number
  ): Promise<FoursquareResponse<FoursquarePlace>> {
    const params: RecommendationsParams = {
      ll: `${latitude},${longitude}`,
      radius: radius || API_DEFAULTS.DEFAULT_RADIUS,
      sort: 'popularity',
      limit: 20,
    };

    return this.getRecommendations(params);
  }

  /**
   * Search by category
   */
  async searchByCategory(
    category: CategoryId,
    location: { latitude: number; longitude: number } | string,
    options?: {
      radius?: number;
      limit?: number;
      openNow?: boolean;
    }
  ): Promise<FoursquareResponse<FoursquarePlace>> {
    // Filter the category to ensure it's a party venue category
    const isPartyCategory = CATEGORY_GROUPS.PARTY_VENUES.includes(category);
    const categories = isPartyCategory ? [category] : [...CATEGORY_GROUPS.PARTY_VENUES];

    const params: PlaceSearchParams = {
      query: '',
      categories,
      limit: options?.limit,
      openNow: options?.openNow,
      sort: 'distance',
    };

    if (typeof location === 'string') {
      params.near = location;
    } else {
      params.ll = `${location.latitude},${location.longitude}`;
      params.radius = options?.radius;
    }

    return this.searchPlaces(params);
  }

  /**
   * Fetch next page of results using cursor from previous response
   */
  async getNextPage(
    previousResponse: FoursquareResponse<FoursquarePlace>,
    originalParams: PlaceSearchParams | RecommendationsParams
  ): Promise<FoursquareResponse<FoursquarePlace> | null> {
    // Check if there's a next page
    if (!previousResponse.pagination?.cursor) {
      return null;
    }

    // Determine which method to use based on params
    if ('query' in originalParams) {
      // It's a search request
      return this.searchPlaces({
        ...originalParams,
        cursor: previousResponse.pagination.cursor,
      });
    } else {
      // It's a recommendations request
      return this.getRecommendations({
        ...originalParams,
        cursor: previousResponse.pagination.cursor,
      });
    }
  }

  /**
   * Utility method exports for convenience
   */
  getPhotoUrl = getPhotoUrl;
  getCategoryIconUrl = getCategoryIconUrl;
  calculateDistance = calculateDistance;
  formatDistance = formatDistance;
  isOpenNow = isOpenNow;
  getFormattedAddress = getFormattedAddress;
  getPrimaryCategory = getPrimaryCategory;
  getRatingStars = getRatingStars;
  getPriceDisplay = getPriceDisplay;

  /**
   * Get request headers
   */
  private getHeaders(): Record<string, string> {
    // Get current app locale and map to Foursquare supported locale
    const currentLocale = i18n.language;
    const foursquareLocale = mapAppLocaleToFoursquare(currentLocale);

    return {
      // Use Bearer token format for new Places API
      Authorization: `Bearer ${this.config.apiKey}`,
      // Add required version header
      'X-Places-Api-Version': this.version,
      Accept: 'application/json',
      'Accept-Language': foursquareLocale,
    };
  }

  /**
   * Parse link header to extract cursor
   */
  private parseLinkHeader(linkHeader: string | null): string | undefined {
    if (!linkHeader) return undefined;

    // Link header format: <https://api.foursquare.com/v3/places/search?cursor=xxx>; rel="next"
    const match = linkHeader.match(/cursor=([^&>]+)/);
    return match?.[1];
  }

  /**
   * Handle API response
   */
  private handleResponse<T>(response: any): FoursquareResponse<T> {
    if (response.data) {
      const result: FoursquareResponse<T> = response.data;

      // Extract pagination info from headers
      if (response.headers?.link) {
        const nextCursor = this.parseLinkHeader(response.headers.link);
        if (nextCursor) {
          result.pagination = {
            cursor: nextCursor,
            link: response.headers.link,
            hasMore: true,
          };
        }
      }

      return result;
    }
    throw new Error('Invalid response format');
  }

  /**
   * Handle single item response
   */
  private handleSingleResponse<T, R extends { data: T }>(response: R): T {
    if (response.data) {
      return response.data;
    }
    throw new Error('Invalid response format');
  }

  /**
   * Handle array response
   */
  private handleArrayResponse<T, R extends { data: T[] }>(response: R): T[] {
    if (response.data) {
      return response.data;
    }
    throw new Error('Invalid response format');
  }

  /**
   * Handle API errors
   */
  private handleError(error: any): FoursquareError {
    if (error.response?.data) {
      const apiError = error.response.data;
      return {
        code: apiError.code || ERROR_CODES.UNKNOWN_ERROR,
        message: apiError.message || 'An unknown error occurred',
        detail: apiError.detail,
      };
    }

    if (error.message) {
      return {
        code: ERROR_CODES.NETWORK_ERROR,
        message: error.message,
      };
    }

    return {
      code: ERROR_CODES.UNKNOWN_ERROR,
      message: 'An unknown error occurred',
    };
  }
}
