import { network } from '../../network';
import { API_DEFAULTS, ERROR_CODES } from '../constants';
import { FoursquarePlacesIntegration } from '../foursquare';
import type { FoursquareConfig, PlaceSearchParams } from '../types';

// Mock the network module
jest.mock('../../network', () => ({
  network: {
    get: jest.fn(),
  },
}));

describe('FoursquarePlacesIntegration', () => {
  let integration: FoursquarePlacesIntegration;
  const mockApiKey = 'test-api-key-123';
  const mockConfig: FoursquareConfig = {
    apiKey: mockApiKey,
  };

  beforeEach(() => {
    integration = new FoursquarePlacesIntegration(mockConfig);
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    it('should use default base URL when not provided', () => {
      const instance = new FoursquarePlacesIntegration({ apiKey: 'test' });
      expect(instance['baseUrl']).toBe(API_DEFAULTS.BASE_URL);
    });

    it('should use custom base URL when provided', () => {
      const customUrl = 'https://custom-api.example.com';
      const instance = new FoursquarePlacesIntegration({
        apiKey: 'test',
        baseUrl: customUrl,
      });
      expect(instance['baseUrl']).toBe(customUrl);
    });
  });

  describe('searchPlaces', () => {
    it('should make request with correct URL and headers', async () => {
      const mockResponse = {
        data: {
          results: [
            {
              fsqId: '123',
              name: 'Test Bar',
              location: { formattedAddress: '123 Main St' },
              categories: [],
            },
          ],
        },
      };

      (network.get as jest.Mock).mockResolvedValue(mockResponse);

      const params: PlaceSearchParams = {
        query: 'bar',
        ll: '40.7,-74.0',
        radius: 1000,
        limit: 10,
      };

      await integration.searchPlaces(params);

      expect(network.get).toHaveBeenCalledWith(
        expect.stringContaining(`${API_DEFAULTS.BASE_URL}/places/search?`),
        {
          Authorization: `Bearer ${mockApiKey}`,
          Accept: 'application/json',
          'Accept-Language': '*',
          'X-Places-Api-Version': API_DEFAULTS.VERSION,
        }
      );
    });

    it('should include party venue categories by default', async () => {
      const mockResponse = { data: { results: [] } };
      (network.get as jest.Mock).mockResolvedValue(mockResponse);

      await integration.searchPlaces({ query: 'test' });

      const callUrl = (network.get as jest.Mock).mock.calls[0][0];
      expect(callUrl).toContain('categories=');
    });

    it('should handle API errors correctly', async () => {
      const mockError = {
        response: {
          data: {
            code: 'RATE_LIMITED',
            message: 'Rate limit exceeded',
          },
        },
      };

      (network.get as jest.Mock).mockRejectedValue(mockError);

      await expect(integration.searchPlaces({ query: 'test' })).rejects.toEqual({
        code: 'RATE_LIMITED',
        message: 'Rate limit exceeded',
      });
    });
  });

  describe('getPlaceDetails', () => {
    it('should request place details with correct endpoint', async () => {
      const mockPlace = {
        data: {
          fsqId: 'test-123',
          name: 'Test Venue',
          location: { formattedAddress: '123 Test St' },
        },
      };

      (network.get as jest.Mock).mockResolvedValue(mockPlace);

      const result = await integration.getPlaceDetails({
        fsq_place_id: 'test-123',
        fields: ['name', 'location'],
      });

      expect(network.get).toHaveBeenCalledWith(
        `${API_DEFAULTS.BASE_URL}/places/test-123?fields=name,location`,
        expect.objectContaining({
          Authorization: `Bearer ${mockApiKey}`,
        })
      );
      expect(result).toEqual(mockPlace.data);
    });
  });

  describe('autocomplete', () => {
    it('should make autocomplete request with correct parameters', async () => {
      const mockResponse = {
        data: {
          results: [
            {
              type: 'place',
              text: { primary: 'Test Bar', secondary: 'New York, NY' },
            },
          ],
        },
      };

      (network.get as jest.Mock).mockResolvedValue(mockResponse);

      await integration.autocomplete({
        query: 'test',
        ll: '40.7,-74.0',
        types: ['place'],
      });

      const callUrl = (network.get as jest.Mock).mock.calls[0][0];
      expect(callUrl).toContain('/autocomplete?');
      expect(callUrl).toContain('query=test');
      expect(callUrl).toContain('ll=40.7%2C-74.0');
      expect(callUrl).toContain('types=place');
    });
  });

  describe('searchNearby', () => {
    it('should search with location and default party categories', async () => {
      const mockResponse = { data: { results: [] } };
      (network.get as jest.Mock).mockResolvedValue(mockResponse);

      await integration.searchNearby(40.7, -74.0, { radius: 500 });

      const callUrl = (network.get as jest.Mock).mock.calls[0][0];
      expect(callUrl).toContain('ll=40.7%2C-74');
      expect(callUrl).toContain('radius=500');
      expect(callUrl).toContain('sort=distance');
      expect(callUrl).toContain('categories=');
    });
  });

  describe('error handling', () => {
    it('should handle network errors', async () => {
      const networkError = new Error('Network request failed');
      (network.get as jest.Mock).mockRejectedValue(networkError);

      await expect(integration.searchPlaces({ query: 'test' })).rejects.toEqual({
        code: ERROR_CODES.NETWORK_ERROR,
        message: 'Network request failed',
      });
    });

    it('should handle unknown errors', async () => {
      (network.get as jest.Mock).mockRejectedValue({});

      await expect(integration.searchPlaces({ query: 'test' })).rejects.toEqual({
        code: ERROR_CODES.UNKNOWN_ERROR,
        message: 'An unknown error occurred',
      });
    });
  });
});
