/**
 * Foursquare Places API Utilities
 * Helper functions for common operations
 */
import {
  API_DEFAULTS,
  CATEGORY_GROUPS,
  DISTANCE_CONVERSION,
  FOURSQUARE_CATEGORIES,
  PARTY_TIME_FILTERS,
} from './constants';
import type {
  CategoryIcon,
  DistanceCalcUnit,
  DistanceUnit,
  FoursquareAttributes,
  FoursquarePlace,
  IconSize,
  PhotoSize,
  PlaceCategory,
  PlaceHours,
  PlaceLocation,
  PlacePhoto,
} from './types';

/**
 * Calculate distance between two points using Haversine formula
 */
export function calculateDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number,
  unit: DistanceCalcUnit = 'km'
): number {
  const R =
    unit === 'km' ? DISTANCE_CONVERSION.EARTH_RADIUS_KM : DISTANCE_CONVERSION.EARTH_RADIUS_MI;
  const dLat = toRad(lat2 - lat1);
  const dLon = toRad(lon2 - lon1);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRad(lat1)) * Math.cos(toRad(lat2)) * Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

/**
 * Convert degrees to radians
 */
export function toRad(degrees: number): number {
  return degrees * (Math.PI / 180);
}

/**
 * Format distance for display
 */
export function formatDistance(meters: number, unit: DistanceUnit = 'metric'): string {
  if (unit === 'imperial') {
    const miles = meters * DISTANCE_CONVERSION.METERS_TO_MILES;
    if (miles < 0.1) {
      const feet = meters * DISTANCE_CONVERSION.METERS_TO_FEET;
      return `${Math.round(feet)} ft`;
    }
    return `${miles.toFixed(1)} mi`;
  } else {
    if (meters < 1000) {
      return `${Math.round(meters)} m`;
    }
    const km = meters * DISTANCE_CONVERSION.METERS_TO_KM;
    return `${km.toFixed(1)} km`;
  }
}

/**
 * Check if place is open now
 */
export function isOpenNow(hours?: PlaceHours): boolean | undefined {
  return hours?.openNow;
}

/**
 * Get formatted address
 */
export function getFormattedAddress(location: PlaceLocation): string {
  return location.formattedAddress || location.address || '';
}

/**
 * Get primary category
 */
export function getPrimaryCategory(categories: PlaceCategory[]): PlaceCategory | undefined {
  return categories.find(cat => cat.primary) || categories[0];
}

/**
 * Get rating stars (convert from 10-point to 5-star scale)
 */
export function getRatingStars(rating?: number): number {
  if (!rating) return 0;
  // Foursquare ratings are out of 10, convert to 5-star scale
  return Math.round((rating / 10) * 5 * 10) / 10;
}

/**
 * Get price display (e.g., "$$$")
 */
export function getPriceDisplay(price?: number): string {
  if (!price) return '';
  return '$'.repeat(Math.min(price, 4));
}

/**
 * Get photo URL with specified size
 */
export function getPhotoUrl(
  photo: PlacePhoto,
  size: PhotoSize = API_DEFAULTS.DEFAULT_PHOTO_SIZE
): string {
  if (size === 'original') {
    return `${photo.prefix}original${photo.suffix}`;
  }
  return `${photo.prefix}${size}${photo.suffix}`;
}

/**
 * Get category icon URL
 */
export function getCategoryIconUrl(
  icon: CategoryIcon,
  size: IconSize = API_DEFAULTS.DEFAULT_ICON_SIZE
): string {
  const bgColor = 'bg_FFFFFF'; // white background
  return `${icon.prefix}${size}_${bgColor}${icon.suffix}`;
}

/**
 * Parse coordinates from string
 * @param ll Latitude,longitude string (e.g., "40.7,-74")
 * @returns Object with lat and lng or null if invalid
 */
export function parseCoordinates(ll: string): { lat: number; lng: number } | null {
  const parts = ll.split(',');
  if (parts.length !== 2) return null;

  const lat = parseFloat(parts[0].trim());
  const lng = parseFloat(parts[1].trim());

  if (isNaN(lat) || isNaN(lng)) return null;
  if (lat < -90 || lat > 90) return null;
  if (lng < -180 || lng > 180) return null;

  return { lat, lng };
}

/**
 * Format coordinates to string
 */
export function formatCoordinates(lat: number, lng: number): string {
  return `${lat.toFixed(6)},${lng.toFixed(6)}`;
}

/**
 * Get display name for autocomplete result
 */
export function getAutocompleteDisplayName(type: string): string {
  const displayNames: Record<string, string> = {
    place: 'Place',
    address: 'Address',
    geo: 'Location',
    search: 'Search',
  };
  return displayNames[type] || type;
}

/**
 * Filter unique places by fsqId
 */
export function getUniquePlaces<T extends { fsqId: string }>(places: T[]): T[] {
  const seen = new Set<string>();
  return places.filter(place => {
    if (seen.has(place.fsqId)) return false;
    seen.add(place.fsqId);
    return true;
  });
}

/**
 * Sort places by distance
 */
export function sortByDistance<T extends { distance?: number }>(places: T[]): T[] {
  return [...places].sort((a, b) => {
    if (!a.distance && !b.distance) return 0;
    if (!a.distance) return 1;
    if (!b.distance) return -1;
    return a.distance - b.distance;
  });
}

/**
 * Sort places by rating
 */
export function sortByRating<T extends { rating?: number }>(places: T[]): T[] {
  return [...places].sort((a, b) => {
    if (!a.rating && !b.rating) return 0;
    if (!a.rating) return 1;
    if (!b.rating) return -1;
    return b.rating - a.rating;
  });
}

/**
 * Check if coordinates are valid
 */
export function areCoordinatesValid(lat: number, lng: number): boolean {
  return lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180;
}

/**
 * Get hours display text
 */
export function getHoursDisplay(hours?: PlaceHours): string {
  if (!hours) return '';

  if (hours.display) return hours.display;

  if (hours.openNow === true) return 'Open now';
  if (hours.openNow === false) return 'Closed now';

  return '';
}

/**
 * Check if place has specific feature
 */
export function hasFeature(features: any, path: string): boolean {
  const keys = path.split('.');
  let current = features;

  for (const key of keys) {
    if (!current || typeof current !== 'object') return false;
    current = current[key];
  }

  return !!current;
}

/**
 * Get day name from day number
 */
export function getDayName(day: number): string {
  const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
  return days[day - 1] || '';
}

/**
 * Format time from 24h to 12h format
 */
export function formatTime(time: string, use24h: boolean = false): string {
  if (!time || !time.includes(':')) return time;

  if (use24h) return time;

  const [hours, minutes] = time.split(':').map(Number);
  const period = hours >= 12 ? 'PM' : 'AM';
  const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;

  return `${displayHours}:${minutes.toString().padStart(2, '0')} ${period}`;
}

/**
 * Truncate text with ellipsis
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength - 3) + '...';
}

/**
 * Get social media platform name from URL
 */
export function getSocialPlatform(url: string): string {
  if (url.includes('facebook.com')) return 'Facebook';
  if (url.includes('instagram.com')) return 'Instagram';
  if (url.includes('twitter.com') || url.includes('x.com')) return 'X (Twitter)';
  if (url.includes('linkedin.com')) return 'LinkedIn';
  if (url.includes('snapchat.com')) return 'Snapchat';
  return 'Social Media';
}

/**
 * Build query string from params object
 */
export function buildQueryString(params: Record<string, any>): string {
  const queryParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value === undefined || value === null) return;

    if (Array.isArray(value)) {
      if (value.length > 0) {
        queryParams.append(key, value.join(','));
      }
    } else if (typeof value === 'boolean') {
      queryParams.append(key, value.toString());
    } else {
      queryParams.append(key, String(value));
    }
  });

  return queryParams.toString();
}

/**
 * Check if a place is a party venue
 */
export function isPartyVenue(place: FoursquarePlace): boolean {
  if (!place.categories || place.categories.length === 0) return false;

  const partyCategories = [...CATEGORY_GROUPS.PARTY_VENUES];
  return place.categories.some(category => partyCategories.includes(category.id));
}

/**
 * Filter places to only include party venues
 */
export function filterPartyVenues<T extends FoursquarePlace>(places: T[]): T[] {
  return places.filter(isPartyVenue);
}

/**
 * Check if venue serves alcohol
 */
export function servesAlcohol(place: FoursquarePlace): boolean {
  // if (!place.attributes?.foodAndDrink?.alcohol) return false;

  // const alcohol = place.attributes.foodAndDrink.alcohol;
  // return (
  //   alcohol.beer || alcohol.beerAndWine || alcohol.cocktails || alcohol.fullBar || alcohol.wine
  // );

  return true;
}

/**
 * Extract coordinates from place object
 */
export function getPlaceCoordinates(
  place: FoursquarePlace
): { latitude: number; longitude: number } | null {
  // New API may have coordinates at top level
  if (place.latitude && place.longitude) {
    return { latitude: place.latitude, longitude: place.longitude };
  }

  // Fallback to geocodes.main if available
  if (place.geocodes?.main) {
    return {
      latitude: place.geocodes.main.latitude,
      longitude: place.geocodes.main.longitude,
    };
  }

  return null;
}

/**
 * Get place attributes (previously features)
 */
export function getPlaceAttributes(place: FoursquarePlace): FoursquareAttributes | undefined {
  // Handle both old and new field names during transition
  return place.attributes || (place as any).features;
}

/**
 * Check if venue has live music
 */
export function hasLiveMusic(place: FoursquarePlace): boolean {
  // return !!place.attributes?.live_music;
  return true;
}

/**
 * Check if venue is open late (after midnight)
 */
export function isOpenLate(place: FoursquarePlace): boolean {
  if (!place.hours?.regular) return false;

  // Check if any day closes after midnight (early morning hours)
  return place.hours.regular.some(day => {
    if (!day.close) return false;
    const closeHour = parseInt(day.close.substring(0, 2));
    return closeHour < 6; // Closes between midnight and 6 AM
  });
}

/**
 * Get party time category for current hour
 */
export function getPartyTimeCategory(): keyof typeof PARTY_TIME_FILTERS | null {
  const currentHour = new Date().getHours();

  for (const [category, times] of Object.entries(PARTY_TIME_FILTERS)) {
    if (times.end < times.start) {
      // Handles times that cross midnight
      if (currentHour >= times.start || currentHour < times.end) {
        return category as keyof typeof PARTY_TIME_FILTERS;
      }
    } else {
      if (currentHour >= times.start && currentHour < times.end) {
        return category as keyof typeof PARTY_TIME_FILTERS;
      }
    }
  }

  return null;
}

/**
 * Get venue type label
 */
export function getVenueTypeLabel(place: FoursquarePlace): string {
  const primaryCategory = getPrimaryCategory(place.categories);
  if (!primaryCategory) return 'Venue';

  // Map category IDs to friendly labels
  const labelMap: Record<string, string> = {
    [FOURSQUARE_CATEGORIES.BAR]: 'Bar',
    [FOURSQUARE_CATEGORIES.BREWERY]: 'Brewery',
    [FOURSQUARE_CATEGORIES.COCKTAIL_BAR]: 'Cocktail Bar',
    [FOURSQUARE_CATEGORIES.WINE_BAR]: 'Wine Bar',
    [FOURSQUARE_CATEGORIES.NIGHTCLUB]: 'Nightclub',
    [FOURSQUARE_CATEGORIES.MUSIC_VENUE]: 'Music Venue',
    [FOURSQUARE_CATEGORIES.LOUNGE]: 'Lounge',
    [FOURSQUARE_CATEGORIES.PUB]: 'Pub',
    [FOURSQUARE_CATEGORIES.CASINO]: 'Casino',
    [FOURSQUARE_CATEGORIES.KARAOKE_BAR]: 'Karaoke Bar',
  };

  return labelMap[primaryCategory.id.toString()] || primaryCategory.name;
}

/**
 * Get party features summary
 */
export function getPartyFeatures(place: FoursquarePlace): string[] {
  const features: string[] = [];

  if (servesAlcohol(place)) features.push('🍺 Drinks');
  if (hasLiveMusic(place)) features.push('🎵 Live Music');
  if (isOpenLate(place)) features.push('🌙 Late Night');
  if (place.attributes?.delivery) features.push('🚚 Delivery');
  if (place.attributes?.atm) features.push('🏧 ATM');
  if (place.attributes?.takes_credit_card) features.push('💳 Credit Cards');
  if (place.attributes?.restroom) features.push('🚻 Restroom');
  if (place.attributes?.outdoor_seating) features.push('🌿 Outdoor');
  if (place.attributes?.wifi) features.push('📶 WiFi');

  return features;
}

/**
 * Check if venue is good for groups
 */
export function isGoodForGroups(place: FoursquarePlace): boolean {
  // Venues with certain features are typically good for groups
  return !!(
    place.attributes?.outdoor_seating ||
    place.attributes?.reservations ||
    (place.price && place.price <= 2) // Affordable venues are often group-friendly
  );
}
