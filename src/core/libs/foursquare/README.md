# Foursquare Places API Integration

> **Important Note**: The category IDs in this integration need to be verified against the latest Foursquare Places API category IDs from their [official documentation](https://docs.foursquare.com/reference/categories).

## Overview

This integration provides a comprehensive interface to the Foursquare Places API, specifically focused on **nightlife and party venues**. The integration automatically filters all results to only show bars, clubs, lounges, and other social entertainment venues.

## Party Venue Focus

This integration is configured to work exclusively with nightlife and party venues:

### Included Venues
- **Bars**: All types including cocktail bars, wine bars, sports bars, dive bars, etc.
- **Clubs**: Nightclubs, dance clubs, music venues, jazz clubs
- **Breweries & Distilleries**: Craft beer and spirits venues
- **Lounges & Pubs**: Social drinking establishments
- **Entertainment**: Casinos, pool halls, karaoke bars, adult entertainment
- **Special Venues**: Rooftop bars, beach bars, hotel bars
- **Events**: Music festivals, beer festivals, party events

### Excluded Venues
The following venue types are automatically filtered out:
- Museums, aquariums, zoos
- Theaters (unless party-focused)
- Banks, ATMs
- Schools, libraries
- Family-oriented attractions
- Educational venues
- Theme parks (unless adult/nightlife focused)

## Key Features

- **Automatic Party Venue Filtering**: All search methods automatically filter results to party venues
- **Place Search**: Search for nightlife venues by query, location, and filters
- **Autocomplete**: Type-ahead search for quick venue discovery
- **Recommendations**: Get trending party spots based on location
- **Place Details**: Comprehensive venue information including hours, features, photos
- **Photos & Tips**: Access user-generated content for venues
- **Party-Specific Utilities**: Check for alcohol service, live music, late hours

## Installation

The integration requires a Foursquare API key:

```env
EXPO_PUBLIC_FOURSQUARE_API_KEY=your_api_key_here
```

## Usage

### Initialize the Integration

```typescript
import { foursquare } from '@/core/integrations';

// The integration is pre-configured with your API key
```

### Search for Party Venues

```typescript
// Search near a location (automatically filtered to party venues)
const results = await foursquare.searchNearby(
  latitude,
  longitude,
  {
    radius: 1000, // meters
    openNow: true,
    limit: 20
  }
);

// Search by query (automatically includes only party venues)
const bars = await foursquare.searchPlaces({
  query: 'cocktail',
  ll: '40.7,-74.0',
  sort: 'distance'
});
```

### Get Venue Details

```typescript
const venue = await foursquare.getPlaceDetails({
  fsqId: 'venue_id_here',
  fields: FIELD_GROUPS.ALL
});

// Check party-specific features
const servesAlcohol = servesAlcohol(venue);
const hasMusic = hasLiveMusic(venue);
const openLate = isOpenLate(venue);
const features = getPartyFeatures(venue); // Returns emoji-labeled features
```

### Party-Specific Utilities

```typescript
import {
  isPartyVenue,
  filterPartyVenues,
  servesAlcohol,
  hasLiveMusic,
  isOpenLate,
  getPartyTimeCategory,
  getVenueTypeLabel,
  getPartyFeatures,
  isGoodForGroups
} from '@/core/integrations/foursquare/utils';

// Check if it's currently happy hour, late night, etc.
const timeCategory = getPartyTimeCategory(); // 'HAPPY_HOUR' | 'LATE_NIGHT' etc.

// Get venue type for display
const typeLabel = getVenueTypeLabel(venue); // 'Cocktail Bar', 'Nightclub', etc.

// Get party features as emoji labels
const features = getPartyFeatures(venue); // ['🍺 Drinks', '🎵 Live Music', '🌙 Late Night']
```

### Category Groups

Use predefined category groups for targeted searches:

```typescript
import { CATEGORY_GROUPS } from '@/core/integrations/foursquare/constants';

// Search only bars
const bars = await foursquare.searchPlaces({
  query: '',
  categories: CATEGORY_GROUPS.BARS,
  ll: '40.7,-74.0'
});

// Search only clubs
const clubs = await foursquare.searchPlaces({
  query: '',
  categories: CATEGORY_GROUPS.CLUBS,
  ll: '40.7,-74.0'
});

// Search craft drink venues
const breweries = await foursquare.searchPlaces({
  query: '',
  categories: CATEGORY_GROUPS.CRAFT_DRINKS,
  ll: '40.7,-74.0'
});
```

### Time-Based Filtering

```typescript
import { PARTY_TIME_FILTERS } from '@/core/integrations/foursquare/constants';

// Get current party time category
const currentTime = getPartyTimeCategory();

// Time categories:
// HAPPY_HOUR: 4 PM - 7 PM
// DINNER_DRINKS: 7 PM - 10 PM
// LATE_NIGHT: 10 PM - 2 AM
// AFTER_HOURS: 2 AM - 6 AM
```

## API Response Types

### FoursquarePlace

The main venue object includes:
- Basic info: name, location, categories
- Contact: phone, website, social media
- Hours: regular hours, open now status
- Features: alcohol options, seating, amenities
- Media: photos, tips from users
- Ratings and pricing

### Party-Specific Features

```typescript
features.foodAndDrink.alcohol: {
  beer?: boolean;
  beerAndWine?: boolean;
  cocktails?: boolean;
  fullBar?: boolean;
  wine?: boolean;
}

features.amenities: {
  liveMusic?: boolean;
  music?: boolean;
  wifi?: boolean;
}

features.seating: {
  rooftop?: boolean;
  outdoor?: boolean;
  bar?: boolean;
}
```

## Error Handling

All methods throw typed errors:

```typescript
try {
  const venues = await foursquare.searchPlaces({ query: 'bar' });
} catch (error) {
  if (error.code === ERROR_CODES.RATE_LIMITED) {
    // Handle rate limiting
  }
}
```

## Best Practices

1. **Always use location**: Provide either `ll` (latitude,longitude) or `near` parameter
2. **Respect rate limits**: The API has rate limits, cache results when possible
3. **Use field groups**: Only request the fields you need to reduce payload size
4. **Handle missing data**: Not all venues have complete information

## Examples

### Find Late Night Venues

```typescript
const lateNightSpots = await foursquare.searchNearby(lat, lng, {
  radius: 2000,
  openNow: true
});

// Further filter for venues open late
const openLate = lateNightSpots.results.filter(venue => isOpenLate(venue));
```

### Get Tonight's Hot Spots

```typescript
const trendingTonight = await foursquare.getTrendingPlaces(
  lat,
  lng,
  1500 // radius
);

// Add party features to each venue
const venuesWithFeatures = trendingTonight.results.map(venue => ({
  ...venue,
  partyFeatures: getPartyFeatures(venue),
  venueType: getVenueTypeLabel(venue)
}));
```

### Search with Filters

```typescript
// Find affordable bars with outdoor seating
const venues = await foursquare.searchPlaces({
  query: 'bar',
  ll: `${lat},${lng}`,
  maxPrice: 2, // $ or $$
  sort: 'distance'
});

// Filter for outdoor seating
const outdoorVenues = venues.results.filter(
  venue => venue.features?.seating?.outdoor
);
```
