/**
 * Foursquare Places API Constants
 * Focused on nightlife, party venues, and social entertainment
 */

/**
 * Nightlife & Party Foursquare Categories
 * Reference: https://docs.foursquare.com/reference/categories
 */
export const FOURSQUARE_CATEGORIES = {
  // Nightlife & Party Venues
  DINING_AND_DRINKING: '63be6904847c3692a84b9bb5',
  NIGHTLIFE: '4d4b7105d754a06376d81259',
  OTHER_NIGHTLIFE: '4bf58dd8d48988d11a941735',
  BAR: '4bf58dd8d48988d116941735',
  BREWERY: '50327c8591d4c4b30a586d5d',
  COCKTAIL_BAR: '4bf58dd8d48988d11e941735',
  WINE_BAR: '4bf58dd8d48988d123941735',
  BEER_BAR: '56aa371ce4b08b9a8d57356c',
  BEER_GARDEN: '4bf58dd8d48988d117941735',
  DIVE_BAR: '4bf58dd8d48988d118941735',
  SPORTS_BAR: '4bf58dd8d48988d11d941735',
  WHISKY_BAR: '4bf58dd8d48988d122941735',
  LOUNGE: '4bf58dd8d48988d121941735',
  PUB: '4bf58dd8d48988d11b941735',
  IRISH_PUB: '52e81612bcbc57f1066b7a06',
  GASTROPUB: '4bf58dd8d48988d155941735',
  SPEAKEASY: '4bf58dd8d48988d1d4941735',
  KARAOKE_BAR: '4bf58dd8d48988d120941735',

  // Clubs & Dance Venues
  NIGHTCLUB: '4bf58dd8d48988d11f941735',
  DANCE_CLUB: '63be6904847c3692a84b9b23',
  MUSIC_VENUE: '4bf58dd8d48988d1e5931735',
  JAZZ_CLUB: '4bf58dd8d48988d1e7931735',
  BLUES_CLUB: '4bf58dd8d48988d1e7931735',
  ROCK_CLUB: '4bf58dd8d48988d1e9931735',

  // Entertainment (Party-focused)
  CASINO: '10005',
  POOL_HALL: '10037',
  BOWLING_ALLEY: '10004',
  ARCADE: '10002', // Can be party venue

  // Events & Festivals
  EVENT: '63be6904847c3692a84b9bb7',
  FESTIVAL: '5267e4d9e4b0ec79466e48c7',
  MUSIC_FESTIVAL: '5267e4d9e4b0ec79466e48d1',
  BEER_FESTIVAL: '62d587aeda6648532de2b88c',

  // Food & Drink (Party-adjacent)
  ROOFTOP_BAR: '5f2c224bb6d05514c70440a3',
  BEACH_BAR: '52e81612bcbc57f1066b7a0d',
  HOTEL_BAR: '4bf58dd8d48988d1d5941735',
  DISTILLERY: '4e0e22f5a56208c4ea9a85a0',
  WINERY: '4bf58dd8d48988d14b941735',
  BEER_STORE: '5370f356bcbc57f1066c94c2',
} as const;

/**
 * Party venue category groups
 */
export const CATEGORY_GROUPS = {
  // All party venues combined
  PARTY_VENUES: [
    FOURSQUARE_CATEGORIES.BAR,
    FOURSQUARE_CATEGORIES.BREWERY,
    FOURSQUARE_CATEGORIES.COCKTAIL_BAR,
    FOURSQUARE_CATEGORIES.WINE_BAR,
    FOURSQUARE_CATEGORIES.BEER_GARDEN,
    FOURSQUARE_CATEGORIES.DIVE_BAR,
    FOURSQUARE_CATEGORIES.SPORTS_BAR,
    FOURSQUARE_CATEGORIES.WHISKY_BAR,
    FOURSQUARE_CATEGORIES.LOUNGE,
    FOURSQUARE_CATEGORIES.PUB,
    FOURSQUARE_CATEGORIES.SPEAKEASY,
    FOURSQUARE_CATEGORIES.KARAOKE_BAR,
    FOURSQUARE_CATEGORIES.NIGHTCLUB,
    FOURSQUARE_CATEGORIES.DANCE_CLUB,
    FOURSQUARE_CATEGORIES.MUSIC_VENUE,
    FOURSQUARE_CATEGORIES.JAZZ_CLUB,
    FOURSQUARE_CATEGORIES.BLUES_CLUB,
    FOURSQUARE_CATEGORIES.ROCK_CLUB,
    FOURSQUARE_CATEGORIES.CASINO,
    FOURSQUARE_CATEGORIES.POOL_HALL,
    FOURSQUARE_CATEGORIES.BOWLING_ALLEY,
    FOURSQUARE_CATEGORIES.ARCADE,
    FOURSQUARE_CATEGORIES.ROOFTOP_BAR,
    FOURSQUARE_CATEGORIES.BEACH_BAR,
    FOURSQUARE_CATEGORIES.HOTEL_BAR,
    FOURSQUARE_CATEGORIES.DISTILLERY,
    FOURSQUARE_CATEGORIES.WINERY,
  ],

  // Bars specifically
  BARS: [
    FOURSQUARE_CATEGORIES.BAR,
    FOURSQUARE_CATEGORIES.COCKTAIL_BAR,
    FOURSQUARE_CATEGORIES.WINE_BAR,
    FOURSQUARE_CATEGORIES.BEER_GARDEN,
    FOURSQUARE_CATEGORIES.DIVE_BAR,
    FOURSQUARE_CATEGORIES.SPORTS_BAR,
    FOURSQUARE_CATEGORIES.WHISKY_BAR,
    FOURSQUARE_CATEGORIES.PUB,
    FOURSQUARE_CATEGORIES.SPEAKEASY,
    FOURSQUARE_CATEGORIES.KARAOKE_BAR,
    FOURSQUARE_CATEGORIES.ROOFTOP_BAR,
    FOURSQUARE_CATEGORIES.BEACH_BAR,
    FOURSQUARE_CATEGORIES.HOTEL_BAR,
  ],

  // Clubs & Music Venues
  CLUBS: [
    FOURSQUARE_CATEGORIES.NIGHTCLUB,
    FOURSQUARE_CATEGORIES.DANCE_CLUB,
    FOURSQUARE_CATEGORIES.MUSIC_VENUE,
    FOURSQUARE_CATEGORIES.JAZZ_CLUB,
    FOURSQUARE_CATEGORIES.BLUES_CLUB,
    FOURSQUARE_CATEGORIES.ROCK_CLUB,
  ],

  // Breweries & Distilleries
  CRAFT_DRINKS: [
    FOURSQUARE_CATEGORIES.BREWERY,
    FOURSQUARE_CATEGORIES.DISTILLERY,
    FOURSQUARE_CATEGORIES.WINERY,
  ],

  // Party Events
  PARTY_EVENTS: [
    FOURSQUARE_CATEGORIES.EVENT,
    FOURSQUARE_CATEGORIES.FESTIVAL,
    FOURSQUARE_CATEGORIES.MUSIC_FESTIVAL,
    FOURSQUARE_CATEGORIES.BEER_FESTIVAL,
  ],

  // Late Night Entertainment
  LATE_NIGHT: [
    FOURSQUARE_CATEGORIES.CASINO,
    FOURSQUARE_CATEGORIES.POOL_HALL,
    FOURSQUARE_CATEGORIES.BOWLING_ALLEY,
    FOURSQUARE_CATEGORIES.ARCADE,
    FOURSQUARE_CATEGORIES.KARAOKE_BAR,
  ],
} as const;

/**
 * API Configuration defaults
 */
export const API_DEFAULTS = {
  BASE_URL: 'https://places-api.foursquare.com',
  VERSION: '2025-06-17',
  DEFAULT_LIMIT: 20,
  MAX_LIMIT: 50,
  DEFAULT_RADIUS: 1000, // meters
  MAX_RADIUS: 100000, // meters
  DEFAULT_PHOTO_SIZE: '300x300' as const,
  DEFAULT_ICON_SIZE: 64 as const,
} as const;

/**
 * Sort options
 */
export const SORT_OPTIONS = {
  RELEVANCE: 'RELEVANCE',
  DISTANCE: 'DISTANCE',
  RATING: 'RATING',
  POPULARITY: 'POPULARITY',
} as const;

/**
 * Photo sort options
 */
export const PHOTO_SORT = {
  NEWEST: 'newest',
  OLDEST: 'oldest',
  POPULAR: 'popular',
} as const;

/**
 * Photo classifications
 */
export const PHOTO_CLASSIFICATIONS = {
  FOOD: 'food',
  INDOOR: 'indoor',
  OUTDOOR: 'outdoor',
  MENU: 'menu',
} as const;

/**
 * Autocomplete types
 */
export const AUTOCOMPLETE_TYPES = {
  PLACE: 'place',
  ADDRESS: 'address',
  GEO: 'geo',
  POI: 'poi',
} as const;

/**
 * Autocomplete bias options
 */
export const AUTOCOMPLETE_BIAS = {
  PROXIMITY: 'proximity',
  POPULARITY: 'popularity',
} as const;

/**
 * Price levels
 */
export const PRICE_LEVELS = {
  CHEAP: 1,
  MODERATE: 2,
  EXPENSIVE: 3,
  VERY_EXPENSIVE: 4,
} as const;

/**
 * Day of week constants (Foursquare uses 1-7, where 1 is Monday)
 */
export const DAYS_OF_WEEK = {
  MONDAY: 1,
  TUESDAY: 2,
  WEDNESDAY: 3,
  THURSDAY: 4,
  FRIDAY: 5,
  SATURDAY: 6,
  SUNDAY: 7,
} as const;

/**
 * Distance conversion constants
 */
export const DISTANCE_CONVERSION = {
  METERS_TO_MILES: 0.000621371,
  METERS_TO_FEET: 3.28084,
  METERS_TO_KM: 0.001,
  EARTH_RADIUS_KM: 6371,
  EARTH_RADIUS_MI: 3959,
} as const;

/**
 * Error codes
 */
export const ERROR_CODES = {
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR',
  INVALID_API_KEY: 'INVALID_API_KEY',
  RATE_LIMITED: 'RATE_LIMITED',
  NOT_FOUND: 'NOT_FOUND',
  INVALID_PARAMS: 'INVALID_PARAMS',
  SERVER_ERROR: 'SERVER_ERROR',
} as const;

/**
 * Field groups for place details
 */
export const FIELD_GROUPS = {
  BASIC: ['name', 'location', 'categories', 'geocodes'],
  CONTACT: ['tel', 'email', 'website', 'socialMedia'],
  HOURS: ['hours', 'timezone'],
  RATINGS: ['rating', 'stats', 'popularity'],
  FEATURES: ['features', 'price', 'verified'],
  MEDIA: ['photos', 'tips'],
  PARTY_FEATURES: [
    'features.foodAndDrink.alcohol',
    'features.amenities.music',
    'features.amenities.liveMusic',
    'features.foodAndDrink.meals.lateNight',
    'features.seating.rooftop',
    'features.seating.outdoor',
  ],
  ALL: [
    'name',
    'location',
    'categories',
    'chains',
    'geocodes',
    'link',
    'relatedPlaces',
    'timezone',
    'description',
    'tel',
    'fax',
    'email',
    'website',
    'socialMedia',
    'verified',
    'hours',
    'rating',
    'price',
    'stats',
    'popularity',
    'photos',
    'tips',
    'tastes',
    'features',
    'storeId',
  ],
} as const;

/**
 * Party time filters
 */
export const PARTY_TIME_FILTERS = {
  HAPPY_HOUR: { start: 16, end: 19 }, // 4 PM - 7 PM
  DINNER_DRINKS: { start: 19, end: 22 }, // 7 PM - 10 PM
  LATE_NIGHT: { start: 22, end: 2 }, // 10 PM - 2 AM
  AFTER_HOURS: { start: 2, end: 6 }, // 2 AM - 6 AM
} as const;
