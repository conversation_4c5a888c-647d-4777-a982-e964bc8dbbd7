// Re-import types for convenience
import { API_DEFAULTS } from './constants';
import { FoursquarePlacesIntegration } from './foursquare';
import type { FoursquareConfig, FoursquareError, FoursquarePlace } from './types';

/**
 * Foursquare Places API Integration
 * Export all types, constants, utilities, and main integration
 */

// Main integration class
export { FoursquarePlacesIntegration } from './foursquare';

// Export all types
export * from './types';

// Export all constants
export {
  FOURSQUARE_CATEGORIES,
  CATEGORY_GROUPS,
  API_DEFAULTS,
  SORT_OPTIONS,
  PHOTO_SORT,
  PHOTO_CLASSIFICATIONS,
  AUTOCOMPLETE_TYPES,
  AUTOCOMPLETE_BIAS,
  PRICE_LEVELS,
  DAYS_OF_WEEK,
  DISTANCE_CONVERSION,
  ERROR_CODES,
  FIELD_GROUPS,
  PARTY_TIME_FILTERS,
} from './constants';

// Export utility functions
export {
  calculateDistance,
  toRad,
  formatDistance,
  isOpenNow,
  getFormattedAddress,
  getPrimaryCategory,
  getRatingStars,
  getPriceDisplay,
  getPhotoUrl,
  getCategoryIconUrl,
  parseCoordinates,
  formatCoordinates,
  getAutocompleteDisplayName,
  getUniquePlaces,
  sortByDistance,
  sortByRating,
  areCoordinatesValid,
  getHoursDisplay,
  hasFeature,
  getDayName,
  formatTime,
  truncateText,
  getSocialPlatform,
  buildQueryString,
  // Party-specific utilities
  isPartyVenue,
  filterPartyVenues,
  servesAlcohol,
  hasLiveMusic,
  isOpenLate,
  getPartyTimeCategory,
  getVenueTypeLabel,
  getPartyFeatures,
  isGoodForGroups,
} from './utils';

// Factory function for creating integration instances
export function createFoursquareIntegration(
  apiKey: string,
  config?: Partial<FoursquareConfig>
): FoursquarePlacesIntegration {
  return new FoursquarePlacesIntegration({
    apiKey,
    ...config,
  });
}

// Global instance (requires API key to be set)
export const foursquare = new FoursquarePlacesIntegration({
  apiKey: process.env.EXPO_PUBLIC_FOURSQUARE_API_KEY || '',
  // Use the new Places API base URL
  baseUrl: process.env.EXPO_PUBLIC_FOURSQUARE_BASE_URL || API_DEFAULTS.BASE_URL,
  version: process.env.EXPO_PUBLIC_FOURSQUARE_VERSION || API_DEFAULTS.VERSION,
});

// Type guard functions
export function isFoursquareError(error: any): error is FoursquareError {
  return error && typeof error.code === 'string' && typeof error.message === 'string';
}

export function hasPlaceHours(place: FoursquarePlace): boolean {
  return !!place.hours && (!!place.hours.regular || !!place.hours.seasonal);
}

export function hasPlacePhotos(place: FoursquarePlace): boolean {
  return !!place.photos && place.photos.length > 0;
}

export function hasPlaceTips(place: FoursquarePlace): boolean {
  return !!place.tips && place.tips.length > 0;
}

export function hasPlaceRating(place: FoursquarePlace): boolean {
  return typeof place.rating === 'number' && place.rating > 0;
}
