/**
 * Foursquare Places API Type Definitions
 * Comprehensive TypeScript interfaces for all Foursquare data structures
 */
import { CATEGORY_GROUPS } from './constants';

/**
 * Foursquare API Configuration
 */
export interface FoursquareConfig {
  apiKey: string;
  version?: string;
  baseUrl?: string;
}

/**
 * Place search parameters
 */
export interface PlaceSearchParams {
  query: string;
  ll?: string; // latitude,longitude format: "40.7,-74"
  near?: string; // Near location (e.g., "Chicago, IL")
  radius?: number; // Search radius in meters (max 100000)
  categories?: CategoryId[] | string[]; // Category IDs to filter
  limit?: number; // Number of results (max 50)
  sort?: 'relevance' | 'distance' | 'rating' | 'popularity';
  openNow?: boolean;
  minPrice?: number; // Price level 1-4
  maxPrice?: number;
  cursor?: string; // Pagination cursor for next page
}

/**
 * Place details parameters
 */
// Update PlaceDetailsParams to use fsq_place_id
export interface PlaceDetailsParams {
  fsq_place_id: string; // Changed from fsqId
  fields?: string[];
}

export interface PhotoParams {
  fsq_place_id: string; // Changed from fsqId
  limit?: number;
  sort?: 'newest' | 'oldest' | 'popular';
  classifications?: string[];
  prefix: string;
  suffix: string;
  width: number;
  height: number;
}

/**
 * Autocomplete parameters
 */
export interface AutocompleteParams {
  query: string;
  ll?: string;
  radius?: number;
  limit?: number;
  types?: ('place' | 'address' | 'geo' | 'poi')[];
  bias?: 'proximity' | 'popularity';
}

/**
 * Place recommendations parameters
 */
export interface RecommendationsParams {
  ll?: string;
  near?: string;
  radius?: number;
  categories?: CategoryId[];
  limit?: number;
  openNow?: boolean;
  sort?: 'relevance' | 'distance' | 'rating' | 'popularity';
  cursor?: string; // Pagination cursor for next page
}

/**
 * Core place data structure
 */
export interface FoursquarePlace {
  // Change from fsq_id to fsq_place_id
  fsq_place_id: string; // Previously fsq_id
  name: string;
  categories: FoursquareCategory[];
  chains?: FoursquareChain[];
  distance?: number;
  // Update geocodes to simple coordinates
  latitude?: number; // Previously in geocodes
  longitude?: number; // Previously in geocodes
  geocodes?: {
    drop_off?: Geocode;
    front_door?: Geocode;
    main?: Geocode;
    road?: Geocode;
    roof?: Geocode;
  };
  link?: string;
  location: FoursquareLocation;
  related_places?: FoursquareRelatedPlaces;
  description?: string;
  tel?: string;
  email?: string;
  website?: string;
  social_media?: FoursquareSocialMedia;
  hours?: FoursquareHours;
  hours_popular?: FoursquareHoursPopular[];
  rating?: number;
  price?: number;
  stats?: FoursquareStats;
  popularity?: number;
  photos?: FoursquarePhoto[];
  tips?: FoursquareTip[];
  tastes?: string[];
  // Changed from features to attributes
  attributes?: FoursquareAttributes; // Previously features
  store_id?: string;
  // New fields
  date_closed?: string; // New field for closed venues
  extended_location?: {
    // New object
    dma?: string;
    census_block?: string;
  };
}

export interface FoursquareCategory {
  id: CategoryId;
  fsq_category_id: string;

  name: string;
  icon: CategoryIcon;
  primary?: boolean;
  short_name: string;
  plural_name: string;
}

export interface FoursquareChain {
  id: string;
  name: string;
}

export interface Geocode {
  latitude: number;
  longitude: number;
}

export interface FoursquareRelatedPlaces {
  count: number;
  items: FoursquarePlace[];
}

export interface FoursquareSocialMedia {
  facebook?: string;
  instagram?: string;
  twitter?: string;
  website?: string;
}

export interface FoursquareStats {
  checkins_count: number;
  users_count: number;
  tip_count: number;
}

export interface FoursquarePhoto {
  id: string;
  createdAt: number;
  prefix: string;
  suffix: string;
  width: number;
  height: number;
}

export interface FoursquareTip {
  id: string;
  createdAt: number;
  text: string;
  agreeCount: number;
  disagreeCount: number;
}

export interface FoursquareTipPhoto {
  id: string;
  createdAt: number;
  prefix: string;
  suffix: string;
  width: number;
  height: number;
}

export interface FoursquareHours {
  open: FoursquareRegularHours[];
  close: FoursquareRegularHours[];
  timezone: string;
  display: string;
  is_local_holiday: boolean;
  open_now: boolean;
  regular: FoursquareRegularHours[];
  seasonal: FoursquareSeasonalHours[];
}

export interface FoursquareSeasonalHours {
  start: string;
  end: string;
  open: FoursquareRegularHours[];
  close: FoursquareRegularHours[];
}

export interface FoursquareHoursPopular {
  open: FoursquareRegularHours[];
  close: FoursquareRegularHours[];
  timezone: string;
}

export interface FoursquareRegularHours {
  day: number;
  open: string;
  close: string;
}

export interface FoursquareLocation {
  address?: string;
  address_extended?: string;
  admin_region?: string;
  census_block?: string;
  country?: string;
  cross_street?: string;
  dma?: string;
  formatted_address: string;
  locality?: string;
  neighborhood?: string[];
  post_town?: string;
  postcode?: string;
  region?: string;
  po_box: string;
}

export interface FoursquareAttributes {
  restroom: unknown;
  outdoor_seating: unknown;
  atm: unknown;
  has_parking: unknown;
  wifi: string;
  delivery: unknown;
  reservations: unknown;
  takes_credit_card: unknown;
}

/**
 * Place location details
 */
export interface PlaceLocation {
  address?: string;
  addressExtended?: string;
  adminRegion?: string;
  censusBlock?: string;
  country?: string;
  crossStreet?: string;
  dma?: string;
  formattedAddress: string;
  locality?: string;
  neighborhood?: string[];
  postTown?: string;
  postcode?: string;
  region?: string;
}

/**
 * Place category
 */
export interface PlaceCategory {
  id: CategoryId;
  name: string;
  icon: CategoryIcon;
  primary?: boolean;
}

/**
 * Category icon
 */
export interface CategoryIcon {
  id: string;
  created_at: string;
  prefix: string;
  suffix: string;
  width: number;
  height: number;
  classifications: string[];
  tip: Tip;
}

export interface Tip {
  id: string;
  created_at: string;
  text: string;
  url: string;
  lang: string;
  agree_count: number;
  disagree_count: number;
}

export interface Chain {
  fsq_chain_id: string;
  name: string;
  logo: Logo;
  parent_id: string;
}

export interface Logo {
  id: string;
  created_at: string;
  prefix: string;
  suffix: string;
  width: number;
  height: number;
  classifications: string[];
  tip: Tip;
}

/**
 * Place chain information
 */
export interface PlaceChain {
  id: string;
  name: string;
}

/**
 * Place geocodes
 */
export interface PlaceGeocodes {
  dropOff?: GeoPoint;
  frontDoor?: GeoPoint;
  main: GeoPoint;
  road?: GeoPoint;
  roof?: GeoPoint;
}

/**
 * Geographic point
 */
export interface GeoPoint {
  latitude: number;
  longitude: number;
}

/**
 * Related places
 */
export interface RelatedPlaces {
  parent?: RelatedPlace;
  children?: RelatedPlace[];
}

/**
 * Related place reference
 */
export interface RelatedPlace {
  fsqId: string;
  name: string;
}

/**
 * Social media links
 */
export interface SocialMedia {
  facebook?: string;
  instagram?: string;
  twitter?: string;
  linkedin?: string;
  snapchat?: string;
}

/**
 * Place hours
 */
export interface PlaceHours {
  display?: string;
  isLocalHoliday?: boolean;
  openNow?: boolean;
  regular?: RegularHours[];
  seasonal?: SeasonalHours[];
}

/**
 * Regular hours
 */
export interface RegularHours {
  close?: string;
  day: number;
  open?: string;
}

/**
 * Seasonal hours
 */
export interface SeasonalHours {
  closed?: boolean;
  endDate?: string;
  hours?: RegularHours[];
  name?: string;
  startDate?: string;
}

/**
 * Place statistics
 */
export interface PlaceStats {
  totalPhotos?: number;
  totalRatings?: number;
  totalTips?: number;
}

/**
 * Place photo
 */
export interface PlacePhoto {
  id: string;
  createdAt: string;
  prefix: string;
  suffix: string;
  width: number;
  height: number;
  classifications?: string[];
  tip?: PhotoTip;
}

/**
 * Photo tip reference
 */
export interface PhotoTip {
  id: string;
  createdAt: string;
  text: string;
  url?: string;
  photo?: string;
  photoUrl?: string;
  agreeCount?: number;
  disagreeCount?: number;
}

/**
 * Place tip
 */
export interface PlaceTip {
  id: string;
  createdAt: string;
  text: string;
  url?: string;
  photo?: PlacePhoto;
  agreeCount?: number;
  disagreeCount?: number;
}

/**
 * Place features
 */
export interface PlaceFeatures {
  payment?: PaymentOptions;
  foodAndDrink?: FoodAndDrinkOptions;
  services?: ServiceOptions;
  amenities?: AmenityOptions;
}

/**
 * Payment options
 */
export interface PaymentOptions {
  creditCards?: CreditCardOptions;
  digitalWallet?: DigitalWalletOptions;
}

/**
 * Credit card options
 */
export interface CreditCardOptions {
  accepts?: boolean;
  amex?: boolean;
  discover?: boolean;
  visa?: boolean;
  mastercard?: boolean;
  unionPay?: boolean;
}

/**
 * Digital wallet options
 */
export interface DigitalWalletOptions {
  accepts?: boolean;
  applePay?: boolean;
  googlePay?: boolean;
  samsungPay?: boolean;
  paypal?: boolean;
}

/**
 * Food and drink options
 */
export interface FoodAndDrinkOptions {
  alcohol?: AlcoholOptions;
  meals?: MealOptions;
  parking?: ParkingOptions;
  reservations?: ReservationOptions;
  seating?: SeatingOptions;
  takeout?: boolean;
  delivery?: boolean;
  driveThrough?: boolean;
}

/**
 * Alcohol options
 */
export interface AlcoholOptions {
  beer?: boolean;
  beerAndWine?: boolean;
  cocktails?: boolean;
  fullBar?: boolean;
  wine?: boolean;
}

/**
 * Meal options
 */
export interface MealOptions {
  breakfast?: boolean;
  brunch?: boolean;
  lunch?: boolean;
  dinner?: boolean;
  dessert?: boolean;
  lateNight?: boolean;
}

/**
 * Parking options
 */
export interface ParkingOptions {
  parking?: boolean;
  privateParking?: boolean;
  publicParking?: boolean;
  valetParking?: boolean;
  streetParking?: boolean;
}

/**
 * Reservation options
 */
export interface ReservationOptions {
  reservations?: boolean;
  onlineReservations?: boolean;
}

/**
 * Seating options
 */
export interface SeatingOptions {
  outdoor?: boolean;
  indoor?: boolean;
  bar?: boolean;
  waterfront?: boolean;
  rooftop?: boolean;
}

/**
 * Service options
 */
export interface ServiceOptions {
  dineIn?: boolean;
  takeout?: boolean;
  delivery?: boolean;
  catering?: boolean;
  driveThrough?: boolean;
  curbsidePickup?: boolean;
}

/**
 * Amenity options
 */
export interface AmenityOptions {
  wifi?: boolean;
  parking?: boolean;
  restroom?: boolean;
  smokingArea?: boolean;
  wheelchair?: boolean;
  powerOutlets?: boolean;
  tvs?: boolean;
  music?: boolean;
  liveMusic?: boolean;
}

/**
 * Autocomplete result
 */
export interface AutocompleteResult {
  type: 'place' | 'address' | 'geo' | 'search';
  text: AutocompleteText;
  place?: FoursquarePlace;
  address?: AutocompleteAddress;
  geo?: AutocompleteGeo;
}

/**
 * Autocomplete text
 */
export interface AutocompleteText {
  primary: string;
  secondary?: string;
  highlight?: TextHighlight[];
}

/**
 * Text highlight
 */
export interface TextHighlight {
  start: number;
  length: number;
}

/**
 * Autocomplete address
 */
export interface AutocompleteAddress {
  address?: string;
  censusBlock?: string;
  country?: string;
  crossStreet?: string;
  dma?: string;
  formattedAddress?: string;
  locality?: string;
  postcode?: string;
  region?: string;
}

/**
 * Autocomplete geo
 */
export interface AutocompleteGeo {
  geocodes: PlaceGeocodes;
  name?: string;
  country?: string;
  state?: string;
  locality?: string;
}

/**
 * API Response wrapper
 */
export interface FoursquareResponse<T> {
  results: T[];
  context?: ResponseContext;
  pagination?: FoursquarePagination;
}

/**
 * Pagination information
 */
export interface FoursquarePagination {
  link?: string; // URL for next page
  cursor?: string; // Cursor token for next page
  hasMore?: boolean; // Whether more results exist
}

/**
 * Response context
 */
export interface ResponseContext {
  geoBounds?: GeoBounds;
}

/**
 * Geographic bounds
 */
export interface GeoBounds {
  circle?: {
    center: GeoPoint;
    radius: number;
  };
}

/**
 * Foursquare error structure
 */
export interface FoursquareError {
  code: string;
  message: string;
  detail?: string;
}

/**
 * Photo size options
 */
export type PhotoSize = '100x100' | '300x300' | '500x500' | 'original';

/**
 * Icon size options
 */
export type IconSize = 32 | 44 | 64 | 88 | 120;

/**
 * Distance unit options
 */
export type DistanceUnit = 'metric' | 'imperial';

/**
 * Distance calculation unit
 */
export type DistanceCalcUnit = 'km' | 'mi';

export type CategoryId = (typeof CATEGORY_GROUPS.PARTY_VENUES)[number];
