/**
 * Network Integration Layer
 * Provides a clean abstraction for HTTP requests and file uploads
 */

export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

export interface NetworkResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
}

export interface NetworkError {
  message: string;
  status?: number;
  statusText?: string;
  code?: string;
}

export interface UploadOptions {
  onProgress?: (progress: UploadProgress) => void;
  headers?: Record<string, string>;
  timeout?: number;
}

/**
 * Network service interface
 */
export interface INetwork {
  get<T>(url: string, headers?: Record<string, string>): Promise<NetworkResponse<T>>;
  post<T>(url: string, data?: any, headers?: Record<string, string>): Promise<NetworkResponse<T>>;
  put<T>(url: string, data?: any, headers?: Record<string, string>): Promise<NetworkResponse<T>>;
  delete<T>(url: string, headers?: Record<string, string>): Promise<NetworkResponse<T>>;
  uploadFile(url: string, fileUri: string, options?: UploadOptions): Promise<NetworkResponse>;
  uploadFormData(
    url: string,
    formData: FormData,
    options?: UploadOptions
  ): Promise<NetworkResponse>;
}

/**
 * Fetch-based network implementation
 */
class FetchNetwork implements INetwork {
  private baseURL: string;
  private defaultHeaders: Record<string, string>;
  private timeout: number;

  constructor(
    baseURL: string = '',
    defaultHeaders: Record<string, string> = {},
    timeout: number = 30000
  ) {
    this.baseURL = baseURL;
    this.defaultHeaders = defaultHeaders;
    this.timeout = timeout;
  }

  async get<T>(url: string, headers?: Record<string, string>): Promise<NetworkResponse<T>> {
    return this.request<T>('GET', url, undefined, headers);
  }

  async post<T>(
    url: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<NetworkResponse<T>> {
    return this.request<T>('POST', url, data, headers);
  }

  async put<T>(
    url: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<NetworkResponse<T>> {
    return this.request<T>('PUT', url, data, headers);
  }

  async delete<T>(url: string, headers?: Record<string, string>): Promise<NetworkResponse<T>> {
    return this.request<T>('DELETE', url, undefined, headers);
  }

  async uploadFile(
    url: string,
    fileUri: string,
    options: UploadOptions = {}
  ): Promise<NetworkResponse> {
    const formData = new FormData();
    formData.append('file', {
      uri: fileUri,
      type: 'image/jpeg',
      name: 'upload.jpg',
    } as any);

    return this.uploadFormData(url, formData, options);
  }

  async uploadFormData(
    url: string,
    formData: FormData,
    options: UploadOptions = {}
  ): Promise<NetworkResponse> {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      const fullUrl = this.buildUrl(url);

      // Set timeout
      xhr.timeout = options.timeout || this.timeout;

      // Track upload progress
      if (options.onProgress) {
        xhr.upload.onprogress = event => {
          if (event.lengthComputable) {
            const progress: UploadProgress = {
              loaded: event.loaded,
              total: event.total,
              percentage: Math.round((event.loaded / event.total) * 100),
            };
            options.onProgress!(progress);
          }
        };
      }

      xhr.onload = () => {
        try {
          const data = xhr.responseText ? JSON.parse(xhr.responseText) : null;
          const response: NetworkResponse = {
            data,
            status: xhr.status,
            statusText: xhr.statusText,
            headers: this.parseResponseHeaders(xhr.getAllResponseHeaders()),
          };

          if (xhr.status >= 200 && xhr.status < 300) {
            resolve(response);
          } else {
            reject(
              this.createNetworkError(
                `Upload failed with status ${xhr.status}`,
                xhr.status,
                xhr.statusText
              )
            );
          }
        } catch (error) {
          reject(this.createNetworkError('Invalid server response'));
        }
      };

      xhr.onerror = () => {
        reject(this.createNetworkError('Network error during upload'));
      };

      xhr.ontimeout = () => {
        reject(this.createNetworkError('Upload timeout'));
      };

      // Set headers
      const allHeaders = { ...this.defaultHeaders, ...options.headers };
      Object.entries(allHeaders).forEach(([key, value]) => {
        xhr.setRequestHeader(key, value);
      });

      xhr.open('POST', fullUrl);
      xhr.send(formData);
    });
  }

  private async request<T>(
    method: string,
    url: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<NetworkResponse<T>> {
    const fullUrl = this.buildUrl(url);
    try {
      const allHeaders = { ...this.defaultHeaders, ...headers };

      // Create AbortController for timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.timeout);

      const config: RequestInit = {
        method,
        headers: allHeaders,
        signal: controller.signal,
      };

      if (data && method !== 'GET') {
        if (typeof data === 'object') {
          config.body = JSON.stringify(data);
          allHeaders['Content-Type'] = 'application/json';
        } else {
          config.body = data;
        }
      }

      const response = await fetch(fullUrl, config);

      // Clear the timeout
      clearTimeout(timeoutId);

      if (!response.ok) {
        // For error responses, get the error details
        let errorDetails = '';
        try {
          const errorText = await response.text();
          errorDetails = errorText ? ` - ${errorText}` : '';
        } catch (e) {
          // Ignore error parsing error response
        }

        throw this.createNetworkError(
          `Request failed with status ${response.status}${errorDetails}`,
          response.status,
          response.statusText
        );
      }

      let responseData: T;
      const contentType = response.headers.get('content-type');

      if (contentType?.includes('application/json')) {
        responseData = await response.json();
      } else {
        responseData = (await response.text()) as unknown as T;
      }

      const networkResponse: NetworkResponse<T> = {
        data: responseData,
        status: response.status,
        statusText: response.statusText,
        headers: this.convertHeaders(response.headers),
      };

      return networkResponse;
    } catch (error) {
      console.error('Network request error details:', {
        error,
        errorName: error instanceof Error ? error.name : 'Unknown',
        errorMessage: error instanceof Error ? error.message : error,
        url: fullUrl,
        method,
      });

      if (error instanceof Error) {
        // Check if it's an abort error (timeout)
        if (error.name === 'AbortError') {
          throw this.createNetworkError('Request timeout');
        }
        throw this.createNetworkError(error.message);
      }
      throw this.createNetworkError('Unknown network error');
    }
  }

  private buildUrl(url: string): string {
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }
    return `${this.baseURL}${url.startsWith('/') ? url : '/' + url}`;
  }

  private convertHeaders(headers: Headers): Record<string, string> {
    const result: Record<string, string> = {};
    headers.forEach((value, key) => {
      result[key] = value;
    });
    return result;
  }

  private parseResponseHeaders(headerString: string): Record<string, string> {
    const headers: Record<string, string> = {};
    headerString.split('\r\n').forEach(line => {
      const parts = line.split(': ');
      if (parts.length === 2) {
        headers[parts[0]] = parts[1];
      }
    });
    return headers;
  }

  private createNetworkError(message: string, status?: number, statusText?: string): NetworkError {
    return {
      message,
      status,
      statusText,
      code: status ? `HTTP_${status}` : 'NETWORK_ERROR',
    };
  }
}

/**
 * Global network instance
 */
export const network = new FetchNetwork(
  process.env.EXPO_PUBLIC_API_URL || 'https://api.movuca.app',
  {
    'Content-Type': 'application/json',
  }
);

/**
 * Network factory for testing and custom configurations
 */
export const createNetwork = (
  baseURL?: string,
  headers?: Record<string, string>,
  timeout?: number
): INetwork => new FetchNetwork(baseURL, headers, timeout);
