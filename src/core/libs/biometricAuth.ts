/**
 * Biometric authentication integration
 * Abstracts expo-local-authentication for security features
 */
import * as LocalAuthentication from 'expo-local-authentication';

/**
 * Biometric authentication types
 */
export interface BiometricType {
  FINGERPRINT: 'fingerprint';
  FACIAL_RECOGNITION: 'facial_recognition';
  IRIS: 'iris';
}

export interface BiometricAuthResult {
  success: boolean;
  error?: string;
  warning?: string;
}

export interface BiometricCapabilities {
  isAvailable: boolean;
  hasHardware: boolean;
  isEnrolled: boolean;
  supportedTypes: string[];
}

/**
 * Biometric authentication integration class
 */
class BiometricAuthIntegration {
  /**
   * Check if biometric authentication is available
   */
  async isAvailable(): Promise<boolean> {
    try {
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      return hasHardware && isEnrolled;
    } catch (error) {
      console.warn('Biometric availability check failed:', error);
      return false;
    }
  }

  /**
   * Get detailed biometric capabilities
   */
  async getCapabilities(): Promise<BiometricCapabilities> {
    try {
      const [hasHardware, isEnrolled, supportedTypes] = await Promise.all([
        LocalAuthentication.hasHardwareAsync(),
        LocalAuthentication.isEnrolledAsync(),
        LocalAuthentication.supportedAuthenticationTypesAsync(),
      ]);

      const typeNames = supportedTypes.map(type => {
        switch (type) {
          case LocalAuthentication.AuthenticationType.FINGERPRINT:
            return 'fingerprint';
          case LocalAuthentication.AuthenticationType.FACIAL_RECOGNITION:
            return 'facial_recognition';
          case LocalAuthentication.AuthenticationType.IRIS:
            return 'iris';
          default:
            return 'unknown';
        }
      });

      return {
        isAvailable: hasHardware && isEnrolled,
        hasHardware,
        isEnrolled,
        supportedTypes: typeNames,
      };
    } catch (error) {
      console.warn('Failed to get biometric capabilities:', error);
      return {
        isAvailable: false,
        hasHardware: false,
        isEnrolled: false,
        supportedTypes: [],
      };
    }
  }

  /**
   * Authenticate user with biometrics
   */
  async authenticate(options?: {
    promptMessage?: string;
    cancelLabel?: string;
    fallbackLabel?: string;
    disableDeviceFallback?: boolean;
  }): Promise<BiometricAuthResult> {
    try {
      const isAvailable = await this.isAvailable();

      if (!isAvailable) {
        return {
          success: false,
          error: 'Biometric authentication is not available',
        };
      }

      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: options?.promptMessage || 'Authenticate to continue',
        cancelLabel: options?.cancelLabel || 'Cancel',
        fallbackLabel: options?.fallbackLabel || 'Use Passcode',
        disableDeviceFallback: options?.disableDeviceFallback || false,
      });

      if (result.success) {
        return { success: true };
      }

      return {
        success: false,
        error: result.error || 'Authentication failed',
        warning: result.warning,
      };
    } catch (error) {
      console.error('Biometric authentication error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Authentication failed',
      };
    }
  }

  /**
   * Get security level of device
   */
  async getSecurityLevel(): Promise<'none' | 'biometric' | 'secret'> {
    try {
      const securityLevel = await LocalAuthentication.getEnrolledLevelAsync();

      switch (securityLevel) {
        case LocalAuthentication.SecurityLevel.NONE:
          return 'none';
        case LocalAuthentication.SecurityLevel.SECRET:
          return 'secret';
        case LocalAuthentication.SecurityLevel.BIOMETRIC_STRONG:
          return 'biometric';
        default:
          return 'none';
      }
    } catch (error) {
      console.warn('Failed to get security level:', error);
      return 'none';
    }
  }

  /**
   * Check if device has biometric hardware but no enrollments
   */
  async needsEnrollment(): Promise<boolean> {
    try {
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      return hasHardware && !isEnrolled;
    } catch (error) {
      console.warn('Failed to check enrollment status:', error);
      return false;
    }
  }
}

// Export singleton instance
export const biometricAuth = new BiometricAuthIntegration();
