{"name": "movuca-mobile-app", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start --dev-client", "ios": "expo run:ios", "android": "expo run:android", "check-config": "expo config", "build:dev": "eas build --profile development", "build:preview": "eas build --profile preview", "build:prod": "eas build --profile production", "prebuild": "expo prebuild", "lint": "eslint \"**/*.{js,jsx,ts,tsx}\" --fix", "lint:check": "eslint \"**/*.{js,jsx,ts,tsx}\"", "format": "prettier \"**/*.{js,jsx,ts,tsx,json}\" --write", "web": "expo start --web", "test": "jest --watchAll", "test:ci": "jest --ci --maxWorkers=4 --silent", "test:dev": "jest --detect<PERSON><PERSON><PERSON><PERSON><PERSON> --coverage=false", "eject": "expo eject"}, "dependencies": {"@allisonadam81/react-native-super-clusters": "^0.0.1", "@animatereactnative/accordion": "^0.1.5", "@animatereactnative/marquee": "^0.5.2", "@animatereactnative/stagger": "^0.3.0", "@apollo/client": "^3.13.8", "@d11/react-native-fast-image": "^8.9.2", "@expo-google-fonts/urbanist": "^0.3.0", "@expo/config-plugins": "~10.1.1", "@expo/vector-icons": "^14.1.0", "@gorhom/bottom-sheet": "^5.1.6", "@hookform/resolvers": "^5.1.1", "@legendapp/list": "^1.1.4", "@marceloterreiro/flash-calendar": "^1.3.0", "@notifee/react-native": "^9.1.8", "@react-native-community/netinfo": "^11.4.1", "@react-native-community/slider": "4.5.6", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/elements": "^2.5.2", "@react-navigation/material-top-tabs": "^7.3.2", "@react-navigation/native": "^7.1.14", "@react-navigation/native-stack": "^7.3.21", "@rnmapbox/maps": "^10.1.39", "@s77rt/react-native-date-picker": "^3.1.0", "@shopify/flash-list": "1.7.6", "@shopify/react-native-skia": "2.1.1", "@shopify/restyle": "2.4.5", "@tanstack/react-query": "^5.81.5", "burnt": "^0.13.0", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "expo": "53.0.16", "expo-apple-authentication": "~7.2.4", "expo-application": "~6.1.5", "expo-asset": "~11.1.6", "expo-auth-session": "~6.2.0", "expo-background-task": "~0.2.8", "expo-blur": "~14.1.5", "expo-build-properties": "~0.14.8", "expo-cellular": "~7.1.5", "expo-constants": "~17.1.6", "expo-crypto": "~14.1.5", "expo-dev-client": "~5.2.3", "expo-device": "~7.1.4", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image-manipulator": "~13.1.7", "expo-image-picker": "~16.1.4", "expo-linking": "~7.1.6", "expo-local-authentication": "~16.0.5", "expo-localization": "~16.1.6", "expo-location": "~18.1.6", "expo-navigation-bar": "~4.2.7", "expo-network": "~7.1.5", "expo-secure-store": "~14.2.3", "expo-sharing": "~13.1.5", "expo-status-bar": "~2.2.3", "expo-store-review": "~8.1.5", "expo-system-ui": "~5.0.10", "expo-updates": "~0.28.15", "expo-web-browser": "~14.2.0", "graphql": "16.10.0", "i18next": "^25.3.0", "immer": "^10.1.1", "jwt-decode": "^4.0.0", "libphonenumber-js": "^1.12.9", "phosphor-react-native": "^2.3.1", "react": "19.0.0", "react-compiler-runtime": "19.1.0-rc.2", "react-dom": "19.0.0", "react-hook-form": "^7.59.0", "react-i18next": "^15.5.3", "react-native": "0.79.5", "react-native-flexible-grid": "^0.2.4", "react-native-gesture-handler": "~2.24.0", "react-native-keyboard-controller": "^1.17.5", "react-native-magic-modal": "^6.1.0", "react-native-maps": "1.20.1", "react-native-mmkv": "^3.3.0", "react-native-pager-view": "6.7.1", "react-native-reanimated": "~3.17.5", "react-native-reanimated-carousel": "^4.0.2", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-tab-view": "^4.1.2", "react-native-toast-message": "^2.3.3", "react-native-web": "^0.20.0", "rive-react-native": "^9.3.4", "zod": "^3.25.71", "zustand": "5.0.3"}, "devDependencies": {"@babel/core": "^7.28.0", "@csark0812/zustand-expo-devtools": "^2.0.2", "@tanstack/react-query-devtools": "^4.40.0", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.0", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/crypto-js": "^4.2.2", "@types/jest": "^29.5.14", "@types/react": "~19.0.14", "@typescript-eslint/eslint-plugin": "8.30.1", "@typescript-eslint/parser": "8.30.1", "ajv": "^8.17.1", "babel-plugin-react-compiler": "^19.1.0-rc.2", "eslint": "^9.30.1", "eslint-config-expo": "~9.2.0", "eslint-config-prettier": "^10.1.5", "eslint-config-universe": "^12.1.0", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react-compiler": "^19.0.0-beta-ebf51a3-20250411", "eslint-plugin-react-hooks": "^6.0.0", "jest": "~29.7.0", "jest-expo": "~53.0.8", "prettier": "^3.6.2", "ts-node": "^10.9.2", "typescript": "~5.8.3"}, "overrides": {"react": "19.0.0", "@expo/config-plugins": "~10.1.1"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["immer", "jwt-decode", "react-native-magic-modal"], "listUnknownPackages": false}}}, "private": true, "patchedDependencies": {"@s77rt/react-native-date-picker@3.1.0": "patches/@<EMAIL>"}}