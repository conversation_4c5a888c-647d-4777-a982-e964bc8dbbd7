{"alerts": {"confirmDelete": {"message": "This action cannot be undone.", "title": "Delete {{item}}?"}, "confirmLogout": {"message": "You'll need to sign in again to access your account.", "title": "Sign out?"}, "unsavedChanges": {"message": "Your unsaved changes will be lost.", "title": "Discard changes?"}}, "auth": {"createAccount": "Create account", "email": "Email", "forgotPassword": "Forgot password?", "login": "Log in", "logout": "Log out", "password": "Password", "signup": "Sign up", "continue": "Continue", "welcomeBack": "Welcome back", "welcomeMessage": "Log in or Sign up", "emailOptionTitle": "Use your email", "phoneOptionTitle": "Use your phone number", "magicLinkOptionTitle": "Use magic link", "or": "OR", "phoneNumberRegistrationTitle": "What's your phone number?", "phoneNumberRegistrationLabel": "Phone number", "phoneNumberRegistrationMessage": "We'll send you a one-time code via SMS to log in.", "phoneNumberRegistrationPlaceholder": "+55 9 9999-9999", "phoneNumberRegistrationButton": "Send code", "chooseMethodSubtitle": "Choose how you'd like to continue", "passwordlessTitle": "Sign in with magic link", "passwordlessSubtitle": "We'll send a magic link to your email address", "emailLabel": "Email", "emailPlaceholder": "Enter your email", "sendMagicLink": "Send magic link", "magicLinkInfo": "Check your email for a secure link to sign in. No password needed!", "dontHaveAccount": "Don't have an account?", "signUp": "Sign up", "verifyTokenTitle": "Check your email", "verifyTokenSubtitle": "We sent a verification link to {{email}}", "tokenLabel": "Verification code", "tokenPlaceholder": "Enter 6-digit code", "verify": "Verify", "didntReceiveToken": "Didn't receive the email?", "resendToken": "Resend email", "resendIn": "Resend in {{seconds}}s", "checkSpamFolder": "Please check your spam folder if you don't see the email", "invalidToken": "Invalid or expired verification code", "magicLinkSent": "Magic link sent successfully", "verification": {"emailTitle": "<PERSON><PERSON><PERSON>", "phoneTitle": "Verify Phone", "emailHeading": "Verify your email", "phoneHeading": "Verify your phone", "emailDescription": "We've sent a 6-digit verification code\nto {{email}}", "phoneDescription": "We've sent a 6-digit verification code\nto {{phone}}", "codeInputLabel": "Enter verification code", "verifyButton": "Verify {{type}}", "verifyingButton": "Verifying...", "resendCodeQuestion": "Didn't receive the code?", "resendCodeButton": "Resend code", "resendCooldown": "Resend in {{seconds}}s", "codeSentTitle": "Code Sent", "codeSentMessage": "A new verification code has been sent to your {{type}}", "errors": {"incompleteCode": "Please enter the complete 6-digit code", "identifierNotFound": "{{type}} not found", "invalidCode": "Invalid verification code", "resendFailed": "Failed to resend code. Please try again.", "verificationFailed": "Verification failed. Please try again."}}, "emailSignupTitle": "Create your account", "emailSignupSubtitle": "Join our community and start connecting", "emailLoginTitle": "Welcome back", "emailLoginSubtitle": "Sign in to continue to your account", "nameLabel": "Full name", "namePlaceholder": "Enter your full name", "passwordLabel": "Password", "passwordPlaceholder": "Create a strong password", "confirmPasswordLabel": "Confirm password", "confirmPasswordPlaceholder": "Re-enter your password", "alreadyHaveAccount": "Already have an account?", "signIn": "Sign in", "loginSuccess": "Successfully signed in!", "registrationSuccess": "Account created successfully!", "errors": {"general": "Authentication failed. Please try again.", "userNotFound": "No account found with this email address.", "invalidPassword": "Incorrect password. Please try again.", "emailAlreadyExists": "An account with this email already exists.", "invalidEmail": "Please enter a valid email address.", "weakPassword": "Password is too weak. Please choose a stronger password.", "invalidToken": "Invalid or expired verification code.", "accountDisabled": "Your account has been disabled. Please contact support.", "tooManyAttempts": "Too many failed attempts. Please try again later.", "verificationRequired": "Please verify your email address to continue.", "unauthenticated": "Please sign in to continue.", "forbidden": "You don't have permission to perform this action.", "badRequest": "Invalid request. Please check your information.", "unauthorized": "Authentication failed. Please sign in again.", "conflict": "This information conflicts with existing data.", "tooManyRequests": "Too many requests. Please wait a moment and try again.", "serverError": "Server error. Please try again later.", "serviceUnavailable": "Service temporarily unavailable. Please try again later.", "networkError": "Network error. Please check your connection.", "titles": {"general": "Authentication Error", "userNotFound": "Account Not Found", "invalidPassword": "Incorrect Password", "emailExists": "Email Already Registered", "tooManyAttempts": "Too Many Attempts", "verificationRequired": "Verification Required", "serverError": "Server Error"}}}, "common": {"back": "Back", "cancel": "Cancel", "confirm": "Confirm", "delete": "Delete", "discard": "Discard", "done": "Done", "edit": "Edit", "error": "Error", "item": "item", "loading": "Loading...", "logout": "Logout", "next": "Next", "no": "No", "refresh": "Refresh", "retry": "Try again", "save": "Save", "yes": "Yes", "tryAgain": "Please try again", "success": "Success"}, "error": {"forbidden": "You do not have permission to perform this action.", "generic": "Something went wrong. Please try again.", "network": "Network error. Please check your connection.", "unauthorized": "You need to log in to continue.", "validation": "Please check your input."}, "language": {"name": "English"}, "notifications": {"empty": {"title": "Empty", "description": "You don't have any notification at this time"}, "categories": {"new": "New", "earlier": "Earlier"}, "achievement": {"message": "{{achievement}}: {{description}}", "title": "Achievement Unlocked!"}, "actions": {"accept": "Accept", "reject": "Reject", "new": "New", "copyCode": "Copy Code", "dismiss": "<PERSON><PERSON><PERSON>", "followBack": "Follow Back", "reply": "Reply", "retry": "Retry", "reviewActivity": "Review Activity", "secureAccount": "Secure Account", "share": "Share", "updateNow": "Update Now", "view": "View", "viewAchievements": "View Achievements", "viewAll": "View All", "viewChangelog": "View Changelog", "viewChat": "View Chat", "viewComment": "View Comment", "viewComments": "View Comments", "viewDetails": "View Details", "viewFollowers": "View Followers", "viewLikes": "View Likes", "viewPost": "View Post", "viewProfile": "View Profile"}, "error": {"title": "Error"}, "mention": {"message": "{{name}} mentioned you: {{content}}", "title": "You were mentioned"}, "multipleComments": {"message": "{{count}} new comments on your posts", "title": "Multiple Comments"}, "multipleFollowers": {"message": "{{count}} people started following you", "title": "New Followers"}, "multipleLikes": {"message": "{{count}} people liked your posts", "title": "Multiple Likes"}, "newComment": {"message": "{{name}} commented on {{post}}: {{comment}}", "title": "New Comment"}, "newFollower": {"message": "{{name}} started following you", "title": "New Follower"}, "newLike": {"message": "{{name}} liked your post: {{post}}", "title": "New Like"}, "security": {"message": "{{action}} from {{location}}", "title": "Security Alert", "unknownLocation": "unknown location"}, "success": {"title": "Success"}, "systemUpdate": {"message": "Version {{version}} is now available with: {{features}}", "title": "App Updated"}, "trending": {"message": "Your post \"{{post}}\" is trending with {{likes}} likes and {{comments}} comments", "title": "Trending Post"}}, "time": {"businessHours": "Business Hours", "expired": "Expired", "yesterday": "Yesterday"}, "trending": {"errorLoadingPosts": "Failed to load trending posts", "errorTitle": "Something went wrong", "filters": {"all": "All", "categories": "Categories", "comingSoon": "Advanced filters coming soon!", "comingSoonDesc": "Category and venue filters will be available in the next update.", "following": "Following", "moreFilters": "More Filters", "nearby": "Nearby", "reset": "Reset", "venues": "Venues"}, "interactions": {"comment": "Comment", "like": "Like", "save": "Save", "saved": "Post saved!", "share": "Share", "shareError": "Failed to share post", "unsaved": "Post removed from saved"}, "noPosts": "No trending posts", "noPostsMessage": "Be the first to share something trending!", "report": {"confirm": "Report", "message": "Are you sure you want to report this content as inappropriate?", "submitted": "Report submitted successfully", "title": "Report Post"}, "share": {"notAvailable": "Sharing not available", "notAvailableMessage": "Sharing is not supported on this platform", "title": "Share Post"}, "sort": {"popular": "Popular", "recent": "Recent", "trending": "Trending"}, "title": "Trending"}, "user": {"avatar": "Profile picture", "bio": "Bio", "coverPhoto": "Cover photo", "editProfile": "Edit profile", "followed": "You are now following this user", "location": "Location", "notifications": "Notifications", "privacy": "Privacy", "profile": "Profile", "settings": "Settings", "unfollowed": "You are no longer following this user"}, "validation": {"comment": {"content": {"maxLength": "Comment must be less than {{max}} characters", "notEmpty": "Comment cannot be empty or only whitespace", "required": "Comment content is required"}}, "email": {"invalid": "Please enter a valid email address", "required": "Email is required"}, "file": {"invalidType": "Invalid file type. Allowed types: {{types}}", "tooLarge": "File size must be less than {{max}}"}, "name": {"maxLength": "Name must be less than {{max}} characters", "minLength": "Name must be at least {{min}} characters", "required": "Name is required"}, "number": {"max": "Value must be at most {{max}}", "min": "Value must be at least {{min}}", "positive": "Value must be positive"}, "password": {"minLength": "Password must be at least {{min}} characters", "noMatch": "Passwords do not match", "required": "Password is required", "weak": "Password is too weak", "uppercase": "Password must contain at least one uppercase letter", "lowercase": "Password must contain at least one lowercase letter", "number": "Password must contain at least one number"}, "confirmPassword": {"required": "Please confirm your password", "mismatch": "Passwords do not match"}, "token": {"required": "Verification code is required", "minLength": "Verification code must be at least {{min}} characters"}, "phone": {"invalid": "Please enter a valid phone number", "required": "Phone number is required"}, "post": {"content": {"maxLength": "Post content must be less than {{max}} characters", "notEmpty": "Post content cannot be empty or only whitespace", "required": "Post content is required"}, "hashtags": {"maxCount": "Maximum {{max}} hashtags allowed"}, "images": {"invalidUrl": "Invalid image URL", "maxCount": "Maximum {{max}} images allowed"}}, "report": {"description": {"maxLength": "Description must be less than {{max}} characters"}, "reason": {"required": "Please select a reason for reporting"}}, "required": "This field is required", "url": {"invalid": "Please enter a valid URL"}, "username": {"invalid": "Username can only contain letters, numbers, and underscores", "maxLength": "Username must be less than {{max}} characters", "minLength": "Username must be at least {{min}} characters", "required": "Username is required"}}, "venue": {"supported": "You are now supporting this venue", "unsupported": "You are no longer supporting this venue", "status": {"open": "Open Now", "closed": "Closed"}, "registration": {"basicDetails": {"title": "Basic Details", "venueName": "Venue Name", "venueNamePlaceholder": "Enter venue name", "musicGenres": "Music Genres", "musicGenresPlaceholder": "Select music genres", "dressCode": "Dress Code", "dressCodePlaceholder": "Select dress codes", "description": "Description", "descriptionPlaceholder": "Tell us about your venue...", "location": "Location", "locationPlaceholder": "Search for venue location", "photos": "Venue Photos", "photosHelper": "{{current}} of {{max}} photos added"}, "openingTimes": {"title": "Opening Times", "regularHours": "Regular Hours", "specialHours": "Special Hours", "specialHoursDescription": "Add special hours for holidays or events", "addSpecialHours": "Add Special Hours", "happyHours": "Happy Hours", "enableHappyHours": "Enable Happy Hours", "happyHoursPlaceholder": "Configure happy hour times", "selectOpeningTime": "Select Opening Time", "selectClosingTime": "Select Closing Time"}, "features": {"title": "Features & Amenities", "clubFeatures": "Club Features", "capacity": "Capacity", "capacityPlaceholder": "Maximum number of guests", "minimumAge": "Minimum Age", "minimumAgePlaceholder": "18", "contactInfo": "Contact Information", "phoneNumber": "Phone Number", "phoneNumberPlaceholder": "****** 567 8900", "email": "Email", "emailPlaceholder": "<EMAIL>", "website": "Website", "websitePlaceholder": "https://example.com", "instagram": "Instagram", "instagramPlaceholder": "@venuename", "categories": {"entertainment": "Entertainment", "areas": "Special Areas", "amenities": "Amenities", "payment": "Payment Methods"}, "parking": "Parking", "wifi": "WiFi", "vip": "VIP Area"}, "summary": {"title": "Review & Submit", "basicDetails": "Basic Details", "openingTimes": "Opening Times", "features": "Features", "venueName": "Venue Name", "musicGenres": "Music Genres", "dressCodes": "Dress Codes", "location": "Location", "capacity": "Capacity", "submit": "Submit for Approval"}, "success": {"title": "Venue Registered!", "message": "Your venue has been submitted for approval. We'll notify you once it's reviewed.", "viewVenue": "View Venue"}, "locationSearch": {"title": "Search Location", "placeholder": "Search for address or place", "noResults": "No results found"}}, "features": {"liveMusic": "Live Music", "djBooth": "DJ <PERSON>", "danceFloor": "Dance Floor", "karaoke": "Karaoke", "vipArea": "VIP Area", "lounge": "Lounge", "privateEventArea": "Private Event Area", "outdoorArea": "Outdoor Area", "smokingArea": "Smoking Area", "parking": "Parking", "wifi": "WiFi", "accessibility": "Accessibility", "coatCheck": "Coat Check", "atm": "ATM", "food": "Food", "drinks": "Drinks", "alcohol": "Alcohol", "smoking": "Smoking", "cash": "Cash"}, "categories": {"entertainment": "Entertainment", "areas": "Special Areas", "amenities": "Amenities", "payment": "Payment Methods"}}, "location": {"search": {"placeholder": "Search for places...", "nearMe": "Near me", "trending": "Trending places", "popular": "Popular", "nearby": "Nearby", "searching": "Searching...", "noResults": "No places found", "tryDifferentSearch": "Try a different search term"}, "autocomplete": {"place": "Place", "address": "Address", "geo": "Location", "search": "Search"}, "categories": {"all": "All Categories", "restaurant": "Restaurant", "cafe": "Cafe", "bar": "Bar", "nightclub": "Nightclub", "shopping": "Shopping", "entertainment": "Entertainment", "sports": "Sports & Recreation", "health": "Health & Medicine", "business": "Business", "travel": "Travel & Transportation", "outdoors": "Outdoors", "arts": "Arts & Entertainment", "food": "Food & Drink"}, "details": {"address": "Address", "phone": "Phone", "website": "Website", "hours": "Hours", "rating": "Rating", "price": "Price", "distance": "Distance", "directions": "Get directions", "call": "Call", "share": "Share", "save": "Save place", "openNow": "Open now", "closedNow": "Closed now", "openingSoon": "Opening soon", "closingSoon": "Closing soon", "viewPhotos": "View photos", "viewTips": "View tips", "checkIn": "Check in", "verified": "Verified"}, "filters": {"title": "Filters", "openNow": "Open now", "distance": "Distance", "rating": "Rating", "price": "Price level", "sortBy": "Sort by", "relevance": "Relevance", "popularity": "Popularity", "apply": "Apply filters", "clear": "Clear all", "priceLevel": {"cheap": "$", "moderate": "$", "expensive": "$$", "veryExpensive": "$$"}}, "features": {"wifi": "WiFi", "parking": "Parking", "outdoorSeating": "Outdoor seating", "takeout": "Takeout", "delivery": "Delivery", "reservations": "Reservations", "wheelchairAccessible": "Wheelchair accessible", "creditCards": "Credit cards accepted", "applePay": "Apple Pay", "googlePay": "Google Pay", "alcohol": "Serves alcohol", "liveMusic": "Live music", "tvs": "TVs", "powerOutlets": "Power outlets"}, "errors": {"locationPermission": "Location permission required", "locationUnavailable": "Location unavailable", "searchFailed": "Search failed. Please try again.", "detailsFailed": "Failed to load place details", "networkError": "Network error. Check your connection.", "apiKeyMissing": "Location service not configured", "rateLimited": "Too many requests. Please wait.", "unknownError": "An unexpected error occurred"}, "permissions": {"title": "Location Access", "message": "Allow Movuca to access your location to find places near you", "allow": "Allow", "deny": "Don't Allow", "settings": "Go to Settings"}, "distance": {"meters": "{{count}} m", "kilometers": "{{count}} km", "feet": "{{count}} ft", "miles": "{{count}} mi"}, "time": {"open24h": "Open 24 hours", "closedToday": "Closed today", "holiday": "Holiday hours", "seasonal": "Seasonal hours", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}}, "onboarding": {"skip": "<PERSON><PERSON>", "next": "Next", "getStarted": "Get Started", "welcome": {"title": "Welcome to Movuca", "description": "Discover amazing content and connect with people who share your interests"}, "share": {"title": "Share & Celebrate", "description": "Share your achievements and celebrate with the community"}, "connect": {"title": "Stay Connected", "description": "Join groups, follow friends, and never miss what matters to you"}}, "welcome": {"alreadyHaveAccount": "Already have an account?", "description": "Discover amazing content and connect with people who share your interests", "getStarted": "Get Started", "legalNotice": "By continuing, you agree to our <0>{{TermsOfService}}</0> and <1>{{PrivacyPolicy}}</1>.", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "title": "Welcome to Movuca"}, "datePicker": {"selectDate": "Select a date", "selectDates": "Select dates", "selectTime": "Select a time", "selectDateTime": "Select date and time", "selectYearMonth": "Select year and month", "confirmText": "Confirm", "cancelText": "Cancel", "today": "Today", "tomorrow": "Tomorrow", "yesterday": "Yesterday"}, "explore": {"tabs": {"discover": "Discover", "local": "Local", "trending": "Trending"}, "selectLocation": "Select location", "selectLocationPrompt": "Select your location to see nearby events", "filter": "Filter", "map": "Map", "list": "List", "change": "Change", "all": "All", "friendsActivity": "Friends Activity", "error": {"loadingEvents": "Unable to load events. Please try again.", "loadingActivities": "Unable to load activities. Please try again.", "loadingTrending": "Unable to load trending content. Please try again.", "locationRequired": "Location access is required to find nearby events"}, "noEventsFound": "No events found", "noActivitiesFound": "No activities found", "timeFilters": {"after11pm": "After 11 PM", "tonight": "Tonight", "tomorrow": "Tomorrow", "thisWeekend": "This Weekend"}, "categories": {"all": "All", "afterParties": "After parties", "houseParties": "House parties", "lounges": "Lounges", "electronic": "Electronic", "clubs": "Clubs"}, "local": {"findingNearby": "Finding nearby events...", "nearbyEvents": "Events within {{radius}}km", "viewMap": "View Map", "viewList": "View List", "noNearbyEvents": "No events found nearby", "change": "Change", "filter": "Filter", "sellingOut": "Selling out", "joinNow": "Join the excitement now!", "reactionsCount": "{{count}} reactions and counting!", "noReactions": "Be the first to react!", "discover": "Discover"}, "trending": {"hotParties": "Hot Parties", "hotVenues": "Hot Venues", "friendsActivity": "Friends Activity", "noTrendingContent": "No trending content at the moment"}}}