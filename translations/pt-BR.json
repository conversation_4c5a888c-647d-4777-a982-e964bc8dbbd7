{"alerts": {"confirmDelete": {"message": "Esta ação não pode ser desfeita.", "title": "Excluir {{item}}?"}, "confirmLogout": {"message": "Você precisará fazer login novamente para acessar sua conta.", "title": "Sair?"}, "unsavedChanges": {"message": "Suas alterações não salvas serão perdidas.", "title": "Descartar alterações?"}}, "auth": {"alreadyHaveAccount": "Já tem uma conta?", "checkSpamFolder": "Por favor, verifique sua pasta de spam se você não vê o e-mail.", "chooseMethodSubtitle": "Escolha como você gostaria de continuar", "confirmPasswordLabel": "Confirme a senha", "confirmPasswordPlaceholder": "Digite sua senha novamente", "continue": "<PERSON><PERSON><PERSON><PERSON>", "createAccount": "C<PERSON><PERSON> conta", "didntReceiveToken": "Não recebeu o e-mail?", "dontHaveAccount": "Não tem uma conta?", "email": "Email", "emailLabel": "E-mail", "emailLoginSubtitle": "Faça o login para continuar em sua conta", "emailLoginTitle": "Bem-vindo de volta", "emailOptionTitle": "Use seu email", "emailPlaceholder": "Digite seu e-mail", "emailSignupSubtitle": "Junte-se à nossa comunidade e comece a se conectar", "emailSignupTitle": "Crie sua conta", "forgotPassword": "Esque<PERSON>u a senha?", "invalidToken": "Código de verificação inválido ou expirado", "verification": {"emailTitle": "Verificar Email", "phoneTitle": "Verificar Telefone", "emailHeading": "Verifique seu email", "phoneHeading": "Verifique seu telefone", "otpLoginHeading": "Digite o código de login", "emailDescription": "Enviamos um código de verificação de 6 dígitos\npara {{email}}", "phoneDescription": "Enviamos um código de verificação de 6 dígitos\npara {{phone}}", "otpLoginDescription": "Digite o código de 6 dígitos que enviamos para {{email}}", "codeInputLabel": "Digite o código de verificação", "verifyButton": "Verificar {{type}}", "verifyingButton": "Verificando...", "resendCodeQuestion": "Não recebeu o código?", "resendCodeButton": "Reenviar código", "resendCooldown": "Reenviar em {{seconds}}s", "codeSentTitle": "<PERSON><PERSON><PERSON>", "codeSentMessage": "Um novo código de verificação foi enviado para seu {{type}}", "errors": {"incompleteCode": "Por favor, digite o código completo de 6 dígitos", "identifierNotFound": "{{type}} não encontrado", "invalidCode": "Código de verificação inválido", "resendFailed": "Falha ao reenviar código. Tente novamente.", "verificationFailed": "Verificação falhou. Tente novamente."}}, "login": "Entrar", "logout": "<PERSON><PERSON>", "magicLinkInfo": "Verifique seu e-mail para um link seguro para fazer login. Nenhuma senha necessária!", "magicLinkOptionTitle": "Use link mágico", "magicLinkSent": "Link mágico enviado com sucesso", "nameLabel": "Nome completo", "namePlaceholder": "Digite seu nome completo", "or": "OU", "password": "<PERSON><PERSON>", "passwordLabel": "<PERSON><PERSON>", "passwordPlaceholder": "Crie uma senha forte", "passwordlessSubtitle": "Enviaremos um link mágico para o seu endereço de e-mail", "passwordlessTitle": "Conectar-se com link mágico", "phoneNumberRegistrationButton": "Enviar código", "phoneNumberRegistrationLabel": "Número de telefone", "phoneNumberRegistrationMessage": "Nós enviaremos um código de uso único via SMS para você fazer o login.", "phoneNumberRegistrationPlaceholder": "+55 9 9999-9999", "phoneNumberRegistrationTitle": "Qual é o seu número de telefone?", "phoneOptionTitle": "Utilize seu número de telefone", "resendIn": "Reenviar em {{seconds}}s", "resendToken": "Reenviar e-mail", "sendMagicLink": "Enviar link mágico", "signIn": "Entrar", "signUp": "Cadastre-se", "signup": "Inscreva-se", "tokenLabel": "Código de verificação", "tokenPlaceholder": "Insira o código de 6 dígitos", "verify": "Verificar", "verifyTokenSubtitle": "Enviamos um link de verificação para {{email}}.", "verifyTokenTitle": "Verifique seu e-mail", "welcomeBack": "Bem-vindo de volta", "welcomeMessage": "<PERSON>aça login ou Crie uma conta"}, "common": {"back": "Voltar", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "delete": "Excluir", "discard": "Descar<PERSON>", "done": "Pronto", "edit": "<PERSON><PERSON>", "error": "Erro", "item": "item", "loading": "Carregando...", "logout": "<PERSON><PERSON>", "next": "Próximo", "no": "Não", "refresh": "<PERSON><PERSON><PERSON><PERSON>", "retry": "Tente novamente", "save": "<PERSON><PERSON>", "success": "Sucesso", "tryAgain": "Por favor, tente novamente", "yes": "<PERSON>m"}, "error": {"forbidden": "Você não tem permissão para realizar esta ação.", "generic": "Algo deu errado. Por favor, tente novamente.", "network": "Erro de rede. Por favor, verifique sua conexão.", "unauthorized": "Você precisa fazer login para continuar.", "validation": "Por favor, verifique sua entrada."}, "language": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "notifications": {"achievement": {"message": "{{conquista}}: {{descri<PERSON>}}", "title": "Conquista Desbloqueada!"}, "actions": {"copyCode": "<PERSON><PERSON>r <PERSON>", "dismiss": "Dispensar", "followBack": "<PERSON><PERSON><PERSON>", "reply": "Resposta", "retry": "Tente novamente", "reviewActivity": "Revisar Atividade", "secureAccount": "<PERSON><PERSON>", "share": "Compartilhar", "updateNow": "<PERSON><PERSON><PERSON><PERSON>", "view": "<PERSON>er", "viewAchievements": "<PERSON>er Con<PERSON>", "viewAll": "<PERSON><PERSON>", "viewChangelog": "Ver Registro de Alterações", "viewChat": "<PERSON><PERSON>", "viewComment": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "viewComments": "<PERSON>er Comentá<PERSON>s", "viewDetails": "<PERSON><PERSON>", "viewFollowers": "<PERSON><PERSON>", "viewLikes": "<PERSON><PERSON>", "viewPost": "Ver Postagem", "viewProfile": "<PERSON><PERSON>"}, "error": {"title": "Erro"}, "mention": {"message": "{{name}} <PERSON><PERSON><PERSON> você: {{content}}", "title": "Você foi mencionado."}, "multipleComments": {"message": "{{count}} novos comentários em suas postagens", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "multipleFollowers": {"message": "{{count}} pessoas come<PERSON> a te seguir", "title": "Novos Seguidores"}, "multipleLikes": {"message": "{{count}} pessoas gostaram das suas postagens", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "newComment": {"message": "{{name}} comentou em {{post}}: {{comment}}", "title": "Novo Comentário"}, "newFollower": {"message": "{{name}} passou a te seguir", "title": "Novo Seguidor"}, "newLike": {"message": "{{name}} gostou da sua publicação: {{post}}", "title": "Novo Curtir"}, "security": {"message": "{{ação}} de {{local}}", "title": "Alerta de Segurança", "unknownLocation": "localização desconhecida"}, "success": {"title": "Sucesso"}, "systemUpdate": {"message": "A versão {{version}} já está disponível com: {{features}}", "title": "Aplicativo Atualizado"}, "trending": {"message": "<PERSON><PERSON> post \"{{post}}\" está em alta com {{likes}} curtidas e {{comments}} coment<PERSON>rios.", "title": "Postagem em Alta"}}, "time": {"businessHours": "<PERSON><PERSON><PERSON><PERSON>l", "expired": "V<PERSON>cid<PERSON>", "yesterday": "Ontem"}, "trending": {"errorLoadingPosts": "Falha ao carregar postagens em alta", "errorTitle": "<PERSON>go deu errado", "filters": {"all": "<PERSON><PERSON>", "categories": "Categorias", "comingSoon": "Filtros avançados em breve!", "comingSoonDesc": "Filtros de categoria e de local estarão disponíveis na próxima atualização.", "following": "<PERSON><PERSON><PERSON>", "moreFilters": "<PERSON><PERSON>", "nearby": "Nas proximidades", "reset": "Redefinir", "venues": "Locais"}, "interactions": {"comment": "<PERSON><PERSON><PERSON><PERSON>", "like": "Curtir", "save": "<PERSON><PERSON>", "saved": "Postagem salva!", "share": "Compartilhar", "shareError": "Falha ao compartilhar a postagem", "unsaved": "Postagem removida dos salvos"}, "noPosts": "Sem posts em alta", "noPostsMessage": "Seja o primeiro a compartilhar algo que está em alta!", "report": {"confirm": "Relat<PERSON><PERSON>", "message": "Você tem certeza de que deseja denunciar este conteúdo como inadequado?", "submitted": "Relatório enviado com sucesso", "title": "Reportar Postagem"}, "share": {"notAvailable": "Compartilhamento não disponível", "notAvailableMessage": "O compartilhamento não é suportado nesta plataforma.", "title": "Compartilhar publicação"}, "sort": {"popular": "Popular", "recent": "<PERSON><PERSON>", "trending": "Tendências"}, "title": "Em alta"}, "user": {"avatar": "Foto do perfil", "bio": "Bio", "coverPhoto": "Foto de capa", "editProfile": "<PERSON><PERSON> perfil", "followed": "Você agora está seguindo este usuário.", "location": "Localização", "notifications": "Notificações", "privacy": "Privacidade", "profile": "Perfil", "settings": "Configurações", "unfollowed": "Você não está mais seguindo este usuário."}, "validation": {"comment": {"content": {"maxLength": "O coment<PERSON>rio deve ter menos de {{max}} caracteres", "notEmpty": "O comentário não pode estar vazio ou conter apenas espaços em branco.", "required": "O conteúdo do comentário é obrigatório"}}, "confirmPassword": {"mismatch": "As senhas não coincidem", "required": "Por favor, confirme sua senha"}, "email": {"invalid": "Por favor, insira um endereço de e-mail válido.", "required": "O email é obrigatório"}, "file": {"invalidType": "Tipo de arquivo inválido. Tipos permitidos: {{types}}", "tooLarge": "O tamanho do arquivo deve ser menor que {{max}}."}, "name": {"maxLength": "O nome deve ter menos de {{max}} caracteres", "minLength": "O nome deve ter pelo menos {{min}} caracteres", "required": "O nome é obrigatório"}, "number": {"max": "O valor deve ser no máximo {{max}}", "min": "O valor deve ser pelo menos {{min}}", "positive": "O valor deve ser positivo."}, "password": {"lowercase": "A senha deve conter pelo menos uma letra minúscula.", "minLength": "A senha deve ter pelo menos {{min}} caracteres.", "noMatch": "As senhas não coincidem", "number": "A senha deve conter pelo menos um número", "required": "Senha é obrigatória", "uppercase": "A senha deve conter pelo menos uma letra mai<PERSON>.", "weak": "A senha é muito fraca"}, "phone": {"invalid": "Por favor, insira um número de telefone válido.", "required": "O número de telefone é obrigatório."}, "post": {"content": {"maxLength": "O conteúdo da postagem deve ter menos de {{max}} caracteres.", "notEmpty": "O conteúdo da postagem não pode estar vazio ou conter apenas espaços em branco.", "required": "O conteúdo do post é necessário"}, "hashtags": {"maxCount": "<PERSON><PERSON><PERSON><PERSON> de {{max}} hashtags permitidas"}, "images": {"invalidUrl": "URL da imagem inválida", "maxCount": "Máximo de {{max}} imagens permitidas"}}, "report": {"description": {"maxLength": "A descrição deve ter menos de {{max}} caracteres."}, "reason": {"required": "Por favor, selecione um motivo para relatar."}}, "required": "Este campo é obrigatório", "token": {"minLength": "O código de verificação deve ter pelo menos {{min}} caracteres", "required": "É necessário um código de verificação"}, "url": {"invalid": "Por favor, insira uma URL válida"}, "username": {"invalid": "O nome de usuário pode conter apenas letras, números e underscores.", "maxLength": "O nome de usuário deve ter menos de {{max}} caracteres.", "minLength": "O nome de usuário deve ter pelo menos {{min}} caracteres.", "required": "O nome de usuário é obrigatório."}}, "venue": {"supported": "Você está agora apoiando este local.", "unsupported": "Você não está mais apoiando este local"}, "welcome": {"alreadyHaveAccount": "Já tem uma conta?", "description": "Descubra conteúdos incríveis e conecte-se com pessoas que compartilham seus interesses.", "getStarted": "<PERSON><PERSON><PERSON>", "legalNotice": "Ao continuar, você concorda com nossos <0>{{TermsOfService}}</0> e <1>{{PrivacyPolicy}}</1>.", "privacyPolicy": "Política de Privacidade", "termsOfService": "Termos de Serviço", "title": "Bem-vindo à Movuca"}}