{"alerts": {"confirmDelete": {"message": "Ta akcja nie może być cofni<PERSON>.", "title": "<PERSON>uń {{item}}?"}, "confirmLogout": {"message": "Będziesz musiał ponownie się zalogować, aby uzy<PERSON>ć dostęp do swojego konta.", "title": "<PERSON><PERSON> ch<PERSON>z się wylogowa<PERSON>?"}, "unsavedChanges": {"message": "Twoje niezapisane zmiany zostaną utracone.", "title": "<PERSON><PERSON> <PERSON><PERSON><PERSON> z<PERSON>?"}}, "auth": {"createAccount": "Utwórz konto", "email": "Email", "forgotPassword": "Zapom<PERSON>ł<PERSON><PERSON> hasła?", "login": "<PERSON><PERSON><PERSON><PERSON>", "logout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "password": "<PERSON><PERSON><PERSON>", "signup": "Zarejestruj się", "welcomeBack": "Witamy z powrotem"}, "common": {"back": "Wstecz", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Potwierdź", "delete": "Usuń", "discard": "<PERSON><PERSON><PERSON><PERSON>", "done": "Zrobione", "edit": "<PERSON><PERSON><PERSON><PERSON>", "error": "Błąd", "item": "element", "loading": "Ładowanie...", "logout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "next": "Następny", "no": "<PERSON><PERSON>", "refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retry": "Spróbuj ponownie", "save": "<PERSON><PERSON><PERSON><PERSON>", "yes": "Tak"}, "error": {"forbidden": "Nie masz uprawnień do wykonania tej akcji.", "generic": "Coś poszło nie tak. Proszę spróbować ponownie.", "network": "Błąd sieci. Proszę sprawdzić połączenie.", "unauthorized": "<PERSON><PERSON><PERSON>, a<PERSON> k<PERSON>.", "validation": "<PERSON>sz<PERSON> sprawdzić swój wpis."}, "language": {"name": "<PERSON><PERSON><PERSON>"}, "notifications": {"achievement": {"message": "{{o<PERSON><PERSON><PERSON><PERSON><PERSON>}}: {{opis}}", "title": "Osiągnięcie odblokowane!"}, "actions": {"copyCode": "<PERSON><PERSON><PERSON><PERSON> kod", "dismiss": "<PERSON><PERSON><PERSON><PERSON>", "followBack": "Obserwuj z powrotem", "reply": "<PERSON><PERSON><PERSON><PERSON><PERSON>ź", "retry": "Ponów próbę", "reviewActivity": "Przegląd aktywności", "secureAccount": "Zabezpiecz konto", "share": "Udostępnij", "updateNow": "Zaktualizuj te<PERSON>", "view": "Widok", "viewAchievements": "<PERSON><PERSON><PERSON><PERSON>", "viewAll": "Zobacz wszystkie", "viewChangelog": "Zobacz dziennik zmian", "viewChat": "<PERSON><PERSON><PERSON><PERSON>", "viewComment": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>ntarz", "viewComments": "Wyświetl komentarze", "viewDetails": "Zobacz szczegóły", "viewFollowers": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>erwuj<PERSON>", "viewLikes": "Zobacz polubienia", "viewPost": "Zobacz post", "viewProfile": "<PERSON><PERSON><PERSON><PERSON> profil"}, "error": {"title": "Błąd"}, "mention": {"message": "{{name}} wspomniał o Tobie: {{content}}", "title": "Zostałeś wspomniany"}, "multipleComments": {"message": "{{count}} nowych komentarzy do twoich postów", "title": "Wiele komentarzy"}, "multipleFollowers": {"message": "{{count}} os<PERSON><PERSON> z<PERSON>zęło cię obserwować", "title": "Nowi obserwujący"}, "multipleLikes": {"message": "{{count}} os<PERSON><PERSON> polubiło twoje posty", "title": "Wiele polubień"}, "newComment": {"message": "{{name}} skomentował na {{post}}: {{comment}}", "title": "Nowy komentarz"}, "newFollower": {"message": "{{name}} zac<PERSON><PERSON>ł cię obserwować", "title": "Nowy Obserwujący"}, "newLike": {"message": "{{name}} polubił(a) Twój post: {{post}}", "title": "Nowa polubienie"}, "security": {"message": "{{action}} z {{location}}", "title": "Ostrzeżenie o bezpieczeństwie", "unknownLocation": "<PERSON><PERSON><PERSON><PERSON> lo<PERSON>"}, "success": {"title": "Sukces"}, "systemUpdate": {"message": "We<PERSON>ja {{version}} jest teraz <PERSON> z: {{features}}", "title": "Aplikacja zaktualizowana"}, "trending": {"message": "Twój post \"{{post}}\" jest popularny, zdobywając {{likes}} polubień i {{comments}} komentarzy.", "title": "Wyróżniony post"}}, "time": {"businessHours": "<PERSON><PERSON><PERSON> pracy", "expired": "<PERSON><PERSON><PERSON>ł<PERSON>", "yesterday": "<PERSON><PERSON><PERSON><PERSON>"}, "trending": {"errorLoadingPosts": "Nie udało się załadować popularnych postów", "errorTitle": "Coś poszło nie tak", "filters": {"all": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "categories": "<PERSON><PERSON><PERSON>", "comingSoon": "Wkró<PERSON>ce dostępne będą zaawansowane filtry!", "comingSoonDesc": "Filtry kategorii i miejsc będą dostępne w następnej aktualizacji.", "following": "Następuj<PERSON><PERSON>", "moreFilters": "Więcej filtrów", "nearby": "W pobliżu", "reset": "<PERSON><PERSON><PERSON><PERSON>", "venues": "<PERSON><PERSON><PERSON>"}, "interactions": {"comment": "Komentarz", "like": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON><PERSON>", "saved": "Post zapisany!", "share": "Udostępnij", "shareError": "<PERSON>e udało się udostępnić postu", "unsaved": "Post usunięty z zapisanych"}, "noPosts": "Brak popularnych postów", "noPostsMessage": "<PERSON><PERSON><PERSON><PERSON>, aby pod<PERSON><PERSON> się czy<PERSON>, co jest na topie!", "report": {"confirm": "<PERSON><PERSON>", "message": "<PERSON>zy na pewno chcesz zgłosić tę treść jako nieodpowiednią?", "submitted": "Raport został pomyślnie przesłany", "title": "Zgłoś post"}, "share": {"notAvailable": "Udostępnianie niedostępne", "notAvailableMessage": "Udostępnianie nie jest obsługiwane na tej platformie.", "title": "Udostępnij post"}, "sort": {"popular": "<PERSON><PERSON>", "recent": "Ostatni", "trending": "<PERSON><PERSON><PERSON>"}, "title": "<PERSON><PERSON><PERSON>"}, "user": {"avatar": "<PERSON><PERSON><PERSON><PERSON><PERSON> profilow<PERSON>", "bio": "Bio", "coverPhoto": "Zd<PERSON><PERSON><PERSON> okładkowe", "editProfile": "<PERSON><PERSON><PERSON><PERSON> profil", "followed": "Teraz obserwujesz tego użytkownika.", "location": "Lokalizacja", "notifications": "Powiadomienia", "privacy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "profile": "Profil", "settings": "Ustawienia", "unfollowed": "Nie obserwujesz już tego użytkownika."}, "validation": {"comment": {"content": {"maxLength": "Komentarz musi mieć mniej niż {{max}} znaków", "notEmpty": "Komentarz nie może być pusty ani składać się wyłącznie z białych znaków.", "required": "<PERSON><PERSON><PERSON><PERSON> komentarza jest wymagana"}}, "email": {"invalid": "Proszę wp<PERSON><PERSON><PERSON><PERSON> prawidłowy adres e-mail.", "required": "Email jest wymagany"}, "file": {"invalidType": "Nieprawidłowy typ pliku. Dozwolone typy: {{types}}", "tooLarge": "Rozmiar pliku musi być mniejszy niż {{max}}"}, "name": {"maxLength": "Nazwa musi mieć mniej niż {{max}} znaków", "minLength": "Nazwa musi mieć co najmniej {{min}} znaków", "required": "<PERSON><PERSON><PERSON> jest wymagana"}, "number": {"max": "<PERSON><PERSON><PERSON><PERSON> musi wyn<PERSON> maksymalnie {{max}}", "min": "<PERSON><PERSON><PERSON><PERSON> musi w<PERSON> co najmniej {{min}}", "positive": "<PERSON><PERSON>ść musi być dodatnia"}, "password": {"minLength": "Hasło musi mieć co najmniej {{min}} znaków", "noMatch": "Hasła nie pasują do siebie", "required": "<PERSON><PERSON><PERSON> jest wymagane", "weak": "<PERSON><PERSON><PERSON> jest zbyt słabe"}, "phone": {"invalid": "Proszę podać prawidłowy numer telefonu", "required": "Numer telefonu jest wymagany"}, "post": {"content": {"maxLength": "Z<PERSON>rt<PERSON>ść posta musi mieć mniej niż {{max}} znaków", "notEmpty": "Treść posta nie może być pusta ani składać się wyłącznie z białych znaków.", "required": "<PERSON><PERSON>ść posta jest wymagana"}, "hashtags": {"maxCount": "<PERSON><PERSON><PERSON><PERSON><PERSON> doz<PERSON>lone {{max}} hashtagi"}, "images": {"invalidUrl": "Nieprawidłowy adres URL obrazu", "maxCount": "<PERSON><PERSON><PERSON><PERSON><PERSON> dozwolone {{max}} obrazy"}}, "report": {"description": {"maxLength": "Opis musi mieć mniej niż {{max}} znaków"}, "reason": {"required": "Proszę wybrać powód zgłoszenia"}}, "required": "To pole jest wymagane", "url": {"invalid": "Proszę wp<PERSON><PERSON><PERSON><PERSON> prawidłowy adres URL"}, "username": {"invalid": "Nazwa użytkownika może zawierać tylko litery, cyfry i znaki podkreślenia.", "maxLength": "Nazwa użytkownika musi mieć mniej niż {{max}} znaków", "minLength": "Nazwa użytkownika musi mieć co najmniej {{min}} znaków", "required": "Wymagana jest nazwa użytkownika"}}, "venue": {"supported": "Wspierasz teraz to miejsce", "unsupported": "Nie wspierasz już tej lokalizacji."}, "welcome": {"alreadyHaveAccount": "<PERSON><PERSON> masz już konto?", "description": "Odkrywaj niesamowite treści i łącz się z ludźmi, którzy podzielają Twoje zainteresowania.", "getStarted": "Rozpocznij", "legalNotice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, z<PERSON><PERSON><PERSON>z się z naszymi <0>{{TermsOfService}}</0> oraz <1>{{PrivacyPolicy}}</1>.", "privacyPolicy": "Polityka p<PERSON>watności", "termsOfService": "Warunki korzystania z usługi", "title": "Witamy w Movuca"}}