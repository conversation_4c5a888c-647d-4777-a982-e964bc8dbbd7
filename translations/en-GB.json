{"alerts": {"confirmDelete": {"message": "This action cannot be undone.", "title": "Delete {{item}}?"}, "confirmLogout": {"message": "You'll need to sign in again to access your account.", "title": "Sign out?"}, "unsavedChanges": {"message": "Your unsaved changes will be lost.", "title": "Are you sure you want to discard the changes?"}}, "auth": {"createAccount": "Create account", "email": "Email Address", "forgotPassword": "Forgotten your password?", "login": "Log in", "logout": "Log out", "password": "Password", "signup": "Sign up", "welcomeBack": "Welcome back"}, "common": {"back": "Back", "cancel": "Cancel", "confirm": "Confirm", "delete": "Delete", "discard": "Discard", "done": "Done", "edit": "Edit", "error": "Error", "item": "item", "loading": "Loading...", "logout": "Logout", "next": "Next", "no": "No", "refresh": "Refresh", "retry": "Try again", "save": "Save", "yes": "Yes"}, "error": {"forbidden": "You do not have permission to carry out this action.", "generic": "Something went wrong. Please try again later.", "network": "Network error. Please check your connection.", "unauthorized": "You need to log in to continue.", "validation": "Please check your input again."}, "language": {"name": "English"}, "notifications": {"achievement": {"message": "{{achievement}}: {{description}}", "title": "Achievement Unlocked!"}, "actions": {"copyCode": "Copy Code", "dismiss": "Clear", "followBack": "Follow Back", "reply": "Reply", "retry": "Re<PERSON> Again", "reviewActivity": "Review Activity", "secureAccount": "Secure Account", "share": "Share", "updateNow": "Update Now", "view": "View", "viewAchievements": "View Achievements", "viewAll": "View All", "viewChangelog": "View Change Log", "viewChat": "View Chat", "viewComment": "View Comment", "viewComments": "View Comments", "viewDetails": "View Details", "viewFollowers": "View Followers", "viewLikes": "View Likes", "viewPost": "View Post", "viewProfile": "View Profile"}, "error": {"title": "Error"}, "mention": {"message": "{{name}} has mentioned you: {{content}}", "title": "You were mentioned"}, "multipleComments": {"message": "{{count}} new comments on your posts", "title": "Multiple Comments"}, "multipleFollowers": {"message": "{{count}} people have started following you", "title": "New Followers"}, "multipleLikes": {"message": "{{count}} people liked your posts", "title": "Multiple Likes"}, "newComment": {"message": "{{name}} commented on {{post}}: {{comment}}", "title": "New Comment"}, "newFollower": {"message": "{{name}} has started following you", "title": "New Follower"}, "newLike": {"message": "{{name}} liked your post: {{post}}", "title": "New Like"}, "security": {"message": "{{action}} from {{location}}", "title": "Security Notification", "unknownLocation": "unknown location"}, "success": {"title": "Success"}, "systemUpdate": {"message": "Version {{version}} is now available with: {{features}}", "title": "App Updated"}, "trending": {"message": "Your post \"{{post}}\" is trending with {{likes}} likes and {{comments}} comments", "title": "Trending Post"}}, "time": {"businessHours": "Business Hours", "expired": "Expired", "yesterday": "Yesterday"}, "trending": {"errorLoadingPosts": "Failed to load trending posts", "errorTitle": "There was a problem with your request.", "filters": {"all": "All", "categories": "Categories", "comingSoon": "Advanced filters arriving soon!", "comingSoonDesc": "Category and venue filters will be available in the next update.", "following": "Following", "moreFilters": "More Filters", "nearby": "Nearby", "reset": "Reset", "venues": "Venues"}, "interactions": {"comment": "Comment", "like": "Like", "save": "Save", "saved": "Post saved!", "share": "Share", "shareError": "Failed to share post", "unsaved": "Post removed from saved items"}, "noPosts": "No trending posts", "noPostsMessage": "Be the first to share something that's trending!", "report": {"confirm": "Report", "message": "Are you sure you want to report this content as unsuitable?", "submitted": "Report submitted successfully", "title": "Report Post"}, "share": {"notAvailable": "Sharing not available", "notAvailableMessage": "Sharing is not supported on this platform.", "title": "Share Post"}, "sort": {"popular": "Popular", "recent": "Recent", "trending": "Trending"}, "title": "Trending"}, "user": {"avatar": "Profile picture", "bio": "Bio", "coverPhoto": "Cover photo", "editProfile": "Edit profile", "followed": "You are now following this user", "location": "Location", "notifications": "Notifications", "privacy": "Privacy", "profile": "Profile", "settings": "Settings", "unfollowed": "You are no longer following this user."}, "validation": {"comment": {"content": {"maxLength": "Comment must be fewer than {{max}} characters", "notEmpty": "Comment cannot be empty or contain only whitespace", "required": "Comment content is required."}}, "email": {"invalid": "Please enter a valid email address.", "required": "Email is required."}, "file": {"invalidType": "Invalid file type. Allowed types: {{types}}", "tooLarge": "File size must be less than {{max}}"}, "name": {"maxLength": "Name must be fewer than {{max}} characters", "minLength": "Name must be at least {{min}} characters", "required": "Name is required."}, "number": {"max": "Value must be at most {{max}}", "min": "Value must be at least {{min}}", "positive": "Value must be positive."}, "password": {"minLength": "Password must be at least {{min}} characters", "noMatch": "Passwords do not match", "required": "Password is required.", "weak": "Password is too weak."}, "phone": {"invalid": "Please enter a valid telephone number", "required": "A phone number is required."}, "post": {"content": {"maxLength": "Post content must be fewer than {{max}} characters", "notEmpty": "Post content cannot be empty or contain only whitespace", "required": "Post content is required."}, "hashtags": {"maxCount": "Maximum {{max}} hashtags allowed"}, "images": {"invalidUrl": "Invalid image URL", "maxCount": "Maximum {{max}} images allowed"}}, "report": {"description": {"maxLength": "Description must be fewer than {{max}} characters"}, "reason": {"required": "Please select a reason for reporting"}}, "required": "This field is mandatory", "url": {"invalid": "Please enter a valid URL."}, "username": {"invalid": "Username can only contain letters, numbers, and underscores.", "maxLength": "Username must be fewer than {{max}} characters", "minLength": "Username must be at least {{min}} characters", "required": "Username is required."}}, "venue": {"supported": "You are now supporting this venue", "unsupported": "You are no longer supporting this venue."}, "welcome": {"alreadyHaveAccount": "Already have an account?", "description": "Discover amazing content and connect with people who share your interests.", "getStarted": "Get Started", "legalNotice": "By continuing, you agree to our <0>{{TermsOfService}}</0> and <1>{{PrivacyPolicy}}</1>.", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "title": "Welcome to Movuca"}}