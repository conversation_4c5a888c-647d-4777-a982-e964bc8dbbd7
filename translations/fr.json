{"alerts": {"confirmDelete": {"message": "Cette action ne peut pas être annulée.", "title": "Supprimer {{item}} ?"}, "confirmLogout": {"message": "Vous devrez vous reconnecter pour accéder à votre compte.", "title": "Se déconnecter ?"}, "unsavedChanges": {"message": "Vos modifications non enregistrées seront perdues.", "title": "<PERSON><PERSON> les modifications ?"}}, "auth": {"createAccount": "<PERSON><PERSON><PERSON> un compte", "email": "E-mail", "forgotPassword": "Mot de passe oublié ?", "login": "Se connecter", "logout": "Déconnexion", "password": "Mot de passe", "signup": "Inscription", "welcomeBack": "Bienvenue de retour"}, "common": {"back": "Retour", "cancel": "Annuler", "confirm": "Confirmer", "delete": "<PERSON><PERSON><PERSON><PERSON>", "discard": "<PERSON><PERSON>", "done": "Fait", "edit": "É<PERSON>er", "error": "<PERSON><PERSON><PERSON>", "item": "article", "loading": "Chargement...", "logout": "Déconnexion", "next": "Suivant", "no": "Non", "refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retry": "Essayez encore", "save": "Enregistrer", "yes": "O<PERSON>"}, "error": {"forbidden": "Vous n'avez pas la permission d'effectuer cette action.", "generic": "<PERSON><PERSON><PERSON> chose a mal tourné. Veuillez essayer à nouveau.", "network": "Erreur de réseau. Veuillez vérifier votre connexion.", "unauthorized": "Vous devez vous connecter pour continuer.", "validation": "Veuillez vérifier votre saisie."}, "language": {"name": "<PERSON><PERSON><PERSON>"}, "notifications": {"achievement": {"message": "{{réalisation}} : {{description}}", "title": "Succès débloqué !"}, "actions": {"copyCode": "Copier le code", "dismiss": "<PERSON><PERSON><PERSON>", "followBack": "Suivre en retour", "reply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retry": "<PERSON><PERSON><PERSON><PERSON>", "reviewActivity": "Activité d'évaluation", "secureAccount": "<PERSON><PERSON><PERSON>", "share": "Partager", "updateNow": "Mettre à jour maintenant", "view": "Voir", "viewAchievements": "Voir les réalisations", "viewAll": "Voir tout", "viewChangelog": "Voir le journal des modifications", "viewChat": "Voir le chat", "viewComment": "Voir le commentaire", "viewComments": "Voir les commentaires", "viewDetails": "Voir les détails", "viewFollowers": "Voir les abonnés", "viewLikes": "Voir les likes", "viewPost": "Voir le post", "viewProfile": "Voir le profil"}, "error": {"title": "<PERSON><PERSON><PERSON>"}, "mention": {"message": "{{name}} a mentionné vous : {{content}}", "title": "<PERSON><PERSON> avez é<PERSON>."}, "multipleComments": {"message": "{{count}} nouveaux commentaires sur vos publications", "title": "Commentaires multiples"}, "multipleFollowers": {"message": "{{count}} personnes ont commencé à vous suivre", "title": "Nouveaux abonnés"}, "multipleLikes": {"message": "{{count}} personnes ont aimé vos publications", "title": "Multiples J'aime"}, "newComment": {"message": "{{name}} a commenté sur {{post}} : {{comment}}", "title": "Nouveau commentaire"}, "newFollower": {"message": "{{name}} a commencé à vous suivre", "title": "Nouvel abonné"}, "newLike": {"message": "{{name}} a aimé votre publication : {{post}}", "title": "Nouveau J'aime"}, "security": {"message": "{{action}} de {{location}}", "title": "<PERSON><PERSON><PERSON> de sécurité", "unknownLocation": "emplacement inconnu"}, "success": {"title": "Su<PERSON>ès"}, "systemUpdate": {"message": "La version {{version}} est maintenant disponible avec : {{features}}", "title": "Application mise à jour"}, "trending": {"message": "Votre publication \"{{post}}\" est tendance avec {{likes}} likes et {{comments}} commentaires", "title": "Publication tendance"}}, "time": {"businessHours": "Heures d'ouverture", "expired": "Expiré", "yesterday": "<PERSON>er"}, "trending": {"errorLoadingPosts": "Échec du chargement des publications tendance", "errorTitle": "<PERSON><PERSON><PERSON> chose a mal tourné", "filters": {"all": "<PERSON>ut", "categories": "Catégories", "comingSoon": "Des filtres avancés bientôt disponibles !", "comingSoonDesc": "Les filtres de catégorie et de lieu seront disponibles dans la prochaine mise à jour.", "following": "Suivant", "moreFilters": "Plus de filtres", "nearby": "À proximité", "reset": "Réinitialiser", "venues": "<PERSON><PERSON>"}, "interactions": {"comment": "Comment", "like": "<PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON><PERSON>", "saved": "Publication enregistrée !", "share": "Partager", "shareError": "Échec du partage de la publication", "unsaved": "Publication retirée des sauvegardés"}, "noPosts": "Aucun post tendance", "noPostsMessage": "Soyez le premier à partager quelque chose de tendance !", "report": {"confirm": "Rapport", "message": "Êtes-vous sûr de vouloir signaler ce contenu comme inapproprié ?", "submitted": "Rapport soumis avec succès", "title": "Signaler le message"}, "share": {"notAvailable": "Partage non disponible", "notAvailableMessage": "Le partage n'est pas pris en charge sur cette plateforme.", "title": "Partager le post"}, "sort": {"popular": "Populaire", "recent": "<PERSON><PERSON><PERSON>", "trending": "Tendance"}, "title": "Tendances"}, "user": {"avatar": "Photo de profil", "bio": "Bio", "coverPhoto": "Photo de couverture", "editProfile": "Modifier le profil", "followed": "Vous suivez maintenant cet utilisateur.", "location": "Emplacement", "notifications": "Notifications", "privacy": "Confidentialité", "profile": "Profil", "settings": "Paramètres", "unfollowed": "Vous ne suivez plus cet utilisateur."}, "validation": {"comment": {"content": {"maxLength": "Le commentaire doit comporter moins de {{max}} caractères.", "notEmpty": "Le commentaire ne peut pas être vide ou ne contenir que des espaces.", "required": "Le contenu des commentaires est requis."}}, "email": {"invalid": "Veu<PERSON>z entrer une adresse email valide", "required": "L'email est requis"}, "file": {"invalidType": "Type de fichier invalide. Types autorisés : {{types}}", "tooLarge": "La taille du fichier doit être inférieure à {{max}}"}, "name": {"maxLength": "Le nom doit comporter moins de {{max}} caractères.", "minLength": "Le nom doit comporter au moins {{min}} caractères", "required": "Le nom est requis."}, "number": {"max": "La valeur doit être au maximum {{max}}", "min": "La valeur doit être d'au moins {{min}}", "positive": "La valeur doit être positive."}, "password": {"minLength": "Le mot de passe doit contenir au moins {{min}} caractères.", "noMatch": "Les mots de passe ne correspondent pas.", "required": "Un mot de passe est requis", "weak": "Le mot de passe est trop faible."}, "phone": {"invalid": "Veuillez entrer un numéro de téléphone valide.", "required": "Le numéro de téléphone est requis."}, "post": {"content": {"maxLength": "Le contenu de la publication doit comporter moins de {{max}} caractères.", "notEmpty": "Le contenu du message ne peut pas être vide ou composé uniquement d'espaces.", "required": "Le contenu du post est requis"}, "hashtags": {"maxCount": "Maximum {{max}} hashtags autorisés"}, "images": {"invalidUrl": "URL d'image invalide", "maxCount": "Maximum {{max}} images autorisées"}}, "report": {"description": {"maxLength": "La description doit être inférieure à {{max}} caractères."}, "reason": {"required": "Veuillez sélectionner une raison pour signaler."}}, "required": "Ce champ est requis", "url": {"invalid": "Veuillez entrer une URL valide"}, "username": {"invalid": "Le nom d'utilisateur ne peut contenir que des lettres, des chiffres et des caractères de soulignement.", "maxLength": "Le nom d'utilisateur doit contenir moins de {{max}} caractères.", "minLength": "Le nom d'utilisateur doit comporter au moins {{min}} caractères.", "required": "Le nom d'utilisateur est requis"}}, "venue": {"supported": "Vous soutenez maintenant ce lieu.", "unsupported": "Vous ne soutenez plus cet endroit."}, "welcome": {"alreadyHaveAccount": "Vous avez déjà un compte ?", "description": "Découvrez du contenu incroyable et connectez-vous avec des personnes qui partagent vos intérêts.", "getStarted": "Commencer", "legalNotice": "En continuant, vous acceptez nos <0>{{TermsOfService}}</0> et <1>{{PrivacyPolicy}}</1>.", "privacyPolicy": "Politique de confidentialité", "termsOfService": "Conditions d'utilisation", "title": "Bienvenue à Movuca"}}