{"alerts": {"confirmDelete": {"message": "Esta ação não pode ser desfeita.", "title": "Excluir {{item}}?"}, "confirmLogout": {"message": "Você precisará fazer login novamente para acessar sua conta.", "title": "Sair?"}, "unsavedChanges": {"message": "Suas alterações não salvas serão perdidas.", "title": "Descartar alterações?"}}, "auth": {"createAccount": "C<PERSON><PERSON> conta", "email": "Email", "forgotPassword": "Esque<PERSON>u a senha?", "login": "Entrar", "logout": "<PERSON><PERSON>", "password": "<PERSON><PERSON>", "signup": "Inscreva-se", "welcomeBack": "Bem-vindo de volta"}, "common": {"back": "Voltar", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "delete": "Excluir", "discard": "Descar<PERSON>", "done": "Pronto", "edit": "<PERSON><PERSON>", "error": "Erro", "item": "item", "loading": "Carregando...", "logout": "<PERSON><PERSON>", "next": "Próximo", "no": "Não", "refresh": "<PERSON><PERSON><PERSON><PERSON>", "retry": "Tente novamente", "save": "<PERSON><PERSON>", "yes": "<PERSON>m"}, "error": {"forbidden": "Você não tem permissão para realizar esta ação.", "generic": "Algo deu errado. Por favor, tente novamente.", "network": "Erro de rede. Por favor, verifique sua conexão.", "unauthorized": "Você precisa fazer login para continuar.", "validation": "Por favor, verifique sua entrada."}, "language": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "notifications": {"achievement": {"message": "{{realização}}: {{descrição}}", "title": "Conquista Desbloqueada!"}, "actions": {"copyCode": "<PERSON><PERSON>r <PERSON>", "dismiss": "Dispensar", "followBack": "<PERSON><PERSON><PERSON>", "reply": "Resposta", "retry": "Tente novamente", "reviewActivity": "Atividade de Revisão", "secureAccount": "<PERSON><PERSON>", "share": "Compartilhar", "updateNow": "Atual<PERSON>r agora", "view": "Visualizar", "viewAchievements": "<PERSON>er Con<PERSON>", "viewAll": "<PERSON><PERSON>", "viewChangelog": "Ver Registro de Mudanças", "viewChat": "<PERSON><PERSON>", "viewComment": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "viewComments": "<PERSON>er Comentá<PERSON>s", "viewDetails": "<PERSON><PERSON>", "viewFollowers": "<PERSON><PERSON>", "viewLikes": "<PERSON><PERSON>", "viewPost": "Ver Postagem", "viewProfile": "<PERSON><PERSON>"}, "error": {"title": "Erro"}, "mention": {"message": "{{name}} <PERSON><PERSON><PERSON> você: {{content}}", "title": "Você foi mencionado"}, "multipleComments": {"message": "{{count}} novos comentários em suas publicações", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "multipleFollowers": {"message": "{{count}} pessoas come<PERSON> a te seguir", "title": "Novos Seguidores"}, "multipleLikes": {"message": "{{count}} pessoa<PERSON> gostaram das suas publicações", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "newComment": {"message": "{{name}} comentou em {{post}}: {{comment}}", "title": "Novo comentário"}, "newFollower": {"message": "{{name}} come<PERSON><PERSON> a te seguir", "title": "Novo Seguidores"}, "newLike": {"message": "{{name}} gostou da sua postagem: {{post}}", "title": "Novo Curtir"}, "security": {"message": "{{ação}} de {{localização}}", "title": "Alerta de Segurança", "unknownLocation": "localização desconhecida"}, "success": {"title": "Sucesso"}, "systemUpdate": {"message": "A versão {{version}} já está disponível com: {{features}}", "title": "Aplicativo Atualizado"}, "trending": {"message": "<PERSON><PERSON> post \"{{post}}\" está em alta com {{likes}} curtidas e {{comments}} coment<PERSON><PERSON>s", "title": "Postagem em Alta"}}, "time": {"businessHours": "Horário de Funcionamento", "expired": "V<PERSON>cid<PERSON>", "yesterday": "Ontem"}, "trending": {"errorLoadingPosts": "Falha ao carregar posts em alta", "errorTitle": "<PERSON>go deu errado", "filters": {"all": "Todos", "categories": "Categorias", "comingSoon": "Filtros avançados em breve!", "comingSoonDesc": "Os filtros de categoria e local estarão disponíveis na próxima atualização.", "following": "<PERSON><PERSON><PERSON>", "moreFilters": "<PERSON><PERSON>", "nearby": "<PERSON><PERSON>", "reset": "<PERSON><PERSON><PERSON>", "venues": "Locais"}, "interactions": {"comment": "<PERSON><PERSON><PERSON><PERSON>", "like": "Curtir", "save": "<PERSON><PERSON>", "saved": "Postagem salva!", "share": "Compartilhar", "shareError": "Falha ao compartilhar a publicação", "unsaved": "Postagem removida dos salvos"}, "noPosts": "Sem postagens em alta", "noPostsMessage": "Seja o primeiro a compartilhar algo que está em alta!", "report": {"confirm": "Relat<PERSON><PERSON>", "message": "Você tem certeza de que deseja denunciar este conteúdo como inadequado?", "submitted": "Relatório enviado com sucesso", "title": "Denunciar Postagem"}, "share": {"notAvailable": "Compartilhamento não disponível", "notAvailableMessage": "O compartilhamento não é suportado nesta plataforma.", "title": "Compartilhar Postagem"}, "sort": {"popular": "Popular", "recent": "<PERSON><PERSON>", "trending": "Tendências"}, "title": "Em alta"}, "user": {"avatar": "Foto de perfil", "bio": "Bio", "coverPhoto": "Foto de capa", "editProfile": "<PERSON><PERSON> perfil", "followed": "Você agora está seguindo este usuário", "location": "Localização", "notifications": "Notificações", "privacy": "Privacidade", "profile": "Perfil", "settings": "Configurações", "unfollowed": "Você não está mais seguindo este usuário."}, "validation": {"comment": {"content": {"maxLength": "O coment<PERSON>rio deve ter menos de {{max}} caracteres.", "notEmpty": "O comentário não pode estar vazio ou conter apenas espaços em branco.", "required": "O conteúdo do comentário é obrigatório."}}, "email": {"invalid": "Por favor, insira um endereço de email válido.", "required": "O email é obrigatório"}, "file": {"invalidType": "Tipo de arquivo inválido. Tipos permitidos: {{types}}", "tooLarge": "O tamanho do arquivo deve ser menor que {{max}}"}, "name": {"maxLength": "O nome deve ter menos de {{max}} caracteres", "minLength": "O nome deve ter pelo menos {{min}} caracteres.", "required": "Nome é obrigatório"}, "number": {"max": "O valor deve ser no máximo {{max}}", "min": "O valor deve ser pelo menos {{min}}", "positive": "O valor deve ser positivo."}, "password": {"minLength": "A senha deve ter pelo menos {{min}} caracteres", "noMatch": "As senhas não coincidem", "required": "A senha é obrigatória", "weak": "A senha é muito fraca"}, "phone": {"invalid": "Por favor, insira um número de telefone válido.", "required": "O número de telefone é obrigatório."}, "post": {"content": {"maxLength": "O conteúdo da postagem deve ter menos de {{max}} caracteres.", "notEmpty": "O conteúdo da postagem não pode estar vazio ou conter apenas espaços em branco.", "required": "O conteúdo do post é obrigatório."}, "hashtags": {"maxCount": "<PERSON><PERSON><PERSON><PERSON> de {{max}} hashtags permitidos"}, "images": {"invalidUrl": "URL de imagem inválido", "maxCount": "Máximo de {{max}} imagens permitidas"}}, "report": {"description": {"maxLength": "A descrição deve ter menos de {{max}} caracteres"}, "reason": {"required": "Por favor, selecione uma razão para relatar"}}, "required": "Este campo é obrigatório", "url": {"invalid": "Por favor, insira uma URL válida"}, "username": {"invalid": "O nome de usuário pode conter apenas letras, números e underscores.", "maxLength": "O nome de usuário deve ter menos de {{max}} caracteres", "minLength": "O nome de usuário deve ter pelo menos {{min}} caracteres", "required": "Nome de usuário é obrigatório"}}, "venue": {"supported": "Você agora está apoiando este local", "unsupported": "Você não está mais apoiando este local."}, "welcome": {"alreadyHaveAccount": "Já tem uma conta?", "description": "Descubra conteúdos incríveis e conecte-se com pessoas que compartilham seus interesses.", "getStarted": "<PERSON><PERSON><PERSON>", "legalNotice": "Ao continuar, você concorda com nossos <0>{{TermsOfService}}</0> e <1>{{PrivacyPolicy}}</1>.", "privacyPolicy": "Política de Privacidade", "termsOfService": "Termos de Serviço", "title": "Bem-vindo à Movuca"}}